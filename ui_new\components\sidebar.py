#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代化侧边栏组件
Modern Sidebar Component

作者: AI Assistant
版本: 2.0.0
"""

import logging
from typing import Dict, List, Tuple, Optional
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QFrame, QButtonGroup, QSizePolicy
)
from PySide6.QtCore import Qt, Signal, QPropertyAnimation, QEasingCurve, QRect
from PySide6.QtGui import QIcon, QPixmap


class ModernSidebar(QWidget):
    """现代化侧边栏组件"""
    
    # 信号定义
    page_changed = Signal(str)  # 页面切换信号
    collapse_toggled = Signal(bool)  # 折叠状态切换信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        
        # 状态属性
        self.is_collapsed = False
        self.current_page = None
        
        # UI组件
        self.header_widget = None
        self.logo_label = None
        self.logo_text = None
        self.nav_menu = None
        self.user_profile = None
        self.nav_buttons = {}
        self.nav_sections = {}
        self.button_group = None
        
        # 动画
        self.collapse_animation = None
        
        # 初始化UI
        self.init_ui()
        self.setup_animations()
        self.setup_navigation()
        
        self.logger.info("现代化侧边栏组件初始化完成")
    
    def init_ui(self):
        """初始化用户界面"""
        try:
            # 设置对象名称和属性
            self.setObjectName("sidebar")
            self.setFixedWidth(280)
            self.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Expanding)
            
            # 主布局
            main_layout = QVBoxLayout(self)
            main_layout.setContentsMargins(0, 0, 0, 0)
            main_layout.setSpacing(0)
            
            # 创建头部
            self.create_header()
            main_layout.addWidget(self.header_widget)
            
            # 创建导航菜单
            self.create_nav_menu()
            main_layout.addWidget(self.nav_menu)
            
            # 添加弹性空间
            main_layout.addStretch()
            
            # 创建用户信息区域
            self.create_user_profile()
            main_layout.addWidget(self.user_profile)
            
        except Exception as e:
            self.logger.error(f"侧边栏UI初始化失败: {e}")
            raise
    
    def create_header(self):
        """创建头部区域"""
        self.header_widget = QWidget()
        self.header_widget.setObjectName("sidebar_header")
        
        layout = QHBoxLayout(self.header_widget)
        layout.setContentsMargins(20, 24, 20, 24)
        layout.setSpacing(12)
        
        # Logo
        self.logo_label = QLabel("NK")
        self.logo_label.setObjectName("logo")
        self.logo_label.setAlignment(Qt.AlignCenter)
        self.logo_label.setFixedSize(48, 48)
        layout.addWidget(self.logo_label)
        
        # 应用名称
        self.logo_text = QLabel("神经康复系统")
        self.logo_text.setObjectName("logo_text")
        layout.addWidget(self.logo_text)
        
        layout.addStretch()
    
    def create_nav_menu(self):
        """创建导航菜单"""
        self.nav_menu = QWidget()
        self.nav_menu.setObjectName("nav_menu")
        
        self.nav_layout = QVBoxLayout(self.nav_menu)
        self.nav_layout.setContentsMargins(0, 20, 0, 20)
        self.nav_layout.setSpacing(8)
        
        # 创建按钮组（用于单选效果）
        self.button_group = QButtonGroup(self)
        self.button_group.setExclusive(True)
    
    def create_user_profile(self):
        """创建用户信息区域"""
        self.user_profile = QWidget()
        self.user_profile.setObjectName("user_profile")
        
        layout = QHBoxLayout(self.user_profile)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(12)
        
        # 用户头像
        self.user_avatar = QLabel("A")
        self.user_avatar.setObjectName("user_avatar")
        self.user_avatar.setAlignment(Qt.AlignCenter)
        self.user_avatar.setFixedSize(36, 36)
        layout.addWidget(self.user_avatar)
        
        # 用户信息
        user_info_layout = QVBoxLayout()
        user_info_layout.setContentsMargins(0, 0, 0, 0)
        user_info_layout.setSpacing(2)
        
        self.user_name = QLabel("未登录")
        self.user_name.setObjectName("user_name")
        user_info_layout.addWidget(self.user_name)
        
        self.user_role = QLabel("请先登录")
        self.user_role.setObjectName("user_role")
        user_info_layout.addWidget(self.user_role)
        
        layout.addLayout(user_info_layout)
        layout.addStretch()
    
    def setup_animations(self):
        """设置动画效果"""
        # 折叠动画
        self.collapse_animation = QPropertyAnimation(self, b"geometry")
        self.collapse_animation.setDuration(300)
        self.collapse_animation.setEasingCurve(QEasingCurve.OutCubic)
    
    def setup_navigation(self):
        """设置导航结构"""
        # 导航配置
        nav_config = [
            {
                "title": "核心功能",
                "items": [
                    ("dashboard", "实时监测仪表板", "📊"),
                    ("patients", "患者管理", "👥"),
                    ("treatment", "治疗系统", "⚕️"),
                ]
            },
            {
                "title": "数据分析",
                "items": [
                    ("data_management", "数据管理", "💾"),
                    ("reports", "报告分析", "📈"),
                ]
            },
            {
                "title": "系统管理",
                "items": [
                    ("users", "用户管理", "👤"),
                    ("settings", "系统设置", "⚙️"),
                    ("logout", "退出系统", "🚪"),
                ]
            }
        ]
        
        # 创建导航结构
        for section in nav_config:
            self.add_nav_section(section["title"], section["items"])
    
    def add_nav_section(self, title: str, items: List[Tuple[str, str, str]]):
        """添加导航分组"""
        try:
            # 分组标题
            if not self.is_collapsed:
                title_label = QLabel(title)
                title_label.setProperty("class", "nav-title")
                self.nav_layout.addWidget(title_label)
                self.nav_sections[title] = title_label
            
            # 分组项目
            for page_id, text, icon in items:
                button = self.create_nav_button(page_id, text, icon)
                self.nav_layout.addWidget(button)
                self.nav_buttons[page_id] = button
                self.button_group.addButton(button)
            
            # 添加分组间距
            self.nav_layout.addSpacing(16)
            
        except Exception as e:
            self.logger.error(f"添加导航分组失败: {e}")
    
    def create_nav_button(self, page_id: str, text: str, icon: str) -> QPushButton:
        """创建导航按钮"""
        button = QPushButton()
        button.setObjectName(f"nav_button_{page_id}")
        button.setProperty("class", "nav-item")
        button.setCheckable(True)
        button.setAutoExclusive(False)  # 由ButtonGroup管理
        
        # 设置按钮内容
        if self.is_collapsed:
            button.setText(icon)
            button.setToolTip(text)
        else:
            button.setText(f"{icon}  {text}")
        
        # 连接信号
        button.clicked.connect(lambda checked, pid=page_id: self.on_nav_button_clicked(pid))
        
        return button
    
    def on_nav_button_clicked(self, page_id: str):
        """处理导航按钮点击"""
        try:
            if page_id == "logout":
                # 特殊处理退出系统
                self.handle_logout()
                return
            
            # 更新当前页面
            self.current_page = page_id
            
            # 发送页面切换信号
            self.page_changed.emit(page_id)
            
            self.logger.info(f"切换到页面: {page_id}")
            
        except Exception as e:
            self.logger.error(f"处理导航按钮点击失败: {e}")
    
    def handle_logout(self):
        """处理退出系统"""
        # 这里可以添加退出确认对话框
        # 暂时只发送信号
        self.page_changed.emit("logout")
    
    def set_active_page(self, page_id: str):
        """设置活跃页面"""
        try:
            if page_id in self.nav_buttons:
                # 取消所有按钮选中状态
                for button in self.nav_buttons.values():
                    button.setChecked(False)
                
                # 设置指定按钮为选中状态
                self.nav_buttons[page_id].setChecked(True)
                self.current_page = page_id
                
        except Exception as e:
            self.logger.error(f"设置活跃页面失败: {e}")
    
    def toggle_collapse(self):
        """切换折叠状态"""
        try:
            self.is_collapsed = not self.is_collapsed
            
            # 执行折叠动画
            current_rect = self.geometry()
            if self.is_collapsed:
                target_width = 80
            else:
                target_width = 280
            
            target_rect = QRect(current_rect.x(), current_rect.y(), 
                              target_width, current_rect.height())
            
            self.collapse_animation.setStartValue(current_rect)
            self.collapse_animation.setEndValue(target_rect)
            self.collapse_animation.start()
            
            # 更新UI元素显示状态
            self.update_collapse_state()
            
            # 发送折叠状态信号
            self.collapse_toggled.emit(self.is_collapsed)
            
        except Exception as e:
            self.logger.error(f"切换折叠状态失败: {e}")
    
    def update_collapse_state(self):
        """更新折叠状态下的UI"""
        try:
            # 更新Logo文本显示
            self.logo_text.setVisible(not self.is_collapsed)
            
            # 更新用户信息显示
            self.user_name.setVisible(not self.is_collapsed)
            self.user_role.setVisible(not self.is_collapsed)
            
            # 更新导航按钮文本
            for page_id, button in self.nav_buttons.items():
                if self.is_collapsed:
                    # 只显示图标
                    icon = button.text().split()[0] if button.text() else "📄"
                    button.setText(icon)
                else:
                    # 显示图标+文字
                    self.update_button_text(page_id, button)
            
            # 更新分组标题显示
            for title_label in self.nav_sections.values():
                title_label.setVisible(not self.is_collapsed)
                
        except Exception as e:
            self.logger.error(f"更新折叠状态失败: {e}")
    
    def update_button_text(self, page_id: str, button: QPushButton):
        """更新按钮文本"""
        # 这里可以根据page_id设置完整的按钮文本
        # 简化处理，保持原有文本
        pass
    
    def update_user_info(self, username: str, role: str):
        """更新用户信息"""
        try:
            self.user_name.setText(username)
            self.user_role.setText(role)
            
            # 更新头像（使用用户名首字母）
            if username and len(username) > 0:
                self.user_avatar.setText(username[0].upper())
            else:
                self.user_avatar.setText("?")
                
        except Exception as e:
            self.logger.error(f"更新用户信息失败: {e}")

# NK脑机接口康复训练系统 - 完整开发文档

## 项目概述

### 系统简介
NK脑机接口康复训练系统是一个医疗级的脑机接口系统，专门用于神经康复治疗。本项目是从原有的QT C++系统迁移到Python平台的版本，保持了原有的所有功能，并新增了日志系统和权限管理等功能。

### 技术特点
- **医疗级标准**: 符合医疗器械软件标准，具备完善的错误处理和恢复机制
- **模块化设计**: 便于后续功能升级和维护，松耦合架构
- **高性能**: 实时数据处理，延迟小于100ms
- **高可靠性**: 完善的错误处理和恢复机制
- **跨平台**: 基于Python开发，支持Windows平台

### 版本信息
- **当前版本**: 1.0.0
- **构建日期**: 2024-12-19
- **开发公司**: 山东海天智能工程有限公司

## 系统架构

### 整体架构
```
┌─────────────────────────────────────────────────────────────┐
│                    用户界面层 (UI Layer)                      │
├─────────────────────────────────────────────────────────────┤
│  患者管理UI │ 治疗系统UI │ 报告分析UI │ 系统设置UI │ 用户管理UI │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   业务逻辑层 (Business Layer)                 │
├─────────────────────────────────────────────────────────────┤
│ 主窗口控制 │ 权限管理 │ 治疗工作流 │ 报告生成 │ 性能优化器    │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    核心服务层 (Core Layer)                    │
├─────────────────────────────────────────────────────────────┤
│ 数据库管理 │ 信号处理 │ 机器学习 │ 设备控制 │ 网络通信       │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   基础设施层 (Infrastructure)                 │
├─────────────────────────────────────────────────────────────┤
│ 日志系统 │ 配置管理 │ 单实例检查 │ 语音提示 │ 图表生成        │
└─────────────────────────────────────────────────────────────┘
```

### 核心模块
1. **主程序入口** (`main.py`) - 应用程序启动和初始化
2. **核心模块** (`core/`) - 业务逻辑和核心功能
3. **用户界面** (`ui/`) - 图形用户界面组件
4. **工具模块** (`utils/`) - 配置管理和工具函数
5. **算法模块** (`algorithms/`) - 信号处理和机器学习算法
6. **数据目录** (`data/`) - 数据库和配置文件
7. **资源文件** (`resources/`) - 样式、图片和模板
8. **第三方库** (`libs/`) - 外部DLL和库文件

## 技术栈

### 核心依赖
- **Python**: 3.9+ (推荐3.11)
- **PySide6**: 6.4.0+ (Qt界面框架)
- **NumPy**: 1.21.0+ (数值计算)
- **Matplotlib**: 3.5.0+ (图形绘制)
- **PySerial**: 3.5+ (串口通信)

### 信号处理依赖
- **SciPy**: 1.7.0+ (科学计算)
- **MNE-Python**: 1.0.0+ (专业脑电信号处理)
- **PyWavelets**: 1.3.0+ (小波变换)
- **Scikit-learn**: 1.0.0+ (机器学习)

### 深度学习依赖
- **TensorFlow**: 2.18.1+ (深度学习框架)
- **Keras**: 3.0+ (高级神经网络API)

### 可选依赖
- **Pandas**: 1.3.0+ (数据处理)
- **Joblib**: 1.1.0+ (并行处理)
- **PyTTSx3**: 2.90+ (语音合成)
- **TQDM**: 4.62.0+ (进度条)
- **ReportLab**: 3.6.0+ (PDF生成)

## 数据库设计

### 数据库类型
- **类型**: SQLite3
- **位置**: `data/nk_system.db`
- **备份**: 自动备份到 `data/backup/`

### 核心表结构

#### 1. 患者信息表 (bingren)
```sql
CREATE TABLE bingren (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    bianhao TEXT UNIQUE NOT NULL,        -- 患者编号
    xingming TEXT NOT NULL,              -- 姓名
    nianling INTEGER,                    -- 年龄
    xingbie TEXT,                        -- 性别
    shenfenzheng TEXT,                   -- 身份证号
    zhenduan TEXT,                       -- 诊断
    zhuzhi_yishi TEXT,                   -- 主治医师
    luru_shijian TIMESTAMP DEFAULT CURRENT_TIMESTAMP,  -- 录入时间
    beizhu TEXT,                         -- 备注
    is_active INTEGER DEFAULT 1          -- 是否活跃
);
```

#### 2. 治疗记录表 (zhiliao)
```sql
CREATE TABLE zhiliao (
    zhiliaobh INTEGER PRIMARY KEY AUTOINCREMENT,  -- 治疗编号
    treat_number INTEGER NOT NULL,                -- 患者治疗次数
    bianh TEXT NOT NULL,                          -- 患者编号
    riqi DATE NOT NULL,                           -- 治疗日期
    shijian TIME NOT NULL,                        -- 治疗时间
    defen TEXT,                                   -- 得分(实际/总数)
    chufa_cishu INTEGER DEFAULT 0,                -- 触发次数
    zlsj INTEGER DEFAULT 0,                       -- 治疗时长(分钟)
    zlms TEXT,                                    -- 治疗描述(优/良/中/差)
    czy TEXT,                                     -- 操作员
    upload_status INTEGER DEFAULT 0,              -- 上传状态
    FOREIGN KEY (bianh) REFERENCES bingren(bianhao)
);
```

#### 3. 脑电数据表 (Edata)
```sql
CREATE TABLE Edata (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    bianh TEXT NOT NULL,                 -- 患者编号
    shijian TIMESTAMP DEFAULT CURRENT_TIMESTAMP,  -- 时间戳
    delta_power REAL,                    -- δ波功率
    theta_power REAL,                    -- θ波功率
    alpha_power REAL,                    -- α波功率
    beta_power REAL,                     -- β波功率
    gamma_power REAL,                    -- γ波功率
    mu_power REAL,                       -- μ波功率
    zhuangtai INTEGER,                   -- 状态(0:休息,1:运动想象)
    FOREIGN KEY (bianh) REFERENCES bingren(bianhao)
);
```

#### 4. 医院信息表 (yiyuan)
```sql
CREATE TABLE yiyuan (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    mingcheng TEXT NOT NULL,             -- 医院名称
    keshi TEXT,                          -- 科室
    shebeiid TEXT,                       -- 设备ID
    dizhi TEXT,                          -- 地址
    lianxiren TEXT,                      -- 联系人
    dianhua TEXT                         -- 电话
);
```

#### 5. 医生信息表 (doctor)
```sql
CREATE TABLE doctor (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,                  -- 医生姓名
    title TEXT,                          -- 职称
    department TEXT,                     -- 科室
    phone TEXT,                          -- 电话
    email TEXT,                          -- 邮箱
    is_active INTEGER DEFAULT 1,         -- 是否活跃
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 6. 操作员表 (operator)
```sql
CREATE TABLE operator (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,       -- 用户名
    password TEXT NOT NULL,              -- 密码(加密)
    real_name TEXT,                      -- 真实姓名
    role TEXT DEFAULT 'operator',        -- 角色
    permissions TEXT,                    -- 权限(JSON格式)
    is_active INTEGER DEFAULT 1,         -- 是否活跃
    last_login TIMESTAMP,                -- 最后登录时间
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 7. 系统日志表 (system_logs)
```sql
CREATE TABLE system_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    level TEXT NOT NULL,                 -- 日志级别
    module TEXT,                         -- 模块名
    message TEXT NOT NULL,               -- 日志消息
    user_id TEXT,                        -- 用户ID
    ip_address TEXT,                     -- IP地址
    details TEXT                         -- 详细信息(JSON)
);
```

## 设备接口

### 脑电设备接口

#### 硬件规格
- **芯片**: ADS1299 (8通道24位ADC)
- **采样率**: 125Hz
- **通道数**: 8通道
- **电极位置**: PZ, P3, P4, C3, CZ, C4, F3, F4 (10-20标准)
- **通信接口**: 串口 (默认COM8, 115200波特率)

#### 数据包格式
```
包头: 0x5A 0xA5 (2字节)
数据: 4组 × 24字节 = 96字节 (每组8通道×3字节)
包尾: 0x0D 0x0A (2字节)
总长度: 100字节
```

#### 控制命令
- **开始采集**: "START"
- **停止采集**: "STOP"
- **连接超时**: 3秒
- **最大重试**: 3次

### 电刺激设备接口

#### 硬件规格
- **DLL库**: RecoveryDLL.dll
- **通道数**: 2通道 (A通道、B通道)
- **电流范围**: 1-100mA (步长1mA)
- **频率范围**: 2-160Hz
- **脉宽范围**: 10-500μs

#### 参数配置
```python
STIMULATION_CONFIG = {
    'max_current': 100,           # 最大电流(mA)
    'min_current': 1,             # 最小电流(mA)
    'current_step': 1,            # 电流步长(mA)
    'default_frequency': 20,      # 默认频率(Hz)
    'default_pulse_width': 200,   # 默认脉宽(μs)
    'default_relax_time': 5,      # 默认休息时间(s)
    'default_climb_time': 2,      # 默认上升时间(s)
    'default_work_time': 10,      # 默认工作时间(s)
    'default_fall_time': 2,       # 默认下降时间(s)
    'port_num': 1,                # 端口号
    'connection_timeout': 5       # 连接超时(s)
}
```

#### DLL接口函数
- **OpenRecPort()**: 打开设备端口
- **CloseRecPort()**: 关闭设备端口
- **IsRecOpen()**: 检查设备状态
- **SetRecParams()**: 设置刺激参数
- **StartStimulation()**: 开始刺激
- **StopStimulation()**: 停止刺激

## 信号处理算法

### 预处理流程
1. **带通滤波**: 0.5-50Hz
2. **陷波滤波**: 50Hz (电源干扰)
3. **伪迹检测**: 基于幅值和梯度的自动检测
4. **阻抗监测**: 实时电极阻抗检查

### 特征提取算法

#### 1. 功率谱密度 (PSD)
- **频带划分**: δ(0.5-4Hz), θ(4-8Hz), α(8-13Hz), μ(8-12Hz), β(13-30Hz), γ(30-50Hz)
- **窗口长度**: 2秒
- **重叠率**: 50%
- **特征维度**: 6频带 × 8通道 = 48维

#### 2. 共同空间模式 (CSP)
- **标准CSP**: 6个组件
- **正则化CSP**: 正则化参数0.1
- **Filter Bank CSP**: 5个频带，每频带2个组件
- **特征维度**: 10维 (FBCSP)

#### 3. 小波变换特征
- **小波类型**: Daubechies 4
- **分解层数**: 5层
- **统计特征**: 均值、标准差、方差、最大值、能量
- **特征维度**: 6层 × 5统计量 × 8通道 = 240维

### 分类算法

#### 1. 传统机器学习
- **支持向量机 (SVM)**: RBF核，C=1.0, γ=0.1
- **随机森林**: 100棵树，最大深度10
- **逻辑回归**: L2正则化，C=1.0

#### 2. 深度学习 (EEGNet)
```python
# EEGNet架构参数
EEGNET_CONFIG = {
    'nb_classes': 2,              # 分类数
    'Chans': 8,                   # 通道数
    'Samples': 250,               # 样本点数(2秒×125Hz)
    'dropoutRate': 0.25,          # Dropout率
    'kernLength': 64,             # 卷积核长度
    'F1': 8,                      # 第一层滤波器数
    'D': 2,                       # 深度乘数
    'F2': 16,                     # 第二层滤波器数
    'dropoutType': 'Dropout'      # Dropout类型
}
```

## 网络通信

### HTTP通信
- **服务器地址**: http://*************:8082/shdekf/Api/
- **超时时间**: 30秒
- **重试次数**: 1次
- **数据格式**: JSON

#### API接口
1. **患者数据上传**: `/uploadPatient`
2. **治疗数据上传**: `/uploadTreatment`
3. **设备状态上传**: `/updateEquipment`

### UDP通信
- **VR系统地址**: 127.0.0.1:3004
- **本地绑定端口**: 3005
- **重试次数**: 3次
- **重试间隔**: 0.01秒

#### UDP指令格式
```python
# 治疗指令
commands = {
    'treat': '开始治疗准备',
    'start': '开始电刺激',
    'stop': '停止电刺激',
    'stopall': '结束治疗'
}
```

## 配置管理

### 配置文件结构
```json
{
  "database": {
    "type": "sqlite",
    "path": "data/nk_system.db",
    "backup_path": "data/backup",
    "auto_backup": true,
    "backup_interval": 86400,
    "min_treatment_duration": 1
  },
  "stimulation": {
    "dll_path": "libs/RecoveryDLL.dll",
    "max_current": 100,
    "min_current": 1,
    "current_step": 1,
    "port_num": 7,
    "connection_timeout": 5
  },
  "eeg": {
    "serial_port": "COM8",
    "baud_rate": 115200,
    "sample_rate": 125.0,
    "channels": 8,
    "timeout": 5.0,
    "channel_names": ["PZ", "P3", "P4", "C3", "CZ", "C4", "F3", "F4"]
  },
  "signal_processing": {
    "filter_config": {
      "highpass_freq": 0.5,
      "lowpass_freq": 50.0,
      "notch_freq": 50.0,
      "filter_order": 4
    },
    "deep_learning": {
      "model_type": "eegnet",
      "training_samples": 100,
      "epochs": 50,
      "batch_size": 32,
      "learning_rate": 0.001,
      "temperature": 1.0,
      "dropout_rate": 0.25
    }
  }
}
```

### 环境变量支持
- **NK_DB_PATH**: 数据库路径
- **NK_LOG_LEVEL**: 日志级别
- **NK_SERIAL_PORT**: 串口号
- **NK_BAUD_RATE**: 波特率

## 权限管理

### 用户角色
1. **系统管理员 (admin)**: 所有权限
2. **医生 (doctor)**: 患者管理、治疗操作、报告生成、数据分析
3. **技师 (technician)**: 治疗操作、设备控制、数据采集
4. **操作员 (operator)**: 患者基本信息、治疗操作

### 密码策略
- **最小长度**: 6位
- **必须包含数字**: 是
- **密码有效期**: 90天
- **大小写要求**: 否
- **特殊字符要求**: 否

### 权限验证
```python
def check_permission(user_role: str, required_permission: str) -> bool:
    """检查用户权限"""
    role_permissions = PERMISSION_CONFIG['roles'].get(user_role, {})
    permissions = role_permissions.get('permissions', [])
    return 'all' in permissions or required_permission in permissions
```

## 治疗工作流程

### 工作流程设计
```
开始治疗 → 前置条件检查 → 发送treat指令 → 等待运动想象 → 检测到运动想象 → 发送start指令
    ↓
启动电刺激 → 刺激持续 → 刺激结束 → 发送stop指令 → 根据自适应学习决定 → 继续/结束
    ↓
治疗时长到达 → 发送stopall指令 → 保存治疗数据 → 上传平台 → 治疗完成
```

### 前置条件检查
1. **脑电设备连接**: 检查串口连接状态
2. **电刺激设备连接**: 检查DLL设备状态
3. **患者选择**: 确保已选择患者
4. **模型加载**: 确保分类模型已加载
5. **通道配置**: 检查电刺激通道选择和电流值

### 状态管理
- **空闲状态**: 等待开始治疗
- **准备状态**: 发送treat指令，等待运动想象
- **检测状态**: 运动想象检测中
- **刺激状态**: 电刺激进行中
- **休息状态**: 刺激间隔休息
- **结束状态**: 治疗完成

### 超时机制
- **运动想象超时**: 30秒
- **电刺激超时**: 根据工作时间设置
- **总治疗超时**: 根据单次治疗时长设置

## 实时显示系统

### 脑电信号实时显示
- **实时曲线**: 使用PyQtGraph绘制8通道实时波形
- **更新频率**: 25Hz (每40ms更新一次)
- **显示窗口**: 4秒数据窗口
- **自适应缩放**: 防止信号超出显示范围

### 脑电地形图
- **更新频率**: 1Hz
- **数据窗口**: 250ms
- **颜色映射**: 蓝色(低)到红色(高)
- **电极位置**: 10-20标准位置
- **插值方法**: 球面样条插值

### 电极阻抗显示
- **实时监测**: 8通道阻抗状态
- **颜色指示**: 绿色(良好) < 5kΩ, 黄色(一般) 5-10kΩ, 红色(差) > 10kΩ
- **更新频率**: 1Hz
- **位置显示**: 左侧面板，所有页面可见

## 日志系统

### 日志级别
- **DEBUG**: 调试信息
- **INFO**: 一般信息
- **WARNING**: 警告信息
- **ERROR**: 错误信息
- **CRITICAL**: 严重错误

### 日志格式
```
%(asctime)s - %(name)s - %(levelname)s - %(message)s
```

### 日志文件管理
- **主日志**: `logs/nk_system.log`
- **错误日志**: `logs/error.log`
- **最大文件大小**: 10MB
- **备份文件数**: 5个
- **自动轮转**: 文件大小超限时自动轮转

### 操作审计
- **用户登录/登出**: 记录用户身份和时间
- **患者操作**: 添加、编辑、删除患者
- **治疗操作**: 开始、停止、参数修改
- **系统配置**: 参数修改、设备连接
- **数据操作**: 数据导出、备份、恢复

## 报告生成系统

### 报告类型
1. **个人综合报告**: 单个患者的完整治疗历程
2. **按日统计报告**: 每日治疗统计和趋势分析
3. **设备使用报告**: 设备运行状态和使用统计
4. **治疗效果报告**: 治疗效果分析和对比

### 报告内容

#### 个人综合报告
- **患者基本信息**: 编号、姓名、年龄、性别、诊断
- **治疗历程统计**: 总次数、累计时长、平均得分
- **最近治疗表现**: 最近5次治疗记录
- **脑电特征分析**: 各频带平均功率分析
- **治疗建议**: 基于数据的个性化建议

#### 按日统计报告
- **每日治疗统计**: 治疗次数、患者人数、平均得分
- **趋势分析图表**: 治疗量、患者数、得分趋势
- **效果分布**: 优良中差比例统计
- **新患者统计**: 新增患者数量和信息

### 图表生成
- **使用库**: Matplotlib 3.10.0
- **图表类型**: 折线图、柱状图、饼图、散点图
- **中文支持**: 配置中文字体，支持中文标签
- **导出格式**: PNG、PDF、SVG

### PDF导出
- **使用库**: ReportLab (可选)
- **模板系统**: 支持自定义报告模板
- **页面布局**: A4纸张，标准医疗报告格式
- **水印支持**: 可添加医院logo和水印

## 性能优化

### 内存管理
- **连接池**: 数据库连接池，避免频繁创建连接
- **缓存机制**: 患者信息和配置缓存
- **垃圾回收**: 定期清理无用对象
- **内存监控**: 实时监控内存使用情况

### 计算优化
- **多线程**: 信号处理和界面分离
- **向量化**: 使用NumPy向量化计算
- **算法优化**: 优化CSP和特征提取算法
- **GPU加速**: 支持TensorFlow GPU加速(可选)

### 界面优化
- **异步更新**: 避免界面阻塞
- **延迟加载**: 按需加载数据和组件
- **缓存渲染**: 缓存图表和图像
- **响应式设计**: 自适应不同分辨率

## 测试体系

### 单元测试
- **测试框架**: pytest
- **覆盖率**: 目标80%以上
- **测试文件**: `test_*.py`
- **自动化**: 支持自动化测试运行

### 集成测试
- **系统集成**: 测试各模块间的集成
- **设备集成**: 测试硬件设备接口
- **数据库集成**: 测试数据库操作
- **网络集成**: 测试网络通信功能

### 性能测试
- **响应时间**: 测试各功能响应时间
- **并发测试**: 测试多用户并发使用
- **压力测试**: 测试系统极限负载
- **内存泄漏**: 检测内存泄漏问题

### 验收测试
- **功能验收**: 验证所有功能正常工作
- **用户验收**: 用户使用体验测试
- **医疗标准**: 符合医疗器械软件标准
- **安全测试**: 数据安全和权限测试

## 部署指南

### 环境要求
- **操作系统**: Windows 10/11 (64位)
- **Python版本**: 3.9+ (推荐3.11)
- **内存**: 8GB RAM (推荐16GB)
- **存储**: 至少2GB可用空间
- **网络**: 用于数据上传和VR通信

### 安装步骤
1. **下载项目**: 获取完整项目文件
2. **安装Python**: 确保Python 3.9+已安装
3. **安装依赖**: 运行 `python install_dependencies.py`
4. **配置设备**: 连接脑电和电刺激设备
5. **初始化数据库**: 首次运行自动创建数据库
6. **启动系统**: 运行 `python main.py`

### 配置检查
```bash
# 检查依赖
python check_dependencies.py

# 运行测试
python test_system.py

# 快速验证
python quick_start.py
```

### 生产部署
1. **环境隔离**: 使用虚拟环境
2. **配置优化**: 调整生产环境配置
3. **日志配置**: 设置适当的日志级别
4. **备份策略**: 配置自动备份
5. **监控设置**: 设置系统监控
6. **安全加固**: 修改默认密码和配置

## 故障排除

### 常见问题

#### 1. 系统无法启动
**症状**: 程序启动失败或崩溃
**可能原因**:
- Python版本不兼容
- 依赖包缺失或版本不匹配
- 配置文件错误
- 权限不足

**解决方案**:
```bash
# 检查Python版本
python --version

# 检查依赖
python check_dependencies.py

# 重新安装依赖
python install_dependencies.py

# 检查日志
type logs\nk_system.log
```

#### 2. 脑电设备连接失败
**症状**: 无法连接脑电设备
**可能原因**:
- 串口号错误
- 设备驱动未安装
- 串口被占用
- 设备未开机

**解决方案**:
1. 检查设备管理器中的串口号
2. 确认设备驱动已正确安装
3. 关闭其他可能占用串口的程序
4. 检查设备电源和连接线

#### 3. 电刺激设备连接失败
**症状**: 电刺激设备无法连接
**可能原因**:
- DLL文件缺失
- 端口号错误
- 设备未开机
- USB驱动问题

**解决方案**:
1. 确认 `libs/RecoveryDLL.dll` 文件存在
2. 检查设备端口号配置
3. 重新插拔USB连接
4. 重启设备和软件

#### 4. 数据库错误
**症状**: 数据库操作失败
**可能原因**:
- 数据库文件损坏
- 磁盘空间不足
- 权限问题
- 并发访问冲突

**解决方案**:
```bash
# 检查数据库完整性
python -c "import sqlite3; conn=sqlite3.connect('data/nk_system.db'); conn.execute('PRAGMA integrity_check')"

# 从备份恢复
copy data\backup\nk_system_backup_*.db data\nk_system.db

# 重新初始化数据库
python fix_database.py
```

#### 5. 界面显示异常
**症状**: 界面布局错乱或显示不正常
**可能原因**:
- DPI设置问题
- 样式文件缺失
- Qt库版本问题
- 显卡驱动问题

**解决方案**:
1. 调整系统DPI设置为100%
2. 检查 `resources/styles/` 目录
3. 更新PySide6到最新版本
4. 更新显卡驱动

### 日志分析
- **查看实时日志**: 在系统设置中查看日志
- **错误日志**: 重点关注ERROR和CRITICAL级别
- **性能日志**: 关注响应时间和内存使用
- **操作日志**: 追踪用户操作和系统状态

### 技术支持
- **开发团队**: 联系开发团队获取技术支持
- **用户手册**: 查看详细的用户操作手册
- **在线文档**: 访问在线技术文档
- **社区支持**: 参与用户社区讨论

## 开发指南

### 代码规范
- **命名规范**: 使用有意义的变量名和函数名
- **注释规范**: 为重要代码块添加注释
- **模块化**: 将不同功能封装成独立模块
- **错误处理**: 完善的异常捕获和处理
- **日志记录**: 关键操作添加日志记录

### 新功能开发
1. **需求分析**: 明确功能需求和技术要求
2. **设计方案**: 设计技术方案和接口
3. **编码实现**: 按照规范编写代码
4. **单元测试**: 编写和运行单元测试
5. **集成测试**: 测试与现有系统的集成
6. **文档更新**: 更新相关文档

### 扩展开发
- **新增算法**: 在 `algorithms/` 目录添加新算法
- **新增界面**: 在 `ui/` 目录添加新界面组件
- **新增设备**: 在 `core/` 目录添加设备接口
- **新增报告**: 扩展报告生成功能

### 版本管理
- **版本号**: 遵循语义化版本号规范
- **变更日志**: 记录每个版本的变更内容
- **兼容性**: 保持向后兼容性
- **升级路径**: 提供清晰的升级路径

## 附录

### A. 依赖包列表
```
# 核心依赖
PySide6>=6.4.0          # Qt界面框架
numpy>=1.21.0           # 数值计算
matplotlib>=3.5.0       # 图形绘制
pyserial>=3.5           # 串口通信

# 信号处理
scipy>=1.7.0            # 科学计算
mne>=1.0.0              # 脑电信号处理
PyWavelets>=1.3.0       # 小波变换
scikit-learn>=1.0.0     # 机器学习

# 深度学习
tensorflow>=2.18.1      # 深度学习框架

# 可选依赖
pandas>=1.3.0           # 数据处理
joblib>=1.1.0           # 并行处理
pyttsx3>=2.90           # 语音合成
tqdm>=4.62.0            # 进度条
reportlab>=3.6.0        # PDF生成
psutil>=5.8.0           # 系统监控
```

### B. 配置文件模板
参见 `data/user_config.json` 文件

### C. 数据库初始化脚本
参见 `core/database_manager.py` 中的 `_create_tables()` 方法

### D. API接口文档
详细的HTTP API接口文档请参考服务器端文档

### E. 硬件接口规范
- **脑电设备**: ADS1299芯片技术规范
- **电刺激设备**: RecoveryDLL接口规范

---

**文档版本**: 1.0.0
**最后更新**: 2024-12-19
**维护团队**: 山东海天智能工程有限公司

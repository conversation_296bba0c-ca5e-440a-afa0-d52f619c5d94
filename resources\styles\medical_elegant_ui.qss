/* 
脑机接口康复训练系统 - 简洁高档医疗UI样式表 v1.0
Brain-Computer Interface Medical UI Stylesheet - Elegant & Clean
简洁、高档、专业的医疗设备界面设计
*/

/* ==================== 全局样式 ==================== */
* {
    font-family: "Microsoft YaHei UI", "Microsoft YaHei", "Segoe UI", Arial, sans-serif;
    outline: none;
}

/* 主窗口样式 - 简洁白色背景 */
QMainWindow {
    background-color: #ffffff;
    color: #2d3748;
}

/* ==================== 菜单栏样式 ==================== */
QMenuBar {
    background-color: #f7fafc;
    color: #2d3748;
    border: none;
    border-bottom: 1px solid #e2e8f0;
    padding: 2px;
    font-weight: 600;
    font-size: 12px;
}

QMenuBar::item {
    background-color: transparent;
    padding: 4px 8px;
    border-radius: 4px;
    margin: 1px 2px;
    font-weight: 600;
}

QMenuBar::item:selected {
    background-color: #edf2f7;
    color: #2b6cb0;
}

QMenuBar::item:pressed {
    background-color: #e2e8f0;
}

QMenu {
    background-color: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 6px;
}

QMenu::item {
    padding: 10px 16px;
    border-radius: 6px;
    margin: 2px;
    color: #4a5568;
    font-weight: 500;
}

QMenu::item:selected {
    background-color: #edf2f7;
    color: #2b6cb0;
}

/* ==================== 状态栏样式 ==================== */
QStatusBar {
    background-color: #f7fafc;
    border-top: 1px solid #e2e8f0;
    color: #4a5568;
    font-size: 11px;
    font-weight: 500;
    padding: 2px;
}

QStatusBar::item {
    border: none;
    padding: 2px 4px;
    margin: 0 2px;
    border-radius: 3px;
}

QStatusBar QLabel {
    color: #4a5568;
    font-weight: 500;
    padding: 1px 3px;
    border-radius: 2px;
}

/* ==================== 导航区域样式 ==================== */
QFrame#navigation_frame {
    background-color: #f7fafc;
    border-right: 1px solid #e2e8f0;
}

/* ==================== 导航按钮样式 ==================== */
QToolButton {
    background-color: transparent;
    border: none;
    color: #4a5568;
    padding: 12px 8px;
    text-align: center;
    border-radius: 8px;
    margin: 4px 6px;
    font-size: 13px;
    font-weight: 600;
    min-height: 36px;
    min-width: 100px;
}

QToolButton:hover {
    background-color: #edf2f7;
    color: #2b6cb0;
    border: 1px solid #e2e8f0;
}

QToolButton:checked {
    background-color: #2b6cb0;
    color: white;
    font-weight: 700;
    border: 1px solid #2c5282;
}

QToolButton:disabled {
    color: #a0aec0;
    background-color: transparent;
}

/* ==================== 按钮样式 ==================== */
QPushButton {
    background-color: #2b6cb0;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    min-width: 50px;
    min-height: 20px;
}

QPushButton:hover {
    background-color: #2c5282;
}

QPushButton:pressed {
    background-color: #2a4365;
}

QPushButton:disabled {
    background-color: #e2e8f0;
    color: #a0aec0;
}

/* 主要按钮样式 */
QPushButton.primary {
    background-color: #3182ce;
}

QPushButton.primary:hover {
    background-color: #2c5282;
}

/* 危险按钮样式 */
QPushButton.danger {
    background-color: #e53e3e;
}

QPushButton.danger:hover {
    background-color: #c53030;
}

/* 成功按钮样式 */
QPushButton.success {
    background-color: #38a169;
}

QPushButton.success:hover {
    background-color: #2f855a;
}

/* 警告按钮样式 */
QPushButton.warning {
    background-color: #d69e2e;
}

QPushButton.warning:hover {
    background-color: #b7791f;
}

/* 次要按钮样式 */
QPushButton.secondary {
    background-color: #edf2f7;
    color: #4a5568;
    border: 1px solid #e2e8f0;
}

QPushButton.secondary:hover {
    background-color: #e2e8f0;
    color: #2d3748;
}

/* 大尺寸按钮 */
QPushButton.large {
    padding: 12px 24px;
    font-size: 14px;
    min-height: 40px;
    min-width: 120px;
}

/* 小尺寸按钮 */
QPushButton.small {
    padding: 6px 12px;
    font-size: 12px;
    min-height: 24px;
    min-width: 60px;
}

/* ==================== 输入框样式 ==================== */
QLineEdit {
    background-color: white;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    padding: 4px 6px;
    font-size: 11px;
    color: #2d3748;
    font-weight: 500;
    selection-background-color: #3182ce;
    selection-color: white;
}

QLineEdit:focus {
    border-color: #3182ce;
    background-color: #f7fafc;
}

QLineEdit:hover {
    border-color: #cbd5e0;
}

QLineEdit:disabled {
    background-color: #f7fafc;
    color: #a0aec0;
    border-color: #e2e8f0;
}

QLineEdit[readOnly="true"] {
    background-color: #f7fafc;
    color: #718096;
    border-color: #e2e8f0;
}

/* ==================== 文本区域样式 ==================== */
QTextEdit {
    background-color: white;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    padding: 4px;
    font-size: 11px;
    color: #2d3748;
    font-weight: 500;
    selection-background-color: #3182ce;
    selection-color: white;
}

QTextEdit:focus {
    border-color: #3182ce;
    background-color: #f7fafc;
}

QTextEdit:hover {
    border-color: #cbd5e0;
}

/* 系统日志文本区域特殊样式 */
QTextEdit#system_log {
    background-color: #2d3748;
    color: #e2e8f0;
    border: 1px solid #4a5568;
    font-family: "Consolas", "Monaco", "Courier New", monospace;
    font-size: 12px;
}

/* ==================== 表格样式 ==================== */
QTableWidget {
    background-color: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    gridline-color: #f7fafc;
    selection-background-color: #3182ce;
    selection-color: white;
    alternate-background-color: #f7fafc;
    font-size: 12px;
}

QTableWidget::item {
    padding: 2px 4px;
    border-bottom: 1px solid #f7fafc;
    color: #2d3748;
    font-weight: 500;
}

QTableWidget::item:selected {
    background-color: #3182ce;
    color: white;
    font-weight: 600;
}

QTableWidget::item:hover {
    background-color: #edf2f7;
    color: #2b6cb0;
}

QHeaderView::section {
    background-color: #f7fafc;
    color: #2d3748;
    padding: 4px 3px;
    border: none;
    border-right: 1px solid #e2e8f0;
    border-bottom: 1px solid #e2e8f0;
    font-weight: 700;
    font-size: 10px;
}

QHeaderView::section:hover {
    background-color: #edf2f7;
}

QHeaderView::section:first {
    border-top-left-radius: 8px;
}

QHeaderView::section:last {
    border-top-right-radius: 8px;
    border-right: none;
}

/* ==================== 列表样式 ==================== */
QListWidget {
    background-color: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 6px;
}

QListWidget::item {
    padding: 10px 12px;
    border-radius: 6px;
    margin: 2px;
    color: #2d3748;
    font-weight: 500;
    border: 1px solid transparent;
}

QListWidget::item:selected {
    background-color: #3182ce;
    color: white;
    font-weight: 600;
}

QListWidget::item:hover {
    background-color: #edf2f7;
    color: #2b6cb0;
}

/* ==================== 组合框样式 ==================== */
QComboBox {
    background-color: white;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 10px 12px;
    font-size: 13px;
    color: #2d3748;
    font-weight: 500;
    min-width: 120px;
    min-height: 24px;
}

QComboBox:focus {
    border-color: #3182ce;
    background-color: #f7fafc;
}

QComboBox:hover {
    border-color: #cbd5e0;
}

QComboBox::drop-down {
    border: none;
    width: 24px;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
    background-color: #f7fafc;
}

QComboBox::down-arrow {
    image: none;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #4a5568;
    width: 0;
    height: 0;
}

QComboBox QAbstractItemView {
    background-color: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    selection-background-color: #3182ce;
    selection-color: white;
    padding: 4px;
}

QComboBox QAbstractItemView::item {
    padding: 10px 12px;
    border-radius: 6px;
    margin: 2px;
}

QComboBox QAbstractItemView::item:hover {
    background-color: #edf2f7;
    color: #2b6cb0;
}

/* ==================== 复选框样式 ==================== */
QCheckBox {
    color: #2d3748;
    font-size: 13px;
    font-weight: 500;
    spacing: 10px;
}

QCheckBox::indicator {
    width: 18px;
    height: 18px;
    border: 2px solid #e2e8f0;
    border-radius: 4px;
    background-color: white;
}

QCheckBox::indicator:checked {
    background-color: #3182ce;
    border-color: #3182ce;
    image: none;
}

QCheckBox::indicator:hover {
    border-color: #3182ce;
}

QCheckBox::indicator:disabled {
    background-color: #f7fafc;
    border-color: #e2e8f0;
}

/* ==================== 分组框样式 ==================== */
QGroupBox {
    font-weight: 600;
    font-size: 11px;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    margin-top: 4px;
    padding-top: 4px;
    color: #2d3748;
    background-color: white;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 8px;
    padding: 1px 6px;
    background-color: #f7fafc;
    color: #2d3748;
    border: 1px solid #e2e8f0;
    border-radius: 3px;
    font-weight: 700;
    font-size: 10px;
}

/* ==================== 进度条样式 ==================== */
QProgressBar {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    text-align: center;
    font-weight: 600;
    font-size: 12px;
    color: #2d3748;
    background-color: #f7fafc;
    min-height: 20px;
}

QProgressBar::chunk {
    background-color: #3182ce;
    border-radius: 7px;
}

/* ==================== 滚动条样式 ==================== */
QScrollBar:vertical {
    background-color: #f7fafc;
    width: 14px;
    border-radius: 7px;
    border: 1px solid #e2e8f0;
}

QScrollBar::handle:vertical {
    background-color: #cbd5e0;
    border-radius: 6px;
    min-height: 30px;
    margin: 1px;
}

QScrollBar::handle:vertical:hover {
    background-color: #a0aec0;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;
    background: transparent;
}

QScrollBar:horizontal {
    background-color: #f7fafc;
    height: 14px;
    border-radius: 7px;
    border: 1px solid #e2e8f0;
}

QScrollBar::handle:horizontal {
    background-color: #cbd5e0;
    border-radius: 6px;
    min-width: 30px;
    margin: 1px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #a0aec0;
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:horizontal {
    width: 0px;
    background: transparent;
}

/* ==================== 标签页样式 ==================== */
QTabWidget::pane {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background-color: white;
    padding: 4px;
}

QTabWidget::tab-bar {
    alignment: center;
}

QTabBar::tab {
    background-color: #f7fafc;
    border: 1px solid #e2e8f0;
    padding: 10px 20px;
    margin-right: 4px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    font-weight: 600;
    color: #4a5568;
    font-size: 13px;
}

QTabBar::tab:selected {
    background-color: white;
    color: #2b6cb0;
    border-bottom-color: white;
    font-weight: 700;
}

QTabBar::tab:hover {
    background-color: #edf2f7;
    color: #2b6cb0;
}

/* ==================== 数值输入框样式 ==================== */
QSpinBox, QDoubleSpinBox {
    background-color: white;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 10px 12px;
    font-size: 13px;
    font-weight: 600;
    color: #2d3748;
    min-width: 80px;
}

QSpinBox:focus, QDoubleSpinBox:focus {
    border-color: #3182ce;
    background-color: #f7fafc;
}

QSpinBox::up-button, QDoubleSpinBox::up-button {
    background-color: #f7fafc;
    border-top-right-radius: 6px;
    width: 20px;
    border-left: 1px solid #e2e8f0;
}

QSpinBox::down-button, QDoubleSpinBox::down-button {
    background-color: #f7fafc;
    border-bottom-right-radius: 6px;
    width: 20px;
    border-left: 1px solid #e2e8f0;
}

QSpinBox::up-button:hover, QDoubleSpinBox::up-button:hover {
    background-color: #edf2f7;
}

QSpinBox::down-button:hover, QDoubleSpinBox::down-button:hover {
    background-color: #edf2f7;
}

QSpinBox::up-arrow, QDoubleSpinBox::up-arrow {
    image: none;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 4px solid #4a5568;
    width: 0;
    height: 0;
}

QSpinBox::down-arrow, QDoubleSpinBox::down-arrow {
    image: none;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 4px solid #4a5568;
    width: 0;
    height: 0;
}

/* ==================== 医疗设备状态指示器 ==================== */

/* 设备连接状态标签 */
QLabel.device_status {
    font-weight: 600;
    font-size: 12px;
    padding: 6px 12px;
    border-radius: 6px;
    border: 1px solid transparent;
}

QLabel.device_connected {
    background-color: #38a169;
    color: white;
    border-color: #38a169;
}

QLabel.device_disconnected {
    background-color: #e53e3e;
    color: white;
    border-color: #e53e3e;
}

QLabel.device_connecting {
    background-color: #d69e2e;
    color: white;
    border-color: #d69e2e;
}

/* 治疗状态指示器 */
QLabel.treatment_status {
    font-weight: 600;
    font-size: 13px;
    padding: 8px 16px;
    border-radius: 6px;
    border: 1px solid transparent;
}

QLabel.treatment_active {
    background-color: #3182ce;
    color: white;
    border-color: #3182ce;
}

QLabel.treatment_paused {
    background-color: #d69e2e;
    color: white;
    border-color: #d69e2e;
}

QLabel.treatment_stopped {
    background-color: #718096;
    color: white;
    border-color: #718096;
}

/* 实时数据显示区域 */
QFrame.realtime_display {
    background-color: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 12px;
}

/* 脑电信号显示区域 */
QFrame.eeg_display {
    background-color: #2d3748;
    border: 1px solid #4a5568;
    border-radius: 8px;
    padding: 12px;
}

/* 控制面板区域 */
QFrame.control_panel {
    background-color: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 16px;
}

/* ==================== 对话框样式 ==================== */
QDialog {
    background-color: white;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
}

QMessageBox {
    background-color: white;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
}

QMessageBox QLabel {
    color: #2d3748;
    font-size: 13px;
    font-weight: 500;
    padding: 12px;
}

/* ==================== 特殊医疗标签样式 ==================== */
QLabel.medical_title {
    font-size: 18px;
    font-weight: 700;
    color: #2b6cb0;
    padding: 12px 0;
    border-bottom: 2px solid #e2e8f0;
    margin-bottom: 12px;
}

QLabel.medical_subtitle {
    font-size: 14px;
    font-weight: 600;
    color: #2d3748;
    padding: 6px 0;
}

QLabel.medical_value {
    font-size: 16px;
    font-weight: 700;
    color: #2d3748;
    background-color: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 8px 12px;
    min-width: 60px;
    text-align: center;
}

/* ==================== 工具提示样式 ==================== */
QToolTip {
    background-color: #2d3748;
    color: white;
    border: 1px solid #4a5568;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 12px;
    font-weight: 500;
}

/* ==================== 登录界面特殊样式 ==================== */
QWidget#login_widget {
    background-color: white;
    border-radius: 16px;
}

/* ==================== 医疗警告样式 ==================== */
QFrame.medical_warning {
    background-color: #fef5e7;
    border: 1px solid #d69e2e;
    border-radius: 8px;
    padding: 12px;
}

QFrame.medical_error {
    background-color: #fed7d7;
    border: 1px solid #e53e3e;
    border-radius: 8px;
    padding: 12px;
}

QFrame.medical_success {
    background-color: #f0fff4;
    border: 1px solid #38a169;
    border-radius: 8px;
    padding: 12px;
}

/* ==================== 滑块样式 ==================== */
QSlider::groove:horizontal {
    border: 1px solid #e2e8f0;
    height: 8px;
    background-color: #f7fafc;
    border-radius: 4px;
}

QSlider::handle:horizontal {
    background-color: #3182ce;
    border: 2px solid white;
    width: 20px;
    height: 20px;
    margin: -8px 0;
    border-radius: 12px;
}

QSlider::handle:horizontal:hover {
    background-color: #2c5282;
}

QSlider::sub-page:horizontal {
    background-color: #3182ce;
    border-radius: 4px;
}

/* ==================== 电极阻抗显示 ==================== */
QLabel.electrode_impedance {
    font-weight: 600;
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 4px;
    min-width: 40px;
    text-align: center;
}

QLabel.impedance_good {
    background-color: #38a169;
    color: white;
}

QLabel.impedance_warning {
    background-color: #d69e2e;
    color: white;
}

QLabel.impedance_bad {
    background-color: #e53e3e;
    color: white;
}

QLabel.impedance_unknown {
    background-color: #718096;
    color: white;
}

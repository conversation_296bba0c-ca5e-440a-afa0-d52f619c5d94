#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代化顶部栏组件
Modern Top Bar Component

作者: AI Assistant
版本: 2.0.0
"""

import logging
from typing import Dict, List, Optional
from PySide6.QtWidgets import (
    QWidget, QHBoxLayout, QVBoxLayout, QLabel, QPushButton,
    QFrame, QSizePolicy
)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QIcon


class StatusIndicator(QWidget):
    """状态指示器组件"""
    
    def __init__(self, text: str, status: str = "normal", parent=None):
        super().__init__(parent)
        self.status = status
        self.init_ui(text)
    
    def init_ui(self, text: str):
        """初始化UI"""
        self.setProperty("class", f"status-indicator {self.status}")
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(6, 6, 6, 6)
        layout.setSpacing(6)
        
        # 状态点
        self.status_dot = QLabel("●")
        self.status_dot.setProperty("class", f"status-text {self.status}")
        layout.addWidget(self.status_dot)
        
        # 状态文本
        self.status_text = QLabel(text)
        self.status_text.setProperty("class", f"status-text {self.status}")
        layout.addWidget(self.status_text)
    
    def update_status(self, text: str, status: str = "normal"):
        """更新状态"""
        self.status = status
        self.status_text.setText(text)
        self.setProperty("class", f"status-indicator {status}")
        self.status_dot.setProperty("class", f"status-text {status}")
        self.status_text.setProperty("class", f"status-text {status}")
        
        # 刷新样式
        self.style().unpolish(self)
        self.style().polish(self)


class ThemeSwitch(QWidget):
    """主题切换开关组件"""
    
    theme_changed = Signal(str)  # 主题切换信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_theme = "medical"  # 默认医疗主题
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        self.setObjectName("theme_switch")
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(8)
        
        # 医疗主题标签
        self.medical_label = QLabel("医疗")
        self.medical_label.setProperty("class", "theme-label")
        layout.addWidget(self.medical_label)
        
        # 切换按钮
        self.switch_button = QPushButton()
        self.switch_button.setFixedSize(48, 24)
        self.switch_button.setCheckable(True)
        self.switch_button.setChecked(False)  # 默认医疗主题
        self.switch_button.clicked.connect(self.toggle_theme)
        layout.addWidget(self.switch_button)
        
        # 科技主题标签
        self.tech_label = QLabel("科技")
        self.tech_label.setProperty("class", "theme-label")
        layout.addWidget(self.tech_label)
    
    def toggle_theme(self):
        """切换主题"""
        if self.current_theme == "medical":
            self.current_theme = "tech"
        else:
            self.current_theme = "medical"
        
        self.theme_changed.emit(self.current_theme)
    
    def set_theme(self, theme_name: str):
        """设置主题"""
        self.current_theme = theme_name
        self.switch_button.setChecked(theme_name == "tech")


class ModernTopBar(QWidget):
    """现代化顶部栏组件"""
    
    # 信号定义
    menu_toggle_clicked = Signal()  # 菜单切换信号
    theme_changed = Signal(str)     # 主题切换信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        
        # UI组件
        self.menu_toggle_button = None
        self.page_title = None
        self.theme_switch = None
        self.status_indicators = {}
        
        # 定时器
        self.status_update_timer = None
        
        # 初始化UI
        self.init_ui()
        self.setup_status_indicators()
        self.setup_timer()
        
        self.logger.info("现代化顶部栏组件初始化完成")
    
    def init_ui(self):
        """初始化用户界面"""
        try:
            # 设置对象名称和属性
            self.setObjectName("top_bar")
            self.setFixedHeight(72)
            self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
            
            # 主布局
            main_layout = QHBoxLayout(self)
            main_layout.setContentsMargins(24, 0, 24, 0)
            main_layout.setSpacing(16)
            
            # 左侧区域
            self.create_left_section()
            main_layout.addWidget(self.left_section)
            
            # 中间弹性空间
            main_layout.addStretch()
            
            # 右侧区域
            self.create_right_section()
            main_layout.addWidget(self.right_section)
            
        except Exception as e:
            self.logger.error(f"顶部栏UI初始化失败: {e}")
            raise
    
    def create_left_section(self):
        """创建左侧区域"""
        self.left_section = QWidget()
        
        layout = QHBoxLayout(self.left_section)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(16)
        
        # 菜单切换按钮
        self.menu_toggle_button = QPushButton("☰")
        self.menu_toggle_button.setObjectName("menu_toggle")
        self.menu_toggle_button.setFixedSize(40, 40)
        self.menu_toggle_button.clicked.connect(self.on_menu_toggle_clicked)
        layout.addWidget(self.menu_toggle_button)
        
        # 页面标题
        self.page_title = QLabel("实时监测仪表板")
        self.page_title.setObjectName("page_title")
        layout.addWidget(self.page_title)
    
    def create_right_section(self):
        """创建右侧区域"""
        self.right_section = QWidget()
        
        layout = QHBoxLayout(self.right_section)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(16)
        
        # 主题切换开关
        self.theme_switch = ThemeSwitch()
        self.theme_switch.theme_changed.connect(self.on_theme_changed)
        layout.addWidget(self.theme_switch)
        
        # 状态指示器容器
        self.status_container = QWidget()
        self.status_layout = QHBoxLayout(self.status_container)
        self.status_layout.setContentsMargins(0, 0, 0, 0)
        self.status_layout.setSpacing(12)
        layout.addWidget(self.status_container)
    
    def setup_status_indicators(self):
        """设置状态指示器"""
        try:
            # 创建状态指示器
            indicators_config = [
                ("device", "设备离线", "error"),
                ("signal", "信号检测中", "warning"),
                ("system", "系统正常", "normal"),
            ]
            
            for indicator_id, text, status in indicators_config:
                indicator = StatusIndicator(text, status)
                self.status_indicators[indicator_id] = indicator
                self.status_layout.addWidget(indicator)
                
        except Exception as e:
            self.logger.error(f"设置状态指示器失败: {e}")
    
    def setup_timer(self):
        """设置定时器"""
        try:
            # 状态更新定时器
            self.status_update_timer = QTimer()
            self.status_update_timer.timeout.connect(self.update_status_indicators)
            self.status_update_timer.start(5000)  # 每5秒更新一次
            
        except Exception as e:
            self.logger.error(f"设置定时器失败: {e}")
    
    def on_menu_toggle_clicked(self):
        """处理菜单切换按钮点击"""
        self.menu_toggle_clicked.emit()
    
    def on_theme_changed(self, theme_name: str):
        """处理主题切换"""
        self.theme_changed.emit(theme_name)
    
    def set_page_title(self, title: str):
        """设置页面标题"""
        try:
            self.page_title.setText(title)
        except Exception as e:
            self.logger.error(f"设置页面标题失败: {e}")
    
    def update_status_indicator(self, indicator_id: str, text: str, status: str = "normal"):
        """更新状态指示器"""
        try:
            if indicator_id in self.status_indicators:
                self.status_indicators[indicator_id].update_status(text, status)
        except Exception as e:
            self.logger.error(f"更新状态指示器失败: {e}")
    
    def update_status_indicators(self):
        """更新所有状态指示器"""
        try:
            # 这里可以添加实际的状态检查逻辑
            # 暂时使用模拟数据
            import random
            
            # 模拟设备状态
            device_statuses = [
                ("设备在线", "normal"),
                ("设备离线", "error"),
                ("设备连接中", "warning")
            ]
            device_text, device_status = random.choice(device_statuses)
            self.update_status_indicator("device", device_text, device_status)
            
            # 模拟信号状态
            signal_statuses = [
                ("信号良好", "normal"),
                ("信号微弱", "warning"),
                ("信号中断", "error")
            ]
            signal_text, signal_status = random.choice(signal_statuses)
            self.update_status_indicator("signal", signal_text, signal_status)
            
        except Exception as e:
            self.logger.error(f"更新状态指示器失败: {e}")
    
    def set_device_status(self, status: str, text: str = None):
        """设置设备状态"""
        if text is None:
            status_texts = {
                "normal": "设备在线",
                "warning": "设备连接中", 
                "error": "设备离线"
            }
            text = status_texts.get(status, "设备状态未知")
        
        self.update_status_indicator("device", text, status)
    
    def set_signal_status(self, status: str, text: str = None):
        """设置信号状态"""
        if text is None:
            status_texts = {
                "normal": "信号良好",
                "warning": "信号微弱",
                "error": "信号中断"
            }
            text = status_texts.get(status, "信号状态未知")
        
        self.update_status_indicator("signal", text, status)
    
    def set_system_status(self, status: str, text: str = None):
        """设置系统状态"""
        if text is None:
            status_texts = {
                "normal": "系统正常",
                "warning": "系统警告",
                "error": "系统错误"
            }
            text = status_texts.get(status, "系统状态未知")
        
        self.update_status_indicator("system", text, status)

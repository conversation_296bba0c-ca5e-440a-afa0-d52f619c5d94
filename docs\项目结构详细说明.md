# NK脑机接口系统 - 项目结构详细说明

## 项目目录结构

```
Python_NK_System/
├── 📄 main.py                          # 主程序入口，应用程序启动和初始化
├── 📄 README.md                        # 项目说明文档
├── 📄 requirements.txt                 # Python依赖包列表
├── 📄 启动系统.bat                     # Windows快速启动脚本
├── 📄 install_dependencies.py          # 自动依赖安装脚本
├── 📄 check_dependencies.py            # 依赖检查脚本
├── 📄 test_system.py                   # 系统测试脚本
├── 📄 quick_start.py                   # 快速功能演示脚本
├── 📄 fix_database.py                  # 数据库修复工具
│
├── 📁 core/                            # 核心业务逻辑模块
│   ├── 📄 __init__.py                  # 模块初始化文件
│   ├── 📄 main_window.py               # 主窗口控制器，整个应用的UI入口
│   ├── 📄 database_manager.py          # 数据库管理器，SQLite操作封装
│   ├── 📄 logger_system.py             # 日志系统，操作记录和错误追踪
│   ├── 📄 auth_manager.py              # 权限管理器，用户认证和授权
│   ├── 📄 performance_optimizer.py     # 性能优化器，系统性能监控和优化
│   ├── 📄 eeg_device.py                # 脑电设备接口，ADS1299设备控制
│   ├── 📄 signal_processor.py          # 信号处理器，脑电信号预处理和特征提取
│   ├── 📄 stimulation_device.py        # 电刺激设备接口，RecoveryDLL封装
│   ├── 📄 stimulation_device_qt.py     # 电刺激设备Qt集成版本
│   ├── 📄 ml_model.py                  # 机器学习模型，传统分类算法
│   ├── 📄 eegnet_model.py              # EEGNet深度学习模型
│   ├── 📄 motor_imagery_trainer.py     # 运动想象训练器，训练流程控制
│   ├── 📄 treatment_workflow.py        # 治疗工作流程，完整治疗流程管理
│   ├── 📄 transfer_learning.py         # 迁移学习模块，预训练模型管理
│   ├── 📄 pretrained_model_manager.py  # 预训练模型管理器
│   ├── 📄 dataset_manager.py           # 数据集管理器，BCI数据集处理
│   ├── 📄 http_client.py               # HTTP客户端，平台数据上传
│   ├── 📄 udp_communicator.py          # UDP通信器，VR系统通信
│   ├── 📄 voice_prompt.py              # 语音提示系统，训练语音指导
│   ├── 📄 report_generator.py          # 报告生成器，治疗报告自动生成
│   ├── 📄 chart_generator.py           # 图表生成器，数据可视化
│   └── 📄 pdf_exporter.py              # PDF导出器，报告PDF输出
│
├── 📁 ui/                              # 用户界面模块
│   ├── 📄 __init__.py                  # 模块初始化文件
│   ├── 📄 login_dialog.py              # 登录对话框，用户身份验证界面
│   ├── 📄 patient_management_ui.py     # 患者管理界面，患者信息CRUD操作
│   ├── 📄 treatment_ui.py              # 治疗系统界面，脑电训练和电刺激控制
│   ├── 📄 report_ui.py                 # 报告分析界面，数据分析和报告生成
│   ├── 📄 settings_ui.py               # 系统设置界面，参数配置和系统管理
│   ├── 📄 user_management_ui.py        # 用户管理界面，用户权限管理
│   ├── 📄 model_adjustment_widget.py   # 模型调整组件，算法参数调节
│   └── 📄 transfer_learning_dialog.py  # 迁移学习对话框，预训练模型选择
│
├── 📁 algorithms/                      # 算法模块
│   └── 📄 motor_imagery_classifier.py  # 运动想象分类器，传统机器学习算法
│
├── 📁 utils/                           # 工具模块
│   ├── 📄 __init__.py                  # 模块初始化文件
│   ├── 📄 app_config.py                # 应用配置管理，系统参数和配置
│   └── 📄 single_instance.py           # 单实例检查，防止重复启动
│
├── 📁 data/                            # 数据目录
│   ├── 📄 nk_system.db                 # 主数据库文件，SQLite数据库
│   ├── 📄 user_config.json             # 用户配置文件，个性化设置
│   ├── 📄 user_config_backup.json      # 配置备份文件
│   ├── 📁 backup/                      # 数据库备份目录
│   ├── 📁 bci_dataset/                 # BCI数据集目录，训练数据
│   ├── 📁 datasets/                    # 其他数据集目录
│   ├── 📁 models/                      # 训练模型存储目录
│   └── 📁 pretrained_models/           # 预训练模型目录
│
├── 📁 logs/                            # 日志目录
│   ├── 📄 nk_system.log                # 主日志文件，系统运行日志
│   ├── 📄 nk_system.log.1              # 日志轮转文件
│   ├── 📄 error.log                    # 错误日志文件
│   └── 📄 nk_system_backup_*.log       # 日志备份文件
│
├── 📁 resources/                       # 资源文件目录
│   ├── 📁 images/                      # 图片资源，界面图标和图片
│   ├── 📁 styles/                      # 样式文件，Qt样式表
│   │   └── 📄 main_style.qss           # 主样式表文件
│   └── 📁 templates/                   # 模板文件，报告模板
│
├── 📁 libs/                            # 第三方库目录
│   ├── 📄 RecoveryDLL.dll              # 电刺激控制动态库
│   ├── 📄 RecoveryDLL.lib              # 静态库文件
│   ├── 📄 RecoveryDLLd.lib             # 调试版静态库
│   └── 📄 RecoveryModuleDLL.h          # 头文件
│
├── 📁 reports/                         # 报告输出目录
│   └── 📄 患者报告_*.pdf               # 生成的患者报告文件
│
├── 📁 tools/                           # 开发工具目录
│   ├── 📄 create_compatible_pretrained_model.py  # 兼容模型创建工具
│   ├── 📄 create_stroke_pretrained.py  # 中风患者预训练模型创建
│   ├── 📄 download_bci_dataset.py      # BCI数据集下载工具
│   ├── 📄 download_physionet_dataset.py # PhysioNet数据集下载
│   ├── 📄 pretrain_eegnet.py           # EEGNet预训练工具
│   ├── 📄 process_real_bci_dataset.py  # 真实BCI数据集处理
│   └── 📄 train_pretrained_model.py    # 预训练模型训练工具
│
├── 📁 docs/                            # 文档目录
│   ├── 📄 完整开发文档.md              # 完整开发文档
│   ├── 📄 项目结构详细说明.md          # 本文档
│   ├── 📄 motor_imagery_training_guide.md  # 运动想象训练指南
│   ├── 📄 transfer_learning_guide.md   # 迁移学习指南
│   └── 📄 real_bci_transfer_learning_summary.md  # 真实BCI迁移学习总结
│
├── 📁 plugins/                         # Qt插件目录
│   └── 📁 platforms/                   # 平台插件
│
├── 📁 pretrained_models/               # 预训练模型存储
│   ├── 📄 eegnet_*.keras               # EEGNet模型文件
│   ├── 📄 eegnet_*_info.json          # 模型信息文件
│   └── 📄 eegnet_*_scaler.pkl         # 数据缩放器文件
│
└── 📁 test_data/                       # 测试数据目录
    └── 各种测试相关的数据文件
```

## 核心模块详细说明

### 1. 主程序模块 (main.py)
- **功能**: 应用程序入口点，负责系统初始化和启动
- **主要类**: `NKApplication`
- **关键功能**:
  - 依赖检查和环境验证
  - 单实例检查
  - 启动画面显示
  - 数据库初始化
  - 主窗口创建和显示
  - 资源清理和退出处理

### 2. 核心业务模块 (core/)

#### 2.1 主窗口控制器 (main_window.py)
- **功能**: 主界面控制和页面管理
- **主要类**: `MainWindow`
- **关键功能**:
  - 标签页管理（患者管理、治疗系统、报告分析、系统设置、用户管理）
  - 状态栏信息显示
  - 菜单栏功能
  - 电极阻抗显示
  - 系统状态监控

#### 2.2 数据库管理器 (database_manager.py)
- **功能**: 数据库操作封装和管理
- **主要类**: `DatabaseManager`
- **关键功能**:
  - SQLite数据库连接池管理
  - 表结构创建和维护
  - CRUD操作封装
  - 事务处理
  - 数据备份和恢复
  - 数据完整性检查

#### 2.3 脑电设备接口 (eeg_device.py)
- **功能**: ADS1299脑电设备控制
- **主要类**: `EEGDevice`
- **关键功能**:
  - 串口通信管理
  - 数据包解析
  - 实时数据采集
  - 电极阻抗监测
  - 设备状态检查
  - 连接异常处理

#### 2.4 信号处理器 (signal_processor.py)
- **功能**: 脑电信号预处理和特征提取
- **主要类**: `SignalProcessor`
- **关键功能**:
  - 数字滤波（带通、陷波）
  - 伪迹检测和去除
  - 功率谱密度计算
  - CSP特征提取
  - 小波变换特征
  - 实时信号处理

#### 2.5 电刺激设备接口 (stimulation_device.py)
- **功能**: 电刺激设备控制
- **主要类**: `StimulationDevice`
- **关键功能**:
  - DLL接口封装
  - 参数设置和验证
  - 刺激控制（开始/停止）
  - 设备状态监控
  - 预刺激功能
  - 安全保护机制

#### 2.6 机器学习模型 (ml_model.py, eegnet_model.py)
- **功能**: 运动想象分类算法
- **主要类**: `MLModel`, `EEGNetModel`
- **关键功能**:
  - 传统机器学习算法（SVM、随机森林）
  - EEGNet深度学习模型
  - 模型训练和验证
  - 实时分类预测
  - 模型保存和加载
  - 性能评估

#### 2.7 治疗工作流程 (treatment_workflow.py)
- **功能**: 完整治疗流程管理
- **主要类**: `TreatmentWorkflow`
- **关键功能**:
  - 治疗状态机管理
  - 运动想象检测
  - 电刺激触发控制
  - 超时处理
  - 数据记录
  - 自适应学习

### 3. 用户界面模块 (ui/)

#### 3.1 患者管理界面 (patient_management_ui.py)
- **功能**: 患者信息管理
- **主要类**: `PatientManagementUI`
- **关键功能**:
  - 患者信息录入和编辑
  - 患者列表显示和搜索
  - 治疗记录查看
  - 数据导入导出
  - 批量操作

#### 3.2 治疗系统界面 (treatment_ui.py)
- **功能**: 脑电训练和电刺激治疗
- **主要类**: `TreatmentUI`
- **关键功能**:
  - 脑电信号实时显示
  - 电刺激参数设置
  - 治疗流程控制
  - 分类结果显示
  - 治疗数据记录

#### 3.3 报告分析界面 (report_ui.py)
- **功能**: 数据分析和报告生成
- **主要类**: `ReportUI`
- **关键功能**:
  - 患者数据查询
  - 统计分析图表
  - 报告模板选择
  - PDF报告生成
  - 数据导出

#### 3.4 系统设置界面 (settings_ui.py)
- **功能**: 系统参数配置
- **主要类**: `SettingsUI`
- **关键功能**:
  - 设备参数配置
  - 算法参数调节
  - 网络设置
  - 数据库管理
  - 系统日志查看

### 4. 工具模块 (utils/)

#### 4.1 应用配置管理 (app_config.py)
- **功能**: 系统配置管理
- **主要类**: `AppConfig`
- **关键功能**:
  - 配置参数定义
  - 配置文件读写
  - 环境变量支持
  - 配置验证
  - 默认值管理

#### 4.2 单实例检查 (single_instance.py)
- **功能**: 防止程序重复启动
- **主要类**: `SingleInstance`
- **关键功能**:
  - 进程锁管理
  - 实例检查
  - 资源清理
  - 跨平台支持

## 数据流架构

### 1. 脑电数据流
```
ADS1299设备 → 串口通信 → 数据包解析 → 实时缓冲 → 信号预处理 → 特征提取 → 分类预测 → 结果显示
                                    ↓
                              数据库存储 ← 治疗记录 ← 工作流程控制
```

### 2. 电刺激控制流
```
用户界面 → 参数验证 → DLL接口 → 设备控制 → 状态反馈 → 界面更新
    ↓
治疗工作流 → 自动触发 → 刺激执行 → 状态监控 → 安全保护
```

### 3. 数据存储流
```
实时数据 → 缓冲区 → 批量写入 → SQLite数据库 → 备份机制
    ↓
治疗记录 → 数据验证 → 平台上传 → HTTP接口 → 云端存储
```

## 配置管理架构

### 1. 配置层次
```
默认配置 (app_config.py) → 用户配置文件 (user_config.json) → 环境变量 → 运行时设置
```

### 2. 配置分类
- **系统配置**: 数据库路径、日志设置、基本参数
- **设备配置**: 串口参数、DLL路径、设备参数
- **算法配置**: 滤波参数、特征提取参数、模型参数
- **界面配置**: 主题、字体、窗口大小
- **网络配置**: HTTP接口、UDP通信参数

### 3. 配置管理特性
- **热更新**: 部分配置支持运行时修改
- **验证机制**: 配置参数有效性检查
- **备份恢复**: 配置文件自动备份
- **版本兼容**: 配置格式向后兼容

## 安全架构

### 1. 权限控制
- **角色定义**: 管理员、医生、技师、操作员
- **权限矩阵**: 功能权限精细控制
- **会话管理**: 用户登录状态管理
- **操作审计**: 关键操作日志记录

### 2. 数据安全
- **密码加密**: 用户密码哈希存储
- **数据完整性**: 数据库事务保护
- **备份机制**: 自动数据备份
- **访问控制**: 文件权限管理

### 3. 系统安全
- **单实例保护**: 防止重复启动
- **异常处理**: 完善的错误处理机制
- **资源保护**: 内存和文件资源管理
- **日志监控**: 系统状态实时监控

这个项目结构体现了医疗级软件的严格要求，具有良好的模块化设计、完善的错误处理机制和全面的功能覆盖。

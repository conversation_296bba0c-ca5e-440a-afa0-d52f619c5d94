# 运动想象预训练系统使用指南

## 概述

运动想象预训练系统是NK脑机接口系统的核心功能之一，用于训练患者的运动想象能力，并建立个性化的分类模型。该系统采用先进的脑电信号处理技术和机器学习算法，确保训练效果和模型的鲁棒性。

## 系统特点

### 🧠 智能信号处理
- **多层滤波**: 工频陷波、带通滤波、空间滤波
- **伪迹去除**: 基于ICA的自动伪迹检测和去除
- **质量评估**: 实时信号质量评估，确保数据可用性
- **特征提取**: 功率谱密度、频带功率、谱熵、Hjorth参数

### 🎯 科学训练流程
- **随机序列**: 运动想象和平静状态随机出现，避免预期效应
- **时间控制**: 每状态持续3-5秒，状态间休息2秒
- **多轮训练**: 支持多轮累积训练，提高模型性能
- **实时反馈**: 显示训练进度、状态、信号质量

### 🤖 先进机器学习
- **CSP空间滤波**: 提取运动想象相关的空间特征
- **多算法支持**: LDA、SVM、随机森林
- **增量学习**: 多轮训练数据累积，避免重复训练
- **性能评估**: 交叉验证、混淆矩阵、准确率分析

### 🔊 语音引导系统
- **智能提示**: 自动语音引导训练过程
- **状态提醒**: 实时提示当前应执行的动作
- **多语言支持**: 支持中文语音提示

## 使用步骤

### 1. 准备阶段

#### 1.1 设备连接
1. 确保脑电帽正确佩戴，电极接触良好
2. 在治疗界面点击"连接脑电设备"
3. 等待连接成功提示

#### 1.2 环境准备
- 选择安静的环境，减少外界干扰
- 确保患者处于舒适的坐姿
- 关闭可能产生电磁干扰的设备

### 2. 训练配置

#### 2.1 参数设置
- **训练时长**: 建议50秒（可调整10-300秒）
- **训练次数**: 建议每轮10-20次试验
- **已加载模型**: 显示当前使用的模型

#### 2.2 训练说明
向患者说明训练要求：
- **运动想象**: 想象握拳动作，但不要实际执行
- **平静状态**: 保持放松，不进行任何想象
- **注意力集中**: 根据语音提示切换状态

### 3. 训练执行

#### 3.1 开始训练
1. 点击"开始训练"按钮
2. 系统进入准备阶段（3秒倒计时）
3. 根据语音提示进行相应动作

#### 3.2 训练过程
- **状态显示**: 实时显示当前训练状态
- **进度条**: 显示训练进度
- **质量监控**: 监控信号质量，确保数据有效性
- **日志记录**: 详细记录训练过程

#### 3.3 训练控制
- **暂停训练**: 可随时暂停训练
- **停止训练**: 完全停止当前训练轮次
- **继续训练**: 暂停后可继续训练

### 4. 模型管理

#### 4.1 首次保存
- 第一轮训练完成后，系统提示是否保存模型
- 输入模型名称（建议包含患者信息和日期）
- 系统自动保存模型并显示在"已加载模型"框中

#### 4.2 增量训练
- 后续训练轮次会累积到已有模型
- 每轮训练后可选择是否保存更新
- 系统自动评估是否需要继续训练

#### 4.3 模型评估
训练完成后系统提供多维度评估：
- **数据量**: 可用试验数量
- **信号质量**: 平均信号质量评分
- **数据平衡**: 两类数据的平衡性
- **训练建议**: 基于评估结果的建议

## 训练建议

### 🎯 训练策略

#### 初期训练（第1-3轮）
- **目标**: 建立基础模型
- **试验数**: 每轮15-20次
- **重点**: 确保信号质量，建立稳定的想象模式

#### 中期训练（第4-6轮）
- **目标**: 提高模型准确率
- **试验数**: 每轮10-15次
- **重点**: 优化想象策略，提高一致性

#### 后期训练（第7轮以上）
- **目标**: 精细调优
- **试验数**: 每轮5-10次
- **重点**: 维持模型性能，适应性训练

### 📊 质量控制

#### 信号质量标准
- **优秀**: 质量评分 > 0.8
- **良好**: 质量评分 0.6-0.8
- **可用**: 质量评分 0.4-0.6
- **不可用**: 质量评分 < 0.4

#### 训练效果评估
- **准确率**: 目标 > 70%
- **交叉验证**: 稳定性评估
- **数据量**: 每类至少20个有效试验

### ⚠️ 注意事项

#### 训练过程中
1. **保持专注**: 避免分心，专注于当前任务
2. **动作一致**: 每次运动想象保持相同的想象内容
3. **避免实际动作**: 只进行想象，不要实际执行动作
4. **休息充分**: 感到疲劳时及时休息

#### 信号质量
1. **电极检查**: 定期检查电极接触
2. **环境控制**: 避免电磁干扰
3. **姿势保持**: 维持稳定的坐姿
4. **肌肉放松**: 避免不必要的肌肉紧张

## 故障排除

### 常见问题

#### 1. 信号质量差
**症状**: 质量评分持续低于0.4
**解决方案**:
- 检查电极接触是否良好
- 重新涂抹导电膏
- 检查环境是否有电磁干扰
- 确认患者状态是否放松

#### 2. 训练效果不佳
**症状**: 模型准确率低于60%
**解决方案**:
- 增加训练轮次
- 改善运动想象策略
- 检查训练时的专注度
- 考虑调整训练参数

#### 3. 模型保存失败
**症状**: 提示模型保存错误
**解决方案**:
- 检查磁盘空间是否充足
- 确认有写入权限
- 重启系统后重试

#### 4. 语音提示无声音
**症状**: 没有语音引导
**解决方案**:
- 检查音响设备连接
- 确认系统音量设置
- 检查TTS组件是否安装

## 技术参数

### 信号处理参数
- **采样率**: 125 Hz
- **通道数**: 8通道（PZ,P3,P4,C3,CZ,C4,F3,F4）
- **滤波范围**: 8-30 Hz（mu和beta节律）
- **窗口长度**: 1秒（125个采样点）

### 训练参数
- **运动想象时长**: 4秒
- **平静状态时长**: 4秒
- **休息时间**: 2秒
- **准备时间**: 3秒

### 模型参数
- **特征提取**: CSP + 功率谱特征
- **分类算法**: 线性判别分析（LDA）
- **交叉验证**: 5折交叉验证
- **性能指标**: 准确率、精确率、召回率、F1分数

## 更新日志

### v1.0.0 (2024-12-19)
- ✅ 完整的运动想象训练流程
- ✅ 智能信号预处理和质量评估
- ✅ 多算法机器学习模型
- ✅ 语音引导系统
- ✅ 增量学习和模型管理
- ✅ 实时训练监控和评估

---

**技术支持**: 如有问题请联系技术支持团队
**文档版本**: v1.0.0
**最后更新**: 2024-12-19

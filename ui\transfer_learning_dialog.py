#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
迁移学习配置对话框
Transfer Learning Configuration Dialog

提供迁移学习的详细配置界面，包括：
1. 预训练模型选择
2. 微调参数设置
3. 数据集选择
4. 高级选项配置

作者: AI Assistant
版本: 1.0.0
"""

import logging
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                               QGroupBox, QLabel, QComboBox, QSpinBox, QDoubleSpinBox,
                               QCheckBox, QPushButton, QTextEdit, QProgressBar,
                               QTabWidget, QWidget, QMessageBox, QFrame)
from PySide6.QtCore import Qt, Signal, QThread, QTimer
from PySide6.QtGui import QFont

class TransferLearningWorker(QThread):
    """迁移学习工作线程"""
    progress_updated = Signal(str, int)
    finished = Signal(bool, str)
    
    def __init__(self, config):
        super().__init__()
        self.config = config
        self.logger = logging.getLogger(__name__)
    
    def run(self):
        """执行迁移学习预训练"""
        try:
            from core.transfer_learning import TransferLearningManager
            
            manager = TransferLearningManager()
            
            def progress_callback(message, progress):
                self.progress_updated.emit(message, progress)
            
            success = manager.pretrain_model(self.config, progress_callback)
            
            if success:
                self.finished.emit(True, "预训练完成")
            else:
                self.finished.emit(False, "预训练失败")
                
        except Exception as e:
            self.logger.error(f"预训练工作线程失败: {e}")
            self.finished.emit(False, f"预训练失败: {e}")


class TransferLearningDialog(QDialog):
    """迁移学习配置对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        self.worker = None
        
        self.setWindowTitle("迁移学习配置")
        self.setFixedSize(600, 500)
        self.setModal(True)
        
        # 初始化UI
        self.init_ui()
        
        # 加载可用模型
        self.load_available_models()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 基本配置标签页
        self.create_basic_config_tab()
        
        # 高级配置标签页
        self.create_advanced_config_tab()
        
        # 预训练标签页
        self.create_pretrain_tab()
        
        # 按钮区域
        self.create_button_area(layout)
    
    def create_basic_config_tab(self):
        """创建基本配置标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 预训练模型选择组
        model_group = QGroupBox("预训练模型选择")
        model_layout = QGridLayout(model_group)
        
        # 使用预训练模型选项
        self.use_pretrained_checkbox = QCheckBox("使用预训练模型")
        self.use_pretrained_checkbox.setChecked(True)
        self.use_pretrained_checkbox.toggled.connect(self.on_use_pretrained_toggled)
        model_layout.addWidget(self.use_pretrained_checkbox, 0, 0, 1, 2)
        
        # 预训练模型选择
        model_layout.addWidget(QLabel("预训练模型:"), 1, 0)
        self.pretrained_model_combo = QComboBox()
        self.pretrained_model_combo.setToolTip("选择要使用的预训练模型")
        model_layout.addWidget(self.pretrained_model_combo, 1, 1)
        
        # 模型信息显示
        self.model_info_text = QTextEdit()
        self.model_info_text.setMaximumHeight(80)
        self.model_info_text.setReadOnly(True)
        model_layout.addWidget(QLabel("模型信息:"), 2, 0)
        model_layout.addWidget(self.model_info_text, 2, 1)
        
        layout.addWidget(model_group)
        
        # 微调配置组
        finetune_group = QGroupBox("微调配置")
        finetune_layout = QGridLayout(finetune_group)
        
        # 冻结层数
        finetune_layout.addWidget(QLabel("冻结层数:"), 0, 0)
        self.freeze_layers_spin = QSpinBox()
        self.freeze_layers_spin.setRange(0, 8)
        self.freeze_layers_spin.setValue(2)
        self.freeze_layers_spin.setToolTip("冻结底层特征提取层的数量")
        finetune_layout.addWidget(self.freeze_layers_spin, 0, 1)
        
        # 微调轮次
        finetune_layout.addWidget(QLabel("微调轮次:"), 1, 0)
        self.finetune_epochs_spin = QSpinBox()
        self.finetune_epochs_spin.setRange(5, 100)
        self.finetune_epochs_spin.setValue(20)
        self.finetune_epochs_spin.setToolTip("微调训练的轮次数")
        finetune_layout.addWidget(self.finetune_epochs_spin, 1, 1)
        
        # 批次大小
        finetune_layout.addWidget(QLabel("批次大小:"), 2, 0)
        self.finetune_batch_spin = QSpinBox()
        self.finetune_batch_spin.setRange(4, 64)
        self.finetune_batch_spin.setValue(16)
        self.finetune_batch_spin.setToolTip("微调时的批次大小")
        finetune_layout.addWidget(self.finetune_batch_spin, 2, 1)
        
        # 学习率
        finetune_layout.addWidget(QLabel("学习率:"), 3, 0)
        self.finetune_lr_spin = QDoubleSpinBox()
        self.finetune_lr_spin.setRange(0.00001, 0.01)
        self.finetune_lr_spin.setDecimals(5)
        self.finetune_lr_spin.setValue(0.0001)
        self.finetune_lr_spin.setToolTip("微调时的学习率")
        finetune_layout.addWidget(self.finetune_lr_spin, 3, 1)
        
        layout.addWidget(finetune_group)
        
        self.tab_widget.addTab(tab, "基本配置")
    
    def create_advanced_config_tab(self):
        """创建高级配置标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 自定义预训练组
        custom_group = QGroupBox("自定义预训练配置")
        custom_layout = QGridLayout(custom_group)
        
        # 数据集选择
        custom_layout.addWidget(QLabel("预训练数据集:"), 0, 0)
        self.dataset_combo = QComboBox()
        self.dataset_combo.addItems([
            "bci_competition_iv_2b",
            "bci_competition_iv_2a", 
            "physionet_mi"
        ])
        custom_layout.addWidget(self.dataset_combo, 0, 1)
        
        # 预训练轮次
        custom_layout.addWidget(QLabel("预训练轮次:"), 1, 0)
        self.pretrain_epochs_spin = QSpinBox()
        self.pretrain_epochs_spin.setRange(10, 200)
        self.pretrain_epochs_spin.setValue(50)
        custom_layout.addWidget(self.pretrain_epochs_spin, 1, 1)
        
        # 最大样本数
        custom_layout.addWidget(QLabel("最大样本数:"), 2, 0)
        self.max_samples_spin = QSpinBox()
        self.max_samples_spin.setRange(100, 2000)
        self.max_samples_spin.setValue(500)
        custom_layout.addWidget(self.max_samples_spin, 2, 1)
        
        # 验证集比例
        custom_layout.addWidget(QLabel("验证集比例:"), 3, 0)
        self.validation_split_spin = QDoubleSpinBox()
        self.validation_split_spin.setRange(0.1, 0.5)
        self.validation_split_spin.setDecimals(1)
        self.validation_split_spin.setValue(0.2)
        custom_layout.addWidget(self.validation_split_spin, 3, 1)
        
        layout.addWidget(custom_group)
        
        # 性能优化组
        optimization_group = QGroupBox("性能优化")
        opt_layout = QGridLayout(optimization_group)
        
        # 早停耐心
        opt_layout.addWidget(QLabel("早停耐心:"), 0, 0)
        self.early_stopping_spin = QSpinBox()
        self.early_stopping_spin.setRange(3, 20)
        self.early_stopping_spin.setValue(5)
        self.early_stopping_spin.setToolTip("验证损失不改善时等待的轮次")
        opt_layout.addWidget(self.early_stopping_spin, 0, 1)
        
        # 学习率衰减
        opt_layout.addWidget(QLabel("学习率衰减:"), 1, 0)
        self.lr_decay_spin = QDoubleSpinBox()
        self.lr_decay_spin.setRange(0.1, 0.9)
        self.lr_decay_spin.setDecimals(1)
        self.lr_decay_spin.setValue(0.5)
        self.lr_decay_spin.setToolTip("学习率衰减因子")
        opt_layout.addWidget(self.lr_decay_spin, 1, 1)
        
        layout.addWidget(optimization_group)
        
        self.tab_widget.addTab(tab, "高级配置")
    
    def create_pretrain_tab(self):
        """创建预训练标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 预训练控制组
        control_group = QGroupBox("预训练控制")
        control_layout = QVBoxLayout(control_group)
        
        # 预训练按钮
        self.pretrain_button = QPushButton("开始预训练")
        self.pretrain_button.clicked.connect(self.start_pretrain)
        control_layout.addWidget(self.pretrain_button)
        
        # 进度条
        self.progress_bar = QProgressBar()
        control_layout.addWidget(self.progress_bar)
        
        # 状态显示
        self.status_label = QLabel("就绪")
        control_layout.addWidget(self.status_label)
        
        layout.addWidget(control_group)
        
        # 日志显示组
        log_group = QGroupBox("预训练日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMaximumHeight(200)
        log_layout.addWidget(self.log_text)
        
        layout.addWidget(log_group)
        
        self.tab_widget.addTab(tab, "预训练")
    
    def create_button_area(self, layout):
        """创建按钮区域"""
        button_layout = QHBoxLayout()
        
        # 确定按钮
        self.ok_button = QPushButton("确定")
        self.ok_button.clicked.connect(self.accept)
        button_layout.addWidget(self.ok_button)
        
        # 取消按钮
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)
        
        # 应用按钮
        self.apply_button = QPushButton("应用")
        self.apply_button.clicked.connect(self.apply_config)
        button_layout.addWidget(self.apply_button)
        
        layout.addLayout(button_layout)
    
    def load_available_models(self):
        """加载可用的预训练模型"""
        try:
            from core.pretrained_model_manager import PretrainedModelManager
            
            manager = PretrainedModelManager()
            models = manager.list_available_models()
            
            self.pretrained_model_combo.clear()
            for model_id, model_info in models.items():
                self.pretrained_model_combo.addItem(model_info.name, model_id)
            
            # 连接信号
            self.pretrained_model_combo.currentTextChanged.connect(self.on_model_selection_changed)
            
            # 显示第一个模型的信息
            if models:
                self.on_model_selection_changed()
            
        except Exception as e:
            self.logger.error(f"加载预训练模型失败: {e}")
            QMessageBox.warning(self, "警告", f"加载预训练模型失败: {e}")
    
    def on_use_pretrained_toggled(self, checked):
        """使用预训练模型选项切换"""
        self.pretrained_model_combo.setEnabled(checked)
        self.model_info_text.setEnabled(checked)
        
        # 切换到相应的标签页
        if checked:
            self.tab_widget.setCurrentIndex(0)  # 基本配置
        else:
            self.tab_widget.setCurrentIndex(1)  # 高级配置
    
    def on_model_selection_changed(self):
        """模型选择改变"""
        try:
            from core.pretrained_model_manager import PretrainedModelManager
            
            model_id = self.pretrained_model_combo.currentData()
            if not model_id:
                return
            
            manager = PretrainedModelManager()
            model_info = manager.get_model_info(model_id)
            
            if model_info:
                info_text = f"描述: {model_info.description}\n"
                info_text += f"数据集: {model_info.dataset}\n"
                info_text += f"准确率: {model_info.accuracy:.3f}\n"
                info_text += f"大小: {model_info.size_mb:.1f}MB\n"
                info_text += f"状态: {'已下载' if model_info.is_downloaded else '未下载'}"
                
                self.model_info_text.setText(info_text)
            
        except Exception as e:
            self.logger.error(f"获取模型信息失败: {e}")
    
    def start_pretrain(self):
        """开始预训练"""
        try:
            if self.worker and self.worker.isRunning():
                QMessageBox.warning(self, "警告", "预训练正在进行中")
                return
            
            # 获取配置
            config = self.get_transfer_config()
            
            # 创建工作线程
            self.worker = TransferLearningWorker(config)
            self.worker.progress_updated.connect(self.on_pretrain_progress)
            self.worker.finished.connect(self.on_pretrain_finished)
            
            # 更新UI状态
            self.pretrain_button.setEnabled(False)
            self.progress_bar.setValue(0)
            self.status_label.setText("预训练中...")
            self.log_text.clear()
            
            # 开始预训练
            self.worker.start()
            
        except Exception as e:
            self.logger.error(f"启动预训练失败: {e}")
            QMessageBox.critical(self, "错误", f"启动预训练失败: {e}")
    
    def on_pretrain_progress(self, message, progress):
        """预训练进度更新"""
        self.progress_bar.setValue(progress)
        self.status_label.setText(message)
        self.log_text.append(f"[{progress:3d}%] {message}")
    
    def on_pretrain_finished(self, success, message):
        """预训练完成"""
        self.pretrain_button.setEnabled(True)
        self.status_label.setText(message)
        self.log_text.append(f"预训练完成: {message}")
        
        if success:
            QMessageBox.information(self, "成功", "预训练完成!")
        else:
            QMessageBox.warning(self, "失败", f"预训练失败: {message}")
    
    def get_transfer_config(self):
        """获取迁移学习配置"""
        from core.transfer_learning import TransferLearningConfig
        
        config = TransferLearningConfig()
        
        # 基本配置
        config.use_pretrained_model = self.use_pretrained_checkbox.isChecked()
        config.pretrained_model_id = self.pretrained_model_combo.currentData() or "eegnet_general"
        config.freeze_layers = self.freeze_layers_spin.value()
        config.finetune_epochs = self.finetune_epochs_spin.value()
        config.finetune_batch_size = self.finetune_batch_spin.value()
        config.finetune_learning_rate = self.finetune_lr_spin.value()
        
        # 高级配置
        config.pretrain_dataset = self.dataset_combo.currentText()
        config.pretrain_epochs = self.pretrain_epochs_spin.value()
        config.max_pretrain_samples = self.max_samples_spin.value()
        config.validation_split = self.validation_split_spin.value()
        
        return config
    
    def apply_config(self):
        """应用配置"""
        try:
            config = self.get_transfer_config()
            
            # 这里可以将配置应用到主界面
            # 通过信号或回调函数传递配置
            
            QMessageBox.information(self, "成功", "配置已应用")
            
        except Exception as e:
            self.logger.error(f"应用配置失败: {e}")
            QMessageBox.critical(self, "错误", f"应用配置失败: {e}")
    
    def closeEvent(self, event):
        """关闭事件"""
        if self.worker and self.worker.isRunning():
            reply = QMessageBox.question(
                self, "确认", "预训练正在进行中，确定要关闭吗？",
                QMessageBox.Yes | QMessageBox.No
            )
            if reply == QMessageBox.Yes:
                self.worker.terminate()
                self.worker.wait()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()

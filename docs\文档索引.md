# NK脑机接口康复训练系统 - 文档索引

## 文档概览

本文档集合提供了NK脑机接口康复训练系统的完整技术文档，涵盖了从系统架构到部署运维的各个方面。

## 📚 核心文档

### 1. [完整开发文档](./完整开发文档.md)
**文档类型**: 核心技术文档  
**适用人群**: 开发人员、技术架构师、项目经理  
**内容概要**:
- 系统概述和技术特点
- 整体架构设计
- 技术栈和依赖关系
- 数据库设计详解
- 设备接口规范
- 信号处理算法
- 网络通信协议
- 配置管理系统
- 权限管理机制
- 治疗工作流程
- 实时显示系统
- 日志系统设计
- 报告生成系统
- 性能优化策略
- 测试体系
- 故障排除指南

### 2. [项目结构详细说明](./项目结构详细说明.md)
**文档类型**: 架构文档  
**适用人群**: 开发人员、系统分析师  
**内容概要**:
- 完整的项目目录结构
- 核心模块功能说明
- 数据流架构设计
- 配置管理架构
- 安全架构设计
- 模块间依赖关系
- 代码组织原则

### 3. [API接口文档](./API接口文档.md)
**文档类型**: 接口规范文档  
**适用人群**: 开发人员、集成工程师  
**内容概要**:
- 内部API接口规范
- 数据库管理器接口
- 脑电设备接口
- 电刺激设备接口
- 信号处理接口
- 机器学习模型接口
- HTTP API接口
- UDP通信接口
- 错误码定义
- 接口使用示例

### 4. [部署运维指南](./部署运维指南.md)
**文档类型**: 运维文档  
**适用人群**: 系统管理员、运维工程师、实施工程师  
**内容概要**:
- 系统要求和环境准备
- 安装部署流程
- 配置设置指南
- 生产环境配置
- 日常维护操作
- 故障诊断和处理
- 更新升级流程
- 监控告警设置
- 性能调优方法
- 备份恢复策略

## 📖 专题文档

### 5. [运动想象训练指南](./motor_imagery_training_guide.md)
**文档类型**: 功能使用指南  
**适用人群**: 医生、技师、操作员  
**内容概要**:
- 运动想象训练原理
- 训练流程和步骤
- 参数设置说明
- 训练效果评估
- 常见问题解答

### 6. [迁移学习指南](./transfer_learning_guide.md)
**文档类型**: 算法使用指南  
**适用人群**: 算法工程师、研究人员  
**内容概要**:
- 迁移学习原理
- 预训练模型使用
- 模型微调方法
- 性能优化技巧
- 实际应用案例

### 7. [真实BCI迁移学习总结](./real_bci_transfer_learning_summary.md)
**文档类型**: 技术总结文档  
**适用人群**: 算法工程师、研究人员  
**内容概要**:
- 真实BCI数据集处理
- 迁移学习实现细节
- 性能评估结果
- 优化建议和改进方向

## 📋 项目文档

### 8. [README.md](../README.md)
**文档类型**: 项目说明文档  
**适用人群**: 所有用户  
**内容概要**:
- 项目概述
- 快速开始指南
- 安装说明
- 基本使用方法
- 故障排除

### 9. [项目迁移需求文档](../项目迁移需求文档.md)
**文档类型**: 需求分析文档  
**适用人群**: 项目经理、开发人员  
**内容概要**:
- 迁移目标和要求
- 原系统分析
- 技术方案设计
- 实现计划

### 10. [项目完成总结](../项目完成总结.md)
**文档类型**: 项目总结文档  
**适用人群**: 项目经理、开发团队  
**内容概要**:
- 项目完成情况
- 技术实现亮点
- 系统验证结果
- 后续开发建议

## 🔧 技术专题文档

### 11. 脑电信号处理算法优化报告
**文档位置**: `../脑电信号处理算法优化报告.md`  
**内容概要**:
- CSP算法增强
- 特征提取优化
- 分类算法改进
- 性能验证结果

### 12. EEGNet深度学习系统优化报告
**文档位置**: `../EEGNet深度学习系统优化完成报告.md`  
**内容概要**:
- EEGNet模型集成
- 深度学习优化
- 迁移学习实现
- 系统性能提升

### 13. 电刺激功能集成报告
**文档位置**: `../电刺激功能集成报告.md`  
**内容概要**:
- 电刺激设备接口
- 参数控制实现
- 安全保护机制
- 治疗流程集成

### 14. 实时显示功能说明
**文档位置**: `../实时显示功能说明.md`  
**内容概要**:
- 实时脑电信号显示
- 脑电地形图实现
- 电极阻抗监测
- 性能优化方案

## 🛠️ 工具和脚本文档

### 15. 依赖检查工具
**文件位置**: `../check_dependencies.py`  
**功能**: 检查系统依赖包安装情况

### 16. 自动安装脚本
**文件位置**: `../install_dependencies.py`  
**功能**: 自动安装所需依赖包

### 17. 系统测试脚本
**文件位置**: `../test_system.py`  
**功能**: 系统功能完整性测试

### 18. 快速启动脚本
**文件位置**: `../quick_start.py`  
**功能**: 快速功能演示和验证

## 📊 报告和分析文档

### 19. 测试验证报告
**文档位置**: `../测试验证报告.md`  
**内容概要**:
- 功能测试结果
- 性能测试数据
- 集成测试验证
- 问题修复记录

### 20. UI修复和改进报告
**文档位置**: `../UI修复总结报告.md`  
**内容概要**:
- 界面优化改进
- 用户体验提升
- 布局调整说明
- 功能增强记录

## 📝 使用指南

### 21. 患者管理使用说明
**内容**: 患者信息管理、治疗记录查看、数据导入导出

### 22. 治疗系统操作指南
**内容**: 脑电训练操作、电刺激治疗、参数设置、数据记录

### 23. 报告分析使用说明
**内容**: 数据查询分析、报告生成、图表制作、PDF导出

### 24. 系统设置配置指南
**内容**: 参数配置、设备设置、用户管理、系统维护

## 🔍 文档使用建议

### 新用户入门路径
1. **项目了解**: 阅读 [README.md](../README.md) 了解项目基本情况
2. **系统架构**: 阅读 [完整开发文档](./完整开发文档.md) 了解技术架构
3. **项目结构**: 阅读 [项目结构详细说明](./项目结构详细说明.md) 了解代码组织
4. **部署安装**: 按照 [部署运维指南](./部署运维指南.md) 进行系统部署

### 开发人员学习路径
1. **技术架构**: [完整开发文档](./完整开发文档.md)
2. **代码结构**: [项目结构详细说明](./项目结构详细说明.md)
3. **接口规范**: [API接口文档](./API接口文档.md)
4. **专题技术**: 各种技术专题报告

### 运维人员参考路径
1. **部署指南**: [部署运维指南](./部署运维指南.md)
2. **系统架构**: [完整开发文档](./完整开发文档.md) 中的架构部分
3. **故障处理**: 各种问题修复报告
4. **监控维护**: 部署运维指南中的监控部分

### 用户操作参考路径
1. **快速开始**: [README.md](../README.md) 中的使用说明
2. **功能指南**: 各种使用说明文档
3. **问题解决**: 故障排除相关文档

## 📞 技术支持

### 文档反馈
如果您在使用文档过程中发现问题或有改进建议，请联系开发团队。

### 技术咨询
对于技术实现细节或使用问题，可以参考相应的专题文档或联系技术支持。

### 更新说明
文档会随着系统版本更新而持续维护，请关注文档版本和更新日期。

---

**文档版本**: 1.0.0  
**最后更新**: 2024-12-19  
**维护团队**: 山东海天智能工程有限公司  
**联系方式**: 请通过项目管理系统提交技术支持请求

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
页面基类
Base Page Class

作者: AI Assistant
版本: 2.0.0
"""

import logging
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton
from PySide6.QtCore import Signal, QTimer, Qt
from PySide6.QtGui import QIcon

# 导入核心业务逻辑
from core.database_manager import DatabaseManager
from core.auth_manager import AuthManager
from core.logger_system import get_logger_system


class BasePage(QWidget, ABC):
    """页面基类 - 所有页面的基础类"""
    
    # 信号定义
    page_loaded = Signal()          # 页面加载完成信号
    page_error = Signal(str)        # 页面错误信号
    data_changed = Signal(dict)     # 数据变更信号
    status_changed = Signal(str, str)  # 状态变更信号 (status_type, message)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 基础属性
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger_system = get_logger_system()
        self.page_id = self.__class__.__name__.lower().replace('page', '')
        
        # 业务逻辑组件
        self.db_manager: Optional[DatabaseManager] = None
        self.auth_manager: Optional[AuthManager] = None
        
        # 页面状态
        self.is_loaded = False
        self.is_visible = False
        self.current_data = {}
        
        # UI组件
        self.main_layout = None
        self.content_widget = None
        self.loading_widget = None
        self.error_widget = None
        
        # 定时器
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_data)
        
        # 初始化
        self.init_base_ui()
        self.setup_base_connections()
        
        self.logger.info(f"页面 {self.page_id} 基类初始化完成")
    
    def init_base_ui(self):
        """初始化基础UI结构"""
        try:
            # 设置对象名称
            self.setObjectName(f"{self.page_id}_page")
            
            # 主布局
            self.main_layout = QVBoxLayout(self)
            self.main_layout.setContentsMargins(24, 24, 24, 24)
            self.main_layout.setSpacing(24)
            
            # 创建加载状态组件
            self.create_loading_widget()
            self.create_error_widget()
            
            # 子类实现具体UI
            self.setup_ui()
            
        except Exception as e:
            self.logger.error(f"初始化基础UI失败: {e}")
            raise
    
    def create_loading_widget(self):
        """创建加载状态组件"""
        self.loading_widget = QWidget()
        self.loading_widget.setObjectName("loading_widget")
        
        layout = QVBoxLayout(self.loading_widget)
        layout.setAlignment(Qt.AlignCenter)
        
        # 加载图标
        loading_label = QLabel("⏳")
        loading_label.setProperty("class", "text-xxxl text-center")
        loading_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(loading_label)
        
        # 加载文本
        loading_text = QLabel("正在加载...")
        loading_text.setProperty("class", "text-lg text-center")
        loading_text.setAlignment(Qt.AlignCenter)
        layout.addWidget(loading_text)
        
        self.loading_widget.hide()
    
    def create_error_widget(self):
        """创建错误状态组件"""
        self.error_widget = QWidget()
        self.error_widget.setObjectName("error_widget")
        
        layout = QVBoxLayout(self.error_widget)
        layout.setAlignment(Qt.AlignCenter)
        
        # 错误图标
        error_label = QLabel("❌")
        error_label.setProperty("class", "text-xxxl text-center")
        error_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(error_label)
        
        # 错误文本
        self.error_text = QLabel("加载失败")
        self.error_text.setProperty("class", "text-lg text-center")
        self.error_text.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.error_text)
        
        # 重试按钮
        retry_button = QPushButton("重试")
        retry_button.setProperty("class", "btn-primary")
        retry_button.clicked.connect(self.reload_page)
        
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(retry_button)
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        self.error_widget.hide()
    
    def setup_base_connections(self):
        """设置基础信号连接"""
        try:
            # 页面状态信号
            self.page_loaded.connect(self.on_page_loaded)
            self.page_error.connect(self.on_page_error)
            
            # 子类实现具体连接
            self.setup_connections()
            
        except Exception as e:
            self.logger.error(f"设置基础信号连接失败: {e}")
    
    @abstractmethod
    def setup_ui(self):
        """子类必须实现的UI设置方法"""
        pass
    
    @abstractmethod
    def setup_connections(self):
        """子类必须实现的信号连接方法"""
        pass
    
    @abstractmethod
    def load_data(self):
        """子类必须实现的数据加载方法"""
        pass
    
    def show_page(self):
        """页面显示时调用"""
        try:
            self.is_visible = True
            
            # 如果页面未加载，则加载数据
            if not self.is_loaded:
                self.load_page()
            else:
                # 已加载则刷新数据
                self.refresh_data()
            
            # 启动自动刷新（如果需要）
            self.start_auto_refresh()
            
            self.logger.info(f"页面 {self.page_id} 已显示")
            
        except Exception as e:
            self.logger.error(f"显示页面失败: {e}")
            self.show_error(f"显示页面失败: {e}")
    
    def hide_page(self):
        """页面隐藏时调用"""
        try:
            self.is_visible = False
            
            # 停止自动刷新
            self.stop_auto_refresh()
            
            self.logger.info(f"页面 {self.page_id} 已隐藏")
            
        except Exception as e:
            self.logger.error(f"隐藏页面失败: {e}")
    
    def load_page(self):
        """加载页面"""
        try:
            # 显示加载状态
            self.show_loading()
            
            # 检查权限
            if not self.check_permissions():
                self.show_error("权限不足，无法访问此页面")
                return
            
            # 加载数据
            self.load_data()
            
        except Exception as e:
            self.logger.error(f"加载页面失败: {e}")
            self.show_error(f"加载页面失败: {e}")
    
    def reload_page(self):
        """重新加载页面"""
        self.is_loaded = False
        self.load_page()
    
    def refresh_data(self):
        """刷新数据（子类可重写）"""
        try:
            if self.is_visible and self.is_loaded:
                self.load_data()
        except Exception as e:
            self.logger.error(f"刷新数据失败: {e}")
    
    def show_loading(self):
        """显示加载状态"""
        try:
            # 隐藏其他组件
            if self.content_widget:
                self.content_widget.hide()
            if self.error_widget:
                self.error_widget.hide()
            
            # 显示加载组件
            if self.loading_widget not in [self.main_layout.itemAt(i).widget() 
                                          for i in range(self.main_layout.count())]:
                self.main_layout.addWidget(self.loading_widget)
            
            self.loading_widget.show()
            
        except Exception as e:
            self.logger.error(f"显示加载状态失败: {e}")
    
    def show_content(self):
        """显示内容"""
        try:
            # 隐藏其他组件
            if self.loading_widget:
                self.loading_widget.hide()
            if self.error_widget:
                self.error_widget.hide()
            
            # 显示内容组件
            if self.content_widget:
                self.content_widget.show()
            
        except Exception as e:
            self.logger.error(f"显示内容失败: {e}")
    
    def show_error(self, error_message: str):
        """显示错误状态"""
        try:
            # 隐藏其他组件
            if self.content_widget:
                self.content_widget.hide()
            if self.loading_widget:
                self.loading_widget.hide()
            
            # 更新错误信息
            self.error_text.setText(error_message)
            
            # 显示错误组件
            if self.error_widget not in [self.main_layout.itemAt(i).widget() 
                                        for i in range(self.main_layout.count())]:
                self.main_layout.addWidget(self.error_widget)
            
            self.error_widget.show()
            
            # 发送错误信号
            self.page_error.emit(error_message)
            
        except Exception as e:
            self.logger.error(f"显示错误状态失败: {e}")
    
    def on_page_loaded(self):
        """页面加载完成处理"""
        self.is_loaded = True
        self.show_content()
        self.logger.info(f"页面 {self.page_id} 加载完成")
    
    def on_page_error(self, error_message: str):
        """页面错误处理"""
        self.logger.error(f"页面 {self.page_id} 错误: {error_message}")
    
    def check_permissions(self) -> bool:
        """检查页面权限（子类可重写）"""
        return True
    
    def start_auto_refresh(self, interval: int = 30000):
        """启动自动刷新"""
        if interval > 0:
            self.refresh_timer.start(interval)
    
    def stop_auto_refresh(self):
        """停止自动刷新"""
        self.refresh_timer.stop()
    
    def apply_theme(self, theme_name: str):
        """应用主题样式（子类可重写）"""
        try:
            # 基础主题应用逻辑
            self.logger.debug(f"页面 {self.page_id} 应用主题: {theme_name}")
        except Exception as e:
            self.logger.error(f"应用主题失败: {e}")
    
    # 业务逻辑设置方法（直接迁移模式）
    def set_database_manager(self, db_manager: DatabaseManager):
        """设置数据库管理器（直接使用，无适配器）"""
        self.db_manager = db_manager
        self.logger.debug(f"页面 {self.page_id} 数据库管理器设置完成")

        # 页面加载完成后立即刷新数据
        if self.is_loaded:
            self.refresh_data()

    def set_auth_manager(self, auth_manager: AuthManager):
        """设置权限管理器（直接使用，无适配器）"""
        self.auth_manager = auth_manager
        self.logger.debug(f"页面 {self.page_id} 权限管理器设置完成")
    
    def log_operation(self, operation: str, details: str = ""):
        """记录操作日志"""
        try:
            if self.logger_system and self.auth_manager:
                current_user = getattr(self.auth_manager, 'current_user', 'unknown')
                self.logger_system.log_operation(current_user, f"{operation} - {details}")
        except Exception as e:
            self.logger.error(f"记录操作日志失败: {e}")
    
    def get_current_user(self) -> str:
        """获取当前用户"""
        if self.auth_manager:
            return getattr(self.auth_manager, 'current_user', 'unknown')
        return 'unknown'
    
    def has_permission(self, permission) -> bool:
        """检查权限"""
        if self.auth_manager:
            return self.auth_manager.has_permission(permission)
        return False

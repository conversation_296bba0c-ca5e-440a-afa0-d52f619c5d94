#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型调整界面组件
Model Adjustment Widget

提供手动调节阈值和难度等级的界面

作者: AI Assistant
版本: 1.0.0
"""

import sys
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QSlider, QSpinBox, QDoubleSpinBox, QPushButton,
                            QGroupBox, QGridLayout, QProgressBar, QTextEdit,
                            QCheckBox)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QFont
import logging

class ModelAdjustmentWidget(QWidget):
    """EEGNet深度学习模型调整界面组件"""

    # 信号定义
    threshold_changed = Signal(float)  # 激活阈值改变
    confidence_threshold_changed = Signal(float)  # 置信度阈值改变
    difficulty_changed = Signal(int)  # 敏感度等级改变（保持兼容性）
    temperature_changed = Signal(float)  # 温度缩放参数改变
    smoothing_window_changed = Signal(int)  # 平滑窗口改变
    class_weight_changed = Signal(float)  # 类别权重改变
    auto_calibrate_requested = Signal()  # 请求自动校准

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        self.model = None
        self.adjustment_manager = None

        # 初始化界面
        self.init_ui()

        # 定时器用于更新统计信息
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_statistics)
        self.update_timer.start(2000)  # 每2秒更新一次

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout()

        # 标题
        title_label = QLabel("EEGNet深度学习调整面板")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #2E8B57; font-weight: bold;")
        layout.addWidget(title_label)

        # 神经网络配置组
        self.create_neural_network_group(layout)

        # 激活阈值调整组
        self.create_activation_threshold_group(layout)

        # 敏感度调整组
        self.create_sensitivity_group(layout)

        # 深度学习参数组
        self.create_deep_learning_params_group(layout)

        # 自动校准组
        self.create_calibration_group(layout)

        # 统计信息组
        self.create_statistics_group(layout)

        self.setLayout(layout)
        self.setWindowTitle("EEGNet深度学习调整面板")
        self.resize(450, 700)

    def create_neural_network_group(self, parent_layout):
        """创建神经网络配置组"""
        group = QGroupBox("EEGNet深度学习配置")
        layout = QVBoxLayout()

        # 网络架构信息
        arch_layout = QHBoxLayout()
        arch_layout.addWidget(QLabel("神经网络架构:"))

        self.architecture_label = QLabel("EEGNet (专用脑电深度学习)")
        self.architecture_label.setStyleSheet("color: #2E8B57; font-weight: bold;")
        arch_layout.addWidget(self.architecture_label)
        arch_layout.addStretch()

        layout.addLayout(arch_layout)

        # 架构说明
        self.architecture_info = QLabel()
        self.architecture_info.setWordWrap(True)
        self.architecture_info.setStyleSheet("color: gray; font-size: 10px;")
        self.architecture_info.setText("EEGNet是专为脑电信号设计的卷积神经网络，采用深度可分离卷积和时空特征提取，适合运动想象分类任务")
        layout.addWidget(self.architecture_info)

        group.setLayout(layout)
        parent_layout.addWidget(group)

    def create_activation_threshold_group(self, parent_layout):
        """创建激活阈值调整组"""
        group = QGroupBox("神经网络激活阈值")
        layout = QGridLayout()

        # 激活阈值（原决策阈值）
        layout.addWidget(QLabel("激活阈值:"), 0, 0)
        self.activation_threshold_spin = QDoubleSpinBox()
        self.activation_threshold_spin.setRange(0.1, 0.9)
        self.activation_threshold_spin.setSingleStep(0.01)
        self.activation_threshold_spin.setDecimals(3)
        self.activation_threshold_spin.setValue(0.5)
        self.activation_threshold_spin.setToolTip("神经网络输出层激活阈值，控制分类边界")
        self.activation_threshold_spin.valueChanged.connect(self.on_activation_threshold_changed)
        layout.addWidget(self.activation_threshold_spin, 0, 1)

        self.activation_slider = QSlider(Qt.Horizontal)
        self.activation_slider.setRange(10, 90)
        self.activation_slider.setValue(50)
        self.activation_slider.valueChanged.connect(self.on_activation_slider_changed)
        layout.addWidget(self.activation_slider, 0, 2)

        # 置信度阈值
        layout.addWidget(QLabel("置信度阈值:"), 1, 0)
        self.confidence_threshold_spin = QDoubleSpinBox()
        self.confidence_threshold_spin.setRange(0.3, 0.9)
        self.confidence_threshold_spin.setSingleStep(0.01)
        self.confidence_threshold_spin.setDecimals(3)
        self.confidence_threshold_spin.setValue(0.6)
        self.confidence_threshold_spin.setToolTip("预测置信度阈值，低于此值的预测将被过滤")
        self.confidence_threshold_spin.valueChanged.connect(self.on_confidence_threshold_changed)
        layout.addWidget(self.confidence_threshold_spin, 1, 1)

        self.confidence_slider = QSlider(Qt.Horizontal)
        self.confidence_slider.setRange(30, 90)
        self.confidence_slider.setValue(60)
        self.confidence_slider.valueChanged.connect(self.on_confidence_slider_changed)
        layout.addWidget(self.confidence_slider, 1, 2)

        group.setLayout(layout)
        parent_layout.addWidget(group)

    def create_sensitivity_group(self, parent_layout):
        """创建敏感度调整组"""
        group = QGroupBox("神经网络敏感度")
        layout = QVBoxLayout()

        # 敏感度滑块
        sensitivity_layout = QHBoxLayout()
        sensitivity_layout.addWidget(QLabel("敏感度等级:"))

        self.sensitivity_slider = QSlider(Qt.Horizontal)
        self.sensitivity_slider.setRange(1, 5)
        self.sensitivity_slider.setValue(3)
        self.sensitivity_slider.setTickPosition(QSlider.TicksBelow)
        self.sensitivity_slider.setTickInterval(1)
        self.sensitivity_slider.setToolTip("调整神经网络对运动想象信号的敏感度")
        self.sensitivity_slider.valueChanged.connect(self.on_sensitivity_changed)
        sensitivity_layout.addWidget(self.sensitivity_slider)

        self.sensitivity_label = QLabel("3")
        self.sensitivity_label.setMinimumWidth(20)
        sensitivity_layout.addWidget(self.sensitivity_label)

        layout.addLayout(sensitivity_layout)

        # 敏感度说明
        self.sensitivity_info = QLabel("中等敏感度 - 标准神经网络响应")
        self.sensitivity_info.setStyleSheet("color: gray; font-size: 10px;")
        layout.addWidget(self.sensitivity_info)

        group.setLayout(layout)
        parent_layout.addWidget(group)

    def create_deep_learning_params_group(self, parent_layout):
        """创建深度学习参数组"""
        group = QGroupBox("深度学习专用参数")
        layout = QGridLayout()

        # 温度缩放参数
        layout.addWidget(QLabel("温度缩放:"), 0, 0)
        self.temperature_spin = QDoubleSpinBox()
        self.temperature_spin.setRange(0.1, 5.0)
        self.temperature_spin.setSingleStep(0.1)
        self.temperature_spin.setDecimals(1)
        self.temperature_spin.setValue(1.0)
        self.temperature_spin.setToolTip("温度缩放参数，调整预测概率的平滑度")
        self.temperature_spin.valueChanged.connect(self.on_temperature_changed)
        layout.addWidget(self.temperature_spin, 0, 1)

        # 预测平滑窗口
        layout.addWidget(QLabel("平滑窗口:"), 1, 0)
        self.smoothing_window_spin = QSpinBox()
        self.smoothing_window_spin.setRange(1, 10)
        self.smoothing_window_spin.setValue(3)
        self.smoothing_window_spin.setToolTip("预测结果平滑窗口大小")
        self.smoothing_window_spin.valueChanged.connect(self.on_smoothing_window_changed)
        layout.addWidget(self.smoothing_window_spin, 1, 1)

        # 类别权重比例
        layout.addWidget(QLabel("类别权重:"), 2, 0)
        self.class_weight_spin = QDoubleSpinBox()
        self.class_weight_spin.setRange(0.1, 5.0)
        self.class_weight_spin.setSingleStep(0.1)
        self.class_weight_spin.setDecimals(1)
        self.class_weight_spin.setValue(1.0)
        self.class_weight_spin.setToolTip("运动想象类别相对于平静状态的权重比例")
        self.class_weight_spin.valueChanged.connect(self.on_class_weight_changed)
        layout.addWidget(self.class_weight_spin, 2, 1)

        group.setLayout(layout)
        parent_layout.addWidget(group)

    def create_calibration_group(self, parent_layout):
        """创建自动校准组"""
        group = QGroupBox("自动校准")
        layout = QVBoxLayout()

        # 校准按钮和设置
        calibration_layout = QHBoxLayout()

        self.auto_calibrate_btn = QPushButton("自动校准")
        self.auto_calibrate_btn.clicked.connect(self.on_auto_calibrate)
        calibration_layout.addWidget(self.auto_calibrate_btn)

        self.target_ratio_spin = QDoubleSpinBox()
        self.target_ratio_spin.setRange(0.1, 0.8)
        self.target_ratio_spin.setSingleStep(0.05)
        self.target_ratio_spin.setDecimals(2)
        self.target_ratio_spin.setValue(0.30)
        self.target_ratio_spin.setPrefix("目标活跃率: ")
        calibration_layout.addWidget(self.target_ratio_spin)

        layout.addLayout(calibration_layout)

        # 自动校准选项
        self.auto_calibrate_checkbox = QCheckBox("启用自动校准")
        self.auto_calibrate_checkbox.setToolTip("当预测分布异常时自动校准")
        layout.addWidget(self.auto_calibrate_checkbox)

        group.setLayout(layout)
        parent_layout.addWidget(group)

    def create_statistics_group(self, parent_layout):
        """创建统计信息组"""
        group = QGroupBox("预测统计")
        layout = QVBoxLayout()

        # 统计标签
        self.stats_layout = QGridLayout()

        self.stats_layout.addWidget(QLabel("总预测次数:"), 0, 0)
        self.total_predictions_label = QLabel("0")
        self.stats_layout.addWidget(self.total_predictions_label, 0, 1)

        self.stats_layout.addWidget(QLabel("活跃预测:"), 1, 0)
        self.active_predictions_label = QLabel("0")
        self.stats_layout.addWidget(self.active_predictions_label, 1, 1)

        self.stats_layout.addWidget(QLabel("活跃比例:"), 2, 0)
        self.active_ratio_label = QLabel("0.00")
        self.stats_layout.addWidget(self.active_ratio_label, 2, 1)

        self.stats_layout.addWidget(QLabel("平均置信度:"), 3, 0)
        self.avg_confidence_label = QLabel("0.00")
        self.stats_layout.addWidget(self.avg_confidence_label, 3, 1)

        layout.addLayout(self.stats_layout)

        # 活跃比例进度条
        self.active_ratio_bar = QProgressBar()
        self.active_ratio_bar.setRange(0, 100)
        self.active_ratio_bar.setValue(0)
        self.active_ratio_bar.setFormat("活跃比例: %p%")
        layout.addWidget(self.active_ratio_bar)

        # 建议信息
        self.recommendations_text = QTextEdit()
        self.recommendations_text.setMaximumHeight(80)
        self.recommendations_text.setPlaceholderText("系统建议将在这里显示...")
        layout.addWidget(self.recommendations_text)

        group.setLayout(layout)
        parent_layout.addWidget(group)

    def set_model_and_manager(self, model, adjustment_manager):
        """设置模型和调整管理器"""
        self.model = model
        self.adjustment_manager = adjustment_manager

        # 从模型加载当前设置
        if model and hasattr(model, 'model_info'):
            model_info = model.model_info
            self.activation_threshold_spin.setValue(model_info.decision_threshold)
            self.confidence_threshold_spin.setValue(model_info.confidence_threshold)
            self.sensitivity_slider.setValue(model_info.difficulty_level)

            # 加载深度学习参数
            if hasattr(model_info, 'temperature'):
                self.temperature_spin.setValue(model_info.temperature)
            if hasattr(model_info, 'smoothing_window'):
                self.smoothing_window_spin.setValue(model_info.smoothing_window)
            if hasattr(model_info, 'class_weight_ratio'):
                self.class_weight_spin.setValue(model_info.class_weight_ratio)

            # 更新滑块
            self.activation_slider.setValue(int(model_info.decision_threshold * 100))
            self.confidence_slider.setValue(int(model_info.confidence_threshold * 100))

    def on_activation_threshold_changed(self, value):
        """激活阈值改变事件"""
        self.activation_slider.setValue(int(value * 100))
        self.threshold_changed.emit(value)
        if self.model:
            self.model.model_info.decision_threshold = value

    def on_activation_slider_changed(self, value):
        """激活阈值滑块改变事件"""
        threshold = value / 100.0
        self.activation_threshold_spin.setValue(threshold)

    def on_confidence_threshold_changed(self, value):
        """置信度阈值改变事件"""
        self.confidence_slider.setValue(int(value * 100))
        self.confidence_threshold_changed.emit(value)
        if self.model:
            self.model.model_info.confidence_threshold = value

    def on_confidence_slider_changed(self, value):
        """置信度阈值滑块改变事件"""
        threshold = value / 100.0
        self.confidence_threshold_spin.setValue(threshold)

    def on_sensitivity_changed(self, level):
        """敏感度等级改变事件"""
        self.sensitivity_label.setText(str(level))
        self.update_sensitivity_info(level)
        self.difficulty_changed.emit(level)  # 保持信号兼容性
        if self.adjustment_manager:
            self.adjustment_manager.adjust_difficulty(level)

    def on_temperature_changed(self, value):
        """温度缩放参数改变事件"""
        if self.model and hasattr(self.model, 'model_info'):
            self.model.model_info.temperature = value

    def on_smoothing_window_changed(self, value):
        """平滑窗口改变事件"""
        if self.model and hasattr(self.model, 'model_info'):
            self.model.model_info.smoothing_window = value

    def on_class_weight_changed(self, value):
        """类别权重改变事件"""
        if self.model and hasattr(self.model, 'model_info'):
            self.model.model_info.class_weight_ratio = value

    def on_auto_calibrate(self):
        """自动校准按钮点击事件"""
        if self.adjustment_manager:
            target_ratio = self.target_ratio_spin.value()
            success = self.adjustment_manager.auto_calibrate(target_ratio)
            if success:
                # 更新界面显示
                if self.model:
                    model_info = self.model.model_info
                    self.activation_threshold_spin.setValue(model_info.decision_threshold)
                    self.confidence_threshold_spin.setValue(model_info.confidence_threshold)

        self.auto_calibrate_requested.emit()

    def update_sensitivity_info(self, level):
        """更新敏感度信息"""
        info_text = {
            1: "最低敏感度 - 神经网络响应阈值较高，需要强烈的运动想象信号",
            2: "低敏感度 - 神经网络响应阈值偏高，适合信号质量好的情况",
            3: "中等敏感度 - 标准神经网络响应，平衡准确性和敏感性",
            4: "高敏感度 - 神经网络响应阈值偏低，容易检测到运动想象",
            5: "最高敏感度 - 神经网络响应阈值最低，对微弱信号也敏感"
        }
        self.sensitivity_info.setText(info_text.get(level, ""))

    def update_statistics(self):
        """更新统计信息"""
        if not self.adjustment_manager:
            return

        try:
            stats = self.adjustment_manager.get_prediction_stats()
            if stats:
                # 更新标签
                self.total_predictions_label.setText(str(stats.get('total_predictions', 0)))
                self.active_predictions_label.setText(str(stats.get('active_predictions', 0)))

                active_ratio = stats.get('active_ratio', 0)
                self.active_ratio_label.setText(f"{active_ratio:.2f}")
                self.active_ratio_bar.setValue(int(active_ratio * 100))

                avg_confidence = stats.get('average_confidence', 0)
                self.avg_confidence_label.setText(f"{avg_confidence:.2f}")

                # 更新进度条颜色
                if active_ratio < 0.1:
                    self.active_ratio_bar.setStyleSheet("QProgressBar::chunk { background-color: red; }")
                elif active_ratio > 0.6:
                    self.active_ratio_bar.setStyleSheet("QProgressBar::chunk { background-color: orange; }")
                else:
                    self.active_ratio_bar.setStyleSheet("QProgressBar::chunk { background-color: green; }")

            # 更新建议
            status = self.adjustment_manager.get_model_status()
            recommendations = status.get('recommendations', [])
            if recommendations:
                self.recommendations_text.setText('\n'.join(recommendations))
            else:
                self.recommendations_text.setText("当前模型运行正常")

            # 自动校准检查
            if self.auto_calibrate_checkbox.isChecked() and stats:
                active_ratio = stats.get('active_ratio', 0)
                if active_ratio < 0.1 or active_ratio > 0.6:
                    if stats.get('total_predictions', 0) >= 20:  # 至少20次预测
                        self.on_auto_calibrate()

        except Exception as e:
            self.logger.warning(f"更新统计信息失败: {e}")


# 测试代码
if __name__ == "__main__":
    from PySide6.QtWidgets import QApplication

    app = QApplication(sys.argv)

    widget = ModelAdjustmentWidget()
    widget.show()

    sys.exit(app.exec())

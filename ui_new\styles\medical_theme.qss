/* 医疗主题样式文件 - Medical Theme Styles */
/* 专业医疗设备UI风格 */

/* 主窗口样式 */
QMainWindow {
    background-color: #f8fafc;
    color: #1e293b;
}

QMainWindow#ModernMainWindow {
    background-color: #f8fafc;
    border: none;
}

/* 侧边栏样式 */
QWidget#sidebar {
    background-color: #ffffff;
    border-right: 1px solid #e2e8f0;
    min-width: 280px;
    max-width: 280px;
}

QWidget#sidebar[collapsed="true"] {
    min-width: 80px;
    max-width: 80px;
}

/* 侧边栏头部 */
QWidget#sidebar_header {
    background-color: #ffffff;
    border-bottom: 1px solid #e2e8f0;
    padding: 24px 20px;
}

/* Logo样式 */
QLabel#logo {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                stop:0 #2563eb,
                                stop:1 #0ea5e9);
    border-radius: 12px;
    color: white;
    font-weight: 700;
    font-size: 20px;
    min-width: 48px;
    max-width: 48px;
    min-height: 48px;
    max-height: 48px;
}

QLabel#logo_text {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    margin-left: 12px;
}

/* 导航菜单样式 */
QWidget#nav_menu {
    background-color: transparent;
    padding: 20px 0px;
}

/* 导航分组标题 */
QLabel.nav-title {
    color: #64748b;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 0px 20px 12px 20px;
    margin-bottom: 8px;
}

/* 导航项样式 */
QPushButton.nav-item {
    background-color: transparent;
    border: none;
    padding: 12px 20px;
    margin: 0px 12px 4px 12px;
    border-radius: 8px;
    text-align: left;
    font-size: 14px;
    color: #1e293b;
    min-height: 44px;
}

QPushButton.nav-item:hover {
    background-color: #f1f5f9;
}

QPushButton.nav-item:checked,
QPushButton.nav-item[active="true"] {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                stop:0 #2563eb,
                                stop:1 #0ea5e9);
    color: white;
    font-weight: 500;
}

QPushButton.nav-item:checked:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                stop:0 #1e40af,
                                stop:1 #0284c7);
}

/* 用户信息区域 */
QWidget#user_profile {
    background-color: #f1f5f9;
    border-radius: 8px;
    padding: 12px;
    margin: 20px;
}

QLabel#user_avatar {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                stop:0 #2563eb,
                                stop:1 #0ea5e9);
    border-radius: 18px;
    color: white;
    font-weight: 600;
    min-width: 36px;
    max-width: 36px;
    min-height: 36px;
    max-height: 36px;
}

QLabel#user_name {
    font-weight: 600;
    font-size: 14px;
    color: #1e293b;
}

QLabel#user_role {
    font-size: 12px;
    color: #64748b;
}

/* 顶部栏样式 */
QWidget#top_bar {
    background-color: #ffffff;
    border-bottom: 1px solid #e2e8f0;
    min-height: 72px;
    max-height: 72px;
    padding: 0px 24px;
}

/* 菜单切换按钮 */
QPushButton#menu_toggle {
    background-color: #f1f5f9;
    border: none;
    border-radius: 8px;
    min-width: 40px;
    max-width: 40px;
    min-height: 40px;
    max-height: 40px;
}

QPushButton#menu_toggle:hover {
    background-color: #e2e8f0;
}

/* 页面标题 */
QLabel#page_title {
    font-size: 24px;
    font-weight: 600;
    color: #1e293b;
    margin-left: 16px;
}

/* 主题切换开关 */
QWidget#theme_switch {
    background-color: #f1f5f9;
    border-radius: 8px;
    padding: 8px 12px;
}

QLabel.theme-label {
    font-size: 12px;
    font-weight: 500;
    color: #64748b;
}

/* 状态指示器 */
QWidget.status-indicator {
    background-color: rgba(16, 185, 129, 0.1);
    border-radius: 6px;
    padding: 6px 12px;
}

QWidget.status-indicator.normal {
    background-color: rgba(16, 185, 129, 0.1);
}

QWidget.status-indicator.warning {
    background-color: rgba(245, 158, 11, 0.1);
}

QWidget.status-indicator.error {
    background-color: rgba(239, 68, 68, 0.1);
}

QLabel.status-text {
    font-size: 12px;
    font-weight: 500;
}

QLabel.status-text.normal {
    color: #10b981;
}

QLabel.status-text.warning {
    color: #f59e0b;
}

QLabel.status-text.error {
    color: #ef4444;
}

/* 内容区域 */
QWidget#content_area {
    background-color: #f8fafc;
    padding: 24px;
}

/* 卡片样式 */
QWidget.card {
    background-color: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 20px;
}

QWidget.card-large {
    background-color: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 16px;
    padding: 24px;
}

/* 卡片标题 */
QLabel.card-title {
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 12px;
}

/* 按钮样式 */
QPushButton.btn-primary {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                stop:0 #2563eb,
                                stop:1 #0ea5e9);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 10px 16px;
    font-weight: 500;
    font-size: 14px;
}

QPushButton.btn-primary:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                stop:0 #1e40af,
                                stop:1 #0284c7);
}

QPushButton.btn-primary:pressed {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                stop:0 #1d4ed8,
                                stop:1 #0369a1);
}

QPushButton.btn-secondary {
    background-color: #f1f5f9;
    color: #1e293b;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 10px 16px;
    font-weight: 500;
    font-size: 14px;
}

QPushButton.btn-secondary:hover {
    background-color: #e2e8f0;
}

/* 输入框样式 */
QLineEdit {
    background-color: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 10px 12px;
    font-size: 14px;
    color: #1e293b;
}

QLineEdit:focus {
    border-color: #2563eb;
    outline: none;
}

QLineEdit:disabled {
    background-color: #f1f5f9;
    color: #94a3b8;
}

/* 表格样式 */
QTableWidget {
    background-color: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    gridline-color: #f1f5f9;
    selection-background-color: #eff6ff;
}

QTableWidget::item {
    padding: 12px 8px;
    border-bottom: 1px solid #f1f5f9;
}

QTableWidget::item:selected {
    background-color: #eff6ff;
    color: #1e293b;
}

QHeaderView::section {
    background-color: #f8fafc;
    border: none;
    border-bottom: 1px solid #e2e8f0;
    padding: 12px 8px;
    font-weight: 600;
    color: #374151;
}

/* 医疗主题样式文件 - Medical Theme Styles */
/* 基于complete_bci_system.html的精美设计 */

/* 医疗主题样式文件 - Medical Theme Styles */
/* 基于complete_bci_system.html的精美设计 */
/* QSS不支持CSS变量，直接使用颜色值 */

/* 主窗口样式 */
QMainWindow {
    background-color: #f8fafc;
    color: #1e293b;
    font-family: "Segoe UI", -apple-system, BlinkMacSystemFont, sans-serif;
}

QMainWindow#ModernMainWindow {
    background-color: #f8fafc;
    border: none;
}

/* 侧边栏样式 - 完全按照HTML设计 */
QWidget#sidebar {
    background-color: #ffffff;
    border-right: 1px solid #e2e8f0;
    min-width: 280px;
    max-width: 280px;
}

QWidget#sidebar[collapsed="true"] {
    min-width: 80px;
    max-width: 80px;
}

/* 侧边栏头部 */
QWidget#sidebar_header {
    background-color: #ffffff;
    border-bottom: 1px solid #e2e8f0;
    padding: 24px 20px;
}

/* Logo样式 - 渐变色设计 */
QLabel#logo {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                stop:0 #2563eb,
                                stop:1 #0ea5e9);
    border-radius: 12px;
    color: white;
    font-weight: 700;
    font-size: 20px;
    min-width: 48px;
    max-width: 48px;
    min-height: 48px;
    max-height: 48px;
}

QLabel#logo_text {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    margin-left: 12px;
}

/* 导航菜单样式 */
QWidget#nav_menu {
    background-color: transparent;
    padding: 20px 0px;
}

/* 导航分组标题 */
QLabel.nav-title {
    color: #64748b;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 0px 20px 12px 20px;
    margin-bottom: 8px;
}

/* 导航项样式 - 增强视觉效果 */
QPushButton.nav-item {
    background-color: transparent;
    border: none;
    padding: 12px 20px;
    margin: 0px 12px 4px 12px;
    border-radius: 8px;
    text-align: left;
    font-size: 14px;
    color: #1e293b;
    min-height: 44px;
}

QPushButton.nav-item:hover {
    background-color: #f1f5f9;
}

QPushButton.nav-item:checked,
QPushButton.nav-item[active="true"] {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                stop:0 #2563eb,
                                stop:1 #0ea5e9);
    color: white;
    font-weight: 500;
    border-left: 4px solid #0ea5e9;
}

QPushButton.nav-item:checked:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                stop:0 #1e40af,
                                stop:1 #0284c7);
}

/* 用户信息区域 - 卡片式设计 */
QWidget#user_profile {
    background-color: #f1f5f9;
    border-radius: 8px;
    padding: 12px;
    margin: 20px;
}

QLabel#user_avatar {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                stop:0 #2563eb,
                                stop:1 #0ea5e9);
    border-radius: 18px;
    color: white;
    font-weight: 600;
    min-width: 36px;
    max-width: 36px;
    min-height: 36px;
    max-height: 36px;
}

QLabel#user_name {
    font-weight: 600;
    font-size: 14px;
    color: #1e293b;
}

QLabel#user_role {
    font-size: 12px;
    color: #64748b;
}

/* 顶部栏样式 - 精美设计 */
QWidget#top_bar {
    background-color: #ffffff;
    border-bottom: 1px solid #e2e8f0;
    min-height: 72px;
    max-height: 72px;
    padding: 0px 24px;
}

/* 菜单切换按钮 - 现代化设计 */
QPushButton#menu_toggle {
    background-color: #f1f5f9;
    border: none;
    border-radius: 8px;
    min-width: 40px;
    max-width: 40px;
    min-height: 40px;
    max-height: 40px;
}

QPushButton#menu_toggle:hover {
    background-color: #e2e8f0;
}

/* 页面标题 */
QLabel#page_title {
    font-size: 24px;
    font-weight: 600;
    color: #1e293b;
    margin-left: 16px;
}

/* 主题切换开关 - 精美开关设计 */
QWidget#theme_switch {
    background-color: #f1f5f9;
    border-radius: 8px;
    padding: 8px 12px;
}

QLabel.theme-label {
    font-size: 12px;
    font-weight: 500;
    color: #64748b;
}

/* 状态指示器 - 现代化状态显示 */
QWidget.status-indicator {
    background-color: rgba(16, 185, 129, 0.1);
    border-radius: 6px;
    padding: 6px 12px;
    border: 1px solid rgba(16, 185, 129, 0.2);
}

QWidget.status-indicator.normal {
    background-color: rgba(16, 185, 129, 0.1);
    border-color: rgba(16, 185, 129, 0.2);
}

QWidget.status-indicator.warning {
    background-color: rgba(245, 158, 11, 0.1);
    border-color: rgba(245, 158, 11, 0.2);
}

QWidget.status-indicator.error {
    background-color: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.2);
}

QLabel.status-text {
    font-size: 12px;
    font-weight: 500;
}

QLabel.status-text.normal {
    color: #10b981;
}

QLabel.status-text.warning {
    color: #f59e0b;
}

QLabel.status-text.error {
    color: #ef4444;
}

/* 内容区域 */
QWidget#content_area {
    background-color: #f8fafc;
    padding: 24px;
}

/* 卡片样式 - 现代化卡片设计 */
QWidget.card {
    background-color: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 20px;
}

QWidget.card-large {
    background-color: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 16px;
    padding: 24px;
}

/* 卡片标题 */
QLabel.card-title {
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 12px;
}

/* 按钮样式 - 渐变色按钮 */
QPushButton.btn-primary {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                stop:0 #2563eb,
                                stop:1 #0ea5e9);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 10px 16px;
    font-weight: 500;
    font-size: 14px;
}

QPushButton.btn-primary:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                stop:0 #1e40af,
                                stop:1 #0284c7);
}

QPushButton.btn-primary:pressed {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                stop:0 #1d4ed8,
                                stop:1 #0369a1);
}

QPushButton.btn-secondary {
    background-color: #f1f5f9;
    color: #1e293b;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 10px 16px;
    font-weight: 500;
    font-size: 14px;
}

QPushButton.btn-secondary:hover {
    background-color: #e2e8f0;
}

/* 输入框样式 - 现代化输入框 */
QLineEdit {
    background-color: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 14px;
    color: #1e293b;
}

QLineEdit:focus {
    border-color: #2563eb;
    outline: none;
}

QLineEdit:disabled {
    background-color: #f1f5f9;
    color: #94a3b8;
}

/* 表格样式 - 现代化表格 */
QTableWidget {
    background-color: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    gridline-color: #f1f5f9;
    selection-background-color: #eff6ff;
}

QTableWidget::item {
    padding: 16px 12px;
    border-bottom: 1px solid #f1f5f9;
}

QTableWidget::item:selected {
    background-color: #eff6ff;
    color: #1e293b;
}

QTableWidget::item:hover {
    background-color: #f8fafc;
}

QHeaderView::section {
    background-color: #f8fafc;
    border: none;
    border-bottom: 1px solid #e2e8f0;
    padding: 16px 12px;
    font-weight: 600;
    color: #374151;
}

/* 进度条样式 - 现代化进度条 */
QProgressBar {
    background-color: #f1f5f9;
    border: none;
    border-radius: 6px;
    height: 12px;
}

QProgressBar::chunk {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                stop:0 #2563eb,
                                stop:1 #0ea5e9);
    border-radius: 6px;
}

/* 滚动条样式 - 现代化滚动条 */
QScrollBar:vertical {
    background: #f8fafc;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background: #e2e8f0;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background: #cbd5e1;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    border: none;
    background: none;
    height: 0;
}

/* 组合框样式 */
QComboBox {
    background-color: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 14px;
    color: #1e293b;
}

QComboBox:hover {
    border-color: #2563eb;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: none;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #64748b;
}

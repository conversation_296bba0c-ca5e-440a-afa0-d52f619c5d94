# 脑机接口康复系统重构指南 - 框架文档

## 🎯 项目重构目标

将现有的脑机接口康复训练系统从传统Windows桌面应用重构为现代化的单页应用架构，使用PySide6，支持双主题切换（医疗风格/科技风格）。

## 📁 推荐项目结构样式-可以根据现有的项目结构及内容更改

```
brain_computer_interface/
├── main.py                          # 主程序入口
├── config/                          # 配置文件
│   ├── __init__.py
│   ├── settings.py                  # 应用配置
│   └── themes.py                    # 主题配置
├── ui/                              # UI界面层
│   ├── __init__.py
│   ├── main_window.py               # 主窗口控制器
│   ├── components/                  # 可复用组件
│   │   ├── __init__.py
│   │   ├── sidebar.py               # 侧边栏组件
│   │   ├── top_bar.py               # 顶部栏组件
│   │   ├── patient_card.py          # 患者卡片组件
│   │   ├── chart_widget.py          # 图表组件
│   │   └── status_indicator.py      # 状态指示器组件
│   ├── pages/                       # 页面模块
│   │   ├── __init__.py
│   │   ├── dashboard_page.py        # 实时监测页面
│   │   ├── patients_page.py         # 患者管理页面
│   │   ├── reports_page.py          # 数据报告页面
│   │   ├── training_page.py         # 康复训练页面
│   │   ├── evaluation_page.py       # 疗效评估页面
│   │   ├── sessions_page.py         # 治疗记录页面
│   │   ├── analytics_page.py        # 趋势分析页面
│   │   ├── users_page.py            # 用户管理页面
│   │   └── settings_page.py         # 系统设置页面
│   └── styles/                      # 样式文件
│       ├── __init__.py
│       ├── medical_theme.qss        # 医疗主题样式
│       ├── tech_theme.qss           # 科技主题样式
│       └── base_styles.qss          # 基础样式
├── core/                            # 核心业务逻辑
│   ├── __init__.py
│   ├── data_manager.py              # 数据管理
│   ├── patient_manager.py           # 患者管理
│   ├── session_manager.py           # 会话管理
│   ├── device_manager.py            # 设备管理
│   └── report_generator.py          # 报告生成
├── database/                        # 数据库相关
│   ├── __init__.py
│   ├── models.py                    # 数据模型
│   ├── database.py                  # 数据库连接
│   └── migrations/                  # 数据库迁移
├── utils/                           # 工具函数
│   ├── __init__.py
│   ├── helpers.py                   # 辅助函数
│   └── validators.py                # 数据验证
├── resources/                       # 资源文件
│   ├── icons/                       # 图标文件
│   ├── images/                      # 图片资源
│   └── fonts/                       # 字体文件
└── requirements.txt                 # 依赖包列表
```

## 🎨 设计系统规范

### 颜色系统

#### 医疗主题 (Medical Theme)
```python
MEDICAL_THEME = {
    'primary': '#2563eb',           # 主要蓝色
    'secondary': '#1e40af',         # 深蓝色
    'accent': '#0ea5e9',            # 强调蓝色
    'success': '#10b981',           # 成功绿色
    'warning': '#f59e0b',           # 警告橙色
    'danger': '#ef4444',            # 危险红色
    'bg_primary': '#f8fafc',        # 主背景（浅灰）
    'bg_secondary': '#ffffff',      # 次背景（白色）
    'bg_tertiary': '#f1f5f9',       # 三级背景
    'text_primary': '#1e293b',      # 主文字色
    'text_secondary': '#64748b',    # 次文字色
    'border': '#e2e8f0',            # 边框色
}
```

#### 科技主题 (Tech Theme)
```python
TECH_THEME = {
    'primary': '#06b6d4',           # 主要青色
    'secondary': '#0891b2',         # 深青色
    'accent': '#00d4ff',            # 强调荧光青
    'success': '#00ff88',           # 成功荧光绿
    'warning': '#ffaa00',           # 警告金色
    'danger': '#ff3366',            # 危险粉红
    'bg_primary': '#0f172a',        # 主背景（深蓝黑）
    'bg_secondary': '#1e293b',      # 次背景（深灰蓝）
    'bg_tertiary': '#334155',       # 三级背景
    'text_primary': '#f1f5f9',      # 主文字色（浅色）
    'text_secondary': '#94a3b8',    # 次文字色
    'border': '#475569',            # 边框色
}
```

### 字体系统
```python
FONT_SYSTEM = {
    'primary': 'Segoe UI, -apple-system, BlinkMacSystemFont, sans-serif',
    'mono': 'Consolas, Monaco, monospace',
    'sizes': {
        'xs': 12,
        'sm': 14,
        'base': 16,
        'lg': 18,
        'xl': 20,
        'xxl': 24,
        'xxxl': 32,
    }
}
```

### 间距系统
```python
SPACING = {
    'xs': 4,
    'sm': 8,
    'base': 16,
    'lg': 24,
    'xl': 32,
    'xxl': 48,
}
```

### 圆角系统
```python
BORDER_RADIUS = {
    'small': 6,
    'base': 8,
    'large': 12,
    'xl': 16,
    'full': 9999,  # 完全圆角
}
```

## 🏗️ 核心架构设计

### 1. 主窗口架构 (main_window.py)
```python
from PySide6.QtWidgets import QMainWindow, QHBoxLayout, QWidget, QStackedWidget
from PySide6.QtCore import Signal, QPropertyAnimation, QEasingCurve

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_connections()
        self.setup_animations()
    
    def setup_ui(self):
        """设置主界面布局"""
        # 主容器：水平布局（侧边栏 + 主内容区）
        # 侧边栏：固定宽度280px，可折叠到80px
        # 主内容区：垂直布局（顶部栏 + 页面内容）
        
    def setup_connections(self):
        """设置信号连接"""
        # 菜单点击事件
        # 主题切换事件
        # 侧边栏折叠事件
        
    def setup_animations(self):
        """设置页面切换动画"""
        # 页面淡入淡出动画
        # 侧边栏展开收缩动画
        
    def switch_page(self, page_name):
        """页面切换核心方法"""
        pass
        
    def toggle_theme(self):
        """主题切换核心方法"""
        pass
```

### 2. 侧边栏组件 (sidebar.py)
```python
from PySide6.QtWidgets import QWidget, QVBoxLayout
from PySide6.QtCore import Signal

class Sidebar(QWidget):
    page_changed = Signal(str)  # 页面切换信号
    
    def __init__(self):
        super().__init__()
        self.is_collapsed = False
        self.setup_ui()
        
    def setup_ui(self):
        """创建侧边栏结构"""
        # 头部：Logo + 应用名称
        # 导航菜单：分组显示，支持图标+文字
        # 底部：用户信息
        
    def add_nav_section(self, title, items):
        """添加导航分组"""
        pass
        
    def add_nav_item(self, icon, text, page_name, active=False):
        """添加导航项"""
        pass
        
    def toggle_collapse(self):
        """切换折叠状态"""
        pass
        
    def set_active_item(self, page_name):
        """设置活跃菜单项"""
        pass
```

### 3. 页面基类 (base_page.py)
```python
from PySide6.QtWidgets import QWidget
from abc import ABC, abstractmethod

class BasePage(QWidget, ABC):
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_connections()
        
    @abstractmethod
    def setup_ui(self):
        """子类必须实现的UI设置方法"""
        pass
        
    @abstractmethod  
    def setup_connections(self):
        """子类必须实现的信号连接方法"""
        pass
        
    def show_page(self):
        """页面显示时调用"""
        pass
        
    def hide_page(self):
        """页面隐藏时调用"""
        pass
        
    def apply_theme(self, theme_name):
        """应用主题样式"""
        pass
```

## 🔧 关键组件实现

### 1. 实时监测页面 (dashboard_page.py)
```python
class DashboardPage(BasePage):
    def setup_ui(self):
        # 网格布局：2列（主监测区 + 控制区）
        # 主监测区：脑电可视化 + 信号指标
        # 右侧分为：控制面板 + 患者信息
        
    def setup_brain_visualization(self):
        """设置脑电可视化组件"""
        # 圆形脑图 + 电极点
        # 实时波形显示
        # 信号质量指示
        
    def setup_signal_metrics(self):
        """设置信号指标显示"""
        # Alpha/Beta/Theta/Delta频率
        # 信噪比、识别率等关键指标
        
    def update_real_time_data(self):
        """实时数据更新"""
        # 每秒更新脑电数据
        # 动画效果更新
```

### 2. 患者管理页面 (patients_page.py)
```python
class PatientsPage(BasePage):
    def setup_ui(self):
        # 水平分割：患者列表(60%) + 患者详情(40%)
        # 左侧：搜索框 + 患者表格 + 分页
        # 右侧：患者详细信息 + 康复进度
        
    def setup_patient_list(self):
        """设置患者列表"""
        # 搜索功能
        # 表格显示：头像、姓名、年龄、诊断、状态
        # 行点击选择
        
    def setup_patient_detail(self):
        """设置患者详情"""
        # 大头像 + 基本信息
        # 康复进度条
        # 最近训练记录
        
    def on_patient_selected(self, patient_id):
        """患者选择事件"""
        # 更新右侧详情显示
        # 加载患者数据
```

### 3. 数据报告页面 (reports_page.py)
```python
class ReportsPage(BasePage):
    def setup_ui(self):
        # 水平分割：控制面板(320px) + 报告内容(剩余)
        # 左侧：报告配置表单
        # 右侧：统计卡片 + 图表 + 导出按钮
        
    def setup_report_controls(self):
        """设置报告控制面板"""
        # 报告类型选择
        # 时间范围选择
        # 患者范围选择
        # 数据类型复选框
        
    def setup_report_content(self):
        """设置报告内容区"""
        # 4个统计卡片
        # 主趋势图表
        # 侧边图表和数据表格
        
    def generate_report(self):
        """生成报告"""
        # 收集表单数据
        # 查询数据库
        # 生成图表
        # 更新界面
```

## 📊 图表组件实现

### PyQtGraph集成示例
```python
import pyqtgraph as pg
from PySide6.QtWidgets import QWidget, QVBoxLayout

class EEGChart(QWidget):
    def __init__(self):
        super().__init__()
        self.setup_chart()
        
    def setup_chart(self):
        """设置EEG波形图"""
        layout = QVBoxLayout()
        
        # 创建PyQtGraph绘图控件
        self.plot_widget = pg.PlotWidget()
        self.plot_widget.setBackground('transparent')
        self.plot_widget.showGrid(x=True, y=True, alpha=0.3)
        
        # 设置样式
        self.plot_widget.setLabel('left', '电压 (μV)')
        self.plot_widget.setLabel('bottom', '时间 (s)')
        
        layout.addWidget(self.plot_widget)
        self.setLayout(layout)
        
    def update_data(self, data):
        """更新波形数据"""
        self.plot_widget.clear()
        pen = pg.mkPen(color='#06b6d4', width=2)
        self.plot_widget.plot(data, pen=pen)
```

## 🎨 QSS样式实现

### 医疗主题样式 (medical_theme.qss)
```css
/* 主窗口 */
QMainWindow {
    background-color: #f8fafc;
    color: #1e293b;
}

/* 侧边栏 */
#sidebar {
    background-color: #ffffff;
    border-right: 1px solid #e2e8f0;
    min-width: 280px;
    max-width: 280px;
}

#sidebar[collapsed="true"] {
    min-width: 80px;
    max-width: 80px;
}

/* 导航项 */
.nav-item {
    background-color: transparent;
    border: none;
    padding: 12px 20px;
    margin: 0px 12px;
    border-radius: 8px;
    text-align: left;
    font-size: 14px;
    color: #1e293b;
}

.nav-item:hover {
    background-color: #f1f5f9;
}

.nav-item[active="true"] {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                stop:0 #2563eb,
                                stop:1 #0ea5e9);
    color: white;
}

/* 卡片样式 */
.card {
    background-color: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 20px;
}

/* 按钮样式 */
.btn-primary {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                stop:0 #2563eb,
                                stop:1 #0ea5e9);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 10px 16px;
    font-weight: 500;
}

.btn-primary:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                stop:0 #1e40af,
                                stop:1 #0284c7);
}
```

### 科技主题样式 (tech_theme.qss)
```css
/* 主窗口 */
QMainWindow {
    background-color: #0f172a;
    color: #f1f5f9;
}

/* 侧边栏 */
#sidebar {
    background-color: #1e293b;
    border-right: 1px solid #475569;
}

/* 导航项 */
.nav-item {
    background-color: transparent;
    border: none;
    padding: 12px 20px;
    margin: 0px 12px;
    border-radius: 8px;
    text-align: left;
    font-size: 14px;
    color: #f1f5f9;
}

.nav-item:hover {
    background-color: #334155;
}

.nav-item[active="true"] {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                stop:0 #06b6d4,
                                stop:1 #00d4ff);
    color: white;
}

/* 卡片样式 */
.card {
    background-color: #1e293b;
    border: 1px solid #475569;
    border-radius: 12px;
    padding: 20px;
}

/* 发光效果 */
.glow {
    border: 2px solid #00d4ff;
    box-shadow: 0 0 10px #00d4ff;
}
```

## 🚀 实现步骤指导

### 第一阶段：基础架构
1. 创建项目目录结构
2. 实现主题系统和样式文件
3. 开发主窗口和侧边栏组件
4. 实现页面切换机制

### 第二阶段：核心页面
1. 实现实时监测页面（仪表板）
2. 开发患者管理页面
3. 创建数据报告页面
4. 集成数据库连接

### 第三阶段：功能完善
1. 添加图表组件和数据可视化
2. 实现数据导入导出功能
3. 完善用户权限系统
4. 优化性能和响应速度

### 第四阶段：测试和优化
1. 界面响应式适配
2. 主题切换完善
3. 异常处理和日志系统
4. 用户体验优化

## 📝 使用建议

### 提示词模板
```
请按照以下要求重构我的PySide6脑机接口康复系统：

1. 采用单页应用架构，左侧固定导航栏，右侧动态内容区
2. 支持医疗主题（蓝白色调）和科技主题（深色荧光）的切换
3. 使用提供的颜色系统和组件架构
4. 实现平滑的页面切换动画效果
5. 保持现有的数据库结构和业务逻辑

重点关注：
- 现代化的卡片式布局
- 渐变色按钮和hover效果
- 圆角设计和阴影效果
- 响应式布局适配
- PyQtGraph图表集成

请从[具体文件名]开始重构。
```

### 关键文件优先级
1. `main_window.py` - 主窗口架构
2. `sidebar.py` - 导航组件
3. `themes.py` - 主题系统
4. `dashboard_page.py` - 核心监测页面
5. `patients_page.py` - 患者管理页面

## 🎯 期望效果

重构完成后应实现：
- ✅ 现代化的单页应用体验
- ✅ 流畅的双主题切换
- ✅ 响应式布局适配
- ✅ 统一的组件化架构
- ✅ 优雅的动画和交互效果
- ✅ 保持原有功能完整性

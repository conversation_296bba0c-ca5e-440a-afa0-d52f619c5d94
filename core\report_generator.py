#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
报告生成器模块
Report Generator Module

作者: AI Assistant
版本: 1.0.0
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from core.database_manager import DatabaseManager


@dataclass
class PatientSummary:
    """患者摘要信息"""
    bianhao: int
    name: str
    age: int
    gender: str
    diagnosis: str
    doctor: str
    first_treatment: Optional[str]
    total_treatments: int
    total_duration: float
    avg_score: float
    latest_score: float
    improvement_rate: float


@dataclass
class TreatmentStats:
    """治疗统计信息"""
    total_patients: int
    total_treatments: int
    total_duration: float
    avg_score: float
    excellent_count: int  # 优
    good_count: int      # 良
    fair_count: int      # 中
    poor_count: int      # 差


class ReportGenerator:
    """报告生成器"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)
    
    def generate_personal_report(self, patient_id: int, start_date: str, end_date: str, 
                               report_type: str = "综合报告") -> str:
        """生成个人报告"""
        try:
            # 获取患者基本信息
            patient_info = self._get_patient_info(patient_id)
            if not patient_info:
                return "未找到患者信息"
            
            # 获取治疗记录
            treatment_records = self._get_treatment_records(patient_id, start_date, end_date)
            
            # 获取脑电数据
            eeg_data = self._get_eeg_data(patient_id, start_date, end_date)
            
            # 生成报告内容
            if "综合报告" in report_type:
                return self._generate_comprehensive_report(patient_info, treatment_records, eeg_data)
            elif "训练报告" in report_type:
                return self._generate_training_report(patient_info, treatment_records, eeg_data)
            elif "评定报告" in report_type:
                return self._generate_assessment_report(patient_info, treatment_records, eeg_data)
            elif "进度报告" in report_type:
                return self._generate_progress_report(patient_info, treatment_records, eeg_data)
            else:
                return "不支持的报告类型"
                
        except Exception as e:
            self.logger.error(f"生成个人报告失败: {e}")
            return f"报告生成失败: {str(e)}"
    
    def generate_daily_statistics(self, date: str) -> Dict[str, Any]:
        """生成按日统计"""
        try:
            # 当日治疗统计
            daily_treatments = self.db_manager.execute_query("""
                SELECT COUNT(*) as treatment_count,
                       COUNT(DISTINCT bianh) as patient_count,
                       SUM(zlsj) as total_duration,
                       AVG(defen) as avg_score,
                       SUM(CASE WHEN zlms = '优' THEN 1 ELSE 0 END) as excellent_count,
                       SUM(CASE WHEN zlms = '良' THEN 1 ELSE 0 END) as good_count,
                       SUM(CASE WHEN zlms = '中' THEN 1 ELSE 0 END) as fair_count,
                       SUM(CASE WHEN zlms = '差' THEN 1 ELSE 0 END) as poor_count
                FROM zhiliao 
                WHERE rq = ?
            """, (date,))
            
            # 新患者统计
            new_patients = self.db_manager.execute_query("""
                SELECT COUNT(*) as new_patient_count
                FROM bingren 
                WHERE DATE(lrshijian) = ?
            """, (date,))
            
            # 设备使用统计
            device_stats = self._get_device_statistics(date)
            
            result = {
                'date': date,
                'treatment_stats': daily_treatments[0] if daily_treatments else {},
                'new_patients': new_patients[0]['new_patient_count'] if new_patients else 0,
                'device_stats': device_stats
            }
            
            return result
            
        except Exception as e:
            self.logger.error(f"生成日统计失败: {e}")
            return {}
    
    def _get_patient_info(self, patient_id: int) -> Optional[Dict[str, Any]]:
        """获取患者基本信息"""
        results = self.db_manager.execute_query(
            "SELECT * FROM bingren WHERE bianhao = ?", (patient_id,)
        )
        return results[0] if results else None
    
    def _get_treatment_records(self, patient_id: int, start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """获取治疗记录"""
        return self.db_manager.execute_query("""
            SELECT * FROM zhiliao 
            WHERE bianh = ? AND rq BETWEEN ? AND ?
            ORDER BY rq DESC, shijian DESC
        """, (patient_id, start_date, end_date))
    
    def _get_eeg_data(self, patient_id: int, start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """获取脑电数据"""
        return self.db_manager.execute_query("""
            SELECT * FROM Edata 
            WHERE ebianhao = ? AND DATE(eshijian) BETWEEN ? AND ?
            ORDER BY eshijian DESC
        """, (patient_id, start_date, end_date))
    
    def _generate_comprehensive_report(self, patient_info: Dict[str, Any], 
                                     treatment_records: List[Dict[str, Any]], 
                                     eeg_data: List[Dict[str, Any]]) -> str:
        """生成综合报告"""
        report = []
        
        # 报告标题
        report.append("=" * 60)
        report.append("脑机接口康复训练综合报告")
        report.append("=" * 60)
        report.append("")
        
        # 患者基本信息
        report.append("📋 患者基本信息")
        report.append("-" * 30)
        report.append(f"患者编号: {patient_info.get('bianhao', '')}")
        report.append(f"姓名: {patient_info.get('name', '')}")
        report.append(f"年龄: {patient_info.get('age', '')}岁")
        report.append(f"性别: {patient_info.get('xingbie', '')}")
        report.append(f"诊断: {patient_info.get('zhenduan', '')}")
        report.append(f"主治医师: {patient_info.get('zhuzhi', '')}")
        report.append(f"录入时间: {patient_info.get('lrshijian', '')}")
        report.append("")
        
        # 治疗历程统计
        if treatment_records:
            total_treatments = len(treatment_records)
            total_duration = sum(record.get('zlsj', 0) for record in treatment_records)
            avg_score = sum(record.get('defen', 0) for record in treatment_records) / total_treatments
            
            # 治疗效果分布
            excellent = sum(1 for r in treatment_records if r.get('zlms') == '优')
            good = sum(1 for r in treatment_records if r.get('zlms') == '良')
            fair = sum(1 for r in treatment_records if r.get('zlms') == '中')
            poor = sum(1 for r in treatment_records if r.get('zlms') == '差')
            
            report.append("📈 治疗历程统计")
            report.append("-" * 30)
            report.append(f"总治疗次数: {total_treatments}次")
            report.append(f"累计治疗时长: {total_duration:.1f}分钟")
            report.append(f"平均治疗得分: {avg_score:.1f}分")
            report.append(f"治疗效果分布:")
            report.append(f"  优: {excellent}次 ({excellent/total_treatments*100:.1f}%)")
            report.append(f"  良: {good}次 ({good/total_treatments*100:.1f}%)")
            report.append(f"  中: {fair}次 ({fair/total_treatments*100:.1f}%)")
            report.append(f"  差: {poor}次 ({poor/total_treatments*100:.1f}%)")
            report.append("")
            
            # 最近治疗表现
            recent_records = treatment_records[:5]  # 最近5次
            report.append("🎯 最近治疗表现")
            report.append("-" * 30)
            for i, record in enumerate(recent_records, 1):
                report.append(f"{i}. {record.get('rq')} {record.get('shijian')} - "
                            f"得分: {record.get('defen', 0):.1f} - "
                            f"评价: {record.get('zlms', '')}")
            report.append("")
        
        # 脑电特征分析
        if eeg_data:
            avg_theta = sum(d.get('theta', 0) for d in eeg_data) / len(eeg_data)
            avg_alpha = sum(d.get('alpha', 0) for d in eeg_data) / len(eeg_data)
            avg_beta = sum(d.get('low_beta', 0) + d.get('high_beta', 0) for d in eeg_data) / len(eeg_data)
            avg_gamma = sum(d.get('gamma', 0) for d in eeg_data) / len(eeg_data)
            
            report.append("🧠 脑电特征分析")
            report.append("-" * 30)
            report.append(f"平均θ波功率: {avg_theta:.2f}μV²")
            report.append(f"平均α波功率: {avg_alpha:.2f}μV²")
            report.append(f"平均β波功率: {avg_beta:.2f}μV²")
            report.append(f"平均γ波功率: {avg_gamma:.2f}μV²")
            report.append("")
        
        # 治疗建议
        report.append("💡 治疗建议")
        report.append("-" * 30)
        if treatment_records:
            if avg_score >= 80:
                report.append("• 治疗效果优秀，建议继续当前训练方案")
                report.append("• 可适当增加训练难度，提升挑战性")
            elif avg_score >= 60:
                report.append("• 治疗效果良好，建议保持训练频率")
                report.append("• 注意观察疲劳度，适当调整训练强度")
            elif avg_score >= 45:
                report.append("• 治疗效果一般，建议调整训练参数")
                report.append("• 加强基础训练，提高运动想象能力")
            else:
                report.append("• 治疗效果需要改善，建议重新评估训练方案")
                report.append("• 考虑降低训练难度，增强患者信心")
        else:
            report.append("• 暂无治疗记录，建议尽快开始康复训练")
        
        report.append("")
        report.append(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("=" * 60)
        
        return "\n".join(report)
    
    def _generate_training_report(self, patient_info: Dict[str, Any],
                                treatment_records: List[Dict[str, Any]],
                                eeg_data: List[Dict[str, Any]]) -> str:
        """生成训练报告"""
        if not treatment_records:
            return "暂无治疗记录"

        latest_record = treatment_records[0]

        report = []
        report.append("🎯 单次训练详细报告")
        report.append("=" * 60)
        report.append("")

        # 基本信息
        report.append("📋 训练基本信息")
        report.append("-" * 30)
        report.append(f"患者姓名: {patient_info.get('name', '')}")
        report.append(f"患者编号: {patient_info.get('bianhao', '')}")
        report.append(f"训练日期: {latest_record.get('rq', '')}")
        report.append(f"训练时间: {latest_record.get('shijian', '')}")
        report.append(f"操作员: {latest_record.get('czy', '')}")
        report.append(f"主治医师: {latest_record.get('zhuzhi', '')}")
        report.append("")

        # 训练参数
        report.append("⚙️ 训练参数设置")
        report.append("-" * 30)
        report.append(f"训练时长: {latest_record.get('zlsj', 0):.1f} 分钟")
        report.append(f"要求次数: {latest_record.get('yaoqiucs', 0)} 次")
        report.append(f"实际完成: {latest_record.get('shijics', 0)} 次")
        report.append(f"完成率: {(latest_record.get('shijics', 0) / max(latest_record.get('yaoqiucs', 1), 1) * 100):.1f}%")
        report.append("")

        # 训练表现
        report.append("📊 训练表现分析")
        report.append("-" * 30)
        success_rate = latest_record.get('defen', 0)
        report.append(f"成功率: {success_rate:.1f}%")
        report.append(f"治疗评价: {latest_record.get('zlms', '')}")

        # 表现评估
        if success_rate >= 85:
            report.append("✅ 表现评估: 优秀")
            report.append("  - 运动想象能力强")
            report.append("  - 脑机接口控制稳定")
            report.append("  - 建议适当增加难度")
        elif success_rate >= 70:
            report.append("✅ 表现评估: 良好")
            report.append("  - 基本掌握运动想象技巧")
            report.append("  - 有进一步提升空间")
            report.append("  - 建议保持当前训练强度")
        elif success_rate >= 50:
            report.append("⚠️ 表现评估: 一般")
            report.append("  - 运动想象能力需要加强")
            report.append("  - 建议增加基础训练")
            report.append("  - 可考虑降低训练难度")
        else:
            report.append("❌ 表现评估: 需要改善")
            report.append("  - 运动想象技巧不熟练")
            report.append("  - 建议重新评估训练方案")
            report.append("  - 可能需要调整设备参数")

        report.append("")

        # 脑电信号分析
        if eeg_data:
            latest_eeg = eeg_data[0]
            report.append("🧠 脑电信号分析")
            report.append("-" * 30)
            report.append(f"θ波功率: {latest_eeg.get('theta', 0):.2f} μV²")
            report.append(f"α波功率: {latest_eeg.get('alpha', 0):.2f} μV²")
            report.append(f"β波功率: {(latest_eeg.get('low_beta', 0) + latest_eeg.get('high_beta', 0)):.2f} μV²")
            report.append(f"γ波功率: {latest_eeg.get('gamma', 0):.2f} μV²")

            # 信号质量评估
            alpha_power = latest_eeg.get('alpha', 0)
            if alpha_power > 15:
                report.append("✅ 信号质量: 优秀")
            elif alpha_power > 10:
                report.append("✅ 信号质量: 良好")
            else:
                report.append("⚠️ 信号质量: 需要改善")
            report.append("")

        # 训练建议
        report.append("💡 个性化训练建议")
        report.append("-" * 30)

        duration = latest_record.get('zlsj', 0)
        if duration < 20:
            report.append("• 建议延长训练时间至20-30分钟")
        elif duration > 45:
            report.append("• 训练时间较长，注意观察疲劳度")

        if success_rate < 60:
            report.append("• 建议降低训练难度，增强患者信心")
            report.append("• 加强运动想象基础训练")
        elif success_rate > 85:
            report.append("• 可适当增加训练难度")
            report.append("• 考虑引入更复杂的训练任务")

        report.append("• 保持规律的训练频率")
        report.append("• 注意训练环境的安静和舒适")

        report.append("")
        report.append("📅 下次训练建议")
        report.append("-" * 30)
        report.append("• 建议训练间隔: 1-2天")
        report.append("• 最佳训练时段: 上午10-11点或下午2-4点")
        report.append("• 训练前准备: 充分休息，保持良好心态")

        report.append("")
        report.append(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("=" * 60)

        return "\n".join(report)
    
    def _generate_assessment_report(self, patient_info: Dict[str, Any], 
                                  treatment_records: List[Dict[str, Any]], 
                                  eeg_data: List[Dict[str, Any]]) -> str:
        """生成评定报告"""
        return "评定报告功能开发中..."
    
    def _generate_progress_report(self, patient_info: Dict[str, Any], 
                                treatment_records: List[Dict[str, Any]], 
                                eeg_data: List[Dict[str, Any]]) -> str:
        """生成进度报告"""
        return "进度报告功能开发中..."
    
    def generate_weekly_statistics(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """生成按周统计"""
        try:
            # 按周统计治疗数据
            weekly_stats = self.db_manager.execute_query("""
                SELECT
                    strftime('%Y-%W', rq) as week,
                    COUNT(*) as treatment_count,
                    COUNT(DISTINCT bianh) as patient_count,
                    AVG(defen) as avg_score,
                    SUM(zlsj) as total_duration,
                    SUM(CASE WHEN zlms = '优' THEN 1 ELSE 0 END) as excellent_count,
                    SUM(CASE WHEN zlms = '良' THEN 1 ELSE 0 END) as good_count,
                    SUM(CASE WHEN zlms = '中' THEN 1 ELSE 0 END) as fair_count,
                    SUM(CASE WHEN zlms = '差' THEN 1 ELSE 0 END) as poor_count
                FROM zhiliao
                WHERE rq BETWEEN ? AND ?
                GROUP BY strftime('%Y-%W', rq)
                ORDER BY week
            """, (start_date, end_date))

            # 计算周度趋势
            weekly_trends = []
            for i, week_stat in enumerate(weekly_stats):
                if i > 0:
                    prev_score = weekly_stats[i-1].get('avg_score', 0) or 0
                    curr_score = week_stat.get('avg_score', 0) or 0
                    trend = ((curr_score - prev_score) / max(prev_score, 1)) * 100
                    weekly_trends.append({
                        'week': week_stat.get('week'),
                        'trend_percentage': trend
                    })

            return {
                'weekly_stats': weekly_stats,
                'weekly_trends': weekly_trends,
                'summary': {
                    'total_weeks': len(weekly_stats),
                    'avg_treatments_per_week': sum(s.get('treatment_count', 0) for s in weekly_stats) / max(len(weekly_stats), 1),
                    'avg_patients_per_week': sum(s.get('patient_count', 0) for s in weekly_stats) / max(len(weekly_stats), 1)
                }
            }

        except Exception as e:
            self.logger.error(f"生成周统计失败: {e}")
            return {}

    def generate_monthly_statistics(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """生成按月统计"""
        try:
            # 按月统计治疗数据
            monthly_stats = self.db_manager.execute_query("""
                SELECT
                    strftime('%Y-%m', rq) as month,
                    COUNT(*) as treatment_count,
                    COUNT(DISTINCT bianh) as patient_count,
                    AVG(defen) as avg_score,
                    SUM(zlsj) as total_duration,
                    SUM(CASE WHEN zlms = '优' THEN 1 ELSE 0 END) as excellent_count,
                    SUM(CASE WHEN zlms = '良' THEN 1 ELSE 0 END) as good_count,
                    SUM(CASE WHEN zlms = '中' THEN 1 ELSE 0 END) as fair_count,
                    SUM(CASE WHEN zlms = '差' THEN 1 ELSE 0 END) as poor_count
                FROM zhiliao
                WHERE rq BETWEEN ? AND ?
                GROUP BY strftime('%Y-%m', rq)
                ORDER BY month
            """, (start_date, end_date))

            # 计算月度改善率
            improvement_rates = []
            for stat in monthly_stats:
                total_treatments = stat.get('treatment_count', 0)
                excellent_good = stat.get('excellent_count', 0) + stat.get('good_count', 0)
                improvement_rate = (excellent_good / max(total_treatments, 1)) * 100
                improvement_rates.append({
                    'month': stat.get('month'),
                    'improvement_rate': improvement_rate
                })

            # 患者康复进度分析
            patient_progress = self.db_manager.execute_query("""
                SELECT
                    bianh,
                    COUNT(*) as total_treatments,
                    AVG(defen) as avg_score,
                    MAX(defen) - MIN(defen) as score_improvement,
                    strftime('%Y-%m', MIN(rq)) as first_month,
                    strftime('%Y-%m', MAX(rq)) as last_month
                FROM zhiliao
                WHERE rq BETWEEN ? AND ?
                GROUP BY bianh
                HAVING COUNT(*) >= 3
                ORDER BY score_improvement DESC
            """, (start_date, end_date))

            return {
                'monthly_stats': monthly_stats,
                'improvement_rates': improvement_rates,
                'patient_progress': patient_progress,
                'summary': {
                    'total_months': len(monthly_stats),
                    'avg_treatments_per_month': sum(s.get('treatment_count', 0) for s in monthly_stats) / max(len(monthly_stats), 1),
                    'avg_improvement_rate': sum(r.get('improvement_rate', 0) for r in improvement_rates) / max(len(improvement_rates), 1),
                    'top_improving_patients': len([p for p in patient_progress if p.get('score_improvement', 0) > 10])
                }
            }

        except Exception as e:
            self.logger.error(f"生成月统计失败: {e}")
            return {}

    def generate_patient_statistics(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """生成按患者统计"""
        try:
            # 患者表现排名
            patient_rankings = self.db_manager.execute_query("""
                SELECT
                    z.bianh,
                    b.name,
                    COUNT(*) as treatment_count,
                    AVG(z.defen) as avg_score,
                    MAX(z.defen) as best_score,
                    MIN(z.defen) as worst_score,
                    SUM(z.zlsj) as total_duration,
                    SUM(CASE WHEN z.zlms = '优' THEN 1 ELSE 0 END) as excellent_count,
                    (MAX(z.defen) - MIN(z.defen)) as improvement
                FROM zhiliao z
                JOIN bingren b ON z.bianh = b.bianhao
                WHERE z.rq BETWEEN ? AND ?
                GROUP BY z.bianh, b.name
                HAVING COUNT(*) >= 2
                ORDER BY avg_score DESC
            """, (start_date, end_date))

            # 年龄组分析
            age_group_stats = self.db_manager.execute_query("""
                SELECT
                    CASE
                        WHEN b.age < 30 THEN '青年组(<30)'
                        WHEN b.age < 50 THEN '中年组(30-50)'
                        WHEN b.age < 70 THEN '中老年组(50-70)'
                        ELSE '老年组(>=70)'
                    END as age_group,
                    COUNT(DISTINCT z.bianh) as patient_count,
                    AVG(z.defen) as avg_score,
                    COUNT(*) as treatment_count
                FROM zhiliao z
                JOIN bingren b ON z.bianh = b.bianhao
                WHERE z.rq BETWEEN ? AND ?
                GROUP BY age_group
                ORDER BY avg_score DESC
            """, (start_date, end_date))

            # 性别差异分析
            gender_stats = self.db_manager.execute_query("""
                SELECT
                    b.xingbie as gender,
                    COUNT(DISTINCT z.bianh) as patient_count,
                    AVG(z.defen) as avg_score,
                    COUNT(*) as treatment_count
                FROM zhiliao z
                JOIN bingren b ON z.bianh = b.bianhao
                WHERE z.rq BETWEEN ? AND ?
                GROUP BY b.xingbie
            """, (start_date, end_date))

            return {
                'patient_rankings': patient_rankings,
                'age_group_stats': age_group_stats,
                'gender_stats': gender_stats,
                'summary': {
                    'total_patients': len(patient_rankings),
                    'top_performer': patient_rankings[0] if patient_rankings else None,
                    'most_improved': max(patient_rankings, key=lambda x: x.get('improvement', 0)) if patient_rankings else None,
                    'most_active': max(patient_rankings, key=lambda x: x.get('treatment_count', 0)) if patient_rankings else None
                }
            }

        except Exception as e:
            self.logger.error(f"生成患者统计失败: {e}")
            return {}

    def _get_device_statistics(self, date: str) -> Dict[str, Any]:
        """获取设备统计信息"""
        # 简化版设备统计
        return {
            'online_duration': 8.0,  # 小时
            'utilization_rate': 75.0,  # 百分比
            'error_count': 0
        }

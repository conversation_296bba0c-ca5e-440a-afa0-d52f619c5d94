<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>脑机接口康复医疗器械 UI 设计风格展示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f0f2f5;
            padding: 20px;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
        }
        
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .styles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(750px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .style-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s ease;
        }
        
        .style-card:hover {
            transform: translateY(-5px);
        }
        
        .style-header {
            padding: 20px;
            font-weight: 600;
            font-size: 1.3em;
            color: white;
            text-align: center;
        }
        
        .medical-modern {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .bci-tech {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        }
        
        .rehab-pro {
            background: linear-gradient(135deg, #134e5e 0%, #71b280 100%);
        }
        
        .material-med {
            background: linear-gradient(135deg, #ee0979 0%, #ff6a00 100%);
        }
        
        .style-content {
            padding: 0;
            height: 500px;
            position: relative;
            overflow: hidden;
        }
        
        /* 现代医疗仪表板风格 */
        .modern-dashboard {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 20px;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        
        .top-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            margin-bottom: 20px;
        }
        
        .status-indicators {
            display: flex;
            gap: 15px;
        }
        
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-normal { background: #10b981; }
        .status-warning { background: #f59e0b; }
        .status-error { background: #ef4444; }
        
        .main-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            flex: 1;
        }
        
        .chart-area {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            position: relative;
        }
        
        .wave-animation {
            width: 100%;
            height: 200px;
            background: linear-gradient(90deg, #3b82f6, #10b981, #f59e0b);
            border-radius: 4px;
            position: relative;
            overflow: hidden;
        }
        
        .wave {
            position: absolute;
            top: 50%;
            left: 0;
            width: 100%;
            height: 2px;
            background: #1e40af;
            animation: waveMove 2s linear infinite;
        }
        
        @keyframes waveMove {
            0% { transform: translateX(-100%) translateY(-50%); }
            100% { transform: translateX(100%) translateY(-50%); }
        }
        
        .controls-panel {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .control-item {
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .slider {
            width: 60%;
            height: 4px;
            background: #e2e8f0;
            border-radius: 2px;
            position: relative;
        }
        
        .slider::after {
            content: '';
            position: absolute;
            left: 30%;
            top: -2px;
            width: 8px;
            height: 8px;
            background: #3b82f6;
            border-radius: 50%;
        }
        
        /* 科技感BCI风格 */
        .bci-interface {
            background: #0f172a;
            color: #e2e8f0;
            padding: 20px;
            height: 100%;
            position: relative;
        }
        
        .bci-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            height: 100%;
        }
        
        .brain-visual {
            background: radial-gradient(circle at center, #1e293b 0%, #0f172a 70%);
            border: 1px solid #334155;
            border-radius: 8px;
            padding: 20px;
            position: relative;
            overflow: hidden;
        }
        
        .brain-waves {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 150px;
            height: 150px;
            border: 2px solid #0ea5e9;
            border-radius: 50%;
            animation: pulse 2s ease-in-out infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
            50% { transform: translate(-50%, -50%) scale(1.1); opacity: 0.7; }
        }
        
        .data-streams {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .data-row {
            background: rgba(30, 41, 59, 0.5);
            border: 1px solid #334155;
            border-radius: 4px;
            padding: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .neural-indicator {
            width: 50px;
            height: 20px;
            background: linear-gradient(90deg, #0ea5e9, #10b981);
            border-radius: 2px;
            position: relative;
            overflow: hidden;
        }
        
        .neural-pulse {
            position: absolute;
            top: 0;
            left: -50px;
            width: 50px;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.8), transparent);
            animation: neuralScan 1.5s linear infinite;
        }
        
        @keyframes neuralScan {
            0% { left: -50px; }
            100% { left: 100%; }
        }
        
        /* 康复设备专业风格 */
        .rehab-interface {
            background: #f8fafc;
            padding: 20px;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        
        .rehab-header {
            background: linear-gradient(135deg, #0f766e, #14b8a6);
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .patient-info {
            display: flex;
            gap: 20px;
        }
        
        .progress-section {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .progress-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border-left: 4px solid #14b8a6;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            margin-top: 10px;
            position: relative;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #14b8a6, #10b981);
            border-radius: 4px;
            width: 65%;
            transition: width 1s ease;
        }
        
        /* Material Design医疗风格 */
        .material-interface {
            background: #fafafa;
            padding: 20px;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        
        .material-header {
            background: white;
            padding: 20px;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 24px;
            elevation: 2;
        }
        
        .fab-container {
            position: absolute;
            bottom: 20px;
            right: 20px;
        }
        
        .fab {
            width: 56px;
            height: 56px;
            background: #e91e63;
            border-radius: 50%;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            transition: box-shadow 0.3s ease;
        }
        
        .fab:hover {
            box-shadow: 0 6px 12px rgba(0,0,0,0.4);
        }
        
        .material-cards {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            flex: 1;
        }
        
        .material-card {
            background: white;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
            transition: box-shadow 0.3s ease;
        }
        
        .material-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        .feature-list {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .feature-list h2 {
            color: #2c3e50;
            margin-bottom: 25px;
            font-size: 1.8em;
            text-align: center;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .feature-item {
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .feature-item:hover {
            border-color: #3b82f6;
            background: #f8fafc;
            transform: translateY(-2px);
        }
        
        .feature-item h3 {
            color: #1e40af;
            margin-bottom: 10px;
            font-size: 1.2em;
        }
        
        .feature-item p {
            color: #64748b;
            line-height: 1.6;
        }
        
        .tech-stack {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-top: 30px;
        }
        
        .tech-stack h2 {
            text-align: center;
            margin-bottom: 25px;
            font-size: 1.8em;
        }
        
        .tech-items {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
        }
        
        .tech-item {
            background: rgba(255,255,255,0.2);
            padding: 12px 20px;
            border-radius: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>脑机接口康复医疗器械 UI 设计风格展示</h1>
        
        <div class="styles-grid">
            <!-- 现代医疗仪表板风格 -->
            <div class="style-card">
                <div class="style-header medical-modern">
                    🏥 现代医疗仪表板风格
                </div>
                <div class="style-content">
                    <div class="modern-dashboard">
                        <div class="top-bar">
                            <div>
                                <strong>患者: 张三</strong> | 会话 #1247
                            </div>
                            <div class="status-indicators">
                                <div><span class="status-dot status-normal"></span>设备正常</div>
                                <div><span class="status-dot status-warning"></span>信号质量</div>
                                <div><span class="status-dot status-normal"></span>数据传输</div>
                            </div>
                        </div>
                        
                        <div class="main-grid">
                            <div class="chart-area">
                                <h3 style="margin-bottom: 15px; color: #374151;">实时脑电波监测</h3>
                                <div class="wave-animation">
                                    <div class="wave"></div>
                                </div>
                                <div style="margin-top: 15px; display: flex; justify-content: space-between; font-size: 0.9em; color: #6b7280;">
                                    <span>Alpha: 8.2Hz</span>
                                    <span>Beta: 15.7Hz</span>
                                    <span>Theta: 6.1Hz</span>
                                    <span>Delta: 2.3Hz</span>
                                </div>
                            </div>
                            
                            <div class="controls-panel">
                                <h3 style="margin-bottom: 20px; color: #374151;">康复参数</h3>
                                <div class="control-item">
                                    <label>刺激强度</label>
                                    <div class="slider"></div>
                                </div>
                                <div class="control-item">
                                    <label>训练时长</label>
                                    <span style="color: #3b82f6; font-weight: 600;">15分钟</span>
                                </div>
                                <div class="control-item">
                                    <label>反馈阈值</label>
                                    <div class="slider"></div>
                                </div>
                                <div style="margin-top: 30px;">
                                    <div style="background: #ecfdf5; padding: 15px; border-radius: 6px; border-left: 3px solid #10b981;">
                                        <strong style="color: #047857;">康复进度: 73%</strong>
                                        <div style="font-size: 0.9em; color: #065f46; margin-top: 5px;">
                                            本周训练5次，表现优秀
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 科技感BCI风格 -->
            <div class="style-card">
                <div class="style-header bci-tech">
                    🧠 科技感脑机接口风格
                </div>
                <div class="style-content">
                    <div class="bci-interface">
                        <div class="bci-grid">
                            <div class="brain-visual">
                                <div style="text-align: center; margin-bottom: 20px; color: #0ea5e9; font-weight: 600;">
                                    神经活动可视化
                                </div>
                                <div class="brain-waves"></div>
                                <div style="position: absolute; bottom: 10px; left: 10px; font-size: 0.8em; color: #64748b;">
                                    采样率: 1000Hz
                                </div>
                                <div style="position: absolute; bottom: 10px; right: 10px; font-size: 0.8em; color: #64748b;">
                                    信噪比: 23.7dB
                                </div>
                            </div>
                            
                            <div class="data-streams">
                                <div style="color: #0ea5e9; font-weight: 600; margin-bottom: 10px;">
                                    神经信号通道
                                </div>
                                <div class="data-row">
                                    <span>C3 (左运动皮层)</span>
                                    <div class="neural-indicator">
                                        <div class="neural-pulse"></div>
                                    </div>
                                </div>
                                <div class="data-row">
                                    <span>C4 (右运动皮层)</span>
                                    <div class="neural-indicator">
                                        <div class="neural-pulse"></div>
                                    </div>
                                </div>
                                <div class="data-row">
                                    <span>Cz (中央顶区)</span>
                                    <div class="neural-indicator">
                                        <div class="neural-pulse"></div>
                                    </div>
                                </div>
                                <div class="data-row">
                                    <span>Pz (顶区)</span>
                                    <div class="neural-indicator">
                                        <div class="neural-pulse"></div>
                                    </div>
                                </div>
                                
                                <div style="margin-top: 20px; padding: 15px; background: rgba(14, 165, 233, 0.1); border: 1px solid #0ea5e9; border-radius: 4px;">
                                    <div style="color: #0ea5e9; font-weight: 600; margin-bottom: 8px;">意图识别状态</div>
                                    <div style="font-size: 0.9em;">
                                        <div>运动想象准确率: 87.3%</div>
                                        <div>P300响应延迟: 312ms</div>
                                        <div>SSVEP频率锁定: 稳定</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 康复设备专业风格 -->
            <div class="style-card">
                <div class="style-header rehab-pro">
                    🏃‍♂️ 康复设备专业风格
                </div>
                <div class="style-content">
                    <div class="rehab-interface">
                        <div class="rehab-header">
                            <div class="patient-info">
                                <div><strong>患者姓名:</strong> 李四</div>
                                <div><strong>训练类型:</strong> 上肢运动康复</div>
                                <div><strong>治疗师:</strong> 王医生</div>
                            </div>
                            <div style="background: rgba(255,255,255,0.2); padding: 8px 15px; border-radius: 20px;">
                                第12次训练
                            </div>
                        </div>
                        
                        <div class="progress-section">
                            <div class="progress-card">
                                <h4 style="color: #0f766e; margin-bottom: 10px;">肌肉激活度</h4>
                                <div style="font-size: 2em; font-weight: 600; color: #14b8a6; margin-bottom: 10px;">72%</div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 72%;"></div>
                                </div>
                                <div style="margin-top: 10px; font-size: 0.9em; color: #64748b;">
                                    比上次提升8%
                                </div>
                            </div>
                            
                            <div class="progress-card">
                                <h4 style="color: #0f766e; margin-bottom: 10px;">关节活动范围</h4>
                                <div style="font-size: 2em; font-weight: 600; color: #14b8a6; margin-bottom: 10px;">85°</div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 85%;"></div>
                                </div>
                                <div style="margin-top: 10px; font-size: 0.9em; color: #64748b;">
                                    目标角度: 90°
                                </div>
                            </div>
                            
                            <div class="progress-card">
                                <h4 style="color: #0f766e; margin-bottom: 10px;">协调性评分</h4>
                                <div style="font-size: 2em; font-weight: 600; color: #14b8a6; margin-bottom: 10px;">7.8</div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 78%;"></div>
                                </div>
                                <div style="margin-top: 10px; font-size: 0.9em; color: #64748b;">
                                    总分: 10分
                                </div>
                            </div>
                            
                            <div class="progress-card">
                                <h4 style="color: #0f766e; margin-bottom: 10px;">训练完成度</h4>
                                <div style="font-size: 2em; font-weight: 600; color: #14b8a6; margin-bottom: 10px;">65%</div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 65%;"></div>
                                </div>
                                <div style="margin-top: 10px; font-size: 0.9em; color: #64748b;">
                                    剩余时间: 8分钟
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Material Design医疗风格 -->
            <div class="style-card">
                <div class="style-header material-med">
                    🎨 Material Design医疗风格
                </div>
                <div class="style-content">
                    <div class="material-interface">
                        <div class="material-header">
                            <h3 style="color: #e91e63; margin-bottom: 8px;">脑机接口训练系统</h3>
                            <p style="color: #757575; margin: 0;">基于Material Design的现代化医疗界面</p>
                        </div>
                        
                        <div class="material-cards">
                            <div class="material-card">
                                <h4 style="color: #e91e63; margin-bottom: 15px;">实时监测</h4>
                                <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                    <span style="color: #757575;">心率</span>
                                    <span style="color: #4caf50; font-weight: 600;">72 BPM</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                    <span style="color: #757575;">血压</span>
                                    <span style="color: #4caf50; font-weight: 600;">120/80</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                    <span style="color: #757575;">血氧</span>
                                    <span style="color: #4caf50; font-weight: 600;">98%</span>
                                </div>
                                <div style="margin-top: 15px; padding: 10px; background: #e8f5e8; border-radius: 4px;">
                                    <span style="color: #2e7d32; font-size: 0.9em;">所有指标正常</span>
                                </div>
                            </div>
                            
                            <div class="material-card">
                                <h4 style="color: #e91e63; margin-bottom: 15px;">训练记录</h4>
                                <div style="margin-bottom: 15px;">
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                        <span style="color: #757575;">本周训练</span>
                                        <span style="font-weight: 600;">5次</span>
                                    </div>
                                    <div style="height: 4px; background: #e0e0e0; border-radius: 2px; overflow: hidden;">
                                        <div style="height: 100%; width: 71%; background: #e91e63; border-radius: 2px;"></div>
                                    </div>
                                </div>
                                <div style="margin-bottom: 15px;">
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                        <span style="color: #757575;">平均得分</span>
                                        <span style="font-weight: 600;">8.3/10</span>
                                    </div>
                                    <div style="height: 4px; background: #e0e0e0; border-radius: 2px; overflow: hidden;">
                                        <div style="height: 100%; width: 83%; background: #4caf50; border-radius: 2px;"></div>
                                    </div>
                                </div>
                                <div style="padding: 10px; background: #fff3e0; border-radius: 4px;">
                                    <span style="color: #f57c00; font-size: 0.9em;">本周表现提升12%</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="fab-container">
                            <div class="fab">+</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 功能特性对比 -->
        <div class="feature-list">
            <h2>UI设计关键特性对比</h2>
            <div class="features">
                <div class="feature-item">
                    <h3>🏥 现代医疗仪表板</h3>
                    <p><strong>特点:</strong> 简洁清晰，专业可靠，色彩层次分明<br>
                    <strong>适用:</strong> 医院环境，专业医疗人员操作<br>
                    <strong>优势:</strong> 信息密度高，易于快速读取数据</p>
                </div>
                
                <div class="feature-item">
                    <h3>🧠 科技感BCI风格</h3>
                    <p><strong>特点:</strong> 深色主题，炫酷动画，未来科技感<br>
                    <strong>适用:</strong> 研究环境，技术展示<br>
                    <strong>优势:</strong> 视觉冲击力强，突出技术先进性</p>
                </div>
                
                <div class="feature-item">
                    <h3>🏃‍♂️ 康复设备专业</h3>
                    <p><strong>特点:</strong> 进度导向，绿色主题，友好亲切<br>
                    <strong>适用:</strong> 康复训练，患者自主使用<br>
                    <strong>优势:</strong> 激励效果好，减少患者焦虑</p>
                </div>
                
                <div class="feature-item">
                    <h3>🎨 Material Design</h3>
                    <p><strong>特点:</strong> 现代扁平，触控友好，响应式设计<br>
                    <strong>适用:</strong> 移动设备，年轻用户群体<br>
                    <strong>优势:</strong> 用户熟悉度高，交互体验佳</p>
                </div>
            </div>
        </div>
        
        <!-- 技术实现方案 -->
        <div class="tech-stack">
            <h2>您的技术栈实现建议</h2>
            <div class="tech-items">
                <div class="tech-item">PySide6 + Qt Designer</div>
                <div class="tech-item">PyQtGraph (实时图表)</div>
                <div class="tech-item">Matplotlib (静态图表)</div>
                <div class="tech-item">SQLite3 (数据存储)</div>
                <div class="tech-item">qt-material (主题美化)</div>
                <div class="tech-item">QSS (自定义样式)</div>
                <div class="tech-item">QTimer (实时更新)</div>
                <div class="tech-item">QThread (后台处理)</div>
            </div>
        </div>
    </div>
</body>
</html>
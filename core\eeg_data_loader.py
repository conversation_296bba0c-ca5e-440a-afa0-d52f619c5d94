#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脑电数据加载器
EEG Data Loader

提供便捷的脑电原始数据访问接口
支持数据查询、加载和预处理

作者: AI Assistant
版本: 1.0.0
"""

import logging
import h5py
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, Dict, List, Tuple, Any, Union
from dataclasses import dataclass
import random

from core.eeg_raw_data_manager import TrialType, TrialMetadata


@dataclass
class DatasetSplit:
    """数据集分割结果"""
    train_data: List[np.ndarray]
    train_labels: List[int]
    val_data: List[np.ndarray]
    val_labels: List[int]
    test_data: List[np.ndarray]
    test_labels: List[int]
    metadata: Dict[str, Any]


class EEGDataLoader:
    """脑电数据加载器"""
    
    def __init__(self, db_manager=None):
        """初始化数据加载器"""
        self.logger = logging.getLogger(__name__)
        self.db_manager = db_manager
        
        # 缓存
        self._data_cache = {}
        self._cache_size_limit = 100  # 最大缓存条目数
        
        self.logger.info("脑电数据加载器初始化完成")
    
    def load_patient_data(self, patient_id: int, 
                         date_range: Optional[Tuple[datetime, datetime]] = None,
                         session_types: Optional[List[str]] = None,
                         min_quality: float = 0.0) -> List[Dict[str, Any]]:
        """加载患者的脑电数据"""
        try:
            if not self.db_manager:
                self.logger.error("数据库管理器未提供")
                return []
            
            # 构建查询条件
            sql = """
                SELECT s.*, r.trial_id, r.file_path, r.label, r.data_quality,
                       r.duration_seconds, r.start_time as trial_start_time
                FROM eeg_sessions s
                JOIN eeg_raw_data r ON s.session_id = r.session_id
                WHERE s.patient_id = ?
            """
            params = [patient_id]
            
            # 添加日期范围过滤
            if date_range:
                sql += " AND s.start_time >= ? AND s.start_time <= ?"
                params.extend([date_range[0].isoformat(), date_range[1].isoformat()])
            
            # 添加会话类型过滤
            if session_types:
                placeholders = ','.join(['?' for _ in session_types])
                sql += f" AND s.session_type IN ({placeholders})"
                params.extend(session_types)
            
            # 添加质量过滤
            if min_quality > 0:
                sql += " AND r.data_quality >= ?"
                params.append(min_quality)
            
            sql += " ORDER BY s.start_time DESC, r.trial_id"
            
            results = self.db_manager.execute_query(sql, params) or []
            
            self.logger.info(f"加载患者{patient_id}数据: {len(results)}条记录")
            return results
            
        except Exception as e:
            self.logger.error(f"加载患者数据失败: {e}")
            return []
    
    def load_session_data(self, session_id: int, 
                         load_raw_data: bool = False) -> Dict[str, Any]:
        """加载特定会话的数据"""
        try:
            if not self.db_manager:
                return {}
            
            # 获取会话信息
            session_sql = "SELECT * FROM eeg_sessions WHERE session_id = ?"
            session_result = self.db_manager.execute_query(session_sql, [session_id])
            
            if not session_result:
                self.logger.error(f"会话{session_id}不存在")
                return {}
            
            session_info = session_result[0]
            
            # 获取试验列表
            trials_sql = """
                SELECT * FROM eeg_raw_data 
                WHERE session_id = ?
                ORDER BY trial_id
            """
            trials = self.db_manager.execute_query(trials_sql, [session_id]) or []
            
            result = {
                'session_info': session_info,
                'trials': trials,
                'trial_count': len(trials),
                'quality_stats': self._calculate_session_quality_stats(trials)
            }
            
            # 如果需要加载原始数据
            if load_raw_data:
                result['raw_data'] = []
                for trial in trials:
                    trial_data = self._load_trial_raw_data(trial['file_path'], trial['trial_id'])
                    if trial_data:
                        result['raw_data'].append({
                            'trial_id': trial['trial_id'],
                            'label': trial['label'],
                            'data': trial_data[0],
                            'metadata': trial_data[1]
                        })
            
            return result
            
        except Exception as e:
            self.logger.error(f"加载会话数据失败: {e}")
            return {}
    
    def get_training_dataset(self, patient_ids: List[int],
                           balance_classes: bool = True,
                           min_quality: float = 0.7,
                           max_trials_per_patient: Optional[int] = None,
                           test_split: float = 0.2,
                           val_split: float = 0.1) -> DatasetSplit:
        """获取训练数据集"""
        try:
            all_data = []
            all_labels = []
            all_metadata = []
            
            # 收集所有患者的数据
            for patient_id in patient_ids:
                patient_data = self.load_patient_data(
                    patient_id, 
                    min_quality=min_quality
                )
                
                patient_trials = []
                for record in patient_data:
                    trial_data = self._load_trial_raw_data(
                        record['file_path'], 
                        record['trial_id']
                    )
                    
                    if trial_data:
                        patient_trials.append({
                            'data': trial_data[0],
                            'label': record['label'],
                            'metadata': {
                                'patient_id': patient_id,
                                'session_id': record['session_id'],
                                'trial_id': record['trial_id'],
                                'quality': record['data_quality']
                            }
                        })
                
                # 限制每个患者的试验数量
                if max_trials_per_patient and len(patient_trials) > max_trials_per_patient:
                    patient_trials = random.sample(patient_trials, max_trials_per_patient)
                
                # 添加到总数据集
                for trial in patient_trials:
                    all_data.append(trial['data'])
                    all_labels.append(trial['label'])
                    all_metadata.append(trial['metadata'])
            
            if not all_data:
                self.logger.warning("没有找到符合条件的数据")
                return DatasetSplit([], [], [], [], [], [], {})
            
            # 类别平衡
            if balance_classes:
                all_data, all_labels, all_metadata = self._balance_dataset(
                    all_data, all_labels, all_metadata
                )
            
            # 数据集分割
            return self._split_dataset(
                all_data, all_labels, all_metadata,
                test_split, val_split
            )
            
        except Exception as e:
            self.logger.error(f"获取训练数据集失败: {e}")
            return DatasetSplit([], [], [], [], [], [], {})
    
    def load_trial_by_id(self, trial_db_id: int) -> Optional[Tuple[np.ndarray, Dict[str, Any]]]:
        """根据数据库ID加载试验数据"""
        try:
            if not self.db_manager:
                return None
            
            # 查询试验信息
            sql = "SELECT * FROM eeg_raw_data WHERE id = ?"
            result = self.db_manager.execute_query(sql, [trial_db_id])
            
            if not result:
                self.logger.error(f"试验{trial_db_id}不存在")
                return None
            
            trial_info = result[0]
            
            # 加载原始数据
            return self._load_trial_raw_data(
                trial_info['file_path'], 
                trial_info['trial_id']
            )
            
        except Exception as e:
            self.logger.error(f"加载试验数据失败: {e}")
            return None
    
    def search_trials(self, criteria: Dict[str, Any]) -> List[Dict[str, Any]]:
        """搜索符合条件的试验"""
        try:
            if not self.db_manager:
                return []
            
            # 构建查询
            sql = """
                SELECT r.*, s.patient_id, s.session_type, s.start_time as session_start
                FROM eeg_raw_data r
                JOIN eeg_sessions s ON r.session_id = s.session_id
                WHERE 1=1
            """
            params = []
            
            # 添加搜索条件
            if 'patient_ids' in criteria and criteria['patient_ids']:
                placeholders = ','.join(['?' for _ in criteria['patient_ids']])
                sql += f" AND s.patient_id IN ({placeholders})"
                params.extend(criteria['patient_ids'])
            
            if 'labels' in criteria and criteria['labels']:
                placeholders = ','.join(['?' for _ in criteria['labels']])
                sql += f" AND r.label IN ({placeholders})"
                params.extend(criteria['labels'])
            
            if 'min_quality' in criteria:
                sql += " AND r.data_quality >= ?"
                params.append(criteria['min_quality'])
            
            if 'max_quality' in criteria:
                sql += " AND r.data_quality <= ?"
                params.append(criteria['max_quality'])
            
            if 'date_range' in criteria and criteria['date_range']:
                start_date, end_date = criteria['date_range']
                sql += " AND s.start_time >= ? AND s.start_time <= ?"
                params.extend([start_date.isoformat(), end_date.isoformat()])
            
            if 'session_types' in criteria and criteria['session_types']:
                placeholders = ','.join(['?' for _ in criteria['session_types']])
                sql += f" AND s.session_type IN ({placeholders})"
                params.extend(criteria['session_types'])
            
            sql += " ORDER BY s.start_time DESC, r.trial_id"
            
            # 添加限制
            if 'limit' in criteria:
                sql += " LIMIT ?"
                params.append(criteria['limit'])
            
            return self.db_manager.execute_query(sql, params) or []
            
        except Exception as e:
            self.logger.error(f"搜索试验失败: {e}")
            return []
    
    def get_data_statistics(self, patient_ids: Optional[List[int]] = None) -> Dict[str, Any]:
        """获取数据统计信息"""
        try:
            if not self.db_manager:
                return {}
            
            stats = {}
            
            # 基础查询条件
            where_clause = "WHERE 1=1"
            params = []
            
            if patient_ids:
                placeholders = ','.join(['?' for _ in patient_ids])
                where_clause += f" AND s.patient_id IN ({placeholders})"
                params.extend(patient_ids)
            
            # 总体统计
            sql = f"""
                SELECT 
                    COUNT(*) as total_trials,
                    COUNT(DISTINCT s.patient_id) as total_patients,
                    COUNT(DISTINCT s.session_id) as total_sessions,
                    AVG(r.data_quality) as avg_quality,
                    MIN(r.data_quality) as min_quality,
                    MAX(r.data_quality) as max_quality,
                    SUM(r.file_size_bytes) as total_size
                FROM eeg_raw_data r
                JOIN eeg_sessions s ON r.session_id = s.session_id
                {where_clause}
            """
            
            result = self.db_manager.execute_query(sql, params)
            if result:
                stats.update(result[0])
            
            # 标签分布
            sql = f"""
                SELECT r.label, COUNT(*) as count
                FROM eeg_raw_data r
                JOIN eeg_sessions s ON r.session_id = s.session_id
                {where_clause}
                GROUP BY r.label
            """
            
            label_dist = self.db_manager.execute_query(sql, params) or []
            stats['label_distribution'] = {
                str(item['label']): item['count'] for item in label_dist
            }
            
            # 质量分布
            sql = f"""
                SELECT 
                    CASE 
                        WHEN r.data_quality >= 0.9 THEN 'excellent'
                        WHEN r.data_quality >= 0.7 THEN 'good'
                        WHEN r.data_quality >= 0.5 THEN 'fair'
                        ELSE 'poor'
                    END as quality_level,
                    COUNT(*) as count
                FROM eeg_raw_data r
                JOIN eeg_sessions s ON r.session_id = s.session_id
                {where_clause}
                GROUP BY quality_level
            """
            
            quality_dist = self.db_manager.execute_query(sql, params) or []
            stats['quality_distribution'] = {
                item['quality_level']: item['count'] for item in quality_dist
            }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取数据统计失败: {e}")
            return {}
    
    def _load_trial_raw_data(self, file_path: str, trial_id: int) -> Optional[Tuple[np.ndarray, Dict[str, Any]]]:
        """加载试验原始数据（带缓存）"""
        try:
            cache_key = f"{file_path}_{trial_id}"
            
            # 检查缓存
            if cache_key in self._data_cache:
                return self._data_cache[cache_key]
            
            # 检查文件是否存在
            if not Path(file_path).exists():
                self.logger.error(f"文件不存在: {file_path}")
                return None
            
            # 加载数据
            with h5py.File(file_path, 'r') as f:
                trial_group_name = f'trials/trial_{trial_id:03d}'
                
                if trial_group_name not in f:
                    self.logger.error(f"试验数据不存在: {trial_group_name}")
                    return None
                
                trial_group = f[trial_group_name]
                
                # 加载EEG数据
                eeg_data = trial_group['eeg_data'][:]
                
                # 加载元数据
                metadata = {}
                for key in trial_group.attrs.keys():
                    metadata[key] = trial_group.attrs[key]
                
                result = (eeg_data, metadata)
                
                # 添加到缓存（如果缓存未满）
                if len(self._data_cache) < self._cache_size_limit:
                    self._data_cache[cache_key] = result
                
                return result
                
        except Exception as e:
            self.logger.error(f"加载试验原始数据失败: {e}")
            return None

    def _calculate_session_quality_stats(self, trials: List[Dict[str, Any]]) -> Dict[str, float]:
        """计算会话质量统计"""
        if not trials:
            return {}

        qualities = [trial['data_quality'] for trial in trials if trial['data_quality'] is not None]

        if not qualities:
            return {}

        return {
            'mean_quality': np.mean(qualities),
            'std_quality': np.std(qualities),
            'min_quality': np.min(qualities),
            'max_quality': np.max(qualities),
            'high_quality_ratio': sum(1 for q in qualities if q >= 0.8) / len(qualities)
        }

    def _balance_dataset(self, data: List[np.ndarray], labels: List[int],
                        metadata: List[Dict[str, Any]]) -> Tuple[List[np.ndarray], List[int], List[Dict[str, Any]]]:
        """平衡数据集类别"""
        try:
            # 统计各类别数量
            label_counts = {}
            label_indices = {}

            for i, label in enumerate(labels):
                if label not in label_counts:
                    label_counts[label] = 0
                    label_indices[label] = []
                label_counts[label] += 1
                label_indices[label].append(i)

            # 找到最小类别的数量
            min_count = min(label_counts.values())

            # 从每个类别中随机选择相同数量的样本
            balanced_indices = []
            for label, indices in label_indices.items():
                selected_indices = random.sample(indices, min_count)
                balanced_indices.extend(selected_indices)

            # 重新组织数据
            balanced_data = [data[i] for i in balanced_indices]
            balanced_labels = [labels[i] for i in balanced_indices]
            balanced_metadata = [metadata[i] for i in balanced_indices]

            self.logger.info(f"数据集平衡完成: 每类{min_count}个样本")

            return balanced_data, balanced_labels, balanced_metadata

        except Exception as e:
            self.logger.error(f"数据集平衡失败: {e}")
            return data, labels, metadata

    def _split_dataset(self, data: List[np.ndarray], labels: List[int],
                      metadata: List[Dict[str, Any]], test_split: float,
                      val_split: float) -> DatasetSplit:
        """分割数据集"""
        try:
            # 创建索引列表
            indices = list(range(len(data)))
            random.shuffle(indices)

            # 计算分割点
            n_total = len(indices)
            n_test = int(n_total * test_split)
            n_val = int(n_total * val_split)
            n_train = n_total - n_test - n_val

            # 分割索引
            test_indices = indices[:n_test]
            val_indices = indices[n_test:n_test + n_val]
            train_indices = indices[n_test + n_val:]

            # 创建分割后的数据集
            train_data = [data[i] for i in train_indices]
            train_labels = [labels[i] for i in train_indices]

            val_data = [data[i] for i in val_indices]
            val_labels = [labels[i] for i in val_indices]

            test_data = [data[i] for i in test_indices]
            test_labels = [labels[i] for i in test_indices]

            # 创建元数据
            split_metadata = {
                'total_samples': n_total,
                'train_samples': n_train,
                'val_samples': n_val,
                'test_samples': n_test,
                'train_label_dist': self._get_label_distribution(train_labels),
                'val_label_dist': self._get_label_distribution(val_labels),
                'test_label_dist': self._get_label_distribution(test_labels),
                'split_timestamp': datetime.now().isoformat()
            }

            self.logger.info(f"数据集分割完成: 训练{n_train}, 验证{n_val}, 测试{n_test}")

            return DatasetSplit(
                train_data, train_labels,
                val_data, val_labels,
                test_data, test_labels,
                split_metadata
            )

        except Exception as e:
            self.logger.error(f"数据集分割失败: {e}")
            return DatasetSplit([], [], [], [], [], [], {})

    def _get_label_distribution(self, labels: List[int]) -> Dict[str, int]:
        """获取标签分布"""
        distribution = {}
        for label in labels:
            label_str = str(label)
            distribution[label_str] = distribution.get(label_str, 0) + 1
        return distribution

    def clear_cache(self):
        """清空数据缓存"""
        self._data_cache.clear()
        self.logger.info("数据缓存已清空")

    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        return {
            'cache_size': len(self._data_cache),
            'cache_limit': self._cache_size_limit,
            'cache_usage': len(self._data_cache) / self._cache_size_limit if self._cache_size_limit > 0 else 0
        }

/* NK脑机接口系统 - 现代化医疗UI样式表 v2.0 (Qt优化版) */

/* ==================== 全局样式 ==================== */
* {
    font-family: "Microsoft YaHei UI", "Microsoft YaHei", "Segoe UI", "Roboto", Arial, sans-serif;
    outline: none;
}

/* 主窗口样式 - 医疗级渐变背景 */
QMainWindow {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                stop:0 #f8f9fa, stop:0.5 #e9ecef, stop:1 #f8f9fa);
    color: #2c3e50;
}

/* ==================== 菜单栏样式 ==================== */
QMenuBar {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #2E86AB, stop:1 #1e5f7a);
    color: white;
    border: none;
    padding: 6px;
    font-weight: 600;
    font-size: 13px;
}

QMenuBar::item {
    background-color: transparent;
    padding: 10px 16px;
    border-radius: 6px;
    margin: 2px 4px;
}

QMenuBar::item:selected {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #3498db, stop:1 #2980b9);
}

QMenuBar::item:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #1ABC9C, stop:1 #16a085);
}

QMenu {
    background-color: white;
    border: 1px solid #e0e6ed;
    border-radius: 8px;
    padding: 6px;
}

QMenu::item {
    padding: 10px 24px;
    border-radius: 6px;
    margin: 2px;
    color: #2c3e50;
}

QMenu::item:selected {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #3498db, stop:1 #2980b9);
    color: white;
}

/* ==================== 状态栏样式 ==================== */
QStatusBar {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #ffffff, stop:1 #f8f9fa);
    border-top: 2px solid #2E86AB;
    color: #2c3e50;
    font-size: 12px;
    font-weight: 500;
    padding: 4px;
}

QStatusBar::item {
    border: none;
    padding: 4px 8px;
    margin: 0 4px;
    border-radius: 4px;
}

QStatusBar QLabel {
    color: #495057;
    font-weight: 500;
}

/* ==================== 导航区域样式 ==================== */
QFrame#navigation_frame {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #2c3e50, stop:0.5 #34495e, stop:1 #2c3e50);
    border-right: 3px solid #2E86AB;
    border-radius: 0px 8px 8px 0px;
}

/* ==================== 导航按钮样式 ==================== */
QToolButton {
    background-color: transparent;
    border: none;
    color: #ecf0f1;
    padding: 16px 12px;
    text-align: center;
    border-radius: 10px;
    margin: 6px 8px;
    font-size: 13px;
    font-weight: 600;
    min-height: 40px;
}

QToolButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #3498db, stop:1 #2980b9);
    color: white;
}

QToolButton:checked {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #1ABC9C, stop:1 #16a085);
    color: white;
    border: 2px solid #ffffff;
}

QToolButton:disabled {
    color: #7f8c8d;
    background-color: transparent;
}

/* ==================== 按钮样式 ==================== */
QPushButton {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #3498db, stop:1 #2980b9);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 600;
    min-width: 100px;
    min-height: 36px;
}

QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #2980b9, stop:1 #21618c);
}

QPushButton:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #21618c, stop:1 #1a4f6b);
}

QPushButton:disabled {
    background: #bdc3c7;
    color: #7f8c8d;
}

/* 主要按钮样式 - 医疗绿 */
QPushButton.primary {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #1ABC9C, stop:1 #16a085);
}

QPushButton.primary:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #16a085, stop:1 #138d75);
}

/* 危险按钮样式 */
QPushButton.danger {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #e74c3c, stop:1 #c0392b);
}

QPushButton.danger:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #c0392b, stop:1 #a93226);
}

/* 成功按钮样式 */
QPushButton.success {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #27ae60, stop:1 #229954);
}

QPushButton.success:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #229954, stop:1 #1e8449);
}

/* 警告按钮样式 */
QPushButton.warning {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #f39c12, stop:1 #e67e22);
}

QPushButton.warning:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #e67e22, stop:1 #d35400);
}

/* ==================== 输入框样式 ==================== */
QLineEdit {
    background-color: white;
    border: 2px solid #e0e6ed;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 13px;
    color: #2c3e50;
    font-weight: 500;
    selection-background-color: #3498db;
    selection-color: white;
}

QLineEdit:focus {
    border-color: #2E86AB;
    background-color: #f8f9fa;
}

QLineEdit:hover {
    border-color: #3498db;
    background-color: #f8f9fa;
}

QLineEdit:disabled {
    background-color: #f1f3f4;
    color: #6c757d;
    border-color: #dee2e6;
}

/* 只读输入框样式 */
QLineEdit[readOnly="true"] {
    background-color: #f8f9fa;
    color: #6c757d;
    border-color: #dee2e6;
}

/* ==================== 文本区域样式 ==================== */
QTextEdit {
    background-color: white;
    border: 2px solid #e0e6ed;
    border-radius: 8px;
    padding: 12px;
    font-size: 13px;
    color: #2c3e50;
    font-weight: 500;
    selection-background-color: #3498db;
    selection-color: white;
}

QTextEdit:focus {
    border-color: #2E86AB;
    background-color: #f8f9fa;
}

QTextEdit:hover {
    border-color: #3498db;
    background-color: #f8f9fa;
}

/* 系统日志文本区域特殊样式 */
QTextEdit#system_log {
    background-color: #2c3e50;
    color: #ecf0f1;
    border: 2px solid #34495e;
    font-family: "Consolas", "Monaco", "Courier New", monospace;
    font-size: 12px;
}

/* ==================== 表格样式 ==================== */
QTableWidget {
    background-color: white;
    border: 2px solid #e0e6ed;
    border-radius: 10px;
    gridline-color: #f1f3f4;
    selection-background-color: #2E86AB;
    selection-color: white;
    alternate-background-color: #f8f9fa;
}

QTableWidget::item {
    padding: 12px 8px;
    border-bottom: 1px solid #f1f3f4;
    color: #495057;
    font-weight: 500;
}

QTableWidget::item:selected {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #2E86AB, stop:1 #3498db);
    color: white;
    font-weight: 600;
}

QTableWidget::item:hover {
    background-color: #e3f2fd;
    color: #2E86AB;
}

QHeaderView::section {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #2E86AB, stop:1 #1e5f7a);
    color: white;
    padding: 14px 10px;
    border: none;
    font-weight: 700;
    font-size: 13px;
    text-
    letter-spacing: 0.5px;
}

QHeaderView::section:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #3498db, stop:1 #2980b9);
}

QHeaderView::section:first {
    border-top-left-radius: 8px;
}

QHeaderView::section:last {
    border-top-right-radius: 8px;
}

/* ==================== 列表样式 ==================== */
QListWidget {
    background-color: white;
    border: 2px solid #e0e6ed;
    border-radius: 8px;
    padding: 8px;
}

QListWidget::item {
    padding: 12px 16px;
    border-radius: 6px;
    margin: 2px;
    color: #495057;
    font-weight: 500;
    border: 1px solid transparent;
}

QListWidget::item:selected {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #2E86AB, stop:1 #3498db);
    color: white;
    font-weight: 600;
    border: 1px solid #2E86AB;
}

QListWidget::item:hover {
    background-color: #e3f2fd;
    color: #2E86AB;
    border: 1px solid #2E86AB;
}

/* ==================== 组合框样式 ==================== */
QComboBox {
    background-color: white;
    border: 2px solid #e0e6ed;
    border-radius: 8px;
    padding: 10px 16px;
    font-size: 13px;
    color: #2c3e50;
    font-weight: 500;
    min-width: 140px;
    min-height: 20px;
}

QComboBox:focus {
    border-color: #2E86AB;
    background-color: #f8f9fa;
}

QComboBox:hover {
    border-color: #3498db;
    background-color: #f8f9fa;
}

QComboBox::drop-down {
    border: none;
    width: 30px;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #f8f9fa, stop:1 #e9ecef);
}

QComboBox::down-arrow {
    image: none;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #6c757d;
    width: 0;
    height: 0;
}

QComboBox QAbstractItemView {
    background-color: white;
    border: 2px solid #e0e6ed;
    border-radius: 8px;
    selection-background-color: #2E86AB;
    selection-color: white;
    padding: 4px;
}

QComboBox QAbstractItemView::item {
    padding: 10px 16px;
    border-radius: 4px;
    margin: 1px;
}

QComboBox QAbstractItemView::item:hover {
    background-color: #e3f2fd;
    color: #2E86AB;
}

/* ==================== 复选框样式 ==================== */
QCheckBox {
    color: #2c3e50;
    font-size: 13px;
    font-weight: 500;
    spacing: 10px;
}

QCheckBox::indicator {
    width: 20px;
    height: 20px;
    border: 2px solid #e0e6ed;
    border-radius: 6px;
    background-color: white;
}

QCheckBox::indicator:checked {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #2E86AB, stop:1 #3498db);
    border-color: #2E86AB;
    image: none;
}

QCheckBox::indicator:hover {
    border-color: #2E86AB;
    background-color: #f8f9fa;
}

QCheckBox::indicator:disabled {
    background-color: #f1f3f4;
    border-color: #dee2e6;
}

/* ==================== 分组框样式 ==================== */
QGroupBox {
    font-weight: 700;
    font-size: 14px;
    border: 2px solid #e0e6ed;
    border-radius: 12px;
    margin-top: 16px;
    padding-top: 16px;
    color: #2c3e50;
    background-color: white;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 16px;
    padding: 4px 12px;
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #2E86AB, stop:1 #3498db);
    color: white;
    border-radius: 6px;
    font-weight: 700;
    text-
    letter-spacing: 0.5px;
}

/* ==================== 进度条样式 ==================== */
QProgressBar {
    border: 2px solid #e0e6ed;
    border-radius: 10px;
    text-align: center;
    font-weight: 600;
    font-size: 12px;
    color: #2c3e50;
    background-color: #f8f9fa;
    min-height: 20px;
}

QProgressBar::chunk {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #1ABC9C, stop:0.5 #2E86AB, stop:1 #3498db);
    border-radius: 8px;
}

/* ==================== 滚动条样式 ==================== */
QScrollBar:vertical {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #f8f9fa, stop:1 #e9ecef);
    width: 14px;
    border-radius: 7px;
    border: 1px solid #e0e6ed;
}

QScrollBar::handle:vertical {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #2E86AB, stop:1 #3498db);
    border-radius: 6px;
    min-height: 30px;
    margin: 1px;
}

QScrollBar::handle:vertical:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #3498db, stop:1 #2980b9);
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;
    background: transparent;
}

QScrollBar:horizontal {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #f8f9fa, stop:1 #e9ecef);
    height: 14px;
    border-radius: 7px;
    border: 1px solid #e0e6ed;
}

QScrollBar::handle:horizontal {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #2E86AB, stop:1 #3498db);
    border-radius: 6px;
    min-width: 30px;
    margin: 1px;
}

QScrollBar::handle:horizontal:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #3498db, stop:1 #2980b9);
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:horizontal {
    width: 0px;
    background: transparent;
}

/* ==================== 医疗设备专用样式 ==================== */

/* 标签页样式 */
QTabWidget::pane {
    border: 2px solid #e0e6ed;
    border-radius: 8px;
    background-color: white;
}

QTabWidget::tab-bar {
    alignment: center;
}

QTabBar::tab {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #f8f9fa, stop:1 #e9ecef);
    border: 2px solid #e0e6ed;
    padding: 12px 24px;
    margin-right: 4px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    font-weight: 600;
    color: #6c757d;
}

QTabBar::tab:selected {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #2E86AB, stop:1 #1e5f7a);
    color: white;
    border-bottom-color: #2E86AB;
}

QTabBar::tab:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #e3f2fd, stop:1 #bbdefb);
    color: #2E86AB;
}

/* 分割器样式 */
QSplitter::handle {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #e0e6ed, stop:0.5 #2E86AB, stop:1 #e0e6ed);
    border-radius: 2px;
}

QSplitter::handle:horizontal {
    width: 4px;
    margin: 4px 0;
}

QSplitter::handle:vertical {
    height: 4px;
    margin: 0 4px;
}

/* 数值输入框样式 */
QSpinBox, QDoubleSpinBox {
    background-color: white;
    border: 2px solid #e0e6ed;
    border-radius: 8px;
    padding: 10px 12px;
    font-size: 13px;
    font-weight: 600;
    color: #2c3e50;
    min-width: 80px;
}

QSpinBox:focus, QDoubleSpinBox:focus {
    border-color: #2E86AB;
    background-color: #f8f9fa;
}

QSpinBox::up-button, QDoubleSpinBox::up-button {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #3498db, stop:1 #2980b9);
    border-top-right-radius: 6px;
    width: 20px;
}

QSpinBox::down-button, QDoubleSpinBox::down-button {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #3498db, stop:1 #2980b9);
    border-bottom-right-radius: 6px;
    width: 20px;
}

QSpinBox::up-arrow, QDoubleSpinBox::up-arrow {
    image: none;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 4px solid white;
    width: 0;
    height: 0;
}

QSpinBox::down-arrow, QDoubleSpinBox::down-arrow {
    image: none;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 4px solid white;
    width: 0;
    height: 0;
}

/* ==================== 医疗设备状态指示器 ==================== */

/* 设备连接状态标签 */
QLabel.device_status {
    font-weight: 700;
    font-size: 12px;
    padding: 6px 12px;
    border-radius: 6px;
    border: 2px solid transparent;
}

QLabel.device_connected {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #27ae60, stop:1 #2ecc71);
    color: white;
    border-color: #27ae60;
}

QLabel.device_disconnected {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #e74c3c, stop:1 #ec7063);
    color: white;
    border-color: #e74c3c;
}

QLabel.device_connecting {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #f39c12, stop:1 #f7dc6f);
    color: white;
    border-color: #f39c12;
}

/* 实时数据显示区域 */
QFrame.realtime_display {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #2c3e50, stop:1 #34495e);
    border: 3px solid #2E86AB;
    border-radius: 12px;
}

/* 脑电信号显示区域 */
QFrame.eeg_display {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #1a1a1a, stop:1 #2c3e50);
    border: 2px solid #1ABC9C;
    border-radius: 10px;
}

/* 控制面板区域 */
QFrame.control_panel {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #ffffff, stop:0.5 #f8f9fa, stop:1 #ffffff);
    border: 2px solid #e0e6ed;
    border-radius: 12px;
    padding: 16px;
}

/* 电极阻抗显示 */
QLabel.electrode_impedance {
    font-weight: 700;
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 4px;
    min-width: 40px;
    text-align: center;
}

QLabel.impedance_good {
    background-color: #27ae60;
    color: white;
}

QLabel.impedance_warning {
    background-color: #f39c12;
    color: white;
}

QLabel.impedance_bad {
    background-color: #e74c3c;
    color: white;
}

QLabel.impedance_unknown {
    background-color: #95a5a6;
    color: white;
}

/* 治疗状态指示器 */
QLabel.treatment_status {
    font-weight: 700;
    font-size: 14px;
    padding: 8px 16px;
    border-radius: 8px;
    border: 2px solid transparent;
}

QLabel.treatment_active {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #1ABC9C, stop:1 #16a085);
    color: white;
    border-color: #1ABC9C;
}

QLabel.treatment_paused {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #f39c12, stop:1 #e67e22);
    color: white;
    border-color: #f39c12;
}

QLabel.treatment_stopped {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #95a5a6, stop:1 #7f8c8d);
    color: white;
    border-color: #95a5a6;
}

/* ==================== 对话框样式 ==================== */
QDialog {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #f8f9fa, stop:0.5 #ffffff, stop:1 #f8f9fa);
    border: 2px solid #2E86AB;
    border-radius: 12px;
}

QMessageBox {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #f8f9fa, stop:0.5 #ffffff, stop:1 #f8f9fa);
    border: 2px solid #2E86AB;
    border-radius: 12px;
}

QMessageBox QLabel {
    color: #2c3e50;
    font-size: 13px;
    font-weight: 500;
    padding: 8px;
}

/* ==================== 滑块样式 ==================== */
QSlider::groove:horizontal {
    border: 1px solid #e0e6ed;
    height: 8px;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #f8f9fa, stop:1 #e9ecef);
    border-radius: 4px;
}

QSlider::handle:horizontal {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #2E86AB, stop:1 #3498db);
    border: 2px solid #ffffff;
    width: 20px;
    height: 20px;
    margin: -8px 0;
    border-radius: 12px;
}

QSlider::handle:horizontal:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #3498db, stop:1 #2980b9);
}

QSlider::sub-page:horizontal {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #2E86AB, stop:1 #3498db);
    border-radius: 4px;
}

/* ==================== 日期时间选择器样式 ==================== */
QDateEdit, QTimeEdit, QDateTimeEdit {
    background-color: white;
    border: 2px solid #e0e6ed;
    border-radius: 8px;
    padding: 10px 12px;
    font-size: 13px;
    font-weight: 500;
    color: #2c3e50;
    min-width: 120px;
}

QDateEdit:focus, QTimeEdit:focus, QDateTimeEdit:focus {
    border-color: #2E86AB;
    background-color: #f8f9fa;
}

QDateEdit::drop-down, QTimeEdit::drop-down, QDateTimeEdit::drop-down {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #3498db, stop:1 #2980b9);
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
    width: 20px;
}

/* ==================== 工具提示样式 ==================== */
QToolTip {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #2c3e50, stop:1 #34495e);
    color: white;
    border: 2px solid #2E86AB;
    border-radius: 8px;
    padding: 8px 12px;
    font-size: 12px;
    font-weight: 500;
}

/* ==================== 特殊医疗标签样式 ==================== */
QLabel.medical_title {
    font-size: 18px;
    font-weight: 700;
    color: #2E86AB;
    padding: 8px 0;
    border-bottom: 2px solid #2E86AB;
    margin-bottom: 8px;
}

QLabel.medical_subtitle {
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    padding: 4px 0;
}

QLabel.medical_value {
    font-size: 16px;
    font-weight: 700;
    color: #2c3e50;
    background-color: #f8f9fa;
    border: 1px solid #e0e6ed;
    border-radius: 6px;
    padding: 8px 12px;
    min-width: 60px;
    text-align: center;
}

/* ==================== 特殊效果和动画替代 ==================== */
/* 由于Qt不支持CSS3动画，使用颜色变化来模拟效果 */

/* 悬停效果增强 */
QPushButton:hover, QToolButton:hover {
    border: 1px solid #ffffff;
}

/* 焦点效果增强 */
QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
    border-width: 3px;
}

/* 选中效果增强 */
QTableWidget::item:selected, QListWidget::item:selected {
    border: 2px solid #ffffff;
}

/* ==================== 响应式设计支持 ==================== */
/* 为不同尺寸的组件提供适配 */

/* 小尺寸按钮 */
QPushButton.small {
    padding: 6px 12px;
    font-size: 11px;
    min-height: 24px;
    min-width: 60px;
}

/* 大尺寸按钮 */
QPushButton.large {
    padding: 16px 32px;
    font-size: 15px;
    min-height: 48px;
    min-width: 120px;
}

/* 紧凑布局 */
QWidget.compact {
    margin: 2px;
    padding: 4px;
}

/* 宽松布局 */
QWidget.spacious {
    margin: 8px;
    padding: 16px;
}

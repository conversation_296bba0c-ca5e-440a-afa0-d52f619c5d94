#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能优化模块
Performance Optimizer Module

作者: AI Assistant
版本: 1.0.0
"""

import time
import threading
import logging
import psutil
import gc
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from collections import deque
from concurrent.futures import ThreadPoolExecutor, as_completed
import weakref


@dataclass
class PerformanceMetrics:
    """性能指标"""
    cpu_usage: float
    memory_usage: float
    memory_available: float
    response_time: float
    throughput: float
    timestamp: float


class PerformanceMonitor:
    """性能监控器"""

    def __init__(self, max_history: int = 1000):
        self.max_history = max_history
        self.metrics_history = deque(maxlen=max_history)
        self.logger = logging.getLogger(__name__)
        self.monitoring = False
        self.monitor_thread = None
        self.monitor_interval = 1.0  # 监控间隔（秒）

        # 性能阈值
        self.cpu_threshold = 90.0  # CPU使用率阈值（调整为90%，减少误报）
        self.memory_threshold = 85.0  # 内存使用率阈值
        self.response_time_threshold = 2.0  # 响应时间阈值（秒）

        # 回调函数
        self.performance_callbacks = []

    def start_monitoring(self):
        """开始性能监控"""
        if self.monitoring:
            return

        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        self.logger.info("性能监控已启动")

    def stop_monitoring(self):
        """停止性能监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2.0)
        self.logger.info("性能监控已停止")

    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                metrics = self._collect_metrics()
                self.metrics_history.append(metrics)

                # 检查性能阈值
                self._check_thresholds(metrics)

                # 调用回调函数
                for callback in self.performance_callbacks:
                    try:
                        callback(metrics)
                    except Exception as e:
                        self.logger.error(f"性能回调函数执行失败: {e}")

                time.sleep(self.monitor_interval)

            except Exception as e:
                self.logger.error(f"性能监控异常: {e}")
                time.sleep(self.monitor_interval)

    def _collect_metrics(self) -> PerformanceMetrics:
        """收集性能指标"""
        try:
            # CPU使用率
            cpu_usage = psutil.cpu_percent(interval=0.1)

            # 内存使用情况
            memory = psutil.virtual_memory()
            memory_usage = memory.percent
            memory_available = memory.available / (1024 * 1024 * 1024)  # GB

            # 响应时间（简化测量）
            start_time = time.time()
            # 执行一个简单操作来测量响应时间
            _ = [i for i in range(1000)]
            response_time = time.time() - start_time

            # 吞吐量（简化计算）
            throughput = 1.0 / max(response_time, 0.001)

            return PerformanceMetrics(
                cpu_usage=cpu_usage,
                memory_usage=memory_usage,
                memory_available=memory_available,
                response_time=response_time,
                throughput=throughput,
                timestamp=time.time()
            )

        except Exception as e:
            self.logger.error(f"收集性能指标失败: {e}")
            return PerformanceMetrics(0, 0, 0, 0, 0, time.time())

    def _check_thresholds(self, metrics: PerformanceMetrics):
        """检查性能阈值"""
        warnings = []

        if metrics.cpu_usage > self.cpu_threshold:
            warnings.append(f"CPU使用率过高: {metrics.cpu_usage:.1f}%")

        if metrics.memory_usage > self.memory_threshold:
            warnings.append(f"内存使用率过高: {metrics.memory_usage:.1f}%")

        if metrics.response_time > self.response_time_threshold:
            warnings.append(f"响应时间过长: {metrics.response_time:.3f}s")

        for warning in warnings:
            self.logger.warning(warning)

    def add_callback(self, callback: Callable[[PerformanceMetrics], None]):
        """添加性能回调函数"""
        self.performance_callbacks.append(callback)

    def get_current_metrics(self) -> Optional[PerformanceMetrics]:
        """获取当前性能指标"""
        if self.metrics_history:
            return self.metrics_history[-1]
        return None

    def get_average_metrics(self, duration: int = 60) -> Optional[PerformanceMetrics]:
        """获取平均性能指标"""
        if not self.metrics_history:
            return None

        current_time = time.time()
        recent_metrics = [
            m for m in self.metrics_history
            if current_time - m.timestamp <= duration
        ]

        if not recent_metrics:
            return None

        return PerformanceMetrics(
            cpu_usage=sum(m.cpu_usage for m in recent_metrics) / len(recent_metrics),
            memory_usage=sum(m.memory_usage for m in recent_metrics) / len(recent_metrics),
            memory_available=sum(m.memory_available for m in recent_metrics) / len(recent_metrics),
            response_time=sum(m.response_time for m in recent_metrics) / len(recent_metrics),
            throughput=sum(m.throughput for m in recent_metrics) / len(recent_metrics),
            timestamp=current_time
        )


class MemoryOptimizer:
    """内存优化器"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.weak_references = weakref.WeakSet()

        # 内存清理阈值
        self.memory_threshold = 80.0  # 内存使用率阈值
        self.cleanup_interval = 300  # 清理间隔（秒）

        # 自动清理线程
        self.auto_cleanup = True
        self.cleanup_thread = None

        self.start_auto_cleanup()

    def start_auto_cleanup(self):
        """启动自动内存清理"""
        if self.cleanup_thread and self.cleanup_thread.is_alive():
            return

        self.auto_cleanup = True
        self.cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
        self.cleanup_thread.start()
        self.logger.info("自动内存清理已启动")

    def stop_auto_cleanup(self):
        """停止自动内存清理"""
        self.auto_cleanup = False
        if self.cleanup_thread:
            self.cleanup_thread.join(timeout=2.0)
        self.logger.info("自动内存清理已停止")

    def _cleanup_loop(self):
        """清理循环"""
        while self.auto_cleanup:
            try:
                memory = psutil.virtual_memory()
                if memory.percent > self.memory_threshold:
                    self.cleanup_memory()

                time.sleep(self.cleanup_interval)

            except Exception as e:
                self.logger.error(f"自动内存清理异常: {e}")
                time.sleep(self.cleanup_interval)

    def cleanup_memory(self):
        """清理内存"""
        try:
            # 强制垃圾回收
            collected = gc.collect()

            # 清理弱引用
            self.weak_references.clear()

            # 获取清理后的内存使用情况
            memory = psutil.virtual_memory()

            self.logger.info(f"内存清理完成 - 回收对象: {collected}, "
                           f"当前内存使用率: {memory.percent:.1f}%")

        except Exception as e:
            self.logger.error(f"内存清理失败: {e}")

    def register_object(self, obj):
        """注册对象用于内存管理"""
        self.weak_references.add(obj)

    def get_memory_info(self) -> Dict[str, Any]:
        """获取内存信息"""
        try:
            memory = psutil.virtual_memory()
            return {
                'total': memory.total / (1024 * 1024 * 1024),  # GB
                'available': memory.available / (1024 * 1024 * 1024),  # GB
                'used': memory.used / (1024 * 1024 * 1024),  # GB
                'percentage': memory.percent,
                'registered_objects': len(self.weak_references)
            }
        except Exception as e:
            self.logger.error(f"获取内存信息失败: {e}")
            return {}


class TaskScheduler:
    """任务调度器"""

    def __init__(self, max_workers: int = 4):
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.logger = logging.getLogger(__name__)

        # 任务队列
        self.pending_tasks = deque()
        self.running_tasks = {}
        self.completed_tasks = deque(maxlen=1000)

        # 优先级队列
        self.high_priority_tasks = deque()
        self.normal_priority_tasks = deque()
        self.low_priority_tasks = deque()

    def submit_task(self, func: Callable, *args, priority: str = 'normal', **kwargs):
        """提交任务"""
        task_id = f"task_{time.time()}_{id(func)}"
        task_info = {
            'id': task_id,
            'func': func,
            'args': args,
            'kwargs': kwargs,
            'priority': priority,
            'submit_time': time.time()
        }

        # 根据优先级添加到相应队列
        if priority == 'high':
            self.high_priority_tasks.append(task_info)
        elif priority == 'low':
            self.low_priority_tasks.append(task_info)
        else:
            self.normal_priority_tasks.append(task_info)

        # 尝试执行任务
        self._execute_next_task()

        return task_id

    def _execute_next_task(self):
        """执行下一个任务"""
        if len(self.running_tasks) >= self.max_workers:
            return

        # 按优先级选择任务
        task_info = None
        if self.high_priority_tasks:
            task_info = self.high_priority_tasks.popleft()
        elif self.normal_priority_tasks:
            task_info = self.normal_priority_tasks.popleft()
        elif self.low_priority_tasks:
            task_info = self.low_priority_tasks.popleft()

        if task_info:
            future = self.executor.submit(
                task_info['func'],
                *task_info['args'],
                **task_info['kwargs']
            )

            task_info['future'] = future
            task_info['start_time'] = time.time()
            self.running_tasks[task_info['id']] = task_info

            # 添加完成回调
            future.add_done_callback(lambda f: self._task_completed(task_info['id']))

    def _task_completed(self, task_id: str):
        """任务完成回调"""
        if task_id in self.running_tasks:
            task_info = self.running_tasks.pop(task_id)
            task_info['end_time'] = time.time()
            task_info['duration'] = task_info['end_time'] - task_info['start_time']

            self.completed_tasks.append(task_info)

            # 尝试执行下一个任务
            self._execute_next_task()

    def get_task_status(self) -> Dict[str, Any]:
        """获取任务状态"""
        return {
            'running_tasks': len(self.running_tasks),
            'pending_high': len(self.high_priority_tasks),
            'pending_normal': len(self.normal_priority_tasks),
            'pending_low': len(self.low_priority_tasks),
            'completed_tasks': len(self.completed_tasks),
            'max_workers': self.max_workers
        }

    def shutdown(self):
        """关闭调度器"""
        self.executor.shutdown(wait=True)
        self.logger.info("任务调度器已关闭")


class PerformanceOptimizer:
    """性能优化器主类"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # 初始化组件
        self.monitor = PerformanceMonitor()
        self.memory_optimizer = MemoryOptimizer()
        self.task_scheduler = TaskScheduler()

        # 优化配置
        self.optimization_enabled = True
        self.auto_optimization = True

        # 注册性能回调
        self.monitor.add_callback(self._performance_callback)

    def start(self):
        """启动性能优化器"""
        self.monitor.start_monitoring()
        self.memory_optimizer.start_auto_cleanup()
        self.logger.info("性能优化器已启动")

    def stop(self):
        """停止性能优化器"""
        self.monitor.stop_monitoring()
        self.memory_optimizer.stop_auto_cleanup()
        self.task_scheduler.shutdown()
        self.logger.info("性能优化器已停止")

    def _performance_callback(self, metrics: PerformanceMetrics):
        """性能回调函数"""
        if not self.auto_optimization:
            return

        # 根据性能指标自动优化
        if metrics.memory_usage > 85.0:
            self.memory_optimizer.cleanup_memory()

        if metrics.cpu_usage > 90.0:
            # 降低任务调度器的工作线程数
            if self.task_scheduler.max_workers > 1:
                self.task_scheduler.max_workers -= 1
                self.logger.info(f"降低工作线程数到: {self.task_scheduler.max_workers}")

        elif metrics.cpu_usage < 50.0:
            # 增加任务调度器的工作线程数
            if self.task_scheduler.max_workers < 8:
                self.task_scheduler.max_workers += 1
                self.logger.info(f"增加工作线程数到: {self.task_scheduler.max_workers}")

    def optimize_database_queries(self):
        """优化数据库查询"""
        # 这里可以实现数据库查询优化逻辑
        pass

    def optimize_ui_rendering(self):
        """优化UI渲染"""
        # 这里可以实现UI渲染优化逻辑
        pass

    def get_optimization_report(self) -> Dict[str, Any]:
        """获取优化报告"""
        current_metrics = self.monitor.get_current_metrics()
        average_metrics = self.monitor.get_average_metrics()
        memory_info = self.memory_optimizer.get_memory_info()
        task_status = self.task_scheduler.get_task_status()

        return {
            'current_performance': current_metrics.__dict__ if current_metrics else {},
            'average_performance': average_metrics.__dict__ if average_metrics else {},
            'memory_info': memory_info,
            'task_status': task_status,
            'optimization_enabled': self.optimization_enabled,
            'auto_optimization': self.auto_optimization
        }

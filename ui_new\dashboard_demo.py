#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
仪表板演示 - 展示重构后的精美设计
Dashboard Demo - Showcase Refined Design

作者: AI Assistant
版本: 2.0.0
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QWidget, QVBoxLayout, QHBoxLayout
from PySide6.QtCore import Qt, QTimer

# 导入新UI组件
from ui_new.pages.dashboard_page import DashboardPage
from ui_new.themes.theme_manager import ThemeManager


class DashboardDemo(QWidget):
    """仪表板演示窗口"""
    
    def __init__(self):
        super().__init__()
        self.theme_manager = ThemeManager()
        self.dashboard_page = None
        self.init_ui()
        
        # 应用默认主题
        self.theme_manager.apply_theme_styles()
        
        # 启动数据模拟
        self.start_data_simulation()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("NK脑机接口康复系统 - 仪表板演示")
        self.setMinimumSize(1400, 900)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建仪表板页面
        self.dashboard_page = DashboardPage()
        
        # 模拟设置业务组件
        self.dashboard_page.db_manager = None  # 演示模式
        self.dashboard_page.auth_manager = None  # 演示模式
        
        main_layout.addWidget(self.dashboard_page)
        
        # 连接信号
        self.setup_connections()
    
    def setup_connections(self):
        """设置信号连接"""
        # 连接仪表板信号
        if self.dashboard_page:
            self.dashboard_page.recording_started.connect(self.on_recording_started)
            self.dashboard_page.training_started.connect(self.on_training_started)
    
    def start_data_simulation(self):
        """启动数据模拟"""
        # 模拟页面显示
        self.dashboard_page.show_page()
        
        # 启动定时器模拟数据更新
        self.data_timer = QTimer()
        self.data_timer.timeout.connect(self.update_demo_data)
        self.data_timer.start(3000)  # 每3秒更新一次
    
    def update_demo_data(self):
        """更新演示数据"""
        if self.dashboard_page:
            self.dashboard_page.update_simulated_data()
    
    def on_recording_started(self):
        """记录开始处理"""
        print("演示：脑电记录已开始")
    
    def on_training_started(self):
        """训练开始处理"""
        print("演示：康复训练已开始")
    
    def keyPressEvent(self, event):
        """键盘事件处理"""
        if event.key() == Qt.Key_T:
            # 按T键切换主题
            self.theme_manager.toggle_theme()
            print(f"主题已切换到: {self.theme_manager.get_current_theme()}")
        elif event.key() == Qt.Key_R:
            # 按R键开始/停止记录
            if self.dashboard_page:
                self.dashboard_page.start_recording()
        elif event.key() == Qt.Key_S:
            # 按S键停止记录
            if self.dashboard_page:
                self.dashboard_page.pause_recording()
        
        super().keyPressEvent(event)


def main():
    """主函数"""
    print("=" * 60)
    print("NK脑机接口康复系统 - 仪表板演示")
    print("基于complete_bci_system.html的精美设计")
    print("=" * 60)
    
    try:
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("NK脑机接口康复系统 - 仪表板演示")
        
        # 创建演示窗口
        demo = DashboardDemo()
        demo.show()
        
        print("仪表板演示已启动")
        print("\n✨ 新功能展示：")
        print("• 精美的脑电可视化（带脉冲动画）")
        print("• 现代化指标卡片（带发光效果）")
        print("• 渐变色按钮和卡片设计")
        print("• 实时数据更新动画")
        print("• 专业的医疗设备界面风格")
        
        print("\n🎮 交互演示：")
        print("• 点击'开始记录'按钮 - 启动脑电可视化动画")
        print("• 点击'暂停'按钮 - 停止可视化动画")
        print("• 观察指标卡片的数据更新动画")
        print("• 按T键 - 切换主题（医疗/科技）")
        print("• 按R键 - 开始记录")
        print("• 按S键 - 停止记录")
        
        print("\n关闭窗口退出演示")
        
        # 运行应用程序
        return app.exec()
        
    except Exception as e:
        print(f"演示启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())

# 全新机器部署指南

## 🎯 部署策略选择

### 方案一：完全重新初始化（推荐）
**适用场景**：全新机器部署，不需要保留任何现有数据
**优点**：最干净、最安全、最简单
**缺点**：会丢失所有现有数据

### 方案二：选择性数据迁移
**适用场景**：需要保留部分重要数据（如患者信息、治疗记录）
**优点**：保留重要数据
**缺点**：需要手动清理测试数据

### 方案三：完整数据迁移
**适用场景**：需要完整迁移所有数据
**优点**：数据完整保留
**缺点**：会带入测试数据，需要后续清理

## 🚀 推荐方案：完全重新初始化

### 1. 系统自动初始化机制

您的系统具有完善的自动初始化功能：

```python
# 系统启动时自动执行
def initialize(self) -> bool:
    # 1. 创建数据库目录
    self.db_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 2. 创建所有数据库表
    self._create_tables()
    
    # 3. 初始化基础数据
    self._initialize_base_data()
```

### 2. 自动创建的默认数据

**医院信息**：
- ID: 1, 名称: "默认医院", 科室: "康复科", 设备ID: "NK001"

**管理员账户**：
- 用户名: admin
- 密码: admin123
- 角色: 管理员

**默认医生**：
- 张医生（主任医师）
- 李医生（副主任医师）
- 王医生（主治医师）
- 刘医生（主治医师）
- 陈医生（住院医师）

### 3. 部署步骤

#### 步骤1：准备新机器环境
```bash
# 1. 复制系统文件到新机器
# 2. 确保Python环境和依赖包已安装
pip install -r requirements.txt

# 3. 删除旧数据库文件（如果存在）
# 删除 data/nk_system.db
# 删除 data/backup/ 目录下的备份文件
```

#### 步骤2：首次启动系统
```bash
# 直接启动系统
python main.py
```

#### 步骤3：系统自动初始化
- ✅ 自动创建 `data/` 目录
- ✅ 自动创建 `nk_system.db` 数据库文件
- ✅ 自动创建所有必需的数据库表
- ✅ 自动插入默认基础数据

#### 步骤4：配置您的医院信息
1. 使用默认账户登录（admin/admin123）
2. 进入设置界面
3. 修改医院信息为您的实际信息
4. 添加您的医生信息
5. 创建您的操作员账户

## 🧹 当前系统测试数据清理

如果您想在当前系统中清理测试数据，我为您提供了专门的清理工具：

### 清理脚本使用方法

```bash
# 1. 清理所有测试数据（推荐）
python clean_all_test_data.py

# 2. 只清理特定类型的测试数据
python clean_specific_test_data.py
```

### 手动清理步骤

1. **备份当前数据库**
```bash
cp data/nk_system.db data/nk_system_backup.db
```

2. **删除测试患者**
```sql
DELETE FROM bingren WHERE name LIKE '%测试%' OR name LIKE '%test%';
```

3. **删除测试治疗记录**
```sql
DELETE FROM zhiliao WHERE treat_number LIKE '%test%';
```

4. **清理孤立的脑电数据**
```sql
DELETE FROM eeg_raw_data WHERE patient_id NOT IN (SELECT bianhao FROM bingren);
DELETE FROM eeg_sessions WHERE patient_id NOT IN (SELECT bianhao FROM bingren);
```

## 🛠️ 专用清理工具

我已为您创建了专门的测试数据清理工具：

### 使用清理工具

```bash
# 清理所有测试数据
python clean_all_test_data.py
```

**清理工具功能**：
- ✅ 自动备份当前数据库
- ✅ 识别所有测试数据
- ✅ 安全删除测试数据
- ✅ 验证清理结果
- ✅ 保护用户真实数据

## 🎯 最佳部署方案推荐

### 方案选择建议

**如果您的系统中有重要的患者数据和治疗记录**：
→ 使用方案二（选择性数据迁移）

**如果您的系统主要是测试数据**：
→ 使用方案一（完全重新初始化）

### 方案一：完全重新初始化（最简单）

#### 步骤详解

**1. 准备新机器**
```bash
# 复制系统文件（不包括data目录）
cp -r /path/to/system /new/machine/path
# 但不要复制 data/ 目录
```

**2. 首次启动**
```bash
cd /new/machine/path
python main.py
```

**3. 系统自动完成**
- 创建 `data/nk_system.db`
- 创建所有数据库表
- 插入默认数据：
  - 默认医院（ID=1）
  - 管理员账户（admin/admin123）
  - 5个默认医生

**4. 配置您的信息**
- 登录系统（admin/admin123）
- 修改医院信息为您的实际信息
- 添加您的医生和操作员

### 方案二：选择性数据迁移

#### 步骤详解

**1. 在当前机器上清理测试数据**
```bash
# 运行清理工具
python clean_all_test_data.py
```

**2. 备份清理后的数据库**
```bash
# 备份数据库文件
cp data/nk_system.db clean_database_backup.db
```

**3. 复制到新机器**
```bash
# 复制整个系统目录到新机器
cp -r /path/to/system /new/machine/path
```

**4. 在新机器上启动**
```bash
cd /new/machine/path
python main.py
```

## 📋 部署检查清单

### 新机器环境准备
- [ ] Python 3.8+ 已安装
- [ ] 必需依赖包已安装
- [ ] 系统文件已复制
- [ ] 权限设置正确

### 数据库处理
- [ ] 选择了合适的部署方案
- [ ] 备份了重要数据（如果需要）
- [ ] 清理了测试数据（如果选择迁移）
- [ ] 验证了数据库完整性

### 系统配置
- [ ] 首次启动成功
- [ ] 默认账户可以登录
- [ ] 医院信息已配置
- [ ] 医生信息已添加
- [ ] 操作员账户已创建

### 功能验证
- [ ] 患者管理功能正常
- [ ] 治疗功能正常
- [ ] 数据上传功能正常
- [ ] 脑电设备连接正常

## 🔧 故障排除

### 常见问题

**1. 数据库初始化失败**
```bash
# 检查权限
chmod 755 data/
# 手动删除数据库文件重新初始化
rm data/nk_system.db
python main.py
```

**2. 默认账户无法登录**
```bash
# 运行数据库修复工具
python fix_database.py
```

**3. 医院ID冲突**
```bash
# 检查医院信息
python -c "
from core.database_manager import DatabaseManager
db = DatabaseManager()
db.initialize()
hospitals = db.execute_query('SELECT * FROM yiyuan')
for h in hospitals: print(h)
"
```

## 📞 技术支持

如果在部署过程中遇到问题：

1. **检查日志文件**：`logs/` 目录下的日志文件
2. **运行诊断工具**：`python check_system_status.py`
3. **数据库修复**：`python fix_database.py`
4. **重新初始化**：删除 `data/` 目录后重新启动

## 🎉 部署完成确认

部署成功的标志：
- ✅ 系统正常启动
- ✅ 可以使用默认账户登录
- ✅ 医院信息显示正确
- ✅ 患者管理功能正常
- ✅ 治疗功能可以使用
- ✅ 数据上传平台连接正常

**恭喜！您的系统已成功部署到新机器上！**

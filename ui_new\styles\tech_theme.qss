/* 科技主题样式文件 - Tech Theme Styles */
/* 未来科技风格，深色背景+荧光效果 */

/* 主窗口样式 */
QMainWindow {
    background-color: #0f172a;
    color: #f1f5f9;
}

QMainWindow#ModernMainWindow {
    background-color: #0f172a;
    border: none;
}

/* 侧边栏样式 */
QWidget#sidebar {
    background-color: #1e293b;
    border-right: 1px solid #475569;
    min-width: 280px;
    max-width: 280px;
}

QWidget#sidebar[collapsed="true"] {
    min-width: 80px;
    max-width: 80px;
}

/* 侧边栏头部 */
QWidget#sidebar_header {
    background-color: #1e293b;
    border-bottom: 1px solid #475569;
    padding: 24px 20px;
}

/* Logo样式 */
QLabel#logo {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                stop:0 #06b6d4,
                                stop:1 #00d4ff);
    border-radius: 12px;
    color: white;
    font-weight: 700;
    font-size: 20px;
    min-width: 48px;
    max-width: 48px;
    min-height: 48px;
    max-height: 48px;
}

QLabel#logo_text {
    font-size: 18px;
    font-weight: 600;
    color: #f1f5f9;
    margin-left: 12px;
}

/* 导航菜单样式 */
QWidget#nav_menu {
    background-color: transparent;
    padding: 20px 0px;
}

/* 导航分组标题 */
QLabel.nav-title {
    color: #94a3b8;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 0px 20px 12px 20px;
    margin-bottom: 8px;
}

/* 导航项样式 */
QPushButton.nav-item {
    background-color: transparent;
    border: none;
    padding: 12px 20px;
    margin: 0px 12px 4px 12px;
    border-radius: 8px;
    text-align: left;
    font-size: 14px;
    color: #f1f5f9;
    min-height: 44px;
}

QPushButton.nav-item:hover {
    background-color: #334155;
}

QPushButton.nav-item:checked,
QPushButton.nav-item[active="true"] {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                stop:0 #06b6d4,
                                stop:1 #00d4ff);
    color: white;
    font-weight: 500;
}

QPushButton.nav-item:checked:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                stop:0 #0891b2,
                                stop:1 #0ea5e9);
}

/* 用户信息区域 */
QWidget#user_profile {
    background-color: #334155;
    border-radius: 8px;
    padding: 12px;
    margin: 20px;
}

QLabel#user_avatar {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                stop:0 #06b6d4,
                                stop:1 #00d4ff);
    border-radius: 18px;
    color: white;
    font-weight: 600;
    min-width: 36px;
    max-width: 36px;
    min-height: 36px;
    max-height: 36px;
}

QLabel#user_name {
    font-weight: 600;
    font-size: 14px;
    color: #f1f5f9;
}

QLabel#user_role {
    font-size: 12px;
    color: #94a3b8;
}

/* 顶部栏样式 */
QWidget#top_bar {
    background-color: #1e293b;
    border-bottom: 1px solid #475569;
    min-height: 72px;
    max-height: 72px;
    padding: 0px 24px;
}

/* 菜单切换按钮 */
QPushButton#menu_toggle {
    background-color: #334155;
    border: none;
    border-radius: 8px;
    min-width: 40px;
    max-width: 40px;
    min-height: 40px;
    max-height: 40px;
}

QPushButton#menu_toggle:hover {
    background-color: #475569;
}

/* 页面标题 */
QLabel#page_title {
    font-size: 24px;
    font-weight: 600;
    color: #f1f5f9;
    margin-left: 16px;
}

/* 主题切换开关 */
QWidget#theme_switch {
    background-color: #334155;
    border-radius: 8px;
    padding: 8px 12px;
}

QLabel.theme-label {
    font-size: 12px;
    font-weight: 500;
    color: #94a3b8;
}

/* 状态指示器 */
QWidget.status-indicator {
    background-color: rgba(0, 255, 136, 0.1);
    border-radius: 6px;
    padding: 6px 12px;
}

QWidget.status-indicator.normal {
    background-color: rgba(0, 255, 136, 0.1);
}

QWidget.status-indicator.warning {
    background-color: rgba(255, 170, 0, 0.1);
}

QWidget.status-indicator.error {
    background-color: rgba(255, 51, 102, 0.1);
}

QLabel.status-text {
    font-size: 12px;
    font-weight: 500;
}

QLabel.status-text.normal {
    color: #00ff88;
}

QLabel.status-text.warning {
    color: #ffaa00;
}

QLabel.status-text.error {
    color: #ff3366;
}

/* 内容区域 */
QWidget#content_area {
    background-color: #0f172a;
    padding: 24px;
}

/* 卡片样式 */
QWidget.card {
    background-color: #1e293b;
    border: 1px solid #475569;
    border-radius: 12px;
    padding: 20px;
}

QWidget.card-large {
    background-color: #1e293b;
    border: 1px solid #475569;
    border-radius: 16px;
    padding: 24px;
}

/* 发光卡片效果 */
QWidget.card-glow {
    background-color: #1e293b;
    border: 2px solid #00d4ff;
    border-radius: 12px;
    padding: 20px;
}

/* 卡片标题 */
QLabel.card-title {
    font-size: 16px;
    font-weight: 600;
    color: #f1f5f9;
    margin-bottom: 12px;
}

/* 按钮样式 */
QPushButton.btn-primary {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                stop:0 #06b6d4,
                                stop:1 #00d4ff);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 10px 16px;
    font-weight: 500;
    font-size: 14px;
}

QPushButton.btn-primary:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                stop:0 #0891b2,
                                stop:1 #0ea5e9);
}

QPushButton.btn-primary:pressed {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                stop:0 #0e7490,
                                stop:1 #0284c7);
}

QPushButton.btn-secondary {
    background-color: #334155;
    color: #f1f5f9;
    border: 1px solid #475569;
    border-radius: 8px;
    padding: 10px 16px;
    font-weight: 500;
    font-size: 14px;
}

QPushButton.btn-secondary:hover {
    background-color: #475569;
}

/* 输入框样式 */
QLineEdit {
    background-color: #1e293b;
    border: 1px solid #475569;
    border-radius: 8px;
    padding: 10px 12px;
    font-size: 14px;
    color: #f1f5f9;
}

QLineEdit:focus {
    border-color: #06b6d4;
    outline: none;
}

QLineEdit:disabled {
    background-color: #334155;
    color: #64748b;
}

/* 表格样式 */
QTableWidget {
    background-color: #1e293b;
    border: 1px solid #475569;
    border-radius: 8px;
    gridline-color: #334155;
    selection-background-color: #0f3460;
}

QTableWidget::item {
    padding: 12px 8px;
    border-bottom: 1px solid #334155;
    color: #f1f5f9;
}

QTableWidget::item:selected {
    background-color: #0f3460;
    color: #f1f5f9;
}

QHeaderView::section {
    background-color: #0f172a;
    border: none;
    border-bottom: 1px solid #475569;
    padding: 12px 8px;
    font-weight: 600;
    color: #94a3b8;
}

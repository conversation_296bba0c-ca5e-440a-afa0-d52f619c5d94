/* 科技主题样式文件 - Tech Theme Styles */
/* 基于complete_bci_system.html的未来科技风格 */

/* 科技主题样式文件 - Tech Theme Styles */
/* 基于complete_bci_system.html的未来科技风格 */
/* QSS不支持CSS变量，直接使用颜色值 */

/* 主窗口样式 */
QMainWindow {
    background-color: #0f172a;
    color: #f1f5f9;
    font-family: "Segoe UI", -apple-system, BlinkMacSystemFont, sans-serif;
}

QMainWindow#ModernMainWindow {
    background-color: #0f172a;
    border: none;
}

/* 侧边栏样式 - 科技风格 */
QWidget#sidebar {
    background-color: #1e293b;
    border-right: 1px solid #475569;
    min-width: 280px;
    max-width: 280px;
}

QWidget#sidebar[collapsed="true"] {
    min-width: 80px;
    max-width: 80px;
}

/* 侧边栏头部 */
QWidget#sidebar_header {
    background-color: #1e293b;
    border-bottom: 1px solid #475569;
    padding: 24px 20px;
}

/* Logo样式 - 荧光渐变 */
QLabel#logo {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                stop:0 #06b6d4,
                                stop:1 #00d4ff);
    border-radius: 12px;
    color: white;
    font-weight: 700;
    font-size: 20px;
    min-width: 48px;
    max-width: 48px;
    min-height: 48px;
    max-height: 48px;
}

QLabel#logo_text {
    font-size: 18px;
    font-weight: 600;
    color: #f1f5f9;
    margin-left: 12px;
}

/* 导航菜单样式 */
QWidget#nav_menu {
    background-color: transparent;
    padding: 20px 0px;
}

/* 导航分组标题 */
QLabel.nav-title {
    color: #94a3b8;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 0px 20px 12px 20px;
    margin-bottom: 8px;
}

/* 导航项样式 - 荧光效果 */
QPushButton.nav-item {
    background-color: transparent;
    border: none;
    padding: 12px 20px;
    margin: 0px 12px 4px 12px;
    border-radius: 8px;
    text-align: left;
    font-size: 14px;
    color: #f1f5f9;
    min-height: 44px;
}

QPushButton.nav-item:hover {
    background-color: #334155;
}

QPushButton.nav-item:checked,
QPushButton.nav-item[active="true"] {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                stop:0 #06b6d4,
                                stop:1 #00d4ff);
    color: white;
    font-weight: 500;
    border-left: 4px solid #00d4ff;
}

QPushButton.nav-item:checked:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                stop:0 #0891b2,
                                stop:1 #0ea5e9);
}

/* 用户信息区域 - 科技卡片 */
QWidget#user_profile {
    background-color: #334155;
    border-radius: 8px;
    padding: 12px;
    margin: 20px;
    border: 1px solid rgba(0, 212, 255, 0.2);
}

QLabel#user_avatar {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                stop:0 #06b6d4,
                                stop:1 #00d4ff);
    border-radius: 18px;
    color: white;
    font-weight: 600;
    min-width: 36px;
    max-width: 36px;
    min-height: 36px;
    max-height: 36px;
}

QLabel#user_name {
    font-weight: 600;
    font-size: 14px;
    color: #f1f5f9;
}

QLabel#user_role {
    font-size: 12px;
    color: #94a3b8;
}

/* 顶部栏样式 - 科技风格 */
QWidget#top_bar {
    background-color: #1e293b;
    border-bottom: 1px solid #475569;
    min-height: 72px;
    max-height: 72px;
    padding: 0px 24px;
}

/* 菜单切换按钮 - 荧光效果 */
QPushButton#menu_toggle {
    background-color: #334155;
    border: none;
    border-radius: 8px;
    min-width: 40px;
    max-width: 40px;
    min-height: 40px;
    max-height: 40px;
    border: 1px solid rgba(0, 212, 255, 0.2);
}

QPushButton#menu_toggle:hover {
    background-color: #475569;
    border-color: rgba(0, 212, 255, 0.5);
}

/* 页面标题 */
QLabel#page_title {
    font-size: 24px;
    font-weight: 600;
    color: #f1f5f9;
    margin-left: 16px;
}

/* 主题切换开关 - 科技开关 */
QWidget#theme_switch {
    background-color: #334155;
    border-radius: 8px;
    padding: 8px 12px;
    border: 1px solid rgba(0, 212, 255, 0.2);
}

QLabel.theme-label {
    font-size: 12px;
    font-weight: 500;
    color: #94a3b8;
}

/* 状态指示器 - 荧光状态 */
QWidget.status-indicator {
    background-color: rgba(0, 255, 136, 0.1);
    border-radius: 6px;
    padding: 6px 12px;
    border: 1px solid rgba(0, 255, 136, 0.3);
}

QWidget.status-indicator.normal {
    background-color: rgba(0, 255, 136, 0.1);
    border-color: rgba(0, 255, 136, 0.3);
}

QWidget.status-indicator.warning {
    background-color: rgba(255, 170, 0, 0.1);
    border-color: rgba(255, 170, 0, 0.3);
}

QWidget.status-indicator.error {
    background-color: rgba(255, 51, 102, 0.1);
    border-color: rgba(255, 51, 102, 0.3);
}

QLabel.status-text {
    font-size: 12px;
    font-weight: 500;
}

QLabel.status-text.normal {
    color: #00ff88;
}

QLabel.status-text.warning {
    color: #ffaa00;
}

QLabel.status-text.error {
    color: #ff3366;
}

/* 内容区域 - 科技网格背景 */
QWidget#content_area {
    background-color: #0f172a;
    padding: 24px;
}

/* 卡片样式 - 科技卡片 */
QWidget.card {
    background-color: #1e293b;
    border: 1px solid #475569;
    border-radius: 12px;
    padding: 20px;
}

QWidget.card-large {
    background-color: #1e293b;
    border: 1px solid #475569;
    border-radius: 16px;
    padding: 24px;
}

/* 发光卡片效果 */
QWidget.card-glow {
    background-color: #1e293b;
    border: 2px solid #00d4ff;
    border-radius: 12px;
    padding: 20px;
}

/* 卡片标题 */
QLabel.card-title {
    font-size: 16px;
    font-weight: 600;
    color: #f1f5f9;
    margin-bottom: 12px;
}

/* 按钮样式 - 荧光渐变 */
QPushButton.btn-primary {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                stop:0 #06b6d4,
                                stop:1 #00d4ff);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 10px 16px;
    font-weight: 500;
    font-size: 14px;
}

QPushButton.btn-primary:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                stop:0 #0891b2,
                                stop:1 #0ea5e9);
}

QPushButton.btn-primary:pressed {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                stop:0 #0e7490,
                                stop:1 #0284c7);
}

QPushButton.btn-secondary {
    background-color: #334155;
    color: #f1f5f9;
    border: 1px solid #475569;
    border-radius: 8px;
    padding: 10px 16px;
    font-weight: 500;
    font-size: 14px;
}

QPushButton.btn-secondary:hover {
    background-color: #475569;
    border-color: rgba(0, 212, 255, 0.3);
}

/* 输入框样式 - 科技输入框 */
QLineEdit {
    background-color: #1e293b;
    border: 1px solid #475569;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 14px;
    color: #f1f5f9;
}

QLineEdit:focus {
    border-color: #06b6d4;
    outline: none;
}

QLineEdit:disabled {
    background-color: #334155;
    color: #64748b;
}

/* 表格样式 - 科技表格 */
QTableWidget {
    background-color: #1e293b;
    border: 1px solid #475569;
    border-radius: 12px;
    gridline-color: #334155;
    selection-background-color: rgba(6, 182, 212, 0.2);
}

QTableWidget::item {
    padding: 16px 12px;
    border-bottom: 1px solid #334155;
    color: #f1f5f9;
}

QTableWidget::item:selected {
    background-color: rgba(6, 182, 212, 0.2);
    color: #f1f5f9;
}

QTableWidget::item:hover {
    background-color: #334155;
}

QHeaderView::section {
    background-color: #0f172a;
    border: none;
    border-bottom: 1px solid #475569;
    padding: 16px 12px;
    font-weight: 600;
    color: #94a3b8;
}

/* 进度条样式 - 荧光进度条 */
QProgressBar {
    background-color: #334155;
    border: none;
    border-radius: 6px;
    height: 12px;
}

QProgressBar::chunk {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                stop:0 #06b6d4,
                                stop:1 #00d4ff);
    border-radius: 6px;
}

/* 滚动条样式 - 科技滚动条 */
QScrollBar:vertical {
    background: #1e293b;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background: #475569;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background: #64748b;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    border: none;
    background: none;
    height: 0;
}

/* 组合框样式 - 科技下拉框 */
QComboBox {
    background-color: #1e293b;
    border: 1px solid #475569;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 14px;
    color: #f1f5f9;
}

QComboBox:hover {
    border-color: #06b6d4;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: none;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #94a3b8;
}

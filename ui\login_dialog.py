#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
登录对话框模块
Login Dialog Module

作者: AI Assistant
版本: 1.0.0
"""

import logging
from typing import Op<PERSON>, Tuple

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                               QLabel, QLineEdit, QPushButton, QCheckBox,
                               QFrame, QMessageBox, QProgressBar, QGroupBox)
from PySide6.QtCore import Qt, QTimer, Signal, QThread
from PySide6.QtGui import QPixmap, QFont, QIcon, QPalette

from core.auth_manager import AuthManager


class LoginWorker(QThread):
    """登录验证工作线程"""
    login_result = Signal(bool)

    def __init__(self, auth_manager: AuthManager, username: str, password: str):
        super().__init__()
        self.auth_manager = auth_manager
        self.username = username
        self.password = password

    def run(self):
        """执行登录验证"""
        try:
            result = self.auth_manager.login(self.username, self.password)
            self.login_result.emit(result)
        except Exception as e:
            logging.error(f"登录验证异常: {e}")
            self.login_result.emit(False)


class LoginDialog(QDialog):
    """登录对话框"""

    # 信号定义
    login_successful = Signal(dict)  # 登录成功信号

    def __init__(self, auth_manager: AuthManager, parent=None):
        super().__init__(parent)

        self.auth_manager = auth_manager
        self.logger = logging.getLogger(__name__)
        self.login_worker = None
        self._login_in_progress = False  # 登录进行标志

        # 登录尝试计数
        self.login_attempts = 0
        self.max_attempts = 3
        self.lockout_time = 300  # 5分钟锁定
        self.lockout_timer = QTimer()

        # 检查是否首次安装 - 移到init_ui之后
        self.is_first_install = False

        # 初始化界面
        self.init_ui()
        self.setup_connections()

        # 现在检查是否首次安装
        self.is_first_install = self.check_first_install()

        # 如果是首次安装，更新UI显示
        if self.is_first_install:
            self.update_first_install_ui()

        # 设置焦点
        if self.username_edit.text():
            self.password_edit.setFocus()
        else:
            self.username_edit.setFocus()

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("脑机接口康复训练系统 - 用户登录")
        self.setFixedSize(500, 680)  # 增加对话框尺寸，避免重叠
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint | Qt.WindowStaysOnTopHint)
        self.setModal(True)

        # 居中显示
        self.center_on_screen()

        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(20)  # 增加间距
        main_layout.setContentsMargins(40, 30, 40, 30)

        # 标题区域
        self.create_title_area(main_layout)

        # 登录表单区域
        self.create_login_form(main_layout)

        # 按钮区域
        self.create_button_area(main_layout)

        # 状态区域
        self.create_status_area(main_layout)

        # 应用样式
        self.apply_styles()

    def create_title_area(self, layout):
        """创建标题区域"""
        title_frame = QFrame()
        title_layout = QVBoxLayout(title_frame)
        title_layout.setAlignment(Qt.AlignCenter)
        title_layout.setSpacing(10)
        title_layout.setContentsMargins(0, 0, 0, 0)

        # 系统图标
        icon_label = QLabel()
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setFixedSize(80, 80)
        icon_label.setStyleSheet("""
            QLabel {
                background-color: #3498db;
                border-radius: 40px;
                color: white;
                font-size: 28px;
                font-weight: bold;
            }
        """)
        icon_label.setText("NK")
        title_layout.addWidget(icon_label)

        # 系统标题 - 分两行显示避免重叠
        title_label1 = QLabel("脑机接口康复训练系统")
        title_label1.setAlignment(Qt.AlignCenter)
        title_label1.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                margin: 5px 0;
            }
        """)
        title_layout.addWidget(title_label1)

        # 副标题
        subtitle_label = QLabel("Brain-Computer Interface System")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #7f8c8d;
                margin-bottom: 10px;
            }
        """)
        title_layout.addWidget(subtitle_label)

        layout.addWidget(title_frame)

    def create_login_form(self, layout):
        """创建登录表单"""
        form_group = QGroupBox("用户登录")
        form_layout = QVBoxLayout(form_group)
        form_layout.setSpacing(20)  # 增加间距
        form_layout.setContentsMargins(25, 25, 25, 25)  # 增加边距

        # 用户名区域
        username_layout = QVBoxLayout()
        username_layout.setSpacing(8)  # 增加标签和输入框间距

        username_label = QLabel("用户名:")
        username_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        username_layout.addWidget(username_label)

        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("请输入用户名 (默认: admin)")
        self.username_edit.setMinimumHeight(45)  # 增加高度
        self.username_edit.setText("admin")  # 设置默认用户名
        username_layout.addWidget(self.username_edit)

        form_layout.addLayout(username_layout)

        # 添加额外间距
        form_layout.addSpacing(10)

        # 密码区域
        password_layout = QVBoxLayout()
        password_layout.setSpacing(8)  # 增加标签和输入框间距

        password_label = QLabel("密码:")
        password_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
        password_layout.addWidget(password_label)

        self.password_edit = QLineEdit()
        self.password_edit.setPlaceholderText("请输入密码 (默认: admin123)")
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setMinimumHeight(45)  # 增加高度
        password_layout.addWidget(self.password_edit)

        form_layout.addLayout(password_layout)

        # 创建信息提示标签（稍后根据首次安装状态更新）
        self.info_label = QLabel("💡 默认管理员账户: admin / admin123")
        self.info_label.setStyleSheet("""
            QLabel {
                color: #3498db;
                font-size: 12px;
                background-color: #ebf3fd;
                border: 1px solid #3498db;
                border-radius: 4px;
                padding: 8px;
                margin: 5px 0;
            }
        """)
        form_layout.addWidget(self.info_label)

        # 记住用户名
        self.remember_checkbox = QCheckBox("记住用户名")
        self.remember_checkbox.setStyleSheet("color: #2c3e50;")
        form_layout.addWidget(self.remember_checkbox)

        layout.addWidget(form_group)

    def create_button_area(self, layout):
        """创建按钮区域"""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)

        # 登录按钮
        self.login_button = QPushButton("登录")
        self.login_button.setMinimumHeight(40)
        self.login_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)

        # 取消按钮
        self.cancel_button = QPushButton("取消")
        self.cancel_button.setMinimumHeight(40)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
            QPushButton:pressed {
                background-color: #6c7b7d;
            }
        """)

        button_layout.addWidget(self.login_button)
        button_layout.addWidget(self.cancel_button)

        layout.addLayout(button_layout)

    def create_status_area(self, layout):
        """创建状态区域"""
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #3498db;
                border-radius: 4px;
            }
        """)
        layout.addWidget(self.progress_bar)

        # 状态标签
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("""
            QLabel {
                color: #e74c3c;
                font-weight: bold;
                padding: 5px;
            }
        """)
        layout.addWidget(self.status_label)

    def apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QDialog {
                background-color: #ecf0f1;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #2c3e50;
                background-color: #ecf0f1;
            }
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 10px 12px;
                font-size: 13px;
                background-color: white;
                margin: 2px 0;
            }
            QLineEdit:focus {
                border-color: #3498db;
                background-color: #f8f9fa;
            }
            QLineEdit:hover {
                border-color: #95a5a6;
            }
            QCheckBox {
                font-size: 12px;
                color: #2c3e50;
            }
        """)

    def setup_connections(self):
        """设置信号连接"""
        self.login_button.clicked.connect(self.handle_login)
        self.cancel_button.clicked.connect(self.reject)
        self.username_edit.returnPressed.connect(self.on_return_pressed)
        self.password_edit.returnPressed.connect(self.on_return_pressed)
        self.lockout_timer.timeout.connect(self.unlock_login)

    def on_return_pressed(self):
        """处理回车键按下事件"""
        # 防止重复登录请求
        if hasattr(self, '_login_in_progress') and self._login_in_progress:
            self.logger.warning("登录正在进行中，忽略重复请求")
            return
        
        # 使用QTimer延迟处理登录，避免回车键引起的竞态条件
        QTimer.singleShot(50, self.handle_login)

    def handle_login(self):
        """处理登录"""
        try:
            # 防止重复登录请求
            if hasattr(self, '_login_in_progress') and self._login_in_progress:
                self.logger.warning("登录正在进行中，忽略重复请求")
                return

            if self.login_attempts >= self.max_attempts:
                self.show_status("登录已锁定，请稍后再试", error=True)
                return

            username = self.username_edit.text().strip()
            password = self.password_edit.text()

            # 验证输入
            if not username:
                self.show_status("请输入用户名", error=True)
                self.username_edit.setFocus()
                return

            if not password:
                self.show_status("请输入密码", error=True)
                self.password_edit.setFocus()
                return

            # 标记登录正在进行
            self._login_in_progress = True

            # 开始登录验证
            self.start_login_process(username, password)

        except Exception as e:
            self.logger.error(f"处理登录请求失败: {e}")
            self._login_in_progress = False
            self.show_status("登录处理失败，请重试", error=True)

    def start_login_process(self, username: str, password: str):
        """开始登录过程"""
        try:
            # 停止之前的工作线程（如果存在）
            if self.login_worker and self.login_worker.isRunning():
                self.logger.warning("停止之前的登录线程")
                self.login_worker.terminate()
                self.login_worker.wait(1000)  # 等待最多1秒

            # 禁用界面
            self.set_ui_enabled(False)
            self.show_progress("正在验证用户信息...")

            # 创建登录工作线程
            self.login_worker = LoginWorker(self.auth_manager, username, password)
            self.login_worker.login_result.connect(self.on_login_result)

            # 添加线程完成信号处理
            self.login_worker.finished.connect(self.on_login_worker_finished)

            self.login_worker.start()

        except Exception as e:
            self.logger.error(f"启动登录过程失败: {e}")
            self._login_in_progress = False
            self.set_ui_enabled(True)
            self.hide_progress()
            self.show_status("登录启动失败，请重试", error=True)

    def on_login_result(self, success: bool):
        """处理登录结果"""
        try:
            self.hide_progress()
            self.set_ui_enabled(True)

            if success:
                # 登录成功
                user_info = self.auth_manager.get_current_user()
                self.show_status("登录成功", error=False)

                # 保存用户名（如果选择记住）
                if self.remember_checkbox.isChecked():
                    self.save_username(self.username_edit.text())

                # 增加延迟时间，避免竞态条件
                QTimer.singleShot(300, lambda: self.emit_login_successful_signal(user_info))

                # 临时禁用首次登录密码修改提示以避免崩溃
                # TODO: 修复首次登录密码修改提示的崩溃问题后重新启用
                try:
                    # 检查是否需要显示首次登录密码修改提示
                    should_show_password_change = self.should_show_password_change_prompt(user_info)

                    if should_show_password_change:
                        # 暂时只在日志中记录，不显示对话框
                        self.logger.info("检测到admin用户使用默认密码，建议修改密码")
                        # 延迟关闭对话框
                        QTimer.singleShot(800, self.accept)
                    else:
                        # 延迟关闭对话框
                        QTimer.singleShot(800, self.accept)
                except Exception as e:
                    self.logger.error(f"检查密码修改提示时出错: {e}")
                    # 出错时直接关闭对话框
                    QTimer.singleShot(800, self.accept)
            else:
                # 登录失败
                self.login_attempts += 1
                remaining = self.max_attempts - self.login_attempts

                if remaining > 0:
                    self.show_status(f"登录失败，还有 {remaining} 次尝试机会", error=True)
                    self.password_edit.clear()
                    self.password_edit.setFocus()
                else:
                    # 锁定登录
                    self.lock_login()

        except Exception as e:
            self.logger.error(f"处理登录结果失败: {e}")
            self.show_status("登录处理失败，请重试", error=True)
        finally:
            # 重置登录进行标志
            self._login_in_progress = False

    def emit_login_successful_signal(self, user_info):
        """安全发送登录成功信号"""
        try:
            # 检查对话框是否仍然有效
            if not self.isVisible() or not self.isEnabled():
                self.logger.warning("对话框已关闭或禁用，取消发送登录成功信号")
                return
                
            if user_info and isinstance(user_info, dict):
                self.logger.debug("发送登录成功信号")
                self.login_successful.emit(user_info)
            else:
                self.logger.error("用户信息无效，无法发送登录成功信号")
        except Exception as e:
            self.logger.error(f"发送登录成功信号失败: {e}")

    def on_login_worker_finished(self):
        """登录工作线程完成处理"""
        try:
            self.logger.debug("登录工作线程已完成")
            # 清理工作线程引用
            if self.login_worker:
                self.login_worker.deleteLater()
                self.login_worker = None
        except Exception as e:
            self.logger.error(f"清理登录工作线程失败: {e}")

    def lock_login(self):
        """锁定登录"""
        self.show_status(f"登录失败次数过多，锁定 {self.lockout_time//60} 分钟", error=True)
        self.set_ui_enabled(False)
        self.lockout_timer.start(self.lockout_time * 1000)

    def unlock_login(self):
        """解锁登录"""
        self.lockout_timer.stop()
        self.login_attempts = 0
        self.set_ui_enabled(True)
        self.show_status("登录已解锁，请重新尝试", error=False)

    def set_ui_enabled(self, enabled: bool):
        """设置界面启用状态"""
        self.username_edit.setEnabled(enabled)
        self.password_edit.setEnabled(enabled)
        self.login_button.setEnabled(enabled)
        self.remember_checkbox.setEnabled(enabled)

    def show_progress(self, message: str):
        """显示进度"""
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 无限进度条
        self.status_label.setText(message)
        self.status_label.setStyleSheet("QLabel { color: #3498db; }")

    def hide_progress(self):
        """隐藏进度"""
        self.progress_bar.setVisible(False)

    def show_status(self, message: str, error: bool = False):
        """显示状态信息"""
        self.status_label.setText(message)
        if error:
            self.status_label.setStyleSheet("QLabel { color: #e74c3c; }")
        else:
            self.status_label.setStyleSheet("QLabel { color: #27ae60; }")

    def save_username(self, username: str):
        """保存用户名"""
        try:
            from utils.app_config import AppConfig
            # 这里可以实现用户名保存逻辑
            pass
        except Exception as e:
            self.logger.error(f"保存用户名失败: {e}")

    def load_saved_username(self):
        """加载保存的用户名"""
        try:
            # 这里可以实现用户名加载逻辑
            pass
        except Exception as e:
            self.logger.error(f"加载用户名失败: {e}")

    def showEvent(self, event):
        """对话框显示事件"""
        super().showEvent(event)
        self.load_saved_username()

        # 重置状态
        self.login_attempts = 0
        self.status_label.clear()
        self.password_edit.clear()

        # 设置焦点
        if self.username_edit.text():
            self.password_edit.setFocus()
        else:
            self.username_edit.setFocus()

    def closeEvent(self, event):
        """对话框关闭事件"""
        try:
            # 停止工作线程
            if self.login_worker and self.login_worker.isRunning():
                self.logger.debug("关闭对话框时停止登录工作线程")
                self.login_worker.terminate()
                self.login_worker.wait(2000)  # 等待最多2秒

            # 重置登录进行标志
            self._login_in_progress = False

        except Exception as e:
            self.logger.error(f"关闭对话框时清理资源失败: {e}")
        finally:
            super().closeEvent(event)

    def update_first_install_ui(self):
        """更新首次安装的UI显示"""
        if self.is_first_install:
            # 首次安装提示
            self.info_label.setText("🎉 首次安装检测到！\n默认管理员账户: admin / admin123\n登录后请立即修改密码")
            self.info_label.setStyleSheet("""
                QLabel {
                    color: #e67e22;
                    font-size: 12px;
                    background-color: #fef9e7;
                    border: 1px solid #e67e22;
                    border-radius: 4px;
                    padding: 10px;
                    margin: 5px 0;
                    line-height: 1.4;
                }
            """)

    def check_first_install(self):
        """检查是否首次安装"""
        try:
            # 使用不需要权限检查的方法检查数据库中是否有用户
            users = self.auth_manager.get_all_users_without_permission_check()

            # 检查是否只有默认的admin用户且密码未修改
            if len(users) == 1:
                admin_user = users[0]
                if (admin_user['name'] == 'admin' and
                    admin_user['role'] == 'admin'):
                    # 检查是否使用默认密码
                    return self.is_using_default_password(admin_user)

            return len(users) == 0
        except Exception as e:
            self.logger.error(f"检查首次安装状态失败: {e}")
            return True  # 出错时假设是首次安装

    def is_using_default_password(self, user_info):
        """检查用户是否使用默认密码"""
        try:
            # 计算默认密码的哈希值
            default_password = "admin123"
            salt = "NK_BCI_SYSTEM_2024"
            import hashlib
            default_hash = hashlib.sha256((default_password + salt).encode()).hexdigest()

            # 从数据库获取用户的密码哈希
            user_data = self.auth_manager.db_manager.execute_query(
                "SELECT password FROM operator WHERE id = ?",
                (user_info['id'],)
            )

            if user_data:
                return user_data[0]['password'] == default_hash

            return False
        except Exception as e:
            self.logger.error(f"检查默认密码失败: {e}")
            return False

    def should_show_password_change_prompt(self, user_info):
        """检查是否应该显示密码修改提示"""
        try:
            # 只对admin用户进行检查
            if user_info.get('name', '').lower() != 'admin':
                return False

            # 检查是否使用默认密码
            return self.is_using_default_password(user_info)

        except Exception as e:
            self.logger.error(f"检查密码修改提示失败: {e}")
            return False

    def show_first_login_success_and_close(self):
        """显示首次登录成功提示并关闭对话框"""
        try:
            # 保存父窗口引用
            main_window = None
            if self.parent():
                main_window = self.parent()

            # 先关闭登录对话框
            self.accept()

            # 延迟显示首次登录提示，确保主窗口已经显示
            QTimer.singleShot(1000, lambda: self.show_first_login_success(main_window))
        except Exception as e:
            self.logger.error(f"显示首次登录成功提示失败: {e}")
            # 如果出错，直接关闭对话框
            self.accept()

    def show_first_login_success(self, parent=None):
        """显示首次登录成功后的密码修改提示"""
        try:
            from PySide6.QtWidgets import QMessageBox

            # 如果没有传入父窗口，尝试获取主窗口
            if parent is None:
                try:
                    from PySide6.QtWidgets import QApplication
                    app = QApplication.instance()
                    if app:
                        for widget in app.topLevelWidgets():
                            if widget.isVisible() and hasattr(widget, 'auth_manager'):
                                parent = widget
                                break
                except:
                    pass

            reply = QMessageBox.question(
                parent, "首次登录成功",
                "🎉 欢迎使用脑机接口康复训练系统！\n\n"
                "为了系统安全，强烈建议您立即修改默认密码。\n"
                "是否现在修改密码？\n\n"
                "注意：如果选择稍后修改，请尽快在用户管理中修改密码。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            if reply == QMessageBox.Yes:
                self.show_change_password_dialog_standalone(parent)
            else:
                QMessageBox.information(
                    parent, "安全提醒",
                    "请记住尽快修改默认密码！\n\n"
                    "您可以在登录后通过以下方式修改密码：\n"
                    "1. 点击左侧导航栏的「用户管理」\n"
                    "2. 选择admin用户\n"
                    "3. 点击「重置密码」按钮"
                )
        except Exception as e:
            self.logger.error(f"显示首次登录提示失败: {e}")

    def show_change_password_dialog(self):
        """显示修改密码对话框"""
        from PySide6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton, QMessageBox

        dialog = QDialog(self)
        dialog.setWindowTitle("修改默认密码")
        dialog.setFixedSize(400, 250)
        dialog.setModal(True)

        layout = QVBoxLayout(dialog)
        layout.setSpacing(15)
        layout.setContentsMargins(30, 20, 30, 20)

        # 标题
        title_label = QLabel("修改管理员密码")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50;")
        layout.addWidget(title_label)

        # 新密码
        new_password_label = QLabel("新密码:")
        new_password_edit = QLineEdit()
        new_password_edit.setEchoMode(QLineEdit.Password)
        new_password_edit.setPlaceholderText("请输入新密码（至少6位）")
        new_password_edit.setMinimumHeight(35)

        layout.addWidget(new_password_label)
        layout.addWidget(new_password_edit)

        # 确认密码
        confirm_password_label = QLabel("确认密码:")
        confirm_password_edit = QLineEdit()
        confirm_password_edit.setEchoMode(QLineEdit.Password)
        confirm_password_edit.setPlaceholderText("请再次输入新密码")
        confirm_password_edit.setMinimumHeight(35)

        layout.addWidget(confirm_password_label)
        layout.addWidget(confirm_password_edit)

        # 按钮
        button_layout = QHBoxLayout()
        save_button = QPushButton("保存")
        cancel_button = QPushButton("取消")

        button_layout.addWidget(save_button)
        button_layout.addWidget(cancel_button)
        layout.addLayout(button_layout)

        # 按钮事件
        def save_password():
            new_password = new_password_edit.text()
            confirm_password = confirm_password_edit.text()

            if len(new_password) < 6:
                QMessageBox.warning(dialog, "警告", "密码长度至少6位！")
                return

            if new_password != confirm_password:
                QMessageBox.warning(dialog, "警告", "两次输入的密码不一致！")
                return

            # 调用修改密码的方法
            try:
                success = self.auth_manager.change_admin_password("admin", new_password)
                if success:
                    QMessageBox.information(dialog, "成功", "密码修改成功！")
                    dialog.accept()
                else:
                    QMessageBox.critical(dialog, "错误", "密码修改失败！\n可能是权限不足或系统错误。")
            except Exception as e:
                self.logger.error(f"密码修改异常: {e}")
                QMessageBox.critical(dialog, "错误", f"密码修改异常：{e}")

        save_button.clicked.connect(save_password)
        cancel_button.clicked.connect(dialog.reject)

        # 设置焦点
        new_password_edit.setFocus()

        dialog.exec()

    def show_change_password_dialog_standalone(self, parent=None):
        """显示独立的修改密码对话框（用于首次登录后）"""
        try:
            from PySide6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton, QMessageBox

            dialog = QDialog(parent)
            dialog.setWindowTitle("修改默认密码")
            dialog.setFixedSize(400, 250)
            dialog.setModal(True)

            layout = QVBoxLayout(dialog)
            layout.setSpacing(15)
            layout.setContentsMargins(30, 20, 30, 20)

            # 标题
            title_label = QLabel("修改管理员密码")
            title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50;")
            layout.addWidget(title_label)

            # 新密码
            new_password_label = QLabel("新密码:")
            new_password_edit = QLineEdit()
            new_password_edit.setEchoMode(QLineEdit.Password)
            new_password_edit.setPlaceholderText("请输入新密码（至少6位）")
            new_password_edit.setMinimumHeight(35)

            layout.addWidget(new_password_label)
            layout.addWidget(new_password_edit)

            # 确认密码
            confirm_password_label = QLabel("确认密码:")
            confirm_password_edit = QLineEdit()
            confirm_password_edit.setEchoMode(QLineEdit.Password)
            confirm_password_edit.setPlaceholderText("请再次输入新密码")
            confirm_password_edit.setMinimumHeight(35)

            layout.addWidget(confirm_password_label)
            layout.addWidget(confirm_password_edit)

            # 按钮
            button_layout = QHBoxLayout()
            save_button = QPushButton("保存")
            cancel_button = QPushButton("取消")

            button_layout.addWidget(save_button)
            button_layout.addWidget(cancel_button)
            layout.addLayout(button_layout)

            # 按钮事件
            def save_password():
                new_password = new_password_edit.text()
                confirm_password = confirm_password_edit.text()

                if len(new_password) < 6:
                    QMessageBox.warning(dialog, "警告", "密码长度至少6位！")
                    return

                if new_password != confirm_password:
                    QMessageBox.warning(dialog, "警告", "两次输入的密码不一致！")
                    return

                # 调用修改密码的方法
                try:
                    success = self.auth_manager.change_admin_password("admin", new_password)
                    if success:
                        QMessageBox.information(dialog, "成功", "密码修改成功！")
                        dialog.accept()
                    else:
                        QMessageBox.critical(dialog, "错误", "密码修改失败！\n可能是权限不足或系统错误。")
                except Exception as e:
                    self.logger.error(f"密码修改异常: {e}")
                    QMessageBox.critical(dialog, "错误", f"密码修改异常：{e}")

            save_button.clicked.connect(save_password)
            cancel_button.clicked.connect(dialog.reject)

            # 设置焦点
            new_password_edit.setFocus()

            dialog.exec()
        except Exception as e:
            self.logger.error(f"显示独立密码修改对话框失败: {e}")

    def center_on_screen(self):
        """将对话框居中显示在屏幕上"""
        try:
            from PySide6.QtWidgets import QApplication
            screen = QApplication.primaryScreen()
            if screen:
                screen_geometry = screen.availableGeometry()
                dialog_geometry = self.geometry()

                x = (screen_geometry.width() - dialog_geometry.width()) // 2
                y = (screen_geometry.height() - dialog_geometry.height()) // 2

                self.move(x, y)
        except Exception as e:
            self.logger.error(f"居中显示失败: {e}")

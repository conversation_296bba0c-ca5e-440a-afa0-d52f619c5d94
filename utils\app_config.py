#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用程序配置管理
Application Configuration Management

作者: AI Assistant
版本: 1.0.0
"""

import os
import json
from pathlib import Path
from typing import Dict, Any


class AppConfig:
    """应用程序配置类"""

    # 版本信息
    VERSION = "1.0.0"
    BUILD_DATE = "2024-12-19"

    # 应用程序信息
    APP_NAME = "脑机接口康复训练系统"
    APP_NAME_EN = "Brain-Computer Interface System"
    COMPANY_NAME = "山东海天智能工程有限公司"

    # 项目路径
    PROJECT_ROOT = Path(__file__).parent.parent

    # 配置文件路径
    CONFIG_FILE = PROJECT_ROOT / 'data' / 'user_config.json'

    # 数据库配置
    DATABASE_CONFIG = {
        'type': 'sqlite',
        'path': PROJECT_ROOT / 'data' / 'nk_system.db',
        'backup_path': PROJECT_ROOT / 'data' / 'backup',
        'auto_backup': True,
        'backup_interval': 24 * 60 * 60,  # 24小时备份一次
        'treatment_duration': 20,  # 治疗时长(分钟)
        'min_treatment_duration': 5,  # 治疗数据最小时长(分钟)
    }

    # 原始数据存储配置
    RAW_DATA_CONFIG = {
        'enabled': True,                          # 启用原始数据存储
        'storage_format': 'hdf5',                 # 存储格式
        'compression': True,                      # 启用压缩
        'compression_level': 6,                   # 压缩级别 (1-9)
        'max_file_size_mb': 100,                  # 单文件最大大小
        'auto_backup': True,                      # 自动备份
        'backup_interval_hours': 24,              # 备份间隔
        'data_retention_days': 365,               # 数据保留天数
        'quality_threshold': 0.7,                 # 数据质量阈值
        'cache_size_limit': 100,                  # 数据缓存限制
        'enable_data_validation': True,           # 启用数据验证
        'auto_cleanup': True,                     # 自动清理过期数据
    }

    # 日志配置
    LOG_CONFIG = {
        'level': 'INFO',
        'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        'file_path': PROJECT_ROOT / 'logs' / 'nk_system.log',
        'max_file_size': 10 * 1024 * 1024,  # 10MB
        'backup_count': 5,
        'console_output': True,
    }

    # 脑电设备配置
    EEG_CONFIG = {
        'serial_port': 'COM8',
        'baud_rate': 115200,
        'sample_rate': 125.0,  # Hz
        'channels': 8,
        'packet_size': 100,
        'timeout': 5.0,  # 秒
        'channel_names': ['PZ', 'P3', 'P4', 'C3', 'CZ', 'C4', 'F3', 'F4'],  # ADS1299通道顺序
        'packet_header': b'\x5A\xA5',  # 包头
        'packet_footer': b'\x0D\x0A',  # 包尾
        'data_groups_per_packet': 4,  # 每包4组数据
        'bytes_per_group': 24,  # 每组24字节（8通道*3字节）
        'start_command': 'START',  # 开始采集命令
        'stop_command': 'STOP',  # 停止采集命令
        'connection_timeout': 3.0,  # 连接超时时间（秒）
        'max_connection_attempts': 3,  # 最大连接尝试次数
    }

    # 信号处理配置
    SIGNAL_PROCESSING_CONFIG = {
        'filter_config': {
            'highpass_freq': 0.5,  # Hz
            'lowpass_freq': 50.0,  # Hz
            'notch_freq': 50.0,    # Hz (电源干扰)
            'filter_order': 4,
        },
        'feature_extraction': {
            'window_size': 2.0,    # 秒
            'overlap': 0.5,        # 重叠比例
            'frequency_bands': {
                'delta': (0.5, 4.0),
                'theta': (4.0, 8.0),
                'alpha': (8.0, 13.0),
                'mu': (8.0, 12.0),
                'beta': (13.0, 30.0),
                'gamma': (30.0, 50.0),
            }
        },
        'deep_learning': {
            'model_type': 'eegnet',    # 深度学习模型类型
            'training_samples': 100,   # 每个状态的训练样本数
            'validation_split': 0.2,
            'epochs': 50,              # 训练轮数
            'batch_size': 32,          # 批次大小
            'learning_rate': 0.001,    # 学习率
            'temperature': 1.0,        # 温度缩放
            'dropout_rate': 0.25,      # Dropout率
        }
    }

    # 电刺激配置
    STIMULATION_CONFIG = {
        'dll_path': PROJECT_ROOT / 'libs' / 'RecoveryDLL.dll',
        'max_current': 50,    # mA (1-100)
        'min_current': 1,     # mA (1-10)
        'current_step': 1,    # mA (固定为1)
        'default_frequency': 20,      # Hz (2-160)
        'default_pulse_width': 200,   # μs (10-500)
        'default_relax_time': 5,      # s (0-16)
        'default_climb_time': 2,      # s (0-5)
        'default_work_time': 10,      # s (0-30)
        'default_fall_time': 2,       # s (0-5)
        'default_wave_type': 0,       # 0: 双相波, 1: 单相波
        'max_channels': 2,            # 最大通道数
        'port_num': 1,                # 默认端口号
        'connection_timeout': 5,      # 连接超时时间(s) (1-30)
        'current_steps': [1],         # 电流调节步长选项（固定为1）
    }

    # 网络通信配置
    NETWORK_CONFIG = {
        'http': {
            'base_url': 'http://*************:8082/shdekf/Api/',
            'timeout': 30,
            'retry_count': 1,  # 减少重试次数从3次到1次，提升用户体验
            'retry_delay': 1.0,
        },
        'udp': {
            'vr_host': '127.0.0.1',
            'vr_port': 3004,  # 发送到VR系统的端口（原QT项目端口）
            'local_port': 3005,  # 本地绑定端口（原QT项目端口）
        }
    }

    # 治疗工作流程配置
    TREATMENT_WORKFLOW_CONFIG = {
        'motor_imagery_timeout': 30,  # 运动想象超时时间（秒）
        'udp_retry_count': 3,  # UDP指令重发次数
        'udp_retry_delay': 0.01,  # UDP指令重发间隔（秒）
    }

    # 用户界面配置
    UI_CONFIG = {
        'theme': 'dark',  # 'light', 'dark'
        'language': 'zh_CN',
        'font_family': 'Microsoft YaHei',
        'font_size': 10,
        'window_size': (1400, 900),
        'auto_save_interval': 300,  # 秒
    }

    # 权限配置
    PERMISSION_CONFIG = {
        'roles': {
            'admin': {
                'name': '系统管理员',
                'permissions': ['all']
            },
            'doctor': {
                'name': '医生',
                'permissions': [
                    'patient_management',
                    'treatment_operation',
                    'report_generation',
                    'data_analysis'
                ]
            },
            'technician': {
                'name': '技师',
                'permissions': [
                    'treatment_operation',
                    'equipment_control',
                    'data_collection'
                ]
            },
            'operator': {
                'name': '操作员',
                'permissions': [
                    'patient_basic_info',
                    'treatment_operation'
                ]
            }
        },
        'password_policy': {
            'min_length': 6,
            'require_uppercase': False,
            'require_lowercase': False,
            'require_numbers': True,
            'require_special': False,
            'max_age_days': 90,
        }
    }

    # 治疗配置
    TREATMENT_CONFIG = {
        'training_duration': {
            'rest_state': 5.0,      # 秒
            'motor_imagery': 5.0,   # 秒
            'feedback_delay': 1.0,  # 秒
        },
        'session_config': {
            'max_sessions_per_day': 5,
            'min_rest_between_sessions': 300,  # 秒
            'max_session_duration': 1800,     # 秒
        },
        'feedback': {
            'visual_feedback': True,
            'audio_feedback': True,
            'haptic_feedback': True,
            'real_time_threshold': 0.1,  # 秒
        }
    }

    # 报告配置
    REPORT_CONFIG = {
        'template_path': PROJECT_ROOT / 'resources' / 'templates',
        'output_path': PROJECT_ROOT / 'reports',
        'formats': ['pdf', 'html', 'docx'],
        'include_charts': True,
        'chart_dpi': 300,
    }

    @classmethod
    def get_factory_password(cls) -> str:
        """获取厂家配置密码"""
        # 可以从配置文件读取或使用默认密码
        # 建议在生产环境中从加密配置文件读取
        return "NK2024Factory"  # 默认厂家密码，实际部署时应该修改

    @classmethod
    def get_config(cls, section: str) -> Dict[str, Any]:
        """获取指定配置节"""
        config_map = {
            'database': cls.DATABASE_CONFIG,
            'raw_data': cls.RAW_DATA_CONFIG,
            'log': cls.LOG_CONFIG,
            'eeg': cls.EEG_CONFIG,
            'signal_processing': cls.SIGNAL_PROCESSING_CONFIG,
            'stimulation': cls.STIMULATION_CONFIG,
            'network': cls.NETWORK_CONFIG,
            'ui': cls.UI_CONFIG,
            'permission': cls.PERMISSION_CONFIG,
            'treatment': cls.TREATMENT_CONFIG,
            'report': cls.REPORT_CONFIG,
            'paths': {
                'data': str(cls.PROJECT_ROOT / 'data'),
                'logs': str(cls.PROJECT_ROOT / 'logs'),
                'reports': str(cls.PROJECT_ROOT / 'reports'),
                'resources': str(cls.PROJECT_ROOT / 'resources'),
                'libs': str(cls.PROJECT_ROOT / 'libs'),
                'raw_eeg_data': str(cls.PROJECT_ROOT / 'data' / 'raw_eeg_data'),
            }
        }
        return config_map.get(section, {})

    @classmethod
    def create_directories(cls):
        """创建必要的目录"""
        directories = [
            cls.PROJECT_ROOT / 'data',
            cls.PROJECT_ROOT / 'data' / 'backup',
            cls.PROJECT_ROOT / 'logs',
            cls.PROJECT_ROOT / 'reports',
            cls.PROJECT_ROOT / 'resources' / 'images',
            cls.PROJECT_ROOT / 'resources' / 'styles',
            cls.PROJECT_ROOT / 'resources' / 'templates',
            cls.PROJECT_ROOT / 'libs',
        ]

        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)

    @classmethod
    def save_user_config(cls) -> bool:
        """保存用户配置到文件"""
        try:
            # 确保配置目录存在
            cls.CONFIG_FILE.parent.mkdir(parents=True, exist_ok=True)

            # 准备要保存的配置
            user_config = {
                'database': cls.DATABASE_CONFIG,
                'stimulation': cls.STIMULATION_CONFIG,
                'eeg': cls.EEG_CONFIG,
                'signal_processing': cls.SIGNAL_PROCESSING_CONFIG,
                'ui': cls.UI_CONFIG,
                'log': cls.LOG_CONFIG,
                'treatment': cls.TREATMENT_CONFIG,
            }

            # 转换特殊对象为可序列化的格式
            def convert_objects(obj):
                if isinstance(obj, dict):
                    return {k: convert_objects(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [convert_objects(item) for item in obj]
                elif isinstance(obj, Path):
                    return str(obj)
                elif isinstance(obj, bytes):
                    return obj.hex()  # 将bytes转换为十六进制字符串
                else:
                    return obj

            user_config = convert_objects(user_config)

            # 保存到文件
            with open(cls.CONFIG_FILE, 'w', encoding='utf-8') as f:
                json.dump(user_config, f, indent=2, ensure_ascii=False)

            return True

        except Exception as e:
            print(f"保存用户配置失败: {e}")
            return False

    @classmethod
    def load_user_config(cls) -> bool:
        """从文件加载用户配置"""
        try:
            if not cls.CONFIG_FILE.exists():
                return True  # 配置文件不存在是正常的

            with open(cls.CONFIG_FILE, 'r', encoding='utf-8') as f:
                user_config = json.load(f)

            # 转换回特殊对象
            def restore_objects(obj, original_obj):
                if isinstance(obj, dict) and isinstance(original_obj, dict):
                    for k, v in obj.items():
                        if k in original_obj:
                            if isinstance(original_obj[k], bytes) and isinstance(v, str):
                                # 将十六进制字符串转换回bytes
                                try:
                                    obj[k] = bytes.fromhex(v)
                                except ValueError:
                                    pass  # 如果转换失败，保持原值
                            elif isinstance(original_obj[k], Path) and isinstance(v, str):
                                # 将字符串转换回Path
                                obj[k] = Path(v)
                            elif isinstance(v, dict):
                                restore_objects(v, original_obj[k])
                return obj

            # 更新配置
            if 'database' in user_config:
                restore_objects(user_config['database'], cls.DATABASE_CONFIG)
                cls.DATABASE_CONFIG.update(user_config['database'])

            if 'stimulation' in user_config:
                restore_objects(user_config['stimulation'], cls.STIMULATION_CONFIG)
                cls.STIMULATION_CONFIG.update(user_config['stimulation'])

            if 'eeg' in user_config:
                restore_objects(user_config['eeg'], cls.EEG_CONFIG)
                cls.EEG_CONFIG.update(user_config['eeg'])

            if 'signal_processing' in user_config:
                cls.SIGNAL_PROCESSING_CONFIG.update(user_config['signal_processing'])

            if 'ui' in user_config:
                cls.UI_CONFIG.update(user_config['ui'])

            if 'log' in user_config:
                restore_objects(user_config['log'], cls.LOG_CONFIG)
                cls.LOG_CONFIG.update(user_config['log'])

            if 'treatment' in user_config:
                cls.TREATMENT_CONFIG.update(user_config['treatment'])

            return True

        except Exception as e:
            print(f"加载用户配置失败: {e}")
            return False

    @classmethod
    def validate_config(cls) -> bool:
        """验证配置有效性"""
        try:
            # 检查必要的目录
            cls.create_directories()

            # 检查数据库路径
            db_dir = cls.DATABASE_CONFIG['path'].parent
            if not db_dir.exists():
                db_dir.mkdir(parents=True, exist_ok=True)

            # 检查日志路径
            log_dir = cls.LOG_CONFIG['file_path'].parent
            if not log_dir.exists():
                log_dir.mkdir(parents=True, exist_ok=True)

            return True

        except Exception as e:
            print(f"配置验证失败: {e}")
            return False


# 环境变量覆盖配置
def load_env_config():
    """从环境变量加载配置覆盖"""
    # 数据库路径
    if 'NK_DB_PATH' in os.environ:
        AppConfig.DATABASE_CONFIG['path'] = Path(os.environ['NK_DB_PATH'])

    # 日志级别
    if 'NK_LOG_LEVEL' in os.environ:
        AppConfig.LOG_CONFIG['level'] = os.environ['NK_LOG_LEVEL']

    # 串口配置
    if 'NK_SERIAL_PORT' in os.environ:
        AppConfig.EEG_CONFIG['serial_port'] = os.environ['NK_SERIAL_PORT']

    if 'NK_BAUD_RATE' in os.environ:
        AppConfig.EEG_CONFIG['baud_rate'] = int(os.environ['NK_BAUD_RATE'])


# 初始化时加载环境配置和用户配置
load_env_config()
AppConfig.load_user_config()

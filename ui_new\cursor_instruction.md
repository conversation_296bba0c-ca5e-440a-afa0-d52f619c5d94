# Cursor AI 重构指令

## 项目重构要求

请帮我重构脑机接口康复训练系统，从现有项目升级为现代化高端医疗系统界面。

### 🎯 目标效果
参考现代医疗设备界面设计，实现高端、专业、美观的用户体验，符合医疗器械行业标准。

### 🛠 技术栈
- **框架**: PySide6
- **样式**: QSS (Qt Style Sheets)
- **布局**: QGridLayout, QHBoxLayout, QVBoxLayout
- **动画**: QPropertyAnimation

### 📋 核心要求

#### 1. 整体架构
```
主窗口 (QMainWindow)
├── 侧边栏 (Sidebar) - 宽度320px，可折叠至80px
├── 主内容区
│   ├── 顶部栏 (TopBar) - 高度88px
│   └── 页面容器 (QStackedWidget)
```

#### 2. 页面结构 (保持原有6个页面)
- **实时监测仪表板** (dashboard) - 默认首页
- **患者管理** (patients)
- **治疗系统** (treatment)  
- **数据管理** (data)
- **报告分析** (reports)
- **用户管理** (users)
- **系统设置** (settings)

#### 3. 双主题系统
**医疗主题 (默认)**:
- 主色调: #2563eb (蓝色)
- 辅助色: #0ea5e9 (浅蓝)
- 背景: #ffffff (白色)
- 次级背景: #f8fafc (浅灰)

**科技主题**:
- 主色调: #06b6d4 (青色)
- 辅助色: #00d4ff (亮青)
- 背景: #0f172a (深蓝)
- 次级背景: #1e293b (深灰)

#### 4. 关键UI组件

**侧边栏 (sidebar.py)**:
```python
class Sidebar(QWidget):
    # Logo区域: "BCI" + "神经康复系统"
    # 导航分组:
    # - 核心功能: 监测仪表板、患者管理、治疗系统
    # - 数据分析: 数据管理、报告分析
    # - 系统管理: 用户管理、系统设置
    # 用户信息区域
```

**顶部栏 (topbar.py)**:
```python
class TopBar(QWidget):
    # 左侧: 菜单切换按钮 + 页面标题
    # 右侧: 主题切换开关 + 状态指示器
```

**仪表板页面 (dashboard_page.py)**:
```python
class DashboardPage(QWidget):
    # 顶部: 4个统计卡片 (活跃患者、识别率、完成训练、信噪比)
    # 主区域: 脑电监测可视化 (圆形大脑轮廓 + 电极点动画)
    # 右侧: 控制面板 + 患者状态
```

#### 5. 详细样式规范 (QSS)

**🎨 颜色变量系统**
```css
/* 医疗主题颜色 */
--primary-color: #2563eb;        /* 主色调 */
--secondary-color: #1e40af;      /* 深主色 */
--accent-color: #0ea5e9;         /* 强调色 */
--success-color: #10b981;        /* 成功色 */
--warning-color: #f59e0b;        /* 警告色 */
--danger-color: #ef4444;         /* 危险色 */
--bg-primary: #f8fafc;           /* 主背景 */
--bg-secondary: #ffffff;         /* 次级背景 */
--bg-tertiary: #f1f5f9;          /* 三级背景 */
--text-primary: #1e293b;         /* 主文字 */
--text-secondary: #64748b;       /* 次级文字 */
--border-color: #e2e8f0;         /* 边框色 */

/* 科技主题颜色 */
--tech-primary: #06b6d4;         /* 科技主色 */
--tech-secondary: #0891b2;       /* 科技深主色 */
--tech-accent: #00d4ff;          /* 科技强调色 */
--tech-success: #00ff88;         /* 科技成功色 */
--tech-warning: #ffaa00;         /* 科技警告色 */
--tech-danger: #ff3366;          /* 科技危险色 */
--tech-bg-primary: #0f172a;      /* 科技主背景 */
--tech-bg-secondary: #1e293b;    /* 科技次级背景 */
--tech-bg-tertiary: #334155;     /* 科技三级背景 */
--tech-text-primary: #f1f5f9;    /* 科技主文字 */
--tech-text-secondary: #94a3b8;  /* 科技次级文字 */
--tech-border: #475569;          /* 科技边框色 */
```

**📐 尺寸规范**
```css
/* 圆角半径 */
--radius-small: 8px;             /* 小组件 */
--radius-medium: 12px;           /* 中等组件 */
--radius-large: 16px;            /* 卡片 */
--radius-xlarge: 20px;           /* 大容器 */

/* 间距系统 */
--spacing-xs: 4px;               /* 极小间距 */
--spacing-sm: 8px;               /* 小间距 */
--spacing-md: 12px;              /* 中等间距 */
--spacing-lg: 16px;              /* 大间距 */
--spacing-xl: 20px;              /* 超大间距 */
--spacing-2xl: 24px;             /* 巨大间距 */
--spacing-3xl: 32px;             /* 特大间距 */

/* 字体大小 */
--text-xs: 12px;                 /* 极小文字 */
--text-sm: 13px;                 /* 小文字 */
--text-base: 14px;               /* 基础文字 */
--text-lg: 16px;                 /* 大文字 */
--text-xl: 18px;                 /* 超大文字 */
--text-2xl: 20px;                /* 标题文字 */
--text-3xl: 24px;                /* 大标题 */
--text-4xl: 32px;                /* 超大标题 */
```

**🏗 主要组件样式**

**主窗口**:
```css
QMainWindow {
    background-color: #f8fafc;
    font-family: "Microsoft YaHei", "Segoe UI", Arial, sans-serif;
    font-size: 14px;
    color: #1e293b;
}

QMainWindow[theme="tech"] {
    background-color: #0f172a;
    color: #f1f5f9;
}
```

**侧边栏**:
```css
#sidebar {
    background-color: #ffffff;
    border-right: 1px solid #e2e8f0;
    min-width: 320px;
    max-width: 320px;
}

#sidebar[collapsed="true"] {
    min-width: 80px;
    max-width: 80px;
}

#sidebar[theme="tech"] {
    background-color: #1e293b;
    border-right: 1px solid #475569;
}

/* Logo区域 */
#sidebar_header {
    padding: 32px 24px;
    border-bottom: 1px solid #e2e8f0;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #ffffff, stop:1 #f8fafc);
}

#sidebar_header[theme="tech"] {
    border-bottom: 1px solid #475569;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #1e293b, stop:1 #334155);
}

#logo {
    width: 56px;
    height: 56px;
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #2563eb, stop:1 #0ea5e9);
    border-radius: 16px;
    color: white;
    font-size: 24px;
    font-weight: 700;
    border: none;
}

#logo[theme="tech"] {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #06b6d4, stop:1 #00d4ff);
}

#logo_title {
    font-size: 20px;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 4px;
}

#logo_title[theme="tech"] {
    color: #f1f5f9;
}

#logo_subtitle {
    font-size: 14px;
    font-weight: 500;
    color: #64748b;
}

#logo_subtitle[theme="tech"] {
    color: #94a3b8;
}
```

**导航菜单**:
```css
/* 导航分组标题 */
.nav-section-title {
    padding: 0px 24px 16px 24px;
    font-size: 13px;
    font-weight: 700;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.nav-section-title[theme="tech"] {
    color: #94a3b8;
}

/* 导航项按钮 */
.nav-item {
    background-color: transparent;
    border: none;
    padding: 16px 24px;
    margin: 0px 16px 8px 16px;
    border-radius: 12px;
    text-align: left;
    font-size: 14px;
    font-weight: 500;
    color: #1e293b;
    min-height: 48px;
}

.nav-item:hover {
    background-color: #f1f5f9;
    transform: translateX(4px);
}

.nav-item:pressed {
    background-color: #e2e8f0;
}

.nav-item[active="true"] {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #2563eb, stop:1 #0ea5e9);
    color: white;
    font-weight: 600;
}

.nav-item[theme="tech"] {
    color: #f1f5f9;
}

.nav-item[theme="tech"]:hover {
    background-color: #334155;
}

.nav-item[theme="tech"][active="true"] {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #06b6d4, stop:1 #00d4ff);
}

/* 导航图标 */
.nav-icon {
    width: 24px;
    height: 24px;
    margin-right: 16px;
}
```

**顶部栏**:
```css
#topbar {
    background-color: #ffffff;
    border-bottom: 1px solid #e2e8f0;
    min-height: 88px;
    max-height: 88px;
    padding: 0px 32px;
}

#topbar[theme="tech"] {
    background-color: #1e293b;
    border-bottom: 1px solid #475569;
}

#menu_toggle {
    width: 48px;
    height: 48px;
    border: none;
    background-color: #f1f5f9;
    border-radius: 12px;
    font-size: 20px;
    color: #64748b;
}

#menu_toggle:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #2563eb, stop:1 #0ea5e9);
    color: white;
    transform: scale(1.05);
}

#menu_toggle[theme="tech"] {
    background-color: #334155;
    color: #94a3b8;
}

#menu_toggle[theme="tech"]:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #06b6d4, stop:1 #00d4ff);
}

#page_title {
    font-size: 32px;
    font-weight: 700;
    color: #2563eb;
    margin-left: 12px;
}

#page_title[theme="tech"] {
    color: #06b6d4;
}
```

**主题切换开关**:
```css
#theme_switch_container {
    background-color: #f1f5f9;
    border-radius: 12px;
    padding: 12px 16px;
    font-size: 14px;
    font-weight: 600;
}

#theme_switch_container[theme="tech"] {
    background-color: #334155;
}

#theme_switch {
    width: 56px;
    height: 28px;
    background-color: #e2e8f0;
    border-radius: 14px;
    border: none;
    margin: 0px 12px;
}

#theme_switch[active="true"] {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #2563eb, stop:1 #0ea5e9);
}

#theme_switch[theme="tech"][active="true"] {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #06b6d4, stop:1 #00d4ff);
}
```

**状态指示器**:
```css
.status-indicator {
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 600;
    margin-left: 16px;
}

.status-normal {
    background-color: rgba(16, 185, 129, 0.1);
    color: #10b981;
}

.status-warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
}

.status-error {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: currentColor;
    margin-right: 8px;
}
```

**卡片组件**:
```css
.stat-card {
    background-color: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 16px;
    padding: 24px;
    margin: 12px;
    min-height: 120px;
}

.stat-card:hover {
    transform: translateY(-4px);
    border-color: #cbd5e1;
}

.stat-card[theme="tech"] {
    background-color: #1e293b;
    border: 1px solid #475569;
}

.stat-card[theme="tech"]:hover {
    border-color: #64748b;
}

.stat-value {
    font-size: 36px;
    font-weight: 700;
    color: #2563eb;
    margin-bottom: 8px;
    line-height: 1;
}

.stat-value[theme="tech"] {
    color: #06b6d4;
}

.stat-label {
    font-size: 14px;
    color: #64748b;
    font-weight: 500;
    margin-bottom: 8px;
}

.stat-label[theme="tech"] {
    color: #94a3b8;
}

.stat-change {
    font-size: 12px;
    font-weight: 600;
}

.stat-increase {
    color: #10b981;
}

.stat-decrease {
    color: #ef4444;
}

.main-card {
    background-color: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 20px;
    padding: 32px;
    margin: 16px;
}

.main-card[theme="tech"] {
    background-color: #1e293b;
    border: 1px solid #475569;
}

.card-header {
    padding-bottom: 16px;
    margin-bottom: 24px;
    border-bottom: 1px solid #e2e8f0;
}

.card-header[theme="tech"] {
    border-bottom: 1px solid #475569;
}

.card-title {
    font-size: 18px;
    font-weight: 700;
    color: #1e293b;
}

.card-title[theme="tech"] {
    color: #f1f5f9;
}
```

**按钮组件**:
```css
.btn-primary {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #2563eb, stop:1 #0ea5e9);
    color: white;
    border: none;
    border-radius: 10px;
    padding: 12px 20px;
    font-weight: 600;
    font-size: 14px;
    min-height: 44px;
}

.btn-primary:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #1e40af, stop:1 #0891b2);
    transform: translateY(-2px);
}

.btn-primary:pressed {
    transform: translateY(0px);
}

.btn-primary[theme="tech"] {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #06b6d4, stop:1 #00d4ff);
}

.btn-primary[theme="tech"]:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #0891b2, stop:1 #0ea5e9);
}

.btn-secondary {
    background-color: #f1f5f9;
    color: #1e293b;
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    padding: 12px 20px;
    font-weight: 600;
    font-size: 14px;
    min-height: 44px;
}

.btn-secondary:hover {
    background-color: #e2e8f0;
    border-color: #cbd5e1;
}

.btn-secondary[theme="tech"] {
    background-color: #334155;
    color: #f1f5f9;
    border: 1px solid #475569;
}

.btn-secondary[theme="tech"]:hover {
    background-color: #475569;
    border-color: #64748b;
}
```

**表单组件**:
```css
QLineEdit, QComboBox, QSpinBox {
    padding: 12px 16px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background-color: #ffffff;
    font-size: 14px;
    color: #1e293b;
    min-height: 20px;
}

QLineEdit:focus, QComboBox:focus, QSpinBox:focus {
    border-color: #2563eb;
    outline: none;
}

QLineEdit[theme="tech"], QComboBox[theme="tech"], QSpinBox[theme="tech"] {
    background-color: #334155;
    border: 1px solid #475569;
    color: #f1f5f9;
}

QLineEdit[theme="tech"]:focus, QComboBox[theme="tech"]:focus, QSpinBox[theme="tech"]:focus {
    border-color: #06b6d4;
}

QProgressBar {
    border: none;
    background-color: #f1f5f9;
    border-radius: 4px;
    height: 8px;
}

QProgressBar::chunk {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #2563eb, stop:1 #0ea5e9);
    border-radius: 4px;
}

QProgressBar[theme="tech"] {
    background-color: #334155;
}

QProgressBar[theme="tech"]::chunk {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #06b6d4, stop:1 #00d4ff);
}
```

**滚动条**:
```css
QScrollBar:vertical {
    background-color: #f1f5f9;
    width: 8px;
    border-radius: 4px;
}

QScrollBar::handle:vertical {
    background-color: #cbd5e1;
    border-radius: 4px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #94a3b8;
}

QScrollBar[theme="tech"]:vertical {
    background-color: #334155;
}

QScrollBar[theme="tech"]::handle:vertical {
    background-color: #64748b;
}

QScrollBar[theme="tech"]::handle:vertical:hover {
    background-color: #94a3b8;
}

QScrollBar::add-line, QScrollBar::sub-line {
    border: none;
    background: none;
}
```

#### 6. 阴影和动画效果

**🌟 阴影效果实现 (使用QGraphicsDropShadowEffect)**
```python
# 在Python代码中实现阴影效果
from PySide6.QtWidgets import QGraphicsDropShadowEffect
from PySide6.QtGui import QColor
from PySide6.QtCore import Qt

def apply_shadow(widget, theme="medical"):
    """为组件添加阴影效果"""
    shadow = QGraphicsDropShadowEffect()
    
    if theme == "medical":
        # 医疗主题阴影
        shadow.setColor(QColor(0, 0, 0, 25))  # 透明度25的黑色
        shadow.setBlurRadius(10)
        shadow.setOffset(0, 4)
    else:
        # 科技主题阴影
        shadow.setColor(QColor(6, 182, 212, 25))  # 透明度25的青色
        shadow.setBlurRadius(15)
        shadow.setOffset(0, 6)
    
    widget.setGraphicsEffect(shadow)

def apply_large_shadow(widget, theme="medical"):
    """为大型容器添加大阴影"""
    shadow = QGraphicsDropShadowEffect()
    
    if theme == "medical":
        shadow.setColor(QColor(0, 0, 0, 15))
        shadow.setBlurRadius(25)
        shadow.setOffset(0, 10)
    else:
        shadow.setColor(QColor(6, 182, 212, 30))
        shadow.setBlurRadius(30)
        shadow.setOffset(0, 12)
    
    widget.setGraphicsEffect(shadow)

# 阴影应用示例
apply_shadow(sidebar_widget, current_theme)          # 侧边栏阴影
apply_large_shadow(main_card_widget, current_theme)  # 主卡片阴影
apply_shadow(button_widget, current_theme)          # 按钮阴影
```

**🎬 动画过渡效果**
```python
from PySide6.QtCore import QPropertyAnimation, QEasingCurve, QParallelAnimationGroup
from PySide6.QtWidgets import QGraphicsOpacityEffect

class AnimationHelper:
    @staticmethod
    def create_slide_animation(widget, start_pos, end_pos, duration=300):
        """创建滑动动画"""
        animation = QPropertyAnimation(widget, b"pos")
        animation.setDuration(duration)
        animation.setStartValue(start_pos)
        animation.setEndValue(end_pos)
        animation.setEasingCurve(QEasingCurve.OutCubic)
        return animation
    
    @staticmethod
    def create_fade_animation(widget, start_opacity, end_opacity, duration=300):
        """创建淡入淡出动画"""
        opacity_effect = QGraphicsOpacityEffect()
        widget.setGraphicsEffect(opacity_effect)
        
        animation = QPropertyAnimation(opacity_effect, b"opacity")
        animation.setDuration(duration)
        animation.setStartValue(start_opacity)
        animation.setEndValue(end_opacity)
        animation.setEasingCurve(QEasingCurve.OutCubic)
        return animation
    
    @staticmethod
    def create_size_animation(widget, start_size, end_size, duration=300):
        """创建尺寸变化动画"""
        animation = QPropertyAnimation(widget, b"minimumWidth")
        animation.setDuration(duration)
        animation.setStartValue(start_size.width())
        animation.setEndValue(end_size.width())
        animation.setEasingCurve(QEasingCurve.OutCubic)
        return animation

# 具体动画参数
ANIMATION_DURATION_FAST = 200      # 快速动画 (按钮悬停)
ANIMATION_DURATION_NORMAL = 300    # 正常动画 (页面切换)
ANIMATION_DURATION_SLOW = 500      # 慢速动画 (大型转换)

# 缓动曲线类型
EASING_SMOOTH = QEasingCurve.OutCubic      # 平滑过渡
EASING_BOUNCE = QEasingCurve.OutBack       # 弹跳效果
EASING_ELASTIC = QEasingCurve.OutElastic   # 弹性效果
```

**📱 响应式设计断点**
```python
# 响应式断点定义
BREAKPOINT_MOBILE = 768     # 移动端
BREAKPOINT_TABLET = 1024    # 平板端
BREAKPOINT_DESKTOP = 1200   # 桌面端
BREAKPOINT_LARGE = 1600     # 大屏幕

class ResponsiveHelper:
    @staticmethod
    def get_sidebar_width(screen_width):
        """根据屏幕宽度返回侧边栏宽度"""
        if screen_width < BREAKPOINT_MOBILE:
            return 0  # 移动端隐藏侧边栏
        elif screen_width < BREAKPOINT_TABLET:
            return 80  # 平板端折叠侧边栏
        else:
            return 320  # 桌面端完整侧边栏
    
    @staticmethod
    def get_card_columns(screen_width):
        """根据屏幕宽度返回卡片列数"""
        if screen_width < BREAKPOINT_MOBILE:
            return 1  # 移动端单列
        elif screen_width < BREAKPOINT_TABLET:
            return 2  # 平板端双列
        elif screen_width < BREAKPOINT_DESKTOP:
            return 3  # 小桌面三列
        else:
            return 4  # 大桌面四列
    
    @staticmethod
    def get_font_scale(screen_width):
        """根据屏幕宽度返回字体缩放比例"""
        if screen_width < BREAKPOINT_MOBILE:
            return 0.9  # 移动端字体缩小
        elif screen_width < BREAKPOINT_TABLET:
            return 0.95  # 平板端字体略小
        else:
            return 1.0  # 桌面端标准字体
```

**🧠 脑电可视化特殊样式**
```python
class BrainVisualizationStyle:
    """脑电可视化组件的特殊样式参数"""
    
    # 大脑轮廓样式
    BRAIN_OUTLINE_WIDTH = 280          # 大脑轮廓宽度
    BRAIN_OUTLINE_HEIGHT = 280         # 大脑轮廓高度
    BRAIN_BORDER_WIDTH = 3             # 边框宽度
    BRAIN_BORDER_COLOR_MEDICAL = "#0ea5e9"    # 医疗主题边框色
    BRAIN_BORDER_COLOR_TECH = "#00d4ff"       # 科技主题边框色
    
    # 电极样式
    ELECTRODE_SIZE = 16                # 电极直径
    ELECTRODE_BORDER_WIDTH = 3         # 电极边框宽度
    ELECTRODE_COLOR_MEDICAL = "#2563eb"        # 医疗主题电极色
    ELECTRODE_COLOR_TECH = "#06b6d4"          # 科技主题电极色
    
    # 电极位置 (相对于大脑轮廓中心的百分比)
    ELECTRODE_POSITIONS = [
        (0.4, 0.2),    # 左前
        (0.6, 0.2),    # 右前
        (0.15, 0.5),   # 左侧
        (0.85, 0.5),   # 右侧
        (0.35, 0.7),   # 左后
        (0.65, 0.7)    # 右后
    ]
    
    # 动画参数
    PULSE_DURATION = 3000              # 大脑轮廓脉冲周期(毫秒)
    ELECTRODE_GLOW_DURATION = 2000     # 电极闪烁周期(毫秒)
    GLOW_RADIUS_MIN = 5                # 最小发光半径
    GLOW_RADIUS_MAX = 20               # 最大发光半径
```

**🎯 精确的组件尺寸规范**
```python
# 组件尺寸常量
class ComponentSizes:
    # 侧边栏
    SIDEBAR_WIDTH_EXPANDED = 320
    SIDEBAR_WIDTH_COLLAPSED = 80
    SIDEBAR_HEADER_HEIGHT = 120
    
    # 顶部栏
    TOPBAR_HEIGHT = 88
    MENU_TOGGLE_SIZE = 48
    THEME_SWITCH_WIDTH = 56
    THEME_SWITCH_HEIGHT = 28
    
    # 导航项
    NAV_ITEM_HEIGHT = 48
    NAV_ITEM_MARGIN = 8
    NAV_ITEM_PADDING = 16
    NAV_ICON_SIZE = 24
    
    # 卡片
    STAT_CARD_MIN_HEIGHT = 120
    STAT_CARD_PADDING = 24
    MAIN_CARD_PADDING = 32
    
    # 按钮
    BTN_HEIGHT = 44
    BTN_PADDING_HORIZONTAL = 20
    BTN_PADDING_VERTICAL = 12
    
    # 表单元素
    INPUT_HEIGHT = 44
    INPUT_PADDING = 16
    
    # 用户头像
    USER_AVATAR_SIZE = 48
    LOGO_SIZE = 56
    
    # 状态指示器
    STATUS_DOT_SIZE = 8
    STATUS_INDICATOR_PADDING = 8
```

**🔧 CSS变量在QSS中的模拟实现**
```css
/* 由于QSS不支持CSS变量，我们需要在Python中动态生成样式 */

/* 医疗主题完整样式模板 */
QWidget#medical_theme {
    /* 主色调系列 */
}

/* 科技主题完整样式模板 */
QWidget#tech_theme {
    /* 主色调系列 */
}

/* 可以在Python中使用字符串格式化来实现变量效果 */
```

**🎨 动态样式生成示例**
```python
class StyleGenerator:
    @staticmethod
    def generate_theme_style(theme_name):
        """动态生成主题样式"""
        if theme_name == "medical":
            colors = {
                'primary': '#2563eb',
                'secondary': '#1e40af',
                'accent': '#0ea5e9',
                'bg_primary': '#f8fafc',
                'bg_secondary': '#ffffff',
                'text_primary': '#1e293b',
                'border': '#e2e8f0'
            }
        else:  # tech theme
            colors = {
                'primary': '#06b6d4',
                'secondary': '#0891b2',
                'accent': '#00d4ff',
                'bg_primary': '#0f172a',
                'bg_secondary': '#1e293b',
                'text_primary': '#f1f5f9',
                'border': '#475569'
            }
        
        # 使用字符串格式化生成完整样式
        style_template = """
        QMainWindow {{
            background-color: {bg_primary};
            color: {text_primary};
        }}
        
        #sidebar {{
            background-color: {bg_secondary};
            border-right: 1px solid {border};
        }}
        
        .btn-primary {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {primary}, stop:1 {accent});
            color: white;
            border-radius: 10px;
            padding: 12px 20px;
            font-weight: 600;
        }}
        
        .nav-item[active="true"] {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {primary}, stop:1 {accent});
            color: white;
        }}
        """
        
        return style_template.format(**colors)
```

#### 7. 具体实现要求

**文件结构**:
```
项目根目录/
├── main.py                    # 程序入口
├── ui/
│   ├── main_window.py        # 主窗口
│   ├── components/           # UI组件
│   │   ├── sidebar.py
│   │   ├── topbar.py
│   │   └── base_widgets.py
│   └── pages/               # 页面
│       ├── dashboard_page.py
│       ├── patients_page.py
│       └── ...
├── resources/
│   ├── qss/                 # 样式文件
│   │   ├── medical_theme.qss
│   │   └── tech_theme.qss
│   └── icons/               # 图标
└── utils/
    ├── theme_manager.py     # 主题管理
    └── animations.py        # 动画工具
```

### 🔧 具体实现步骤 - 最终版本

1. **第一步**: 创建基础框架 (MainWindow + Sidebar + TopBar)
2. **第二步**: 实现主题系统和QSS样式
3. **第三步**: 开发协调布局的仪表板页面 (一屏显示优化)
4. **第四步**: 迁移现有功能到新框架
5. **第五步**: 添加动画效果和交互优化

### 💡 关键技术点 - 协调布局版

**一屏显示布局实现**:
```python
class DashboardLayoutManager:
    """确保仪表板内容一屏显示的布局管理器"""
    
    def __init__(self):
        # 布局尺寸比例 - 3:8:3 的协调比例
        self.row_ratios = [3, 8, 3]  # 统计卡片:监测区域:实时数据
        self.total_height = "calc(100vh - 120px)"
        
    def calculate_heights(self, available_height):
        """计算各行的具体高度"""
        total_ratio = sum(self.row_ratios)
        spacing = 24 * 2  # 两个间距
        content_height = available_height - spacing
        
        return [
            int(content_height * ratio / total_ratio) 
            for ratio in self.row_ratios
        ]
    
    def apply_fixed_heights(self, widgets):
        """应用固定高度确保布局稳定"""
        heights = [120, 320, 120]  # 最终确定的高度
        for widget, height in zip(widgets, heights):
            widget.setFixedHeight(height)
```

**脑电可视化优化**:
```python
class OptimizedBrainVisualization:
    """优化的脑电可视化 - 180px直径，清晰动画"""
    
    BRAIN_DIAMETER = 180
    ELECTRODE_SIZE = 14
    ANIMATION_SMOOTH = True
    
    def draw_brain_with_electrodes(self, painter, center_point):
        """绘制协调尺寸的大脑和电极"""
        # 大脑轮廓 - 3px边框，清晰可见
        brain_pen = QPen(QColor("#00d4ff"), 3)
        painter.setPen(brain_pen)
        
        radius = self.BRAIN_DIAMETER // 2
        painter.drawEllipse(
            center_point.x() - radius,
            center_point.y() - radius,
            self.BRAIN_DIAMETER,
            self.BRAIN_DIAMETER
        )
        
        # 电极位置 - 对称布局，大小适中
        electrode_positions = [
            (-34, -30), (34, -30),    # 前额
            (-45, 0), (45, 0),        # 颞侧
            (-31, 30), (31, 30)       # 后侧
        ]
        
        for i, (dx, dy) in enumerate(electrode_positions):
            self.draw_electrode(painter, center_point, dx, dy, i)
```

**主题切换实现**:
```python
class ThemeManager(QObject):
    theme_changed = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.current_theme = "tech"  # 默认科技主题
        self.themes = {
            "medical": self.generate_medical_style(),
            "tech": self.generate_tech_style()
        }
    
    def generate_tech_style(self):
        """生成科技主题的完整样式"""
        return """
        /* 科技主题 - 深色配色 */
        QMainWindow {
            background-color: #0f172a;
            color: #f1f5f9;
        }
        
        #stat_card, #brain_monitor_widget, #control_panel_widget,
        #realtime_data_card, #patient_status_widget {
            background-color: #1e293b;
            border: 1px solid #475569;
        }
        
        #stat_value, #data_value {
            color: #06b6d4;
        }
        
        /* 渐变按钮 */
        #btn_primary, #btn_primary_large {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #06b6d4, stop:1 #00d4ff);
        }
        """
    
    def toggle_theme(self):
        """切换主题"""
        new_theme = "medical" if self.current_theme == "tech" else "tech"
        self.load_theme(new_theme)
        
    def load_theme(self, theme_name):
        """加载指定主题"""
        if theme_name in self.themes:
            style = self.themes[theme_name]
            QApplication.instance().setStyleSheet(style)
            self.current_theme = theme_name
            self.theme_changed.emit(theme_name)
```

### ⚠️ 重要注意事项 - 最终版本

1. **严格遵循一屏显示原则**: 
   - 仪表板总高度不超过 `calc(100vh - 120px)`
   - 使用固定高度 120px + 320px + 120px 确保布局稳定
   - 测试不同分辨率下的显示效果

2. **保持协调的视觉比例**:
   - 统计卡片使用32px字体，保持可读性
   - 脑电可视化180px直径，电极14px大小
   - 按钮高度36px/48px，确保触控友好

3. **优化交互体验**:
   - 所有动画流畅运行，避免界面卡顿
   - 主题切换即时生效，无闪烁
   - 响应式设计在移动端良好适配

4. **性能考虑**:
   - 脑电动画使用优化的绘制算法
   - 主题切换使用缓存的样式字符串
   - 避免频繁的布局重计算

5. **代码规范**:
   - 组件名称使用描述性命名（非compact前缀）
   - 所有魔术数字定义为常量
   - 添加详细的类和方法注释

开始重构时请优先实现仪表板的一屏显示效果，确保布局协调美观。有任何问题请随时询问！
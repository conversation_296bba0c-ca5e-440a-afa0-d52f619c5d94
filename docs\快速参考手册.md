# NK脑机接口系统 - 快速参考手册

## 🚀 快速开始

### 系统启动
```bash
# 1. 激活虚拟环境 (如果使用)
nk_env\Scripts\activate

# 2. 启动系统
python main.py

# 3. 或使用批处理文件
启动系统.bat
```

### 首次登录
- **用户名**: admin
- **密码**: 123456
- **建议**: 首次登录后立即修改密码

## 📋 核心功能速查

### 患者管理
| 功能 | 操作路径 | 快捷键 |
|------|----------|--------|
| 添加患者 | 患者管理 → 添加患者 | Ctrl+N |
| 编辑患者 | 双击患者列表项 | F2 |
| 删除患者 | 选中患者 → 删除 | Delete |
| 搜索患者 | 搜索框输入关键词 | Ctrl+F |
| 导出数据 | 数据导出 → 选择格式 | Ctrl+E |

### 治疗系统
| 功能 | 操作路径 | 说明 |
|------|----------|------|
| 连接脑电设备 | 治疗系统 → 连接脑电 | 确保COM8端口正确 |
| 连接电刺激设备 | 治疗系统 → 连接电刺激 | 检查DLL文件路径 |
| 开始治疗 | 选择患者 → 开始治疗 | 需先连接设备 |
| 停止治疗 | 停止治疗按钮 | 自动保存数据 |
| 参数调节 | 在线分类/电刺激参数区 | 实时生效 |

### 报告分析
| 功能 | 操作路径 | 输出格式 |
|------|----------|----------|
| 个人报告 | 报告分析 → 个人综合报告 | PDF |
| 统计报告 | 报告分析 → 按日统计 | PDF/图表 |
| 数据导出 | 数据导出 → 选择格式 | CSV/Excel |
| 图表查看 | 报告预览区域 | 实时显示 |

## ⚙️ 系统配置速查

### 设备配置
```json
// 脑电设备配置
"eeg": {
    "serial_port": "COM8",      // 串口号
    "baud_rate": 115200,        // 波特率
    "sample_rate": 125.0,       // 采样率
    "channels": 8               // 通道数
}

// 电刺激设备配置
"stimulation": {
    "port_num": 1,              // 端口号
    "max_current": 100,         // 最大电流(mA)
    "default_frequency": 20,    // 默认频率(Hz)
    "default_pulse_width": 200  // 默认脉宽(μs)
}
```

### 算法参数
```json
// 信号处理参数
"signal_processing": {
    "filter_config": {
        "highpass_freq": 0.5,   // 高通滤波(Hz)
        "lowpass_freq": 50.0,   // 低通滤波(Hz)
        "notch_freq": 50.0      // 陷波滤波(Hz)
    },
    "deep_learning": {
        "epochs": 50,           // 训练轮数
        "batch_size": 32,       // 批次大小
        "learning_rate": 0.001, // 学习率
        "temperature": 1.0      // 温度参数
    }
}
```

## 🔧 常用命令

### 系统检查
```bash
# 检查Python版本
python --version

# 检查依赖包
python check_dependencies.py

# 系统测试
python test_system.py

# 快速验证
python quick_start.py
```

### 数据库操作
```bash
# 数据库完整性检查
python -c "from core.database_manager import DatabaseManager; db = DatabaseManager(); print('数据库状态正常' if db.validate_database() else '数据库异常')"

# 手动备份
python -c "from core.database_manager import DatabaseManager; db = DatabaseManager(); print('备份成功' if db.backup_database() else '备份失败')"

# 查看数据库大小
dir data\nk_system.db
```

### 日志查看
```bash
# 查看最新日志
type logs\nk_system.log | more

# 查看错误日志
type logs\nk_system.log | findstr "ERROR"

# 查看今天的日志
type logs\nk_system.log | findstr "%date%"
```

## 🚨 故障排除速查

### 启动问题
| 问题 | 可能原因 | 解决方案 |
|------|----------|----------|
| 程序无法启动 | Python版本不兼容 | 检查Python版本(需3.9+) |
| 依赖包错误 | 包未安装或版本不对 | 运行 `python install_dependencies.py` |
| 数据库错误 | 数据库文件损坏 | 从备份恢复或重新初始化 |
| 权限错误 | 文件权限不足 | 以管理员身份运行 |

### 设备连接问题
| 设备 | 问题 | 解决方案 |
|------|------|----------|
| 脑电设备 | 连接失败 | 检查COM端口号、设备驱动 |
| 电刺激设备 | 无法连接 | 检查DLL路径、USB连接 |
| 设备冲突 | 端口被占用 | 关闭其他程序，重新连接 |

### 性能问题
| 症状 | 原因 | 解决方案 |
|------|------|----------|
| 界面卡顿 | 内存不足 | 关闭其他程序，增加内存 |
| 数据处理慢 | CPU负载高 | 降低采样率，优化算法参数 |
| 存储空间不足 | 磁盘满 | 清理日志文件，移动数据 |

## 📊 参数范围速查

### 电刺激参数
| 参数 | 范围 | 单位 | 默认值 |
|------|------|------|--------|
| 频率 | 2-160 | Hz | 20 |
| 脉宽 | 10-500 | μs | 200 |
| 电流 | 1-100 | mA | 15 |
| 休息时间 | 0-16 | s | 5 |
| 上升时间 | 0-5 | s | 2 |
| 工作时间 | 0-30 | s | 10 |
| 下降时间 | 0-5 | s | 2 |

### 脑电参数
| 参数 | 值 | 说明 |
|------|-----|------|
| 采样率 | 125 Hz | 固定值 |
| 通道数 | 8 | PZ,P3,P4,C3,CZ,C4,F3,F4 |
| 数据位数 | 24 bit | ADS1299规格 |
| 阻抗阈值 | <5kΩ | 良好连接标准 |

### 算法参数
| 参数 | 推荐值 | 说明 |
|------|--------|------|
| 训练样本数 | 100 | 每个状态的样本数 |
| 窗口长度 | 2秒 | 特征提取窗口 |
| 重叠率 | 50% | 窗口重叠比例 |
| CSP组件数 | 6 | 空间滤波器数量 |

## 🔐 用户权限速查

### 角色权限
| 角色 | 患者管理 | 治疗操作 | 报告生成 | 系统设置 | 用户管理 |
|------|----------|----------|----------|----------|----------|
| 管理员 | ✓ | ✓ | ✓ | ✓ | ✓ |
| 医生 | ✓ | ✓ | ✓ | ✗ | ✗ |
| 技师 | 部分 | ✓ | 部分 | 部分 | ✗ |
| 操作员 | 部分 | ✓ | ✗ | ✗ | ✗ |

### 密码策略
- **最小长度**: 6位
- **必须包含**: 数字
- **有效期**: 90天
- **历史密码**: 不能重复使用最近3个密码

## 📁 重要文件路径

### 配置文件
```
data/user_config.json          # 用户配置
data/nk_system.db             # 主数据库
logs/nk_system.log            # 系统日志
libs/RecoveryDLL.dll          # 电刺激DLL
```

### 数据目录
```
data/backup/                  # 数据库备份
data/models/                  # 训练模型
data/pretrained_models/       # 预训练模型
reports/                      # 生成报告
```

## 🌐 网络配置

### HTTP接口
- **服务器**: http://111.17.215.37:8082/shdekf/Api/
- **超时**: 30秒
- **重试**: 1次

### UDP通信
- **VR地址**: 127.0.0.1:3004
- **本地端口**: 3005
- **协议**: 文本命令

## 📞 技术支持

### 常用联系方式
- **技术支持**: 通过项目管理系统提交工单
- **紧急故障**: 联系系统管理员
- **用户培训**: 联系培训部门

### 自助资源
- **完整文档**: `docs/完整开发文档.md`
- **API文档**: `docs/API接口文档.md`
- **部署指南**: `docs/部署运维指南.md`
- **在线帮助**: 系统内置帮助功能

## 📝 版本信息

- **当前版本**: 1.0.0
- **发布日期**: 2024-12-19
- **Python要求**: 3.9+
- **系统要求**: Windows 10/11

## ⚡ 性能优化提示

### 系统级优化
1. 设置高性能电源计划
2. 关闭不必要的后台程序
3. 增加虚拟内存大小
4. 使用SSD存储

### 应用级优化
1. 定期清理日志文件
2. 优化数据库(VACUUM)
3. 调整缓冲区大小
4. 启用GPU加速(如有)

### 网络优化
1. 使用有线网络连接
2. 配置防火墙例外
3. 优化网络参数
4. 监控网络延迟

---

**快速参考手册版本**: 1.0.0  
**适用系统版本**: NK脑机接口系统 v1.0.0  
**最后更新**: 2024-12-19

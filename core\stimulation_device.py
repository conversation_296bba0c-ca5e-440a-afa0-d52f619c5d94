#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
电刺激设备控制模块
Electrical Stimulation Device Control Module

作者: AI Assistant
版本: 1.0.0
"""

import logging
import ctypes
import threading
import time
from typing import Optional, Dict, Any, Callable
from dataclasses import dataclass
from enum import Enum
from pathlib import Path

from utils.app_config import AppConfig


class StimulationDeviceStatus(Enum):
    """电刺激设备状态枚举"""
    DISCONNECTED = "未连接"
    CONNECTING = "连接中"
    CONNECTED = "已连接"
    STIMULATING = "刺激中"
    ERROR = "错误"
    TIMEOUT = "超时"


class ChannelState(Enum):
    """通道状态枚举"""
    STOPPED = 0      # 停止
    PAUSED = 1       # 暂停
    ADJUSTING = 2    # 电流调节
    WORKING = 3      # 正常工作


class WaveType(Enum):
    """波形类型枚举"""
    BIPHASIC = 0     # 双相波
    MONOPHASIC = 1   # 单相波


@dataclass
class StimulationParameters:
    """刺激参数"""
    channel_num: int = 1          # 通道号
    frequency: float = 20.0       # 频率 (Hz)
    pulse_width: float = 200.0    # 脉宽 (μs)
    relax_time: float = 5.0       # 休息时间 (s)
    climb_time: float = 2.0       # 上升时间 (s)
    work_time: float = 10.0       # 工作时间 (s)
    fall_time: float = 2.0        # 下降时间 (s)
    wave_type: int = 0            # 波形类型


@dataclass
class DeviceInfo:
    """设备信息"""
    device_id: str = ""
    firmware_version: str = ""
    hardware_version: str = ""
    serial_number: str = ""


class StimulationDevice:
    """电刺激设备控制器"""

    def __init__(self):
        """初始化电刺激设备"""
        self.logger = logging.getLogger(__name__)

        # 设备配置
        self.config = AppConfig.STIMULATION_CONFIG
        self.dll: Optional[ctypes.CDLL] = None
        self.status = StimulationDeviceStatus.DISCONNECTED

        # 设备信息
        self.device_info = DeviceInfo()
        self.current_parameters = StimulationParameters()

        # 状态监控
        self.status_callback: Optional[Callable[[StimulationDeviceStatus], None]] = None
        self.monitor_thread: Optional[threading.Thread] = None
        self.stop_monitoring = threading.Event()

        # 统计信息
        self.connection_count = 0
        self.error_count = 0
        self.last_command_time = 0.0

        # 回调函数相关
        self.callback_func_type = None
        self.callback_function = None
        self.channel_a_status = 0
        self.channel_b_status = 0

        self.logger.info("电刺激设备控制器初始化完成")

    def _data_callback(self, pHandle, lpBuffer, nSize):
        """数据回调函数 - 根据C2说明书修正数据格式

        根据说明书2.2节，数据格式为：
        - 0x55AA (2字节) - 帧头
        - 0x01 (1字节) - 标识
        - A通道状态 (1字节)
        - A通道进度条数据 (1字节)
        - B通道状态 (1字节)
        - B通道进度条数据 (1字节)
        总共7字节

        参数:
            pHandle: 设备句柄
            lpBuffer: 数据缓冲区指针 (字节数组)
            nSize: 数据大小 (字节数)

        返回值:
            nSize: 成功处理数据的大小
            -1: 发生错误，停止读取数据
        """
        try:
            self.logger.debug(f"回调函数收到数据: nSize={nSize}")

            if nSize == 0:
                # 0字节数据包是正常的心跳包或空数据包，不需要警告
                return nSize
            elif nSize >= 6 and lpBuffer:
                # 实际测试发现设备使用6字节short数组格式，而不是说明书中的7字节格式
                # 优先尝试6字节short数组格式（实际设备格式）
                if nSize == 6:
                    # 使用short数组格式解析（实际设备格式）
                    short_buffer = ctypes.cast(lpBuffer, ctypes.POINTER(ctypes.c_short * 6)).contents

                    # 根据实际测试，A通道状态在索引2，B通道状态在索引4
                    # A通道状态 (索引2)
                    new_a_status = short_buffer[2]
                    if self.channel_a_status != new_a_status:
                        self.channel_a_status = new_a_status
                        # 只在调试模式下记录状态变化
                        self.logger.debug(f"A通道状态变化: {self.channel_a_status} -> {new_a_status}")

                        # 状态不一致检查（仅调试模式记录）
                        if new_a_status != 1 and self.status == StimulationDeviceStatus.STIMULATING:
                            status_text = self._get_channel_status_text(new_a_status)
                            self.logger.debug(f"A通道状态不一致: 实际状态为{status_text}（非1状态），但设备状态为'刺激中'")

                    # B通道状态 (索引4)
                    new_b_status = short_buffer[4]
                    if self.channel_b_status != new_b_status:
                        self.channel_b_status = new_b_status
                        # 只在调试模式下记录状态变化
                        self.logger.debug(f"B通道状态变化: {self.channel_b_status} -> {new_b_status}")

                        # 状态不一致检查（仅调试模式记录）
                        if new_b_status != 1 and self.status == StimulationDeviceStatus.STIMULATING:
                            status_text = self._get_channel_status_text(new_b_status)
                            self.logger.debug(f"B通道状态不一致: 实际状态为{status_text}（非1状态），但设备状态为'刺激中'")

                    # 详细日志记录（显示所有6个short值）
                    debug_data = [short_buffer[i] for i in range(6)]
                    self.logger.debug(f"设备状态数据(6个short): {debug_data}, A通道={new_a_status}, B通道={new_b_status}")

                elif nSize >= 7:
                    # 尝试7字节格式（说明书格式，但实际设备可能不使用）
                    buffer_array = ctypes.cast(lpBuffer, ctypes.POINTER(ctypes.c_ubyte * nSize)).contents

                    # 验证帧头：0x55AA + 0x01
                    if buffer_array[0] == 0x55 and buffer_array[1] == 0xAA and buffer_array[2] == 0x01:
                        # 按说明书格式解析
                        new_a_status = buffer_array[3]
                        new_b_status = buffer_array[5]

                        if self.channel_a_status != new_a_status:
                            self.channel_a_status = new_a_status
                            self.logger.debug(f"A通道状态(7字节格式): {new_a_status}")

                        if self.channel_b_status != new_b_status:
                            self.channel_b_status = new_b_status
                            self.logger.debug(f"B通道状态(7字节格式): {new_b_status}")
                    else:
                        # 记录原始数据用于调试
                        raw_data = [hex(buffer_array[i]) for i in range(min(nSize, 10))]
                        self.logger.warning(f"收到非标准7字节格式数据: {raw_data}")

                else:
                    # 其他长度的数据
                    self.logger.debug(f"收到其他长度数据: {nSize}字节")

                    # 尝试按旧格式解析（兼容性处理）
                    if nSize >= 6:
                        # 假设是short数组格式
                        try:
                            short_buffer = ctypes.cast(lpBuffer, ctypes.POINTER(ctypes.c_short * (nSize//2))).contents
                            if len(short_buffer) >= 6:
                                # 按旧格式解析
                                a_status = short_buffer[2] if len(short_buffer) > 2 else 0
                                b_status = short_buffer[4] if len(short_buffer) > 4 else 0

                                if self.channel_a_status != a_status:
                                    self.channel_a_status = a_status
                                    self.logger.info(f"A通道状态(旧格式): {a_status}")

                                if self.channel_b_status != b_status:
                                    self.channel_b_status = b_status
                                    self.logger.info(f"B通道状态(旧格式): {b_status}")
                        except Exception as e:
                            self.logger.warning(f"旧格式解析失败: {e}")
            else:
                # 只对非0长度的异常数据发出警告
                if nSize > 0:
                    self.logger.warning(f"收到异常长度数据: {nSize}字节")

            return nSize

        except Exception as e:
            self.logger.error(f"回调函数处理数据时发生错误: {e}")
            # 记录更详细的错误信息
            import traceback
            self.logger.error(f"错误详情: {traceback.format_exc()}")
            return -1

    def _get_channel_status_text(self, status_code):
        """根据通道状态码获取状态描述文本

        根据实际回调函数返回值：
        - 1: 刺激中
        - 所有非1的状态: 暂停
        """
        if status_code == 1:
            return "刺激中"
        else:
            return "暂停"

    def load_dll(self) -> bool:
        """加载DLL库"""
        try:
            dll_path = self.config['dll_path']
            if not Path(dll_path).exists():
                self.logger.error(f"DLL文件不存在: {dll_path}")
                return False

            # 加载DLL
            self.dll = ctypes.CDLL(str(dll_path))

            # 定义函数原型
            self._define_function_prototypes()

            self.logger.info(f"成功加载DLL: {dll_path}")
            return True

        except Exception as e:
            self.logger.error(f"加载DLL失败: {e}")
            return False

    def _define_function_prototypes(self):
        """定义DLL函数原型"""
        try:
            # 定义回调函数类型 - 根据C2说明书修正参数类型
            # 说明书2.2节：typedef int (__stdcall *funDataProc)(HANDLE pHandle, const LPVOID lpBuffer, const int nSize);
            # lpBuffer应该是字节数组指针，不是short数组指针
            self.callback_func_type = ctypes.WINFUNCTYPE(
                ctypes.c_int,           # 返回值类型
                ctypes.c_void_p,        # HANDLE pHandle
                ctypes.POINTER(ctypes.c_ubyte),  # LPVOID lpBuffer (字节数组)
                ctypes.c_int            # int nSize
            )

            # 设备连接函数
            self.dll.OpenRecPort.argtypes = [
                ctypes.c_int,           # portNum
                ctypes.c_int,           # iReadSize
                self.callback_func_type, # funDataProc callback
                ctypes.c_void_p         # HANDLE pHandle
            ]
            self.dll.OpenRecPort.restype = ctypes.c_int

            self.dll.CloseRecPort.argtypes = []
            self.dll.CloseRecPort.restype = ctypes.c_int

            self.dll.IsRecOpen.argtypes = []
            self.dll.IsRecOpen.restype = ctypes.c_bool

            # 设备信息函数
            self.dll.ReadDeviceInfo.argtypes = [ctypes.c_char_p]
            self.dll.ReadDeviceInfo.restype = ctypes.c_int

            # 设备控制函数
            self.dll.SwitchDeviceState.argtypes = [ctypes.c_int]
            self.dll.SwitchDeviceState.restype = ctypes.c_int

            self.dll.SwitchChannelState.argtypes = [ctypes.c_int, ctypes.c_int]
            self.dll.SwitchChannelState.restype = ctypes.c_int

            # 参数设置函数
            self.dll.StimPara.argtypes = [
                ctypes.c_int,    # ChanNum
                ctypes.c_double, # ActFreq
                ctypes.c_double, # PulseWidth
                ctypes.c_double, # RelaxTime
                ctypes.c_double, # ClimbTime
                ctypes.c_double, # WorkTime
                ctypes.c_double, # FallTime
                ctypes.c_int     # WaveType
            ]
            self.dll.StimPara.restype = ctypes.c_int

            # 电流控制函数
            self.dll.RegulateCurrent.argtypes = [ctypes.c_int, ctypes.c_int, ctypes.c_bool]
            self.dll.RegulateCurrent.restype = ctypes.c_int

            self.dll.CurrentSet.argtypes = [ctypes.c_int, ctypes.c_int]
            self.dll.CurrentSet.restype = ctypes.c_int

            self.logger.info("DLL函数原型定义完成")

        except Exception as e:
            self.logger.error(f"定义DLL函数原型失败: {e}")
            raise

    def connect(self, port_num: int = 1) -> bool:
        """连接电刺激设备 - 增强版本，包含完整的超时和资源清理机制"""
        try:
            self.status = StimulationDeviceStatus.CONNECTING
            self.logger.info(f"正在连接电刺激设备，端口: {port_num}")

            # 连接前先进行预清理，确保DLL状态干净
            self._pre_connection_cleanup()

            # 加载DLL
            if not self.dll and not self.load_dll():
                self.status = StimulationDeviceStatus.ERROR
                return False

            # 创建回调函数
            self.callback_function = self.callback_func_type(self._data_callback)

            # 使用带超时的连接机制
            connection_success = self._connect_with_timeout(port_num)

            if connection_success:
                self.status = StimulationDeviceStatus.CONNECTED
                self.connection_count += 1
                self.logger.info("电刺激设备连接成功")

                # 最小延时等待设备稳定（优化：从0.1秒减少到0.05秒）
                time.sleep(0.05)

                # 切换设备到循环刺激状态
                switch_result = self.dll.SwitchDeviceState(1)
                if switch_result == 0:  # 0表示成功
                    self.logger.info("设备状态切换成功")

                    # 读取设备信息
                    self._read_device_info()

                    # 启动状态监控
                    self._start_status_monitoring()

                    return True
                else:
                    self.logger.warning(f"设备状态切换失败，错误码: {switch_result}")
                    # 即使状态切换失败，连接仍然可能有效
                    return True
            else:
                self.status = StimulationDeviceStatus.ERROR
                self.logger.warning(f"连接电刺激设备超时，端口: {port_num}")

                # 连接失败时进行完整的资源清理
                self._post_connection_failure_cleanup(port_num)
                return False

        except Exception as e:
            self.logger.error(f"连接电刺激设备时发生错误: {e}")
            self.status = StimulationDeviceStatus.ERROR

            # 异常时也要进行完整的资源清理
            try:
                self._post_connection_failure_cleanup(port_num)
            except:
                pass
            return False

    def disconnect(self) -> bool:
        """断开电刺激设备连接 - 优化版本，减少不必要的等待时间"""
        try:
            self.logger.info("正在断开电刺激设备连接")

            # 停止状态监控
            self._stop_status_monitoring()

            # 停止所有刺激（移除延时，设备会自动处理）
            if self.dll and self.is_connected():
                try:
                    # 停止所有通道
                    self.dll.SwitchChannelState(1, 0)  # A通道停止
                    self.dll.SwitchChannelState(2, 0)  # B通道停止
                    # 移除 time.sleep(0.05) - 设备内部会处理状态切换
                except Exception as e:
                    self.logger.warning(f"停止通道时发生错误: {e}")

            # 关闭设备端口
            if self.dll:
                try:
                    result = self.dll.CloseRecPort()
                    if result == 1:  # 修正：1表示成功
                        self.status = StimulationDeviceStatus.DISCONNECTED
                        self.logger.info("电刺激设备断开成功")

                        # 清理回调函数引用
                        self.callback_function = None

                        # 使用快速重置而不是完整重置
                        self._fast_reset_device_state()

                        return True
                    else:
                        self.logger.error(f"断开电刺激设备失败，错误码: {result}")
                        # 即使返回错误，也设置为断开状态
                        self.status = StimulationDeviceStatus.DISCONNECTED
                        self._fast_reset_device_state()
                        return False
                except Exception as e:
                    self.logger.error(f"调用CloseRecPort时发生错误: {e}")
                    self.status = StimulationDeviceStatus.DISCONNECTED
                    self._fast_reset_device_state()
                    return False
            else:
                self.status = StimulationDeviceStatus.DISCONNECTED
                self._fast_reset_device_state()
                return True

        except Exception as e:
            self.logger.error(f"断开电刺激设备时发生错误: {e}")
            self.status = StimulationDeviceStatus.DISCONNECTED
            self._fast_reset_device_state()
            return False

    def _reset_device_state(self):
        """重置设备状态，确保下次连接时状态干净"""
        try:
            # 重置通道状态
            self.channel_a_status = 0
            self.channel_b_status = 0

            # 重置连接计数
            self.connection_count = 0

            # 清理回调函数
            self.callback_function = None

            # 重置设备信息
            self.device_info = DeviceInfo()

            # 重置当前参数为默认值
            self.current_parameters = StimulationParameters()

            # 重置状态为断开
            self.status = StimulationDeviceStatus.DISCONNECTED

            # 重要：增强的DLL重置机制
            self._enhanced_dll_reset()

            self.logger.debug("设备状态已完全重置，包括增强的DLL重置")

        except Exception as e:
            self.logger.error(f"重置设备状态失败: {e}")

    def _fast_reset_device_state(self):
        """快速重置设备状态 - 用于正常断开连接，减少等待时间"""
        try:
            # 重置通道状态
            self.channel_a_status = 0
            self.channel_b_status = 0

            # 重置连接计数
            self.connection_count = 0

            # 清理回调函数
            self.callback_function = None

            # 重置设备信息
            self.device_info = DeviceInfo()

            # 重置当前参数为默认值
            self.current_parameters = StimulationParameters()

            # 重置状态为断开
            self.status = StimulationDeviceStatus.DISCONNECTED

            # 使用轻量级DLL重置（不重新加载DLL）
            self._lightweight_dll_reset()

            self.logger.debug("设备状态已快速重置")

        except Exception as e:
            self.logger.error(f"快速重置设备状态失败: {e}")

    def _lightweight_dll_reset(self):
        """轻量级DLL重置 - 仅清理引用，延迟重新加载DLL"""
        try:
            # 清理Python层面的引用
            self.dll = None
            self.callback_function = None

            # 快速垃圾回收
            import gc
            gc.collect()

            # 不等待，立即返回
            # DLL将在下次连接时重新加载

            self.logger.debug("轻量级DLL重置完成")

        except Exception as e:
            self.logger.error(f"轻量级DLL重置失败: {e}")

    def _enhanced_dll_reset(self):
        """增强的DLL重置机制，确保彻底清理资源"""
        try:
            # 使用完整的DLL重置方法
            if self._complete_dll_reset():
                self.logger.debug("增强DLL重置成功")
            else:
                self.logger.warning("增强DLL重置失败")

        except Exception as e:
            self.logger.error(f"增强DLL重置失败: {e}")

    def _force_release_port(self, port_num: int):
        """强制释放指定端口的资源 - 基于DLL头文件的完整清理方案"""
        try:
            self.logger.debug(f"强制释放端口 {port_num} 的资源")

            if not self.dll:
                return

            # 1. 检查端口是否仍然打开
            try:
                is_open = self.dll.IsRecOpen()
                self.logger.debug(f"端口状态检查: {'打开' if is_open else '关闭'}")
            except Exception as e:
                self.logger.debug(f"检查端口状态失败: {e}")
                is_open = True  # 假设打开，继续清理

            # 2. 如果端口打开，尝试多次关闭
            if is_open:
                for attempt in range(5):  # 增加尝试次数
                    try:
                        result = self.dll.CloseRecPort()
                        if result == 1:
                            self.logger.debug(f"强制关闭端口成功 (尝试 {attempt + 1})")
                            break
                        else:
                            self.logger.debug(f"强制关闭端口失败，错误码: {result} (尝试 {attempt + 1})")
                    except Exception as e:
                        self.logger.debug(f"强制关闭端口异常: {e} (尝试 {attempt + 1})")
                    time.sleep(0.1)  # 优化：减少等待时间

            # 3. 等待DLL内部状态稳定（优化：减少等待时间）
            time.sleep(0.2)  # 优化：从0.5秒减少到0.2秒

            # 4. 验证端口是否真正关闭
            try:
                final_status = self.dll.IsRecOpen()
                if final_status:
                    self.logger.warning("端口仍然显示为打开状态，尝试额外清理")
                    # 额外的清理尝试
                    for i in range(3):
                        try:
                            self.dll.CloseRecPort()
                            time.sleep(0.1)
                        except:
                            pass
                else:
                    self.logger.debug("端口已成功关闭")
            except Exception as e:
                self.logger.debug(f"验证端口状态失败: {e}")

            self.logger.debug(f"端口 {port_num} 资源强制释放完成")

        except Exception as e:
            self.logger.error(f"强制释放端口资源失败: {e}")

    def _complete_dll_reset(self):
        """完整的DLL重置 - 基于厂家DLL规范的彻底重置"""
        try:
            self.logger.debug("开始完整DLL重置")

            # 1. 确保所有端口都关闭
            if self.dll:
                try:
                    # 多次调用CloseRecPort确保关闭
                    for i in range(5):
                        result = self.dll.CloseRecPort()
                        if result == 1:
                            self.logger.debug(f"DLL端口关闭成功 (尝试 {i + 1})")
                        time.sleep(0.1)
                except Exception as e:
                    self.logger.debug(f"DLL端口关闭过程异常: {e}")

            # 2. 清理Python层面的所有引用
            self.dll = None
            self.callback_function = None

            # 3. 强制垃圾回收
            import gc
            gc.collect()

            # 4. 等待系统资源释放（优化：减少等待时间）
            time.sleep(0.3)  # 优化：从1秒减少到0.3秒

            # 5. 重新加载DLL
            if self.load_dll():
                self.logger.debug("完整DLL重置成功")
                return True
            else:
                self.logger.warning("完整DLL重置失败")
                return False

        except Exception as e:
            self.logger.error(f"完整DLL重置失败: {e}")
            return False

    def _pre_connection_cleanup(self):
        """连接前的预清理，确保DLL状态干净"""
        try:
            self.logger.debug("执行连接前预清理")

            # 如果DLL已加载，检查是否有端口打开
            if self.dll:
                try:
                    is_open = self.dll.IsRecOpen()
                    if is_open:
                        self.logger.debug("发现端口仍然打开，执行预清理")
                        # 强制关闭端口
                        for i in range(3):
                            result = self.dll.CloseRecPort()
                            if result == 1:
                                self.logger.debug(f"预清理端口关闭成功 (尝试 {i + 1})")
                                break
                            time.sleep(0.05)  # 优化：减少等待时间

                        # 等待端口完全释放（优化：减少等待时间）
                        time.sleep(0.1)
                except Exception as e:
                    self.logger.debug(f"预清理检查端口状态失败: {e}")

            self.logger.debug("连接前预清理完成")

        except Exception as e:
            self.logger.warning(f"连接前预清理失败: {e}")

    def _connect_with_timeout(self, port_num: int) -> bool:
        """带超时的连接方法 - 优化版本，立即返回超时结果"""
        try:
            # 从配置文件获取超时时间
            timeout_seconds = AppConfig.STIMULATION_CONFIG.get('connection_timeout', 5)
            self.logger.debug(f"使用连接超时: {timeout_seconds}秒")

            import threading
            import time

            connection_result = {'success': False, 'completed': False, 'thread_ref': None}

            def connection_worker():
                """连接工作线程"""
                try:
                    # 打开设备端口 - 注意：返回值1表示成功，其他值表示失败
                    result = self.dll.OpenRecPort(port_num, 6, self.callback_function, None)

                    # 只有在没有超时的情况下才更新结果
                    if not connection_result.get('timeout', False):
                        connection_result['success'] = (result == 1)
                        connection_result['completed'] = True
                        if result == 1:
                            self.logger.debug("DLL连接调用成功")
                        else:
                            self.logger.debug(f"DLL连接调用失败，错误码: {result}")
                    else:
                        self.logger.debug(f"连接线程超时后返回，忽略结果: {result}")

                except Exception as e:
                    if not connection_result.get('timeout', False):
                        self.logger.debug(f"连接工作线程异常: {e}")
                        connection_result['success'] = False
                        connection_result['completed'] = True

            # 启动连接线程
            connection_thread = threading.Thread(target=connection_worker, daemon=True)
            connection_result['thread_ref'] = connection_thread
            connection_thread.start()

            # 等待连接完成或超时
            start_time = time.time()
            while time.time() - start_time < timeout_seconds:
                if connection_result['completed']:
                    break
                time.sleep(0.1)  # 100ms检查间隔

            # 检查连接结果
            if connection_result['completed']:
                if connection_result['success']:
                    self.logger.debug("连接成功完成")
                    return True
                else:
                    self.logger.debug("连接失败")
                    return False
            else:
                # 超时情况 - 标记超时并立即返回
                connection_result['timeout'] = True
                self.logger.warning(f"连接超时 ({timeout_seconds}秒)，连接线程将在后台继续运行")
                return False

        except Exception as e:
            self.logger.error(f"带超时连接失败: {e}")
            return False

    def _post_connection_failure_cleanup(self, port_num: int):
        """连接失败后的超快速清理 - 最优化版本"""
        try:
            self.logger.debug(f"执行连接失败后清理，端口: {port_num}")

            # 对于连接失败的情况，使用最简单的清理
            # 连接都没成功，DLL状态应该是干净的，只需要简单清理即可

            # 1. 尝试一次端口关闭（如果DLL存在）
            if self.dll:
                try:
                    self.dll.CloseRecPort()
                except:
                    pass  # 忽略错误，因为连接本来就失败了

            # 2. 清理引用但不重新加载DLL
            self.dll = None
            self.callback_function = None

            # 3. 快速垃圾回收
            import gc
            gc.collect()

            # 4. 不等待，立即返回
            # DLL将在下次连接时重新加载

            self.logger.debug("连接失败后超快速清理完成")

        except Exception as e:
            self.logger.error(f"连接失败后清理异常: {e}")

    def _fast_release_port(self, port_num: int):
        """快速端口释放 - 用于连接失败的情况"""
        try:
            self.logger.debug(f"快速释放端口: {port_num}")

            if self.dll:
                try:
                    # 只尝试1次关闭，不重试
                    result = self.dll.CloseRecPort()
                    if result == 1:
                        self.logger.debug("快速端口释放成功")
                    else:
                        self.logger.debug(f"快速端口释放返回: {result}")
                except Exception as e:
                    self.logger.debug(f"快速端口释放异常: {e}")

            # 最小等待时间
            time.sleep(0.05)

        except Exception as e:
            self.logger.debug(f"快速端口释放失败: {e}")

    def _reload_dll(self):
        """重新加载DLL，确保DLL内部状态完全重置"""
        try:
            # 清理当前DLL引用
            if self.dll:
                try:
                    # 尝试释放DLL资源
                    del self.dll
                except:
                    pass
                self.dll = None

            # 强制垃圾回收
            import gc
            gc.collect()

            # 短暂延时让系统释放资源
            time.sleep(0.1)

            # 重新加载DLL
            if self.load_dll():
                self.logger.debug("DLL重新加载成功")
            else:
                self.logger.warning("DLL重新加载失败")

        except Exception as e:
            self.logger.error(f"重新加载DLL失败: {e}")

    def is_connected(self) -> bool:
        """检查设备是否已连接"""
        try:
            if self.dll:
                return self.dll.IsRecOpen()
            return False
        except Exception as e:
            self.logger.error(f"检查设备连接状态失败: {e}")
            return False

    def get_status(self) -> StimulationDeviceStatus:
        """获取设备状态"""
        return self.status

    def set_status_callback(self, callback: Callable[[StimulationDeviceStatus], None]):
        """设置状态回调函数"""
        self.status_callback = callback

    def _read_device_info(self) -> bool:
        """读取设备信息"""
        try:
            if not self.dll:
                return False

            # 创建缓冲区
            info_buffer = ctypes.create_string_buffer(20)
            result = self.dll.ReadDeviceInfo(info_buffer)

            if result == 0:
                # 解析设备信息
                info_data = info_buffer.raw
                self.device_info.device_id = info_data[:4].hex()
                self.device_info.firmware_version = info_data[4:8].hex()
                self.device_info.hardware_version = info_data[8:12].hex()
                self.device_info.serial_number = info_data[12:20].hex()

                self.logger.info(f"设备信息读取成功: {self.device_info}")
                return True
            else:
                self.logger.error(f"读取设备信息失败，错误码: {result}")
                return False

        except Exception as e:
            self.logger.error(f"读取设备信息时发生错误: {e}")
            return False

    def set_stimulation_parameters(self, params: StimulationParameters) -> bool:
        """设置刺激参数"""
        try:
            if not self.dll or not self.is_connected():
                self.logger.error("设备未连接，无法设置参数")
                return False

            # 使用安全的DLL调用设置参数
            result = self._safe_dll_call(
                'StimPara',
                params.channel_num,
                params.frequency,
                params.pulse_width,
                params.relax_time,
                params.climb_time,
                params.work_time,
                params.fall_time,
                params.wave_type
            )

            if result is None:
                self.logger.error("设置刺激参数时DLL调用失败")
                return False
            elif result == 0:
                self.current_parameters = params
                self.logger.info(f"刺激参数设置成功: 通道{params.channel_num}, 频率{params.frequency}Hz")
                return True
            else:
                error_msg = self._get_error_message(result)
                self.logger.error(f"设置刺激参数失败，错误码: {result} ({error_msg})")
                return False

        except Exception as e:
            self.logger.error(f"设置刺激参数时发生错误: {e}")
            return False

    def _get_error_message(self, error_code: int) -> str:
        """根据错误码获取错误信息"""
        error_messages = {
            0: "成功",
            1: "命令错误",
            2: "命令参数错误",
            3: "校验错误",
            4: "没有读取到硬件的命令应答",
            5: "将命令写入串口时失败"
        }
        return error_messages.get(error_code, f"未知错误码: {error_code}")

    def _get_detailed_error_message(self, error_code: int) -> str:
        """获取详细错误信息和解决建议"""
        error_details = {
            -1: {
                'message': "设备连接失败",
                'suggestion': "检查设备电源、USB连接、重新安装驱动"
            },
            0: {
                'message': "命令执行成功",
                'suggestion': ""
            },
            1: {
                'message': "命令错误 - 不支持的命令或通道号",
                'suggestion': "检查通道号是否正确（1或2）"
            },
            2: {
                'message': "参数错误 - 参数超出允许范围",
                'suggestion': "检查频率、脉宽、电流等参数是否在安全范围内"
            },
            3: {
                'message': "校验错误 - 数据传输校验失败",
                'suggestion': "检查设备连接稳定性，可能需要重新连接"
            },
            4: {
                'message': "通信超时 - 未收到设备应答",
                'suggestion': "检查设备是否正常响应，可能需要重启设备"
            },
            5: {
                'message': "串口写入失败 - 串口通信错误",
                'suggestion': "检查串口是否被其他程序占用，重新连接设备"
            }
        }

        detail = error_details.get(error_code, {
            'message': f"未知错误码: {error_code}",
            'suggestion': "请联系技术支持"
        })

        if detail['suggestion']:
            return f"{detail['message']}。建议：{detail['suggestion']}"
        else:
            return detail['message']

    def get_stimulation_parameters(self) -> StimulationParameters:
        """获取当前刺激参数"""
        return self.current_parameters

    def diagnose_connection_issues(self) -> Dict[str, Any]:
        """全面诊断连接问题"""
        dll_path = self.config['dll_path']
        diagnosis = {
            'dll_loaded': False,
            'dll_path': str(dll_path),
            'dll_exists': False,
            'available_ports': [],
            'device_detected_ports': [],
            'current_status': self.status.value,
            'recommendations': []
        }

        try:
            # 检查DLL文件
            import os
            diagnosis['dll_exists'] = os.path.exists(dll_path)
            diagnosis['dll_loaded'] = self.dll is not None

            if not diagnosis['dll_exists']:
                diagnosis['recommendations'].append("DLL文件不存在，请检查libs目录")
                return diagnosis

            if not diagnosis['dll_loaded']:
                # 尝试加载DLL进行诊断
                if self.load_dll():
                    diagnosis['dll_loaded'] = True
                else:
                    diagnosis['recommendations'].append("DLL加载失败，请检查DLL版本和依赖")
                    return diagnosis

            # 检测可用串口
            try:
                import serial.tools.list_ports
                ports = serial.tools.list_ports.comports()
                diagnosis['available_ports'] = [port.device for port in ports]
            except ImportError:
                diagnosis['recommendations'].append("无法检测串口，请安装pyserial")

            # 尝试连接各个端口
            for port_num in range(1, 21):  # COM1-COM20
                try:
                    # 临时创建回调函数
                    temp_callback = self.callback_func_type(self._data_callback)
                    result = self.dll.OpenRecPort(port_num, 6, temp_callback, None)

                    if result == 1:  # 连接成功
                        diagnosis['device_detected_ports'].append(port_num)
                        # 立即关闭连接
                        self.dll.CloseRecPort()

                except Exception as e:
                    self.logger.debug(f"端口{port_num}测试失败: {e}")
                    continue

            # 生成建议
            if not diagnosis['device_detected_ports']:
                diagnosis['recommendations'].extend([
                    "未检测到电刺激设备",
                    "请检查设备电源是否开启",
                    "请检查USB连接是否正常",
                    "请确认设备驱动已正确安装"
                ])
            else:
                diagnosis['recommendations'].append(
                    f"检测到设备在端口: {diagnosis['device_detected_ports']}"
                )

        except Exception as e:
            diagnosis['recommendations'].append(f"诊断过程发生错误: {e}")

        return diagnosis

    def prepare_stimulation(self, channel_num: int, current_ma: float) -> bool:
        """准备电刺激（设置参数但不启动）"""
        try:
            self.logger.info(f"准备通道{channel_num}刺激，电流{current_ma}mA")

            # 1. 确保设备连接
            if not self.is_connected():
                self.logger.error("设备未连接，无法准备刺激")
                return False

            # 2. 创建针对特定通道的刺激参数
            channel_params = StimulationParameters(
                channel_num=channel_num,  # 使用传入的通道号
                frequency=self.current_parameters.frequency,
                pulse_width=self.current_parameters.pulse_width,
                relax_time=self.current_parameters.relax_time,
                climb_time=self.current_parameters.climb_time,
                work_time=self.current_parameters.work_time,
                fall_time=self.current_parameters.fall_time,
                wave_type=self.current_parameters.wave_type
            )

            # 3. 设置刺激参数
            if not self.set_stimulation_parameters(channel_params):
                self.logger.error("刺激参数设置失败")
                return False

            # 4. 设置电流强度
            if not self.set_current(channel_num, current_ma):
                self.logger.error("电流设置失败")
                return False

            # 5. 设备切换到循环刺激状态（但不启动通道）
            result = self._safe_dll_call('SwitchDeviceState', 1)  # 1 = 循环刺激状态
            if result != 0:
                error_msg = self._get_detailed_error_message(result)
                self.logger.error(f"设备切换到循环刺激状态失败: {error_msg}")
                return False

            self.logger.info(f"通道{channel_num}电刺激准备完成，电流: {current_ma}mA")
            return True

        except Exception as e:
            self.logger.error(f"准备电刺激时发生异常: {e}")
            return False

    def trigger_stimulation(self, channel_num: int) -> bool:
        """触发电刺激（基于脑电信号）- 严格按照C2说明书流程"""
        try:
            # 确保设备连接
            if not self.is_connected():
                self.logger.error("设备未连接，无法触发刺激")
                return False

            self.logger.info(f"触发通道{channel_num}电刺激")

            # 步骤1：确保设备处于循环刺激状态（关键步骤）
            self.logger.debug("步骤1: 确保设备处于循环刺激状态")
            device_state_result = self._safe_dll_call('SwitchDeviceState', 1)  # 1: 循环刺激
            if device_state_result != 0:
                error_msg = self._get_detailed_error_message(device_state_result)
                self.logger.error(f"切换设备到循环刺激状态失败: {error_msg}")
                return False

            # 短暂延时确保设备状态切换完成
            time.sleep(0.1)

            # 步骤2：根据说明书，电流调节后需要切换通道状态到正常工作状态
            self.logger.debug("步骤2: 切换通道到正常工作状态启动刺激")
            result = self._safe_dll_call('SwitchChannelState', channel_num, 3)  # 3: 正常工作状态
            if result == 0:
                self.status = StimulationDeviceStatus.STIMULATING
                self.logger.info(f"✓ 通道{channel_num}电刺激触发成功")

                # 步骤3：验证刺激状态
                time.sleep(0.2)  # 等待状态稳定
                current_status = self.get_channel_status(channel_num)
                if current_status == 1:
                    self.logger.info("✓ 刺激状态验证成功: 通道处于刺激中状态")
                else:
                    self.logger.warning(f"⚠ 刺激状态验证失败: 通道状态为{current_status} (期望: 1)")

                return True
            else:
                error_msg = self._get_detailed_error_message(result)
                self.logger.error(f"通道{channel_num}电刺激触发失败: {error_msg}")
                return False
        except Exception as e:
            self.logger.error(f"触发电刺激时发生异常: {e}")
            return False

    def pause_stimulation_new(self, channel_num: int) -> bool:
        """暂停电刺激（新实现）"""
        try:
            # 调用 SwitchChannelState(channel, 1) 暂停刺激
            result = self._safe_dll_call('SwitchChannelState', channel_num, 1)
            if result == 0:
                self.logger.info(f"通道{channel_num}电刺激暂停成功")
                return True
            else:
                error_msg = self._get_detailed_error_message(result)
                self.logger.error(f"通道{channel_num}电刺激暂停失败: {error_msg}")
                return False
        except Exception as e:
            self.logger.error(f"暂停电刺激时发生异常: {e}")
            return False

    def start_stimulation_verified(self, channel_num: int, current_ma: float) -> bool:
        """验证式刺激启动流程"""
        try:
            self.logger.info(f"开始启动通道{channel_num}刺激，电流{current_ma}mA")

            # 步骤1：确保设备连接
            if not self.is_connected():
                self.logger.error("设备未连接，无法启动刺激")
                return False

            # 步骤1.5：确保所有通道处于停止状态（重要！）
            self.logger.info("重置所有通道状态")
            for ch in [1, 2]:
                try:
                    result = self._safe_dll_call('SwitchChannelState', ch, 0)  # 0: 停止状态
                    if result == 0:
                        self.logger.debug(f"通道{ch}已重置为停止状态")
                    else:
                        self.logger.warning(f"重置通道{ch}状态失败，错误码: {result}")
                except Exception as e:
                    self.logger.warning(f"重置通道{ch}时发生异常: {e}")

            # 短暂延时确保状态更新
            time.sleep(0.2)  # 增加延时确保状态更新完成

            # 步骤2：设置设备为循环刺激状态（关键步骤，需要确保成功）
            # 先尝试切换到空闲状态(0)，再切换到循环刺激状态(1)，以确保状态转换正确
            idle_result = self._safe_dll_call('SwitchDeviceState', 0)  # 0: 空闲状态
            if idle_result != 0:
                self.logger.warning(f"切换设备到空闲状态失败，错误码: {idle_result}，继续尝试...")

            time.sleep(0.1)  # 给设备状态切换一些时间

            # 尝试最多3次切换到循环刺激状态
            max_retries = 3
            for retry in range(max_retries):
                result = self._safe_dll_call('SwitchDeviceState', 1)  # 1: 循环刺激
                if result == 0:
                    self.logger.info("设备状态切换为循环刺激模式")
                    break
                else:
                    error_msg = self._get_detailed_error_message(result)
                    self.logger.warning(f"切换设备状态失败(尝试 {retry+1}/{max_retries}): {error_msg}")
                    time.sleep(0.2)  # 短暂延时后重试

                    # 最后一次尝试前，重新初始化通道状态
                    if retry == max_retries - 2:
                        for ch in [1, 2]:
                            self._safe_dll_call('SwitchChannelState', ch, 0)
                        time.sleep(0.1)

            # 如果最终无法切换到循环刺激状态，则返回失败
            if result != 0:
                self.logger.error(f"多次尝试后仍无法切换设备到循环刺激状态，错误码: {result}")
                return False

            # 步骤3：设置刺激参数（使用正确的通道号）
            channel_params = StimulationParameters(
                channel_num=channel_num,  # 使用传入的通道号
                frequency=self.current_parameters.frequency,
                pulse_width=self.current_parameters.pulse_width,
                relax_time=self.current_parameters.relax_time,
                climb_time=self.current_parameters.climb_time,
                work_time=self.current_parameters.work_time,
                fall_time=self.current_parameters.fall_time,
                wave_type=self.current_parameters.wave_type
            )

            if not self.set_stimulation_parameters(channel_params):
                self.logger.error("刺激参数设置失败")
                return False
            self.logger.info("刺激参数设置完成")

            # 步骤4：设置电流强度
            if not self.set_current(channel_num, current_ma):
                self.logger.error("电流设置失败")
                return False
            self.logger.info(f"电流设置完成: {current_ma}mA")

            # 步骤5：启动通道（3=正常工作状态）
            result = self._safe_dll_call('SwitchChannelState', channel_num, 3)
            if result != 0:
                error_msg = self._get_detailed_error_message(result)
                self.logger.error(f"启动通道失败: {error_msg}")
                return False

            self.status = StimulationDeviceStatus.STIMULATING
            self.logger.info(f"通道{channel_num}刺激启动成功")

            # 步骤6：验证刺激状态
            time.sleep(0.2)  # 等待状态稳定

            # 使用增强的验证方法
            if self._verify_stimulation_active(channel_num):
                self.logger.info("刺激状态验证成功")
                return True
            else:
                # 状态不一致，进行诊断
                self.logger.warning("刺激状态验证失败，尝试自动修复")

                # 进行详细诊断
                diagnosis = self.diagnose_stimulation_status(channel_num)
                if not diagnosis["is_consistent"]:
                    self.logger.warning(f"状态不一致问题: {diagnosis['error_cause']}")
                    self.logger.info(f"修复建议: {diagnosis['fix_suggestion']}")

                    # 尝试自动修复
                    if self.fix_stimulation_state(channel_num):
                        self.logger.info("状态已自动修复，刺激启动成功")
                        return True
                    else:
                        self.logger.error("自动修复失败，可能需要手动干预")
                        # 即使修复失败，由于命令已发送，仍然返回成功
                        return True

                self.logger.warning("刺激状态验证失败，但启动命令已发送")
                return True  # 仍然返回True，因为命令已成功发送

        except Exception as e:
            self.logger.error(f"启动刺激时发生错误: {e}")
            return False

    def _verify_stimulation_active(self, channel_num: int) -> bool:
        """验证刺激是否真正激活"""
        try:
            # 通过回调函数获取的状态数据验证
            if channel_num == 1:
                return self.channel_a_status == 1  # 1表示刺激中
            elif channel_num == 2:
                return self.channel_b_status == 1
            return False
        except:
            return False



    def stop_stimulation(self, channel_num: int = 1) -> bool:
        """停止刺激"""
        try:
            if not self.dll or not self.is_connected():
                self.logger.error("设备未连接，无法停止刺激")
                return False

            # 切换通道到停止状态
            result = self.dll.SwitchChannelState(channel_num, ChannelState.STOPPED.value)
            if result == 0:
                self.logger.info(f"通道 {channel_num} 停止刺激")

                # 检查是否所有通道都已停止
                self._check_all_channels_stopped()
                return True
            else:
                self.logger.error(f"停止通道刺激失败，错误码: {result}")
                return False

        except Exception as e:
            self.logger.error(f"停止刺激时发生错误: {e}")
            return False

    def stop_all_stimulation(self) -> bool:
        """停止所有刺激"""
        try:
            if not self.dll or not self.is_connected():
                return True  # 设备未连接时认为已停止

            # 先停止所有通道
            self.logger.info("停止所有通道刺激")
            for channel in [1, 2]:  # A通道和B通道
                try:
                    result = self._safe_dll_call('SwitchChannelState', channel, 0)  # 0: 停止状态
                    if result == 0:
                        self.logger.info(f"通道{channel}已停止")
                    else:
                        self.logger.warning(f"停止通道{channel}失败，错误码: {result}")
                except Exception as e:
                    self.logger.warning(f"停止通道{channel}时发生异常: {e}")

            # 短暂延时确保通道状态更新
            time.sleep(0.2)  # 增加延时

            # 切换设备到空闲状态
            result = self._safe_dll_call('SwitchDeviceState', 0)  # 0: 空闲状态
            if result == 0:
                self.status = StimulationDeviceStatus.CONNECTED
                self.logger.info("设备已切换到空闲状态，所有刺激已停止")
            else:
                error_msg = self._get_detailed_error_message(result)
                self.logger.error(f"切换设备到空闲状态失败: {error_msg}")
                # 即使设备状态切换失败，通道已经停止，仍然设置为连接状态
                self.status = StimulationDeviceStatus.CONNECTED

            # 重要：确保设备状态复位完全 - 尝试两次状态切换以确保设备完全重置
            time.sleep(0.3)  # 给设备一些时间处理前一个命令

            # 再次尝试发送空闲状态命令
            retry_result = self._safe_dll_call('SwitchDeviceState', 0)
            if retry_result != 0:
                self.logger.warning(f"第二次切换设备到空闲状态失败，错误码: {retry_result}")

            # 清理内部状态变量
            self.channel_a_status = 0
            self.channel_b_status = 0

            return True

        except Exception as e:
            self.logger.error(f"停止所有刺激时发生错误: {e}")
            return False

    def pause_stimulation(self, channel_num: int = 1) -> bool:
        """暂停刺激"""
        try:
            if not self.dll or not self.is_connected():
                self.logger.error("设备未连接，无法暂停刺激")
                return False

            # 切换通道到暂停状态
            result = self.dll.SwitchChannelState(channel_num, ChannelState.PAUSED.value)
            if result == 0:
                self.logger.info(f"通道 {channel_num} 暂停刺激")
                return True
            else:
                self.logger.error(f"暂停通道刺激失败，错误码: {result}")
                return False

        except Exception as e:
            self.logger.error(f"暂停刺激时发生错误: {e}")
            return False

    def adjust_current(self, channel_num: int, step: int, increase: bool = True) -> bool:
        """调节电流"""
        try:
            if not self.dll or not self.is_connected():
                self.logger.error("设备未连接，无法调节电流")
                return False

            # 调节电流
            result = self.dll.RegulateCurrent(channel_num, step, not increase)  # DLL中true为下调
            if result == 0:
                direction = "增加" if increase else "减少"
                self.logger.info(f"通道 {channel_num} 电流{direction}成功，步长: {step}")
                return True
            else:
                self.logger.error(f"调节电流失败，错误码: {result}")
                return False

        except Exception as e:
            self.logger.error(f"调节电流时发生错误: {e}")
            return False

    def set_current(self, channel_num: int, current_ma: float) -> bool:
        """设置电流强度（单位：mA）- 严格按照C2说明书和QT程序流程"""
        try:
            if not self.dll or not self.is_connected():
                self.logger.error("设备未连接，无法设置电流")
                return False

            # 检查电流范围
            max_current = self.config.get('max_current', 50.0)
            min_current = self.config.get('min_current', 0.1)

            if not (min_current <= current_ma <= max_current):
                self.logger.error(f"电流值超出安全范围: {current_ma}mA, 允许范围: {min_current}-{max_current}mA")
                return False

            self.logger.debug(f"开始设置通道{channel_num}电流: {current_ma}mA")

            # 步骤1：确保设备处于循环刺激状态
            device_result = self._safe_dll_call('SwitchDeviceState', 1)  # 1: 循环刺激
            if device_result != 0:
                self.logger.warning(f"切换设备到循环刺激状态失败，错误码: {device_result}")

            time.sleep(0.1)

            # 步骤2：根据C2说明书，先切换通道到电流调节状态
            switch_result = self._safe_dll_call('SwitchChannelState', channel_num, 2)  # 2: 电流调节状态
            if switch_result != 0:
                error_msg = self._get_detailed_error_message(switch_result)
                self.logger.error(f"切换通道{channel_num}到电流调节状态失败: {error_msg}")
                return False

            time.sleep(0.1)

            # 步骤3：设置电流值
            # 根据协议文档，当调节步长为1mA时，直接使用mA值，不需要乘以10
            current_value = int(current_ma)  # 直接使用mA值
            result = self._safe_dll_call('CurrentSet', channel_num, current_value)
            if result is None:
                self.logger.error("设置电流时DLL调用失败")
                return False
            elif result != 0:
                error_msg = self._get_detailed_error_message(result)
                self.logger.error(f"设置电流失败，错误码: {result} - {error_msg}")
                return False

            self.logger.debug(f"通道 {channel_num} 电流设置成功: {current_ma}mA")

            # 步骤4：验证电流调节状态（仅调试模式记录）
            time.sleep(0.2)
            current_status = self.get_channel_status(channel_num)
            self.logger.debug(f"电流设置后通道状态: {current_status} (期望: 2)")

            return True

        except Exception as e:
            self.logger.error(f"设置电流时发生错误: {e}")
            return False

    def set_current_with_auto_stimulation(self, channel_num: int, current_ma: float) -> bool:
        """设置电流并自动触发3秒刺激（参考QT程序spinBox调节逻辑）"""
        try:
            # 1. 设置电流
            if not self.set_current(channel_num, current_ma):
                return False

            # 2. 自动触发刺激（模拟QT程序中spinBox调节电流时的行为）
            if not self.start_stimulation(channel_num):
                self.logger.warning("电流设置成功但启动刺激失败")
                return False

            # 3. 启动3秒自动停止定时器（为患者提供反馈）
            self._start_auto_stop_timer(channel_num, 3.0)

            return True

        except Exception as e:
            self.logger.error(f"设置电流并自动刺激时发生错误: {e}")
            return False

    def _start_auto_stop_timer(self, channel_num: int, duration_seconds: float):
        """启动自动停止定时器"""
        try:
            import threading

            def auto_stop():
                time.sleep(duration_seconds)
                try:
                    self.stop_stimulation(channel_num)
                    self.logger.info(f"通道{channel_num}自动停止刺激（{duration_seconds}秒后）")
                except Exception as e:
                    self.logger.error(f"自动停止刺激失败: {e}")

            # 启动后台线程执行自动停止
            timer_thread = threading.Thread(target=auto_stop, daemon=True)
            timer_thread.start()

        except Exception as e:
            self.logger.error(f"启动自动停止定时器失败: {e}")

    def start_stimulation(self, channel_num: int) -> bool:
        """启动指定通道的刺激（参考QT程序Button_dianchiji_shart逻辑）"""
        try:
            if not self.dll or not self.is_connected():
                self.logger.error("设备未连接，无法启动刺激")
                return False

            # 步骤1：确保设备处于循环刺激状态
            device_result = self._safe_dll_call('SwitchDeviceState', 1)  # 1: 循环刺激
            if device_result != 0:
                self.logger.warning(f"切换设备到循环刺激状态失败，错误码: {device_result}")

            # 短暂延时
            time.sleep(0.1)

            # 步骤2：切换通道到正常工作状态（3）启动刺激
            result = self._safe_dll_call('SwitchChannelState', channel_num, 3)
            if result == 0:
                self.status = StimulationDeviceStatus.STIMULATING
                self.logger.info(f"通道 {channel_num} 开始刺激")

                # 步骤3：验证刺激状态
                time.sleep(0.3)  # 等待状态更新
                actual_status = self.get_channel_status(channel_num)
                if actual_status == 1:
                    self.logger.info(f"✅ 通道{channel_num}刺激状态验证成功: 刺激中")
                else:
                    self.logger.warning(f"⚠️ 通道{channel_num}刺激状态验证失败: 实际状态{actual_status} (期望: 1)")

                return True
            else:
                error_msg = self._get_detailed_error_message(result)
                self.logger.error(f"启动通道{channel_num}刺激失败: {error_msg}")
                return False

        except Exception as e:
            self.logger.error(f"启动刺激时发生错误: {e}")
            return False

    def stop_stimulation(self, channel_num: int) -> bool:
        """停止指定通道的刺激"""
        try:
            if not self.dll or not self.is_connected():
                self.logger.error("设备未连接，无法停止刺激")
                return False

            # 切换通道到停止状态（0）
            result = self._safe_dll_call('SwitchChannelState', channel_num, 0)
            if result == 0:
                self.logger.info(f"通道 {channel_num} 已停止刺激")
                return True
            else:
                error_msg = self._get_detailed_error_message(result)
                self.logger.error(f"停止通道{channel_num}刺激失败: {error_msg}")
                return False

        except Exception as e:
            self.logger.error(f"停止刺激时发生错误: {e}")
            return False

    def get_device_info(self) -> DeviceInfo:
        """获取设备信息"""
        return self.device_info

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'connection_count': self.connection_count,
            'error_count': self.error_count,
            'last_command_time': self.last_command_time,
            'status': self.status.value,
            'device_info': {
                'device_id': self.device_info.device_id,
                'firmware_version': self.device_info.firmware_version,
                'hardware_version': self.device_info.hardware_version,
                'serial_number': self.device_info.serial_number
            }
        }

    def _check_all_channels_stopped(self):
        """检查所有通道是否都已停止"""
        # 这里可以添加检查逻辑，如果所有通道都停止了，更新设备状态
        self.status = StimulationDeviceStatus.CONNECTED

    def _start_status_monitoring(self):
        """启动状态监控"""
        try:
            if self.monitor_thread and self.monitor_thread.is_alive():
                return

            self.stop_monitoring.clear()
            self.monitor_thread = threading.Thread(target=self._status_monitor_loop, daemon=True)
            self.monitor_thread.start()
            self.logger.info("状态监控线程启动")

        except Exception as e:
            self.logger.error(f"启动状态监控失败: {e}")

    def _stop_status_monitoring(self):
        """停止状态监控"""
        try:
            if self.monitor_thread and self.monitor_thread.is_alive():
                self.stop_monitoring.set()
                self.monitor_thread.join(timeout=2.0)
                self.logger.info("状态监控线程停止")

        except Exception as e:
            self.logger.error(f"停止状态监控失败: {e}")

    def _status_monitor_loop(self):
        """状态监控循环"""
        while not self.stop_monitoring.is_set():
            try:
                # 检查设备连接状态
                if not self.is_connected():
                    if self.status != StimulationDeviceStatus.DISCONNECTED:
                        self.status = StimulationDeviceStatus.DISCONNECTED
                        if self.status_callback:
                            self.status_callback(self.status)

                # 等待一段时间再检查
                self.stop_monitoring.wait(1.0)

            except Exception as e:
                self.logger.error(f"状态监控循环错误: {e}")
                self.error_count += 1
                time.sleep(1.0)

    def _safe_dll_call(self, func_name: str, *args, **kwargs):
        """安全的DLL函数调用，防止程序崩溃"""
        try:
            if not self.dll:
                self.logger.error(f"DLL未加载，无法调用 {func_name}")
                return None

            func = getattr(self.dll, func_name, None)
            if not func:
                self.logger.error(f"DLL中不存在函数 {func_name}")
                return None

            result = func(*args, **kwargs)
            self.last_command_time = time.time()
            return result

        except OSError as e:
            self.logger.error(f"调用DLL函数 {func_name} 时发生系统错误: {e}")
            self.error_count += 1
            return None
        except Exception as e:
            self.logger.error(f"调用DLL函数 {func_name} 时发生未知错误: {e}")
            self.error_count += 1
            return None

    def fast_dual_channel_start(self, channel_a_current: float = 0, channel_b_current: float = 0) -> bool:
        """快速双通道启动，最小化AB通道启动间隔"""
        try:
            if not self.is_connected():
                self.logger.error("设备未连接，无法启动双通道刺激")
                return False

            start_time = time.time()
            self.logger.info(f"开始快速双通道启动: A={channel_a_current}mA, B={channel_b_current}mA")

            # 1. 确保设备处于循环刺激状态（只调用一次）
            device_result = self._safe_dll_call('SwitchDeviceState', 1)
            if device_result != 0:
                self.logger.warning(f"切换设备到循环刺激状态失败，错误码: {device_result}")

            # 2. 批量设置电流（如果需要）
            current_set_time = time.time()
            if channel_a_current > 0:
                self.set_current(1, channel_a_current)
            if channel_b_current > 0:
                self.set_current(2, channel_b_current)
            current_set_elapsed = (time.time() - current_set_time) * 1000

            # 3. 快速连续启动通道（最小延迟）
            channel_start_time = time.time()
            success_count = 0

            if channel_a_current > 0:
                result_a = self._safe_dll_call('SwitchChannelState', 1, 3)
                if result_a == 0:
                    success_count += 1
                    a_elapsed = (time.time() - channel_start_time) * 1000
                    self.logger.info(f"A通道启动成功 (+{a_elapsed:.1f}ms)")
                else:
                    self.logger.error(f"A通道启动失败，错误码: {result_a}")

            if channel_b_current > 0:
                result_b = self._safe_dll_call('SwitchChannelState', 2, 3)
                if result_b == 0:
                    success_count += 1
                    b_elapsed = (time.time() - channel_start_time) * 1000
                    self.logger.info(f"B通道启动成功 (+{b_elapsed:.1f}ms)")
                else:
                    self.logger.error(f"B通道启动失败，错误码: {result_b}")

            # 4. 记录总时间
            total_elapsed = (time.time() - start_time) * 1000
            channel_elapsed = (time.time() - channel_start_time) * 1000

            self.logger.info(f"快速双通道启动完成: 电流设置耗时{current_set_elapsed:.1f}ms, "
                           f"通道启动耗时{channel_elapsed:.1f}ms, 总耗时{total_elapsed:.1f}ms")

            if success_count > 0:
                self.status = StimulationDeviceStatus.STIMULATING
                return True
            else:
                return False

        except Exception as e:
            self.logger.error(f"快速双通道启动失败: {e}")
            return False

    def start_pre_stimulation(self, channel_num: int, current_ma: float, duration: float = 3.0) -> bool:
        """启动预刺激 - 按照指定电流刺激指定时间后自动停止

        参数:
            channel_num: 通道号 (1=A通道, 2=B通道)
            current_ma: 电流值 (mA)
            duration: 刺激持续时间 (秒)

        返回值:
            是否成功启动预刺激
        """
        try:
            if not self.is_connected():
                self.logger.error("设备未连接，无法启动预刺激")
                return False

            if current_ma <= 0:
                self.logger.warning(f"电流值无效: {current_ma}mA，跳过预刺激")
                return False

            channel_name = "A" if channel_num == 1 else "B"
            self.logger.info(f"开始{channel_name}通道预刺激: {current_ma}mA, 持续{duration}秒")

            # 1. 确保设备处于循环刺激状态
            device_result = self._safe_dll_call('SwitchDeviceState', 1)
            if device_result != 0:
                self.logger.warning(f"切换设备到循环刺激状态失败，错误码: {device_result}")

            # 2. 设置电流
            if not self.set_current(channel_num, current_ma):
                self.logger.error(f"{channel_name}通道电流设置失败")
                return False

            # 3. 启动刺激
            result = self._safe_dll_call('SwitchChannelState', channel_num, 3)  # 3: 正常工作
            if result != 0:
                self.logger.error(f"{channel_name}通道启动失败，错误码: {result}")
                return False

            self.logger.debug(f"{channel_name}通道预刺激启动成功")

            # 4. 启动自动停止定时器
            self._start_pre_stimulation_timer(channel_num, duration)

            return True

        except Exception as e:
            self.logger.error(f"启动预刺激失败: {e}")
            return False

    def fast_pre_stimulation(self, channel_num: int, current_ma: float, duration: float = 3.0) -> bool:
        """高性能预刺激 - 优化设备通信，减少延迟

        优化策略：
        1. 确保必要的参数设置
        2. 只在必要时重新设置设备状态
        3. 减少不必要的DLL调用

        参数:
            channel_num: 通道号 (1=A通道, 2=B通道)
            current_ma: 电流值 (mA)
            duration: 刺激持续时间 (秒)

        返回值:
            是否成功启动预刺激
        """
        try:
            if not self.is_connected():
                self.logger.error("设备未连接，无法启动快速预刺激")
                return False

            if current_ma <= 0:
                self.logger.warning(f"电流值无效: {current_ma}mA，跳过快速预刺激")
                return False

            channel_name = "A" if channel_num == 1 else "B"
            start_time = time.time()

            # 1. 确保设备处于循环刺激状态
            device_result = self._safe_dll_call('SwitchDeviceState', 1)
            if device_result != 0:
                self.logger.warning(f"切换设备到循环刺激状态失败，错误码: {device_result}")

            # 2. 确保通道参数已设置（只在首次或参数变化时设置）
            if not hasattr(self, '_channel_params_set') or not self._channel_params_set:
                # 使用当前参数设置通道
                if not self.set_stimulation_parameters(self.current_parameters):
                    self.logger.warning(f"{channel_name}通道参数设置可能失败")
                self._channel_params_set = True

            # 3. 设置电流
            if not self.set_current(channel_num, current_ma):
                self.logger.error(f"{channel_name}通道电流设置失败")
                return False

            # 4. 启动刺激
            result = self._safe_dll_call('SwitchChannelState', channel_num, 3)  # 3: 正常工作

            comm_time = (time.time() - start_time) * 1000

            if result == 0:
                self.logger.debug(f"{channel_name}通道快速预刺激启动成功 ({comm_time:.1f}ms)")

                # 5. 启动自动停止定时器
                self._start_pre_stimulation_timer(channel_num, duration)
                return True
            else:
                self.logger.error(f"{channel_name}通道快速预刺激启动失败，错误码: {result} ({comm_time:.1f}ms)")
                return False

        except Exception as e:
            self.logger.error(f"快速预刺激失败: {e}")
            return False

    def _start_pre_stimulation_timer(self, channel_num: int, duration: float):
        """启动预刺激自动停止定时器"""
        try:
            def stop_pre_stimulation():
                """定时器回调函数 - 停止预刺激"""
                try:
                    channel_name = "A" if channel_num == 1 else "B"

                    # 停止指定通道
                    result = self._safe_dll_call('SwitchChannelState', channel_num, 0)  # 0: 停止
                    if result == 0:
                        self.logger.debug(f"{channel_name}通道预刺激自动停止")
                    else:
                        self.logger.warning(f"{channel_name}通道预刺激停止失败，错误码: {result}")

                except Exception as e:
                    self.logger.error(f"预刺激自动停止时发生错误: {e}")

            # 创建并启动定时器
            timer = threading.Timer(duration, stop_pre_stimulation)
            timer.daemon = True  # 设置为守护线程
            timer.start()

            channel_name = "A" if channel_num == 1 else "B"
            self.logger.debug(f"{channel_name}通道预刺激定时器已启动，{duration}秒后自动停止")

        except Exception as e:
            self.logger.error(f"启动预刺激定时器失败: {e}")

    def get_channel_status(self, channel_num: int) -> int:
        """获取通道状态"""
        try:
            if channel_num == 1:
                return self.channel_a_status
            elif channel_num == 2:
                return self.channel_b_status
            else:
                return 0
        except Exception as e:
            self.logger.error(f"获取通道状态失败: {e}")
            return 0

    def __del__(self):
        """析构函数"""
        try:
            self.disconnect()
        except:
            pass

    def diagnose_stimulation_status(self, channel_num: int) -> Dict[str, Any]:
        """诊断刺激状态，检测是否存在界面显示与实际输出不一致的问题

        参数:
            channel_num: 要诊断的通道号

        返回值:
            包含诊断信息的字典，包括:
            - is_consistent: 界面状态与实际状态是否一致
            - ui_status: 界面显示的状态
            - actual_status: 设备回调报告的实际状态
            - error_cause: 如果不一致，可能的原因
            - fix_suggestion: 修复建议
        """
        result = {
            "is_consistent": True,
            "ui_status": str(self.status.value),
            "actual_status": "未知",
            "error_cause": "",
            "fix_suggestion": ""
        }

        try:
            # 获取通道的实际状态
            actual_channel_status = self.get_channel_status(channel_num)
            result["actual_status"] = self._get_channel_status_text(actual_channel_status)

            # 检查状态一致性
            if self.status == StimulationDeviceStatus.STIMULATING and actual_channel_status != 1:
                result["is_consistent"] = False
                result["error_cause"] = "界面显示'刺激中'，但设备实际状态不是'刺激中'"

                # 根据实际状态提供具体诊断（所有非1状态都是暂停）
                result["error_cause"] += f"，设备处于'暂停'状态（状态码: {actual_channel_status}）"
                result["fix_suggestion"] = "设备处于暂停状态，需要重新发送刺激命令激活通道"

                # 检查设备状态
                device_state_result = self._safe_dll_call('SwitchDeviceState', 1)  # 再次确保设备处于循环刺激状态
                if device_state_result != 0:
                    result["error_cause"] += f"，设备切换到循环刺激状态失败，错误码: {device_state_result}"
                    result["fix_suggestion"] += "。建议断开连接后重新连接设备，确保设备正常响应命令"

            # 如果界面显示非刺激状态，但设备实际在刺激
            elif self.status != StimulationDeviceStatus.STIMULATING and actual_channel_status == 1:
                result["is_consistent"] = False
                result["error_cause"] = f"界面显示'{self.status.value}'，但设备实际状态是'刺激中'"
                result["fix_suggestion"] = "界面状态与设备状态不同步，建议停止刺激后重新开始"

            # 检查设备整体状态
            self.logger.info(f"诊断通道{channel_num}状态: 界面={result['ui_status']}, 实际={result['actual_status']}")

            return result

        except Exception as e:
            self.logger.error(f"诊断刺激状态时发生错误: {e}")
            result["is_consistent"] = False
            result["error_cause"] = f"诊断过程发生错误: {str(e)}"
            result["fix_suggestion"] = "检查设备连接和日志，可能需要重启应用程序"
            return result

    def fix_stimulation_state(self, channel_num: int) -> bool:
        """尝试修复刺激状态不一致问题

        参数:
            channel_num: 要修复的通道号

        返回值:
            修复是否成功
        """
        try:
            self.logger.info(f"尝试修复通道{channel_num}的刺激状态")

            # 诊断当前状态
            diagnosis = self.diagnose_stimulation_status(channel_num)
            if diagnosis["is_consistent"]:
                self.logger.info("状态一致，无需修复")
                return True

            # 根据诊断结果进行修复
            self.logger.info(f"修复原因: {diagnosis['error_cause']}")

            # 1. 先停止所有通道
            for ch in [1, 2]:
                self._safe_dll_call('SwitchChannelState', ch, 0)
            time.sleep(0.2)

            # 2. 切换设备到空闲状态
            idle_result = self._safe_dll_call('SwitchDeviceState', 0)
            if idle_result != 0:
                self.logger.warning(f"切换到空闲状态失败，错误码: {idle_result}")
            time.sleep(0.3)

            # 3. 再次切换到循环刺激状态
            stim_result = self._safe_dll_call('SwitchDeviceState', 1)
            if stim_result != 0:
                self.logger.error(f"切换到循环刺激状态失败，错误码: {stim_result}")
                return False
            time.sleep(0.2)

            # 4. 重新设置参数
            current_ma = 0
            if channel_num == 1:
                # 假设当前参数中已经包含了正确的电流值
                # 这里可以根据实际情况改进
                current_ma = 10.0  # 默认电流值
            elif channel_num == 2:
                current_ma = 8.0   # 默认电流值

            # 重新设置参数和电流
            channel_params = StimulationParameters(
                channel_num=channel_num,
                frequency=self.current_parameters.frequency,
                pulse_width=self.current_parameters.pulse_width,
                relax_time=self.current_parameters.relax_time,
                climb_time=self.current_parameters.climb_time,
                work_time=self.current_parameters.work_time,
                fall_time=self.current_parameters.fall_time,
                wave_type=self.current_parameters.wave_type
            )

            self.set_stimulation_parameters(channel_params)
            self.set_current(channel_num, current_ma)

            # 5. 启动通道
            result = self._safe_dll_call('SwitchChannelState', channel_num, 3)
            if result != 0:
                self.logger.error(f"启动通道失败，错误码: {result}")
                return False

            # 6. 验证修复结果
            time.sleep(0.3)  # 等待状态更新
            post_fix_diagnosis = self.diagnose_stimulation_status(channel_num)
            if post_fix_diagnosis["is_consistent"] and self.get_channel_status(channel_num) == 1:
                self.logger.info("修复成功，状态已同步")
                return True
            else:
                self.logger.warning(f"修复尝试后状态仍不一致: {post_fix_diagnosis['actual_status']}")
                return False

        except Exception as e:
            self.logger.error(f"修复刺激状态时发生错误: {e}")
            return False

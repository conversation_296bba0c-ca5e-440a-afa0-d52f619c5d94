#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EEGNet预训练工具
Independent EEGNet Pretraining Tool

独立的预训练模型生成工具，用于：
1. 下载和处理公开数据集
2. 训练基础EEGNet模型
3. 生成可重用的预训练模型文件

使用方法：
python tools/pretrain_eegnet.py --dataset physionet --output models/eegnet_physionet.keras

作者: AI Assistant
版本: 1.0.0
"""

import argparse
import logging
import sys
import os
from pathlib import Path
import numpy as np
import time

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

try:
    import tensorflow as tf
    from tensorflow import keras
    import mne
    import requests
    TF_AVAILABLE = True
    MNE_AVAILABLE = True
except ImportError as e:
    print(f"❌ 依赖库缺失: {e}")
    print("请安装: pip install tensorflow mne requests")
    sys.exit(1)

from core.eegnet_model import create_eegnet_model


class PretrainDatasetDownloader:
    """预训练数据集下载器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def download_physionet_mi(self, output_dir: Path, max_subjects: int = 10):
        """下载PhysioNet运动想象数据集"""
        print(f"📥 开始下载PhysioNet数据集...")
        
        base_url = "https://physionet.org/files/eegmmidb/1.0.0/"
        subjects = list(range(1, min(max_subjects + 1, 110)))  # 最多109个受试者
        motor_imagery_runs = [3, 7, 11]  # 运动想象任务
        
        all_data = []
        all_labels = []
        
        for subject_idx, subject in enumerate(subjects):
            print(f"  处理受试者 S{subject:03d} ({subject_idx+1}/{len(subjects)})...")
            
            for run in motor_imagery_runs:
                try:
                    # 构建文件URL
                    filename = f"S{subject:03d}R{run:02d}.edf"
                    file_url = f"{base_url}S{subject:03d}/{filename}"
                    local_file = output_dir / filename
                    
                    # 下载文件（如果不存在）
                    if not local_file.exists():
                        print(f"    下载 {filename}...")
                        response = requests.get(file_url, timeout=60)
                        response.raise_for_status()
                        
                        with open(local_file, 'wb') as f:
                            f.write(response.content)
                    
                    # 解析EDF文件
                    raw = mne.io.read_raw_edf(str(local_file), preload=True, verbose=False)
                    
                    # 选择标准电极位置
                    picks = mne.pick_channels(raw.ch_names, 
                                            ['Fc5.', 'Fc3.', 'Fc1.', 'Fcz.', 'Fc2.', 'Fc4.', 'Fc6.', 'C5..'])
                    if len(picks) < 8:
                        # 如果标准电极不够，选择前8个EEG通道
                        picks = mne.pick_types(raw.info, eeg=True)[:8]
                    
                    raw.pick(picks[:8])  # 只保留8个通道
                    
                    # 重采样到125Hz
                    if raw.info['sfreq'] != 125:
                        raw.resample(125)
                    
                    # 提取事件
                    events, event_id = mne.events_from_annotations(raw, verbose=False)
                    
                    # 过滤运动想象事件 (T1: 左手, T2: 右手)
                    mi_events = events[np.isin(events[:, 2], [2, 3])]
                    
                    if len(mi_events) > 0:
                        # 创建epochs
                        epochs = mne.Epochs(raw, mi_events, tmin=0, tmax=4.0, 
                                          baseline=None, preload=True, verbose=False)
                        
                        # 提取数据
                        data = epochs.get_data()  # (n_epochs, n_channels, n_times)
                        labels = mi_events[:len(data), 2] - 2  # 转换为0,1标签
                        
                        all_data.append(data)
                        all_labels.append(labels)
                        
                        print(f"    ✅ 提取 {len(data)} 个试验")
                    
                    # 清理内存
                    del raw, epochs
                    
                except Exception as e:
                    print(f"    ⚠️ 处理 {filename} 失败: {e}")
                    continue
        
        if all_data:
            # 合并所有数据
            X = np.concatenate(all_data, axis=0)
            y = np.concatenate(all_labels, axis=0)
            
            print(f"✅ PhysioNet数据下载完成:")
            print(f"  - 总样本数: {X.shape[0]}")
            print(f"  - 数据形状: {X.shape}")
            print(f"  - 标签分布: {np.bincount(y)}")
            
            return X, y
        else:
            raise ValueError("没有成功下载任何数据")
    
    def create_synthetic_dataset(self, n_samples: int = 1000):
        """创建高质量的合成数据集（用于测试）"""
        print(f"🔧 创建合成数据集 ({n_samples} 样本)...")
        
        # 参数设置
        n_channels = 8
        n_times = 500  # 4秒 @ 125Hz
        sampling_rate = 125
        
        X = np.zeros((n_samples, n_channels, n_times))
        y = np.zeros(n_samples, dtype=int)
        
        for i in range(n_samples):
            # 生成基础脑电信号
            t = np.linspace(0, 4, n_times)
            
            # 标签（0: 左手想象, 1: 右手想象）
            label = i % 2
            y[i] = label
            
            for ch in range(n_channels):
                # 基础噪声
                signal = np.random.randn(n_times) * 10
                
                # α波 (8-13 Hz)
                alpha_freq = 10 + np.random.randn() * 1
                alpha_amp = 15 + np.random.randn() * 3
                signal += alpha_amp * np.sin(2 * np.pi * alpha_freq * t)
                
                # μ波 (8-12 Hz) - 运动想象相关
                mu_freq = 10 + np.random.randn() * 1
                if label == 0:  # 左手想象
                    if ch < 4:  # 左侧通道
                        mu_amp = 5  # ERD (事件相关去同步化)
                    else:
                        mu_amp = 12
                else:  # 右手想象
                    if ch >= 4:  # 右侧通道
                        mu_amp = 5  # ERD
                    else:
                        mu_amp = 12
                
                # 在1-3秒期间应用ERD
                erd_mask = (t >= 1.0) & (t <= 3.0)
                mu_signal = np.ones_like(t) * 12
                mu_signal[erd_mask] = mu_amp
                signal += mu_signal * np.sin(2 * np.pi * mu_freq * t)
                
                # β波 (13-30 Hz)
                beta_freq = 20 + np.random.randn() * 3
                beta_amp = 8 + np.random.randn() * 2
                signal += beta_amp * np.sin(2 * np.pi * beta_freq * t)
                
                # 应用带通滤波
                from scipy.signal import butter, filtfilt
                nyquist = sampling_rate / 2
                low = 1.0 / nyquist
                high = 40.0 / nyquist
                b, a = butter(4, [low, high], btype='band')
                signal = filtfilt(b, a, signal)
                
                X[i, ch, :] = signal
        
        print(f"✅ 合成数据集创建完成:")
        print(f"  - 样本数: {X.shape[0]}")
        print(f"  - 数据形状: {X.shape}")
        print(f"  - 标签分布: {np.bincount(y)}")
        
        return X, y


class EEGNetPretrainer:
    """EEGNet预训练器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.model = None
    
    def train_model(self, X_train, y_train, X_val=None, y_val=None, 
                   epochs=100, batch_size=32, learning_rate=0.001):
        """训练EEGNet模型"""
        print(f"🧠 开始训练EEGNet模型...")
        
        # 数据预处理
        X_train = X_train.reshape(X_train.shape[0], X_train.shape[1], X_train.shape[2], 1)
        if X_val is not None:
            X_val = X_val.reshape(X_val.shape[0], X_val.shape[1], X_val.shape[2], 1)
        
        # 创建模型
        self.model = create_eegnet_model(
            n_channels=X_train.shape[1],
            n_samples=X_train.shape[2],
            n_classes=2,
            dropout_rate=0.25
        )
        
        # 编译模型
        self.model.compile(
            optimizer=keras.optimizers.Adam(learning_rate=learning_rate),
            loss='sparse_categorical_crossentropy',
            metrics=['accuracy']
        )
        
        # 回调函数
        callbacks = [
            keras.callbacks.EarlyStopping(
                monitor='val_accuracy' if X_val is not None else 'accuracy',
                patience=10,
                restore_best_weights=True
            ),
            keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss' if X_val is not None else 'loss',
                factor=0.5,
                patience=5,
                min_lr=1e-6
            )
        ]
        
        # 训练模型
        if X_val is not None:
            history = self.model.fit(
                X_train, y_train,
                validation_data=(X_val, y_val),
                epochs=epochs,
                batch_size=batch_size,
                callbacks=callbacks,
                verbose=1
            )
        else:
            history = self.model.fit(
                X_train, y_train,
                validation_split=0.2,
                epochs=epochs,
                batch_size=batch_size,
                callbacks=callbacks,
                verbose=1
            )
        
        # 获取最终性能
        final_acc = max(history.history['accuracy'])
        final_val_acc = max(history.history.get('val_accuracy', [0]))
        
        print(f"✅ 训练完成:")
        print(f"  - 最佳训练准确率: {final_acc:.3f}")
        print(f"  - 最佳验证准确率: {final_val_acc:.3f}")
        
        return history
    
    def save_model(self, output_path: Path, model_info: dict = None):
        """保存预训练模型"""
        if self.model is None:
            raise ValueError("模型尚未训练")
        
        # 保存模型
        self.model.save(str(output_path))
        
        # 保存模型信息
        if model_info:
            info_path = output_path.with_suffix('.json')
            import json
            with open(info_path, 'w', encoding='utf-8') as f:
                json.dump(model_info, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 模型已保存到: {output_path}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='EEGNet预训练工具')
    parser.add_argument('--dataset', choices=['physionet', 'synthetic'], 
                       default='synthetic', help='数据集类型')
    parser.add_argument('--output', type=str, required=True, 
                       help='输出模型路径')
    parser.add_argument('--subjects', type=int, default=5, 
                       help='PhysioNet数据集受试者数量')
    parser.add_argument('--samples', type=int, default=1000, 
                       help='合成数据集样本数量')
    parser.add_argument('--epochs', type=int, default=50, 
                       help='训练轮次')
    parser.add_argument('--batch-size', type=int, default=32, 
                       help='批次大小')
    parser.add_argument('--learning-rate', type=float, default=0.001, 
                       help='学习率')
    
    args = parser.parse_args()
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    print("🧠 EEGNet预训练工具")
    print("=" * 50)
    
    # 创建输出目录
    output_path = Path(args.output)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 创建临时数据目录
    data_dir = Path("temp_data")
    data_dir.mkdir(exist_ok=True)
    
    try:
        # 下载/创建数据集
        downloader = PretrainDatasetDownloader()
        
        if args.dataset == 'physionet':
            X, y = downloader.download_physionet_mi(data_dir, args.subjects)
        else:
            X, y = downloader.create_synthetic_dataset(args.samples)
        
        # 划分训练和验证集
        from sklearn.model_selection import train_test_split
        X_train, X_val, y_train, y_val = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # 训练模型
        trainer = EEGNetPretrainer()
        history = trainer.train_model(
            X_train, y_train, X_val, y_val,
            epochs=args.epochs,
            batch_size=args.batch_size,
            learning_rate=args.learning_rate
        )
        
        # 保存模型
        model_info = {
            'dataset': args.dataset,
            'subjects': args.subjects if args.dataset == 'physionet' else None,
            'samples': args.samples if args.dataset == 'synthetic' else len(X),
            'accuracy': float(max(history.history['accuracy'])),
            'val_accuracy': float(max(history.history['val_accuracy'])),
            'epochs_trained': len(history.history['accuracy']),
            'created_time': time.time(),
            'model_architecture': 'EEGNet',
            'input_shape': list(X.shape[1:]),
            'n_classes': 2
        }
        
        trainer.save_model(output_path, model_info)
        
        print("\n🎉 预训练完成!")
        print(f"模型文件: {output_path}")
        print(f"模型信息: {output_path.with_suffix('.json')}")
        
    except Exception as e:
        print(f"\n❌ 预训练失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    finally:
        # 清理临时文件
        import shutil
        if data_dir.exists():
            shutil.rmtree(data_dir)
    
    return 0


if __name__ == "__main__":
    sys.exit(main())

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语音提示模块
Voice Prompt Module

作者: AI Assistant
版本: 1.0.0
"""

import logging
import threading
import queue
import time
from typing import Dict, Optional, List
from PySide6.QtCore import QObject, Signal, QThread

try:
    import pyttsx3
    TTS_AVAILABLE = True
except ImportError:
    TTS_AVAILABLE = False
    logging.warning("pyttsx3未安装，语音提示功能将不可用")


class VoicePromptEngine(QObject):
    """语音提示引擎"""

    # 信号定义
    prompt_started = Signal(str)  # 提示开始
    prompt_finished = Signal(str)  # 提示完成

    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(__name__)

        # TTS引擎
        self.tts_engine = None
        self.is_available = False

        # 语音队列
        self.voice_queue = queue.Queue()
        self.is_speaking = False

        # 工作线程
        self.worker_thread = None
        self.stop_flag = threading.Event()

        # 语音配置
        self.voice_config = {
            'rate': 200,      # 语速
            'volume': 0.8,    # 音量
            'voice_id': 0     # 语音ID
        }

        # 预定义提示语
        self.prompts = {
            # 训练状态提示
            'prepare': "准备开始训练",
            'start_motor_imagery': "开始运动想象，想象握拳动作",
            'start_quiet': "保持平静，放松身心",
            'rest': "休息一下",
            'round_complete': "本轮训练完成",
            'training_complete': "训练结束",

            # 倒计时提示
            'countdown_3': "3",
            'countdown_2': "2",
            'countdown_1': "1",
            'countdown_start': "开始",

            # 状态提示
            'good_signal': "信号质量良好",
            'poor_signal': "请检查电极连接",
            'relax': "请放松",
            'focus': "请集中注意力",

            # 结果提示
            'save_success': "模型保存成功",
            'save_failed': "模型保存失败",
            'load_success': "模型加载成功",
            'load_failed': "模型加载失败",
        }

        # 初始化TTS引擎
        self._initialize_tts()

        self.logger.info(f"语音提示引擎初始化完成，可用: {self.is_available}")

    def _initialize_tts(self):
        """初始化TTS引擎"""
        try:
            if not TTS_AVAILABLE:
                self.logger.warning("TTS库不可用")
                return

            self.tts_engine = pyttsx3.init()

            # 设置语音属性
            self.tts_engine.setProperty('rate', self.voice_config['rate'])
            self.tts_engine.setProperty('volume', self.voice_config['volume'])

            # 获取可用语音
            voices = self.tts_engine.getProperty('voices')
            if voices and len(voices) > 0:
                # 尝试选择中文语音
                chinese_voice = None
                for voice in voices:
                    if 'chinese' in voice.name.lower() or 'zh' in voice.id.lower():
                        chinese_voice = voice
                        break

                if chinese_voice:
                    self.tts_engine.setProperty('voice', chinese_voice.id)
                    self.logger.info(f"使用中文语音: {chinese_voice.name}")
                else:
                    # 使用默认语音
                    self.tts_engine.setProperty('voice', voices[0].id)
                    self.logger.info(f"使用默认语音: {voices[0].name}")

            self.is_available = True

            # 启动工作线程
            self._start_worker_thread()

        except Exception as e:
            self.logger.error(f"TTS引擎初始化失败: {e}")
            self.is_available = False

    def _start_worker_thread(self):
        """启动工作线程"""
        if self.worker_thread and self.worker_thread.is_alive():
            return

        self.stop_flag.clear()
        self.worker_thread = threading.Thread(target=self._voice_worker, daemon=True)
        self.worker_thread.start()
        self.logger.debug("语音工作线程已启动")

    def _voice_worker(self):
        """语音工作线程"""
        while not self.stop_flag.is_set():
            try:
                # 从队列获取语音任务
                text = self.voice_queue.get(timeout=1.0)

                if text is None:  # 停止信号
                    break

                # 执行语音播放
                self._speak_text(text)

                # 标记任务完成
                self.voice_queue.task_done()

            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"语音工作线程错误: {e}")

    def _speak_text(self, text: str):
        """播放语音文本"""
        try:
            if not self.is_available or not self.tts_engine:
                self.logger.debug(f"语音不可用，跳过: {text}")
                return

            self.is_speaking = True
            self.prompt_started.emit(text)

            # 播放语音
            self.tts_engine.say(text)
            self.tts_engine.runAndWait()

            self.is_speaking = False
            self.prompt_finished.emit(text)

            self.logger.debug(f"语音播放完成: {text}")

        except Exception as e:
            self.logger.error(f"语音播放失败: {e}")
            self.is_speaking = False

    def speak(self, text: str, priority: bool = False):
        """添加语音到队列"""
        try:
            if not self.is_available:
                self.logger.debug(f"语音不可用，跳过: {text}")
                return

            if priority:
                # 清空队列并添加优先语音
                while not self.voice_queue.empty():
                    try:
                        self.voice_queue.get_nowait()
                    except queue.Empty:
                        break

            self.voice_queue.put(text)
            self.logger.debug(f"语音已添加到队列: {text}")

        except Exception as e:
            self.logger.error(f"添加语音失败: {e}")

    def speak_prompt(self, prompt_key: str, priority: bool = False):
        """播放预定义提示语"""
        if prompt_key in self.prompts:
            self.speak(self.prompts[prompt_key], priority)
        else:
            self.logger.warning(f"未找到提示语: {prompt_key}")

    def speak_countdown(self, seconds: int):
        """播放倒计时"""
        def countdown_worker():
            for i in range(seconds, 0, -1):
                if self.stop_flag.is_set():
                    break
                self.speak(str(i), priority=True)
                time.sleep(1)

            if not self.stop_flag.is_set():
                self.speak_prompt('countdown_start', priority=True)

        # 在单独线程中执行倒计时
        countdown_thread = threading.Thread(target=countdown_worker, daemon=True)
        countdown_thread.start()

    def set_voice_config(self, config: Dict[str, any]):
        """设置语音配置"""
        try:
            if not self.is_available or not self.tts_engine:
                return

            if 'rate' in config:
                self.voice_config['rate'] = config['rate']
                self.tts_engine.setProperty('rate', config['rate'])

            if 'volume' in config:
                self.voice_config['volume'] = config['volume']
                self.tts_engine.setProperty('volume', config['volume'])

            if 'voice_id' in config:
                voices = self.tts_engine.getProperty('voices')
                if voices and 0 <= config['voice_id'] < len(voices):
                    self.voice_config['voice_id'] = config['voice_id']
                    self.tts_engine.setProperty('voice', voices[config['voice_id']].id)

            self.logger.info("语音配置已更新")

        except Exception as e:
            self.logger.error(f"设置语音配置失败: {e}")

    def get_available_voices(self) -> List[Dict[str, str]]:
        """获取可用语音列表"""
        try:
            if not self.is_available or not self.tts_engine:
                return []

            voices = self.tts_engine.getProperty('voices')
            voice_list = []

            for i, voice in enumerate(voices):
                voice_info = {
                    'id': i,
                    'name': voice.name,
                    'language': getattr(voice, 'languages', ['unknown'])[0] if hasattr(voice, 'languages') else 'unknown'
                }
                voice_list.append(voice_info)

            return voice_list

        except Exception as e:
            self.logger.error(f"获取语音列表失败: {e}")
            return []

    def stop_speaking(self):
        """停止当前语音"""
        try:
            if self.is_available and self.tts_engine and self.is_speaking:
                self.tts_engine.stop()
                self.is_speaking = False
                self.logger.debug("语音播放已停止")
        except Exception as e:
            self.logger.error(f"停止语音失败: {e}")

    def clear_queue(self):
        """清空语音队列"""
        while not self.voice_queue.empty():
            try:
                self.voice_queue.get_nowait()
            except queue.Empty:
                break
        self.logger.debug("语音队列已清空")

    def is_speaking_now(self) -> bool:
        """检查是否正在播放语音"""
        return self.is_speaking

    def cleanup(self):
        """清理资源"""
        try:
            # 停止工作线程
            self.stop_flag.set()
            self.voice_queue.put(None)  # 发送停止信号

            if self.worker_thread and self.worker_thread.is_alive():
                self.worker_thread.join(timeout=2.0)

            # 停止TTS引擎
            if self.is_available and self.tts_engine:
                self.tts_engine.stop()

            self.logger.info("语音提示引擎资源清理完成")

        except Exception as e:
            self.logger.error(f"语音提示引擎清理失败: {e}")


# 全局语音提示实例
_voice_engine: Optional[VoicePromptEngine] = None

def get_voice_engine() -> VoicePromptEngine:
    """获取全局语音提示引擎"""
    global _voice_engine
    if _voice_engine is None:
        _voice_engine = VoicePromptEngine()
    return _voice_engine

def speak(text: str, priority: bool = False):
    """快捷语音播放函数"""
    engine = get_voice_engine()
    engine.speak(text, priority)

def speak_prompt(prompt_key: str, priority: bool = False):
    """快捷预定义提示播放函数"""
    engine = get_voice_engine()
    engine.speak_prompt(prompt_key, priority)

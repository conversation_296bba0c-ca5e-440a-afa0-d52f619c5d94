#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查PhysioNet数据集通道名称
Check PhysioNet Dataset Channel Names

检查PhysioNet数据集的实际通道名称格式

作者: AI Assistant
版本: 1.0.0
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

try:
    import mne
    MNE_AVAILABLE = True
except ImportError as e:
    print(f"❌ 依赖库缺失: {e}")
    print("请安装: pip install mne")
    sys.exit(1)


def check_channel_names():
    """检查PhysioNet数据集的通道名称"""
    print("🔍 检查PhysioNet数据集通道名称...")
    
    data_dir = Path("data/bci_dataset/files")
    
    # 检查第一个受试者的第一个文件
    test_file = data_dir / "S001" / "S001R01.edf"
    
    if not test_file.exists():
        print(f"❌ 测试文件不存在: {test_file}")
        return
    
    try:
        # 读取EDF文件
        raw = mne.io.read_raw_edf(str(test_file), preload=False, verbose=False)
        
        print(f"✅ 成功读取文件: {test_file.name}")
        print(f"通道数量: {len(raw.ch_names)}")
        print(f"采样率: {raw.info['sfreq']} Hz")
        
        print(f"\n📋 所有通道名称:")
        for i, ch_name in enumerate(raw.ch_names):
            print(f"  {i+1:2d}. {ch_name}")
        
        # 查找可能的运动皮层通道
        print(f"\n🧠 可能的运动皮层相关通道:")
        motor_keywords = ['C3', 'CZ', 'C4', 'FC', 'CP', 'F3', 'F4', 'P3', 'P4']
        motor_channels = []
        
        for ch_name in raw.ch_names:
            for keyword in motor_keywords:
                if keyword in ch_name.upper():
                    motor_channels.append(ch_name)
                    break
        
        if motor_channels:
            print(f"找到 {len(motor_channels)} 个运动皮层相关通道:")
            for i, ch_name in enumerate(motor_channels):
                print(f"  {i+1:2d}. {ch_name}")
        else:
            print("❌ 未找到明显的运动皮层通道")
        
        # 检查是否有标准的10-20系统通道
        standard_channels = ['Fc5.', 'Fc3.', 'Fc1.', 'Fcz.', 'Fc2.', 'Fc4.', 'Fc6.',
                           'C5..', 'C3..', 'C1..', 'Cz..', 'C2..', 'C4..', 'C6..',
                           'Cp5.', 'Cp3.', 'Cp1.', 'Cpz.', 'Cp2.', 'Cp4.', 'Cp6.']
        
        print(f"\n🎯 标准10-20系统通道匹配:")
        found_standard = []
        for std_ch in standard_channels:
            for ch_name in raw.ch_names:
                if std_ch.lower() in ch_name.lower():
                    found_standard.append(ch_name)
                    print(f"  ✅ {std_ch} -> {ch_name}")
                    break
        
        if len(found_standard) >= 8:
            print(f"\n🎉 找到足够的标准通道: {len(found_standard)} 个")
            print("推荐使用这些通道进行处理")
        else:
            print(f"\n⚠️ 标准通道不足，建议使用前8个EEG通道")
        
        # 显示前8个通道
        print(f"\n📌 前8个通道（备选方案）:")
        for i in range(min(8, len(raw.ch_names))):
            print(f"  {i+1}. {raw.ch_names[i]}")
        
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    check_channel_names()

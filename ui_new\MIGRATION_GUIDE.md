# NK脑机接口康复系统 - 页面迁移指南

## 迁移概述

本指南帮助开发者将现有的UI页面迁移到新的现代化UI框架中。新框架采用直接迁移模式，保持业务逻辑不变，只重构UI层。

## 迁移策略

### 1. 渐进式迁移
- **优先级排序**: 按使用频率和重要性排序
- **逐页迁移**: 一次迁移一个页面，降低风险
- **并行运行**: 新旧系统可以并行运行
- **用户反馈**: 收集用户反馈，持续改进

### 2. 迁移优先级
```
高优先级（核心功能）:
1. 实时监测仪表板 ✅ 已完成
2. 患者管理页面
3. 治疗系统页面

中优先级（常用功能）:
4. 数据管理页面
5. 报告分析页面

低优先级（管理功能）:
6. 用户管理页面
7. 系统设置页面
```

## 迁移步骤详解

### 第一步：分析现有页面

#### 1.1 页面结构分析
```python
# 现有页面示例：ui/patient_management_ui.py
class PatientManagementUI(QWidget):
    def __init__(self):
        # 分析现有结构
        self.setup_ui()
        self.setup_connections()
        self.load_data()
```

#### 1.2 业务逻辑提取
```python
# 识别业务逻辑方法
- get_patients()          # 获取患者列表
- add_patient()           # 添加患者
- update_patient()        # 更新患者
- delete_patient()        # 删除患者
- search_patients()       # 搜索患者
```

#### 1.3 UI组件清单
```python
# 识别UI组件
- QTableWidget           # 患者列表表格
- QLineEdit             # 搜索框、输入框
- QPushButton           # 操作按钮
- QComboBox             # 下拉选择
- QDateEdit             # 日期选择
```

### 第二步：创建新页面框架

#### 2.1 创建页面文件
```bash
# 创建新页面文件
touch ui_new/pages/patients_page.py
```

#### 2.2 页面基础结构
```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
患者管理页面
Patient Management Page

作者: AI Assistant
版本: 2.0.0
"""

from typing import Dict, List, Any, Optional
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
    QLineEdit, QPushButton, QLabel, QMessageBox
)
from PySide6.QtCore import Qt, Signal

from .base_page import BasePage
from core.auth_manager import Permission
from ui_new.config import ui_config


class PatientsPage(BasePage):
    """患者管理页面"""
    
    # 信号定义
    patient_selected = Signal(dict)  # 患者选择信号
    patient_updated = Signal(dict)   # 患者更新信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 页面数据
        self.patients_data = []
        self.current_patient = None
        
        # UI组件
        self.search_input = None
        self.patients_table = None
        self.add_button = None
        self.edit_button = None
        self.delete_button = None
    
    def setup_ui(self):
        """设置UI"""
        # 创建内容组件
        self.content_widget = QWidget()
        self.main_layout.addWidget(self.content_widget)
        
        # 主布局
        layout = QVBoxLayout(self.content_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(24)
        
        # 创建工具栏
        self.create_toolbar(layout)
        
        # 创建患者列表
        self.create_patients_table(layout)
        
        # 创建操作面板
        self.create_action_panel(layout)
    
    def create_toolbar(self, layout):
        """创建工具栏"""
        toolbar = QWidget()
        toolbar.setProperty("class", "card")
        toolbar.setFixedHeight(80)
        
        toolbar_layout = QHBoxLayout(toolbar)
        toolbar_layout.setContentsMargins(20, 20, 20, 20)
        
        # 搜索框
        search_label = QLabel("搜索患者:")
        search_label.setProperty("class", "text-sm font-medium")
        toolbar_layout.addWidget(search_label)
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入患者姓名或编号...")
        self.search_input.setFixedWidth(300)
        toolbar_layout.addWidget(self.search_input)
        
        toolbar_layout.addStretch()
        
        # 添加患者按钮
        self.add_button = QPushButton("添加患者")
        self.add_button.setProperty("class", "btn-primary")
        toolbar_layout.addWidget(self.add_button)
        
        layout.addWidget(toolbar)
    
    def create_patients_table(self, layout):
        """创建患者列表表格"""
        # 表格容器
        table_container = QWidget()
        table_container.setProperty("class", "card-large")
        
        table_layout = QVBoxLayout(table_container)
        table_layout.setContentsMargins(24, 24, 24, 24)
        
        # 表格标题
        table_title = QLabel("患者列表")
        table_title.setProperty("class", "card-title")
        table_layout.addWidget(table_title)
        
        # 患者表格
        self.patients_table = QTableWidget()
        self.setup_table_headers()
        table_layout.addWidget(self.patients_table)
        
        layout.addWidget(table_container)
    
    def create_action_panel(self, layout):
        """创建操作面板"""
        action_panel = QWidget()
        action_panel.setProperty("class", "card")
        action_panel.setFixedHeight(80)
        
        action_layout = QHBoxLayout(action_panel)
        action_layout.setContentsMargins(20, 20, 20, 20)
        
        # 操作按钮
        self.edit_button = QPushButton("编辑患者")
        self.edit_button.setProperty("class", "btn-secondary")
        self.edit_button.setEnabled(False)
        action_layout.addWidget(self.edit_button)
        
        self.delete_button = QPushButton("删除患者")
        self.delete_button.setProperty("class", "btn-secondary")
        self.delete_button.setEnabled(False)
        action_layout.addWidget(self.delete_button)
        
        action_layout.addStretch()
        
        # 统计信息
        self.stats_label = QLabel("总计: 0 位患者")
        self.stats_label.setProperty("class", "text-sm")
        action_layout.addWidget(self.stats_label)
        
        layout.addWidget(action_panel)
    
    def setup_table_headers(self):
        """设置表格标题"""
        headers = ["患者编号", "姓名", "性别", "年龄", "联系电话", "创建时间", "状态"]
        self.patients_table.setColumnCount(len(headers))
        self.patients_table.setHorizontalHeaderLabels(headers)
        
        # 设置列宽
        self.patients_table.setColumnWidth(0, 120)  # 患者编号
        self.patients_table.setColumnWidth(1, 100)  # 姓名
        self.patients_table.setColumnWidth(2, 60)   # 性别
        self.patients_table.setColumnWidth(3, 60)   # 年龄
        self.patients_table.setColumnWidth(4, 120)  # 联系电话
        self.patients_table.setColumnWidth(5, 150)  # 创建时间
        self.patients_table.setColumnWidth(6, 80)   # 状态
    
    def setup_connections(self):
        """设置信号连接"""
        # 搜索功能
        self.search_input.textChanged.connect(self.on_search_changed)
        
        # 表格选择
        self.patients_table.itemSelectionChanged.connect(self.on_selection_changed)
        self.patients_table.itemDoubleClicked.connect(self.on_patient_double_clicked)
        
        # 按钮操作
        self.add_button.clicked.connect(self.add_patient)
        self.edit_button.clicked.connect(self.edit_patient)
        self.delete_button.clicked.connect(self.delete_patient)
    
    def load_data(self):
        """加载数据"""
        try:
            # 检查权限
            if not self.has_permission(Permission.PATIENT_VIEW):
                self.show_error("权限不足，无法查看患者数据")
                return
            
            # 从数据库加载患者数据
            self.patients_data = self.db_manager.get_patients()
            
            # 更新表格
            self.update_table()
            
            # 更新统计信息
            self.update_stats()
            
            # 发送加载完成信号
            self.page_loaded.emit()
            
        except Exception as e:
            self.logger.error(f"加载患者数据失败: {e}")
            self.show_error(f"加载数据失败: {e}")
    
    def update_table(self):
        """更新表格数据"""
        # 实现表格更新逻辑
        pass
    
    def update_stats(self):
        """更新统计信息"""
        count = len(self.patients_data)
        self.stats_label.setText(f"总计: {count} 位患者")
    
    def on_search_changed(self, text: str):
        """搜索变化处理"""
        # 实现搜索过滤逻辑
        pass
    
    def on_selection_changed(self):
        """选择变化处理"""
        # 实现选择处理逻辑
        pass
    
    def on_patient_double_clicked(self, item):
        """患者双击处理"""
        # 实现双击选择逻辑
        pass
    
    def add_patient(self):
        """添加患者"""
        # 实现添加患者逻辑
        pass
    
    def edit_patient(self):
        """编辑患者"""
        # 实现编辑患者逻辑
        pass
    
    def delete_patient(self):
        """删除患者"""
        # 实现删除患者逻辑
        pass
    
    def check_permissions(self) -> bool:
        """检查页面权限"""
        return self.has_permission(Permission.PATIENT_VIEW)
```

### 第三步：迁移业务逻辑

#### 3.1 直接复制方法
```python
# 从现有页面复制业务逻辑方法
def get_patients_from_db(self):
    """从数据库获取患者列表（直接迁移）"""
    # 直接使用现有的数据库查询逻辑
    return self.db_manager.get_patients()

def save_patient_to_db(self, patient_data):
    """保存患者到数据库（直接迁移）"""
    # 直接使用现有的保存逻辑
    return self.db_manager.save_patient(patient_data)
```

#### 3.2 适配新UI结构
```python
def update_table_data(self, patients):
    """更新表格数据（适配新UI）"""
    self.patients_table.setRowCount(len(patients))
    
    for row, patient in enumerate(patients):
        # 使用现有的数据字段
        self.patients_table.setItem(row, 0, QTableWidgetItem(patient.get('bianhao', '')))
        self.patients_table.setItem(row, 1, QTableWidgetItem(patient.get('name', '')))
        # ... 其他字段
```

### 第四步：注册新页面

#### 4.1 更新主窗口
```python
# 在main_window_new.py中添加
from .pages.patients_page import PatientsPage

self.page_classes = {
    'dashboard': DashboardPage,
    'patients': PatientsPage,  # 新增
    # ... 其他页面
}
```

#### 4.2 更新导航
```python
# 在sidebar.py中确认导航项存在
("patients", "患者管理", "👥"),
```

### 第五步：测试和验证

#### 5.1 功能测试
```python
def test_patients_page():
    """测试患者管理页面"""
    # 创建页面实例
    page = PatientsPage()
    
    # 设置业务组件
    page.set_database_manager(db_manager)
    page.set_auth_manager(auth_manager)
    
    # 测试数据加载
    page.load_data()
    
    # 验证UI更新
    assert page.patients_table.rowCount() > 0
```

#### 5.2 集成测试
```bash
# 运行完整测试
python ui_new/test_new_ui.py
```

## 迁移检查清单

### ✅ 页面创建
- [ ] 创建新页面文件
- [ ] 继承BasePage基类
- [ ] 实现必需方法（setup_ui, setup_connections, load_data）
- [ ] 设置页面权限检查

### ✅ UI组件迁移
- [ ] 识别现有UI组件
- [ ] 使用新的卡片式布局
- [ ] 应用新的样式类
- [ ] 实现响应式设计

### ✅ 业务逻辑迁移
- [ ] 直接复制业务方法
- [ ] 适配新的UI结构
- [ ] 保持数据库接口不变
- [ ] 保持权限检查逻辑

### ✅ 信号连接
- [ ] 连接UI组件信号
- [ ] 实现页面间通信
- [ ] 连接业务逻辑信号
- [ ] 处理错误和异常

### ✅ 样式应用
- [ ] 设置组件objectName
- [ ] 应用样式类
- [ ] 支持主题切换
- [ ] 验证视觉效果

### ✅ 测试验证
- [ ] 单元测试
- [ ] 集成测试
- [ ] 用户体验测试
- [ ] 性能测试

## 常见迁移问题

### 1. 布局问题
**问题**: 原有布局在新框架中显示异常
**解决**: 使用新的卡片式布局，设置正确的间距和边距

### 2. 样式问题
**问题**: 组件样式不符合新主题
**解决**: 设置正确的objectName和class属性

### 3. 信号连接问题
**问题**: 原有信号连接在新框架中失效
**解决**: 重新连接信号，使用新的信号槽机制

### 4. 权限问题
**问题**: 权限检查逻辑需要适配
**解决**: 使用BasePage的权限检查方法

### 5. 数据加载问题
**问题**: 数据加载时机不正确
**解决**: 在load_data方法中实现，使用页面生命周期

## 迁移时间估算

### 简单页面（如设置页面）
- **分析时间**: 0.5天
- **迁移时间**: 1天
- **测试时间**: 0.5天
- **总计**: 2天

### 中等页面（如患者管理）
- **分析时间**: 1天
- **迁移时间**: 2-3天
- **测试时间**: 1天
- **总计**: 4-5天

### 复杂页面（如治疗系统）
- **分析时间**: 1-2天
- **迁移时间**: 3-5天
- **测试时间**: 1-2天
- **总计**: 5-9天

## 迁移支持

### 技术支持
- 查看开发指南: `DEVELOPMENT_GUIDE.md`
- 参考示例页面: `dashboard_page.py`
- 运行测试用例: `ui_only_test.py`

### 问题反馈
- 记录迁移过程中的问题
- 更新迁移指南
- 完善开发文档

---

**文档版本**: 2.0.0  
**最后更新**: 2024年  
**维护者**: AI Assistant

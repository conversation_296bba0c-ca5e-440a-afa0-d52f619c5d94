#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
电刺激设备控制模块 - 严格按照QT程序逻辑实现

基于原QT程序的电刺激控制逻辑：
1. Button_dianchiji_open: 连接设备并切换到循环刺激状态
2. spinBox_dianchiji_A/B: 调节电流并自动触发3秒刺激
3. Button_dianchiji_shart: 持续刺激控制
4. NK::callf: 回调函数只读取A、B通道状态用于显示

作者: NK系统开发团队
日期: 2024-05-29
版本: 2.0.0 - QT逻辑重构版
"""

import ctypes
import time
import threading
import logging
from typing import Optional
from dataclasses import dataclass
from enum import Enum
from pathlib import Path

from utils.app_config import AppConfig


class DeviceStatus(Enum):
    """设备状态"""
    DISCONNECTED = "未连接"
    CONNECTED = "已连接"
    ERROR = "错误"


class ChannelState(Enum):
    """通道状态 - 对应QT程序中的状态值"""
    STOPPED = 0      # 停止
    PAUSED = 1       # 暂停
    ADJUSTING = 2    # 电流调节
    WORKING = 3      # 正常工作


@dataclass
class StimulationParameters:
    """刺激参数"""
    channel_num: int = 1          # 通道号 (1=A, 2=B)
    frequency: float = 25.0       # 频率 (Hz)
    pulse_width: float = 250.0    # 脉宽 (μs)
    relax_time: float = 5.0       # 休息时间 (s)
    climb_time: float = 2.0       # 上升时间 (s)
    work_time: float = 10.0       # 工作时间 (s)
    fall_time: float = 2.0        # 下降时间 (s)
    wave_type: int = 0            # 波形类型 (0=双相波, 1=单相波)


class StimulationDevice:
    """电刺激设备控制类 - 基于QT程序逻辑"""

    def __init__(self):
        """初始化设备"""
        self.logger = logging.getLogger(__name__)
        self.config = AppConfig.get_config('stimulation')

        # 设备状态
        self.status = DeviceStatus.DISCONNECTED
        self.dll = None
        self.callback_function = None

        # 通道状态 - 对应QT程序中的dcjtdzt和dcjtdztb变量
        self.channel_a_status = ChannelState.STOPPED.value  # A通道状态
        self.channel_b_status = ChannelState.STOPPED.value  # B通道状态

        # 当前电流值 - 对应QT程序中的spinBox值
        self.channel_a_current = 0.0  # A通道电流 (mA)
        self.channel_b_current = 0.0  # B通道电流 (mA)

        # 自动停止定时器
        self.auto_stop_timers = {}

        self.logger.info("电刺激设备控制器初始化完成")

    def _load_dll(self) -> bool:
        """加载DLL库"""
        try:
            dll_path = self.config['dll_path']
            if not Path(dll_path).exists():
                self.logger.error(f"DLL文件不存在: {dll_path}")
                return False

            self.dll = ctypes.CDLL(str(dll_path))
            self._define_function_prototypes()
            self.logger.info(f"DLL加载成功: {dll_path}")
            return True

        except Exception as e:
            self.logger.error(f"DLL加载失败: {e}")
            return False

    def _define_function_prototypes(self):
        """定义DLL函数原型 - 参考QT程序中的调用方式"""
        try:
            # 定义回调函数类型 - 对应NK::callf
            # static int _stdcall callf(HANDLE pHandle, short* lpBuffer, int nSize);
            self.callback_func_type = ctypes.WINFUNCTYPE(
                ctypes.c_int,                           # 返回值
                ctypes.c_void_p,                        # HANDLE pHandle
                ctypes.POINTER(ctypes.c_short),         # short* lpBuffer
                ctypes.c_int                            # int nSize
            )

            # 设备连接函数
            self.dll.OpenRecPort.argtypes = [
                ctypes.c_int,                           # portNum
                ctypes.c_int,                           # iReadSize (QT中使用6)
                self.callback_func_type,                # funDataProc callback
                ctypes.c_void_p                         # HANDLE pHandle
            ]
            self.dll.OpenRecPort.restype = ctypes.c_int

            # 设备断开函数
            self.dll.CloseRecPort.restype = ctypes.c_int

            # 设备状态切换函数
            self.dll.SwitchDeviceState.argtypes = [ctypes.c_int]
            self.dll.SwitchDeviceState.restype = ctypes.c_int

            # 通道状态切换函数
            self.dll.SwitchChannelState.argtypes = [ctypes.c_int, ctypes.c_int]
            self.dll.SwitchChannelState.restype = ctypes.c_int

            # 电流设置函数
            self.dll.CurrentSet.argtypes = [ctypes.c_int, ctypes.c_int]
            self.dll.CurrentSet.restype = ctypes.c_int

            # 刺激参数设置函数
            self.dll.StimPara.argtypes = [
                ctypes.c_int,    # ChanNum
                ctypes.c_double, # ActFreq
                ctypes.c_double, # PulseWidth
                ctypes.c_double, # RelaxTime
                ctypes.c_double, # ClimbTime
                ctypes.c_double, # WorkTime
                ctypes.c_double, # FallTime
                ctypes.c_int     # WaveType
            ]
            self.dll.StimPara.restype = ctypes.c_int

            self.logger.debug("DLL函数原型定义完成")

        except Exception as e:
            self.logger.error(f"定义DLL函数原型失败: {e}")
            raise

    def _data_callback(self, pHandle, lpBuffer, nSize):
        """数据回调函数 - 对应QT程序中的NK::callf"""
        try:
            if nSize == 0:
                # 0字节数据包是正常的心跳包，不需要处理
                return nSize

            if nSize == 6 and lpBuffer:
                # 6个short值的数据包 - 对应QT程序中的数据格式
                # [设备标识, 设备状态, A通道状态, A进度, B通道状态, B进度]
                short_array = ctypes.cast(lpBuffer, ctypes.POINTER(ctypes.c_short * 6)).contents

                # 更新通道状态 - 对应QT程序中的dcjtdzt和dcjtdztb变量
                new_a_status = short_array[2]  # A通道状态
                new_b_status = short_array[4]  # B通道状态

                # 检测状态变化并记录
                if new_a_status != self.channel_a_status:
                    self.logger.debug(f"A通道状态变化: {self.channel_a_status} -> {new_a_status}")
                    self.channel_a_status = new_a_status

                if new_b_status != self.channel_b_status:
                    self.logger.debug(f"B通道状态变化: {self.channel_b_status} -> {new_b_status}")
                    self.channel_b_status = new_b_status

            return nSize

        except Exception as e:
            self.logger.error(f"回调函数处理数据时发生错误: {e}")
            return nSize

    def connect(self, port_num: int = 7) -> bool:
        """连接设备 - 对应QT程序中的Button_dianchiji_open功能"""
        try:
            if not self._load_dll():
                return False

            self.logger.info(f"尝试连接电刺激设备，端口: COM{port_num}")

            # 创建回调函数
            self.callback_function = self.callback_func_type(self._data_callback)

            # 连接设备 - 参考QT程序: OpenRecPort(portNum, 6, callf, pHandle)
            result = self.dll.OpenRecPort(port_num, 6, self.callback_function, None)

            if result == 1:  # QT程序中1表示成功
                self.status = DeviceStatus.CONNECTED
                self.logger.info("设备连接成功")

                # 切换设备到循环刺激状态 - 对应QT程序中的SwitchDeviceState(1)
                time.sleep(0.1)
                device_state_result = self.dll.SwitchDeviceState(1)  # 1 = 循环刺激状态
                if device_state_result == 0:
                    self.logger.info("设备已切换到循环刺激状态")
                else:
                    self.logger.warning(f"切换设备状态失败，错误码: {device_state_result}")

                return True
            else:
                self.logger.error(f"设备连接失败，错误码: {result}")
                return False

        except Exception as e:
            self.logger.error(f"连接设备时发生错误: {e}")
            self.status = DeviceStatus.ERROR
            return False

    def disconnect(self) -> bool:
        """断开设备连接 - 按照QT程序用户退出时的逻辑"""
        try:
            if self.dll and self.status == DeviceStatus.CONNECTED:
                # 1. 按照QT程序buttonClick()中"用户退出"的逻辑：关闭A、B通道输出
                self.logger.info("用户退出：关闭A、B通道输出并切换电刺激仪工作状态")

                # 停止A通道
                self.dll.SwitchChannelState(1, ChannelState.STOPPED.value)  # A通道停止
                # 停止B通道
                self.dll.SwitchChannelState(2, ChannelState.STOPPED.value)  # B通道停止

                # 切换电刺激仪到空闲状态
                self.dll.SwitchDeviceState(0)  # 0 = 空闲状态

                # 停止所有自动定时器
                for timer in self.auto_stop_timers.values():
                    if timer.is_alive():
                        timer.cancel()
                self.auto_stop_timers.clear()

                # 断开连接
                result = self.dll.CloseRecPort()
                if result == 1:  # QT程序中1表示成功
                    self.logger.info("设备断开成功")
                else:
                    self.logger.warning(f"设备断开失败，错误码: {result}")

                self.status = DeviceStatus.DISCONNECTED
                return True

            return True

        except Exception as e:
            self.logger.error(f"断开设备时发生错误: {e}")
            return False

    def is_connected(self) -> bool:
        """检查设备是否已连接"""
        return self.status == DeviceStatus.CONNECTED

    def get_channel_status(self, channel_num: int) -> int:
        """获取通道状态"""
        if channel_num == 1:
            return self.channel_a_status
        elif channel_num == 2:
            return self.channel_b_status
        else:
            return ChannelState.STOPPED.value

    def get_channel_status_text(self, status: int) -> str:
        """获取通道状态文本

        根据实际回调函数返回值：
        - 1: 刺激中
        - 所有非1的状态: 暂停
        """
        if status == 1:
            return "刺激中"
        else:
            return "暂停"

    def set_stimulation_parameters(self, params: StimulationParameters) -> bool:
        """设置刺激参数"""
        try:
            if not self.is_connected():
                self.logger.error("设备未连接，无法设置参数")
                return False

            result = self.dll.StimPara(
                params.channel_num,
                params.frequency,
                params.pulse_width,
                params.relax_time,
                params.climb_time,
                params.work_time,
                params.fall_time,
                params.wave_type
            )

            if result == 0:
                self.logger.info(f"刺激参数设置成功: 通道{params.channel_num}, 频率{params.frequency}Hz")
                return True
            else:
                self.logger.error(f"刺激参数设置失败，错误码: {result}")
                return False

        except Exception as e:
            self.logger.error(f"设置刺激参数时发生错误: {e}")
            return False

    def set_current(self, channel_num: int, current_ma: float) -> bool:
        """设置电流值 - 对应QT程序中的CurrentSet调用"""
        try:
            if not self.is_connected():
                self.logger.error("设备未连接，无法设置电流")
                return False

            # 根据协议文档，当调节步长为1mA时，直接使用mA值
            current_value = int(current_ma)

            # 调用DLL设置电流
            result = self.dll.CurrentSet(channel_num, current_value)

            if result == 0:
                # 更新内部电流值记录
                if channel_num == 1:
                    self.channel_a_current = current_ma
                elif channel_num == 2:
                    self.channel_b_current = current_ma

                self.logger.debug(f"通道{channel_num}电流设置成功: {current_ma}mA")
                return True
            else:
                self.logger.error(f"通道{channel_num}电流设置失败，错误码: {result}")
                return False

        except Exception as e:
            self.logger.error(f"设置电流时发生错误: {e}")
            return False

    def adjust_current_with_feedback(self, channel_num: int, current_ma: float) -> bool:
        """调节电流并自动触发3秒刺激 - 严格按照QT程序NK::on_spinBox_dianchiji_A/B_valueChanged逻辑"""
        try:
            self.logger.info(f"spinBox调节通道{channel_num}电流: {current_ma}mA (立即启动3秒预刺激)")

            # 1. 设置电流 - 对应QT程序中的CurrentSet调用
            if not self.set_current(channel_num, current_ma):
                return False

            # 2. 立即启动刺激 - 对应QT程序中spinBox valueChanged后立即启动刺激
            # QT程序中没有延时，是立即执行SwitchChannelState(channel, 3)
            result = self.dll.SwitchChannelState(channel_num, ChannelState.WORKING.value)
            if result != 0:
                self.logger.error(f"启动通道{channel_num}刺激失败，错误码: {result}")
                return False

            self.logger.info(f"通道{channel_num}立即启动3秒预刺激")

            # 3. 启动3秒自动停止定时器 - 对应QT程序中的3秒预刺激机制
            self._start_auto_stop_timer(channel_num, 3.0)

            return True

        except Exception as e:
            self.logger.error(f"spinBox调节电流时发生错误: {e}")
            return False

    def start_continuous_stimulation(self, channel_num: int) -> bool:
        """启动持续刺激 - 严格按照QT程序NK::on_Button_dianchiji_shart_clicked逻辑"""
        try:
            if not self.is_connected():
                self.logger.error("设备未连接，无法启动刺激")
                return False

            # 获取当前电流值
            current_ma = self.channel_a_current if channel_num == 1 else self.channel_b_current
            if current_ma <= 0:
                self.logger.error(f"通道{channel_num}电流未设置，无法启动刺激")
                return False

            self.logger.info(f"Button_dianchiji_shart: 启动通道{channel_num}持续刺激，电流: {current_ma}mA")

            # QT程序中NK::on_Button_dianchiji_shart_clicked只执行SwitchChannelState
            # 不执行其他多余操作，立即切换通道状态
            result = self.dll.SwitchChannelState(channel_num, ChannelState.WORKING.value)

            if result == 0:
                self.logger.info(f"通道{channel_num}持续刺激启动成功")
                return True
            else:
                self.logger.error(f"启动通道{channel_num}持续刺激失败，错误码: {result}")
                return False

        except Exception as e:
            self.logger.error(f"启动持续刺激时发生错误: {e}")
            return False

    def stop_stimulation(self, channel_num: int) -> bool:
        """停止刺激 - 按照QT程序NK::on_Button_dianchiji_stop_clicked逻辑"""
        try:
            if not self.is_connected():
                self.logger.error("设备未连接，无法停止刺激")
                return False

            # 取消自动停止定时器
            if channel_num in self.auto_stop_timers:
                timer = self.auto_stop_timers[channel_num]
                if timer.is_alive():
                    timer.cancel()
                del self.auto_stop_timers[channel_num]

            # 按照QT程序NK::on_Button_dianchiji_stop_clicked逻辑：
            # 切换通道到停止状态
            result = self.dll.SwitchChannelState(channel_num, ChannelState.STOPPED.value)

            if result == 0:
                self.logger.info(f"通道{channel_num}刺激已停止")
                return True
            else:
                self.logger.error(f"停止通道{channel_num}刺激失败，错误码: {result}")
                return False

        except Exception as e:
            self.logger.error(f"停止刺激时发生错误: {e}")
            return False

    def stop_all_stimulation_qt_style(self) -> bool:
        """停止所有刺激 - 按照QT程序NK::on_Button_dianchiji_stop_clicked的完整逻辑"""
        try:
            success = True

            self.logger.info("Button_dianchiji_stop: 停止所有通道刺激")

            # 停止A通道
            result_a = self.dll.SwitchChannelState(1, ChannelState.STOPPED.value)
            if result_a == 0:
                self.logger.info("A通道已停止")
            else:
                self.logger.error(f"停止A通道失败，错误码: {result_a}")
                success = False

            # 停止B通道
            result_b = self.dll.SwitchChannelState(2, ChannelState.STOPPED.value)
            if result_b == 0:
                self.logger.info("B通道已停止")
            else:
                self.logger.error(f"停止B通道失败，错误码: {result_b}")
                success = False

            # 切换设备到空闲状态 - 对应QT程序中的SwitchDeviceState(0)
            device_result = self.dll.SwitchDeviceState(0)  # 0 = 空闲状态
            if device_result == 0:
                self.logger.info("设备已切换到空闲状态，所有刺激已停止")
            else:
                self.logger.warning(f"切换设备到空闲状态失败，错误码: {device_result}")
                success = False

            # 清理所有自动停止定时器
            for timer in self.auto_stop_timers.values():
                if timer.is_alive():
                    timer.cancel()
            self.auto_stop_timers.clear()

            return success

        except Exception as e:
            self.logger.error(f"停止所有刺激时发生错误: {e}")
            return False

    def stop_all_stimulation(self) -> bool:
        """停止所有刺激"""
        try:
            success = True

            # 停止A通道
            if not self.stop_stimulation(1):
                success = False

            # 停止B通道
            if not self.stop_stimulation(2):
                success = False

            # 切换设备到空闲状态
            if self.is_connected():
                device_result = self.dll.SwitchDeviceState(0)  # 0 = 空闲状态
                if device_result == 0:
                    self.logger.info("设备已切换到空闲状态")
                else:
                    self.logger.warning(f"切换设备到空闲状态失败，错误码: {device_result}")
                    success = False

            return success

        except Exception as e:
            self.logger.error(f"停止所有刺激时发生错误: {e}")
            return False

    def start_stimulation(self, channel_num: int) -> bool:
        """启动刺激 - 兼容接口，调用持续刺激方法"""
        return self.start_continuous_stimulation(channel_num)



    def _start_auto_stop_timer(self, channel_num: int, duration_seconds: float):
        """启动自动停止定时器"""
        try:
            # 取消之前的定时器
            if channel_num in self.auto_stop_timers:
                old_timer = self.auto_stop_timers[channel_num]
                if old_timer.is_alive():
                    old_timer.cancel()

            # 创建新的定时器
            def auto_stop():
                try:
                    self.stop_stimulation(channel_num)
                    self.logger.info(f"通道{channel_num}自动停止刺激（{duration_seconds}秒后）")
                except Exception as e:
                    self.logger.error(f"自动停止通道{channel_num}刺激失败: {e}")

            timer = threading.Timer(duration_seconds, auto_stop)
            self.auto_stop_timers[channel_num] = timer
            timer.start()

        except Exception as e:
            self.logger.error(f"启动自动停止定时器失败: {e}")

    def get_current_values(self) -> tuple:
        """获取当前电流值"""
        return self.channel_a_current, self.channel_b_current

    def get_status_info(self) -> dict:
        """获取设备状态信息"""
        return {
            'device_status': self.status.value,
            'channel_a_status': self.get_channel_status_text(self.channel_a_status),
            'channel_b_status': self.get_channel_status_text(self.channel_b_status),
            'channel_a_current': self.channel_a_current,
            'channel_b_current': self.channel_b_current,
            'is_connected': self.is_connected()
        }

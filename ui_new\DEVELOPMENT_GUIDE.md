# NK脑机接口康复系统 - 新UI开发指南

## 开发环境设置

### 必需依赖
```bash
# 核心依赖
PySide6 >= 6.5.0
Python >= 3.8

# 可选依赖（用于完整功能）
pyqtgraph >= 0.13.0  # 脑电信号可视化
mne >= 1.0.0         # 脑电数据处理
numpy >= 1.21.0      # 数值计算
```

### 开发工具推荐
- **IDE**: VS Code / PyCharm
- **调试**: Qt Designer (可选)
- **版本控制**: Git
- **代码格式**: Black, isort

## 项目架构详解

### 1. 目录结构
```
ui_new/
├── __init__.py                 # 模块入口
├── main_window_new.py          # 主窗口（单页应用架构）
├── components/                 # 可复用UI组件
│   ├── __init__.py
│   ├── sidebar.py             # 侧边栏导航
│   └── top_bar.py             # 顶部状态栏
├── pages/                     # 页面模块
│   ├── __init__.py
│   ├── base_page.py           # 页面基类
│   └── dashboard_page.py      # 实时监测页面
├── themes/                    # 主题系统
│   ├── __init__.py
│   ├── theme_manager.py       # 主题管理器
│   ├── medical_theme.py       # 医疗主题配置
│   └── tech_theme.py          # 科技主题配置
├── styles/                    # 样式文件
│   ├── __init__.py
│   ├── base_styles.qss        # 基础样式
│   ├── medical_theme.qss      # 医疗主题样式
│   └── tech_theme.qss         # 科技主题样式
└── tests/                     # 测试文件
    ├── ui_only_test.py        # 纯UI测试
    ├── simple_test.py         # 简化测试
    └── test_new_ui.py         # 完整测试
```

### 2. 架构设计原则

#### 2.1 单页应用架构
- **主窗口**: 固定布局（侧边栏 + 主内容区）
- **页面切换**: 通过QStackedWidget实现
- **状态管理**: 集中式状态管理
- **路由系统**: 基于页面ID的导航

#### 2.2 组件化设计
- **可复用性**: 组件独立，可在多处使用
- **松耦合**: 组件间通过信号槽通信
- **单一职责**: 每个组件专注特定功能
- **可测试性**: 组件可独立测试

#### 2.3 直接迁移模式
- **无适配器**: 直接使用现有业务逻辑
- **性能优化**: 减少中间层开销
- **代码简洁**: 避免不必要的抽象
- **维护性**: 降低系统复杂度

## 开发规范

### 1. 代码风格

#### 1.1 命名规范
```python
# 类名：大驼峰
class ModernSidebar(QWidget):
    pass

# 方法名：小写下划线
def setup_ui(self):
    pass

# 变量名：小写下划线
current_theme = "medical"

# 常量：大写下划线
DEFAULT_THEME = "medical"

# 私有方法：前缀下划线
def _internal_method(self):
    pass
```

#### 1.2 类型提示
```python
from typing import Optional, Dict, List, Any

def set_theme(self, theme_name: str) -> bool:
    """设置主题"""
    pass

def get_config(self) -> Dict[str, Any]:
    """获取配置"""
    pass

def process_data(self, data: List[Dict[str, Any]]) -> Optional[str]:
    """处理数据"""
    pass
```

#### 1.3 文档字符串
```python
def create_page(self, page_id: str, page_class: type) -> bool:
    """创建页面实例
    
    Args:
        page_id: 页面唯一标识符
        page_class: 页面类
        
    Returns:
        bool: 创建是否成功
        
    Raises:
        ValueError: 页面ID无效时抛出
        RuntimeError: 页面创建失败时抛出
    """
    pass
```

### 2. 组件开发规范

#### 2.1 组件基类
```python
from PySide6.QtWidgets import QWidget
from PySide6.QtCore import Signal
import logging

class BaseComponent(QWidget):
    """组件基类"""
    
    # 信号定义
    component_loaded = Signal()
    component_error = Signal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger(self.__class__.__name__)
        self.init_ui()
        self.setup_connections()
    
    def init_ui(self):
        """初始化UI（子类实现）"""
        raise NotImplementedError
    
    def setup_connections(self):
        """设置信号连接（子类实现）"""
        raise NotImplementedError
```

#### 2.2 组件开发步骤
1. **继承基类**: 从BaseComponent或QWidget继承
2. **定义信号**: 声明组件对外的信号接口
3. **实现UI**: 在init_ui方法中构建界面
4. **连接信号**: 在setup_connections中连接信号槽
5. **添加样式**: 设置objectName和class属性
6. **编写测试**: 创建组件测试用例

### 3. 页面开发规范

#### 3.1 页面开发模板
```python
from ui_new.pages.base_page import BasePage
from core.auth_manager import Permission

class MyPage(BasePage):
    """我的页面"""
    
    def setup_ui(self):
        """设置UI"""
        # 创建内容组件
        self.content_widget = QWidget()
        self.main_layout.addWidget(self.content_widget)
        
        # 实现具体布局
        layout = QVBoxLayout(self.content_widget)
        # ... 添加UI元素
    
    def setup_connections(self):
        """设置信号连接"""
        # 连接按钮信号
        # self.button.clicked.connect(self.on_button_clicked)
        pass
    
    def load_data(self):
        """加载数据"""
        try:
            # 检查权限
            if not self.has_permission(Permission.REQUIRED_PERMISSION):
                self.show_error("权限不足")
                return
            
            # 加载数据
            data = self.db_manager.get_data()
            
            # 更新UI
            self.update_ui(data)
            
            # 发送加载完成信号
            self.page_loaded.emit()
            
        except Exception as e:
            self.logger.error(f"加载数据失败: {e}")
            self.show_error(f"加载失败: {e}")
    
    def check_permissions(self) -> bool:
        """检查页面权限"""
        return self.has_permission(Permission.REQUIRED_PERMISSION)
```

#### 3.2 页面注册
```python
# 在main_window_new.py中注册页面
from .pages.my_page import MyPage

self.page_classes = {
    'my_page': MyPage,
    # ... 其他页面
}
```

#### 3.3 导航配置
```python
# 在sidebar.py中添加导航项
{
    "title": "我的分组",
    "items": [
        ("my_page", "我的页面", "🏠"),
    ]
}
```

### 4. 主题开发规范

#### 4.1 主题配置结构
```python
class MyTheme:
    """自定义主题配置"""
    
    COLORS = {
        'primary': '#color',
        'secondary': '#color',
        # ... 更多颜色
    }
    
    FONTS = {
        'primary': 'font-family',
        'sizes': {
            'base': 16,
            # ... 更多尺寸
        }
    }
    
    @classmethod
    def get_theme_config(cls) -> Dict[str, Any]:
        return {
            'name': 'my_theme',
            'display_name': '我的主题',
            'colors': cls.COLORS,
            'fonts': cls.FONTS,
        }
```

#### 4.2 QSS样式规范
```css
/* 组件样式 */
QWidget#my_component {
    background-color: var(--bg-primary);
    border-radius: 8px;
}

/* 状态样式 */
QPushButton.my-button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 6px;
}

QPushButton.my-button:hover {
    background-color: var(--primary-hover);
}

QPushButton.my-button:pressed {
    background-color: var(--primary-pressed);
}

/* 响应式样式 */
QWidget.card {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 20px;
}
```

## 测试指南

### 1. 单元测试
```python
import unittest
from PySide6.QtWidgets import QApplication
from ui_new.components.sidebar import ModernSidebar

class TestModernSidebar(unittest.TestCase):
    
    @classmethod
    def setUpClass(cls):
        cls.app = QApplication.instance()
        if cls.app is None:
            cls.app = QApplication([])
    
    def setUp(self):
        self.sidebar = ModernSidebar()
    
    def test_sidebar_creation(self):
        """测试侧边栏创建"""
        self.assertIsNotNone(self.sidebar)
        self.assertEqual(self.sidebar.width(), 280)
    
    def test_theme_application(self):
        """测试主题应用"""
        # 测试主题切换
        pass
```

### 2. 集成测试
```python
def test_page_navigation():
    """测试页面导航"""
    # 创建主窗口
    main_window = ModernMainWindow()
    
    # 测试页面切换
    main_window.switch_to_page('dashboard')
    
    # 验证页面加载
    assert main_window.stacked_widget.currentWidget() is not None
```

### 3. UI测试
```bash
# 运行UI测试
python ui_new/ui_only_test.py

# 运行完整测试
python ui_new/test_new_ui.py
```

## 性能优化

### 1. 延迟加载
```python
def switch_to_page(self, page_id: str):
    """延迟加载页面"""
    if page_id not in self.page_instances:
        # 只在需要时创建页面
        page_instance = self.page_classes[page_id]()
        self.page_instances[page_id] = page_instance
```

### 2. 资源管理
```python
def cleanup_page(self, page_id: str):
    """清理页面资源"""
    if page_id in self.page_instances:
        page = self.page_instances[page_id]
        page.stop_auto_refresh()
        page.cleanup_resources()
```

### 3. 内存优化
```python
def hide_page(self):
    """页面隐藏时释放资源"""
    super().hide_page()
    # 停止定时器
    self.refresh_timer.stop()
    # 清理大对象
    self.large_data = None
```

## 调试技巧

### 1. 日志调试
```python
import logging

# 设置详细日志
logging.basicConfig(level=logging.DEBUG)

# 组件日志
self.logger.debug(f"组件状态: {self.state}")
self.logger.info(f"用户操作: {operation}")
self.logger.error(f"错误信息: {error}")
```

### 2. Qt调试
```python
# 启用Qt日志
import os
os.environ['QT_LOGGING_RULES'] = 'qt.qpa.*.debug=true'

# 检查样式应用
widget.style().unpolish(widget)
widget.style().polish(widget)
```

### 3. 性能分析
```python
import time
import cProfile

# 性能计时
start_time = time.time()
# ... 执行代码
end_time = time.time()
print(f"执行时间: {end_time - start_time:.3f}s")

# 性能分析
cProfile.run('function_to_profile()')
```

## 部署指南

### 1. 打包准备
```python
# requirements.txt
PySide6>=6.5.0
pyqtgraph>=0.13.0
mne>=1.0.0
numpy>=1.21.0
```

### 2. 资源文件
```
resources/
├── images/
│   ├── app_icon.png
│   └── logos/
├── fonts/
└── config/
```

### 3. 配置文件
```python
# config.py
UI_CONFIG = {
    'default_theme': 'medical',
    'auto_save_interval': 300,
    'animation_duration': 300,
    'debug_mode': False,
}
```

## 常见问题

### 1. 样式不生效
- 检查objectName设置
- 验证QSS语法
- 确认主题加载

### 2. 信号连接失败
- 检查信号定义
- 验证槽函数存在
- 确认连接时机

### 3. 页面加载慢
- 使用延迟加载
- 优化数据查询
- 减少UI复杂度

### 4. 内存泄漏
- 正确断开信号连接
- 及时清理资源
- 避免循环引用

---

**文档版本**: 2.0.0  
**最后更新**: 2024年  
**维护者**: AI Assistant

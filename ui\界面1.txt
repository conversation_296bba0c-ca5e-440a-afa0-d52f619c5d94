<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>脑机接口康复医疗系统 - NeuroKinect Pro</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* 现代医疗主题 */
            --medical-bg-primary: #f8fafc;
            --medical-bg-secondary: #ffffff;
            --medical-bg-accent: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --medical-text-primary: #1e293b;
            --medical-text-secondary: #64748b;
            --medical-border: #e2e8f0;
            --medical-accent: #3b82f6;
            --medical-success: #10b981;
            --medical-warning: #f59e0b;
            --medical-danger: #ef4444;
            
            /* 科技感BCI主题 */
            --bci-bg-primary: #0f172a;
            --bci-bg-secondary: #1e293b;
            --bci-bg-accent: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            --bci-text-primary: #e2e8f0;
            --bci-text-secondary: #94a3b8;
            --bci-border: #334155;
            --bci-accent: #0ea5e9;
            --bci-success: #10b981;
            --bci-warning: #f59e0b;
            --bci-danger: #ef4444;
            --bci-glow: #0ea5e9;
        }

        [data-theme="medical"] {
            --bg-primary: var(--medical-bg-primary);
            --bg-secondary: var(--medical-bg-secondary);
            --bg-accent: var(--medical-bg-accent);
            --text-primary: var(--medical-text-primary);
            --text-secondary: var(--medical-text-secondary);
            --border-color: var(--medical-border);
            --accent-color: var(--medical-accent);
            --success-color: var(--medical-success);
            --warning-color: var(--medical-warning);
            --danger-color: var(--medical-danger);
        }

        [data-theme="bci"] {
            --bg-primary: var(--bci-bg-primary);
            --bg-secondary: var(--bci-bg-secondary);
            --bg-accent: var(--bci-bg-accent);
            --text-primary: var(--bci-text-primary);
            --text-secondary: var(--bci-text-secondary);
            --border-color: var(--bci-border);
            --accent-color: var(--bci-accent);
            --success-color: var(--bci-success);
            --warning-color: var(--bci-warning);
            --danger-color: var(--bci-danger);
        }

        body {
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            transition: all 0.3s ease;
            overflow-x: hidden;
        }

        /* 顶部状态栏 */
        .top-status-bar {
            height: 40px;
            background: var(--bg-accent);
            color: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            font-size: 12px;
            position: relative;
            overflow: hidden;
        }

        .top-status-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: statusScan 3s infinite;
        }

        @keyframes statusScan {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .system-status {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .status-online { background: #10b981; }
        .status-warning { background: #f59e0b; }
        .status-error { background: #ef4444; }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* 主容器 */
        .main-container {
            display: flex;
            height: calc(100vh - 40px);
        }

        /* 侧边栏 */
        .sidebar {
            width: 280px;
            background: var(--bg-secondary);
            border-right: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            position: relative;
            transition: all 0.3s ease;
            box-shadow: 2px 0 10px rgba(0,0,0,0.05);
        }

        [data-theme="bci"] .sidebar {
            border-right: 1px solid var(--bci-border);
            box-shadow: 2px 0 20px rgba(14, 165, 233, 0.1);
        }

        .sidebar.collapsed {
            width: 70px;
        }

        /* Logo区域 */
        .logo-section {
            padding: 25px 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 18px;
            font-weight: 700;
            color: var(--text-primary);
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: var(--bg-accent);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            position: relative;
            overflow: hidden;
        }

        [data-theme="bci"] .logo-icon {
            box-shadow: 0 0 20px rgba(14, 165, 233, 0.3);
        }

        .logo-icon::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(transparent, rgba(255,255,255,0.1), transparent);
            animation: logoRotate 4s linear infinite;
        }

        @keyframes logoRotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .sidebar.collapsed .logo-text {
            display: none;
        }

        /* 导航菜单 */
        .nav-menu {
            flex: 1;
            padding: 20px 0;
            overflow-y: auto;
        }

        .nav-section {
            margin-bottom: 30px;
        }

        .nav-section-title {
            padding: 0 20px 10px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            color: var(--text-secondary);
            letter-spacing: 0.5px;
        }

        .sidebar.collapsed .nav-section-title {
            display: none;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: var(--text-secondary);
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
            border-radius: 0 25px 25px 0;
            margin-right: 10px;
        }

        .nav-item:hover {
            background: rgba(59, 130, 246, 0.1);
            color: var(--accent-color);
            transform: translateX(5px);
        }

        .nav-item.active {
            background: var(--accent-color);
            color: white;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
        }

        [data-theme="bci"] .nav-item.active {
            box-shadow: 0 4px 15px rgba(14, 165, 233, 0.3);
        }

        .nav-item.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: white;
        }

        .nav-icon {
            width: 20px;
            height: 20px;
            margin-right: 15px;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .sidebar.collapsed .nav-text {
            display: none;
        }

        .sidebar.collapsed .nav-item {
            justify-content: center;
            margin-right: 0;
            border-radius: 10px;
            margin: 5px 10px;
        }

        .sidebar.collapsed .nav-icon {
            margin-right: 0;
        }

        /* 用户信息 */
        .user-section {
            padding: 20px;
            border-top: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            background: var(--bg-accent);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            position: relative;
        }

        [data-theme="bci"] .user-avatar {
            box-shadow: 0 0 15px rgba(14, 165, 233, 0.3);
        }

        .user-info {
            flex: 1;
        }

        .user-name {
            font-weight: 600;
            color: var(--text-primary);
            font-size: 14px;
        }

        .user-role {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .sidebar.collapsed .user-info {
            display: none;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        /* 顶部导航栏 */
        .top-nav {
            height: 70px;
            background: var(--bg-secondary);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        [data-theme="bci"] .top-nav {
            box-shadow: 0 2px 20px rgba(14, 165, 233, 0.1);
        }

        .nav-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .sidebar-toggle {
            width: 40px;
            height: 40px;
            border: none;
            background: var(--bg-primary);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: var(--text-primary);
            transition: all 0.3s ease;
        }

        .sidebar-toggle:hover {
            background: var(--accent-color);
            color: white;
            transform: scale(1.05);
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .breadcrumb-item {
            color: var(--text-secondary);
        }

        .breadcrumb-item.active {
            color: var(--text-primary);
            font-weight: 600;
        }

        .nav-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        /* 主题切换器 */
        .theme-switcher {
            display: flex;
            align-items: center;
            gap: 10px;
            background: var(--bg-primary);
            border-radius: 25px;
            padding: 4px;
            border: 1px solid var(--border-color);
        }

        .theme-option {
            padding: 8px 16px;
            border-radius: 20px;
            border: none;
            background: transparent;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
            font-weight: 500;
        }

        .theme-option.active {
            background: var(--accent-color);
            color: white;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
        }

        [data-theme="bci"] .theme-option.active {
            box-shadow: 0 2px 8px rgba(14, 165, 233, 0.3);
        }

        /* 通知和快捷操作 */
        .action-buttons {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .action-btn {
            width: 40px;
            height: 40px;
            border: none;
            background: var(--bg-primary);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: var(--text-primary);
            transition: all 0.3s ease;
            position: relative;
        }

        .action-btn:hover {
            background: var(--accent-color);
            color: white;
            transform: translateY(-2px);
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            width: 18px;
            height: 18px;
            background: var(--danger-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            color: white;
            font-weight: 600;
        }

        /* 内容区域 */
        .content-area {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
            background: var(--bg-primary);
        }

        /* 演示内容卡片 */
        .demo-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            height: 100%;
        }

        .demo-card {
            background: var(--bg-secondary);
            border-radius: 12px;
            padding: 25px;
            border: 1px solid var(--border-color);
            box-shadow: 0 4px 20px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }

        [data-theme="bci"] .demo-card {
            box-shadow: 0 4px 20px rgba(14, 165, 233, 0.1);
            border: 1px solid var(--bci-border);
        }

        .demo-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.1);
        }

        [data-theme="bci"] .demo-card:hover {
            box-shadow: 0 8px 30px rgba(14, 165, 233, 0.2);
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card-icon {
            width: 30px;
            height: 30px;
            background: var(--bg-accent);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
        }

        /* 实时监测区域 */
        .monitoring-area {
            height: 200px;
            background: var(--bg-primary);
            border-radius: 8px;
            margin: 15px 0;
            position: relative;
            overflow: hidden;
            border: 1px solid var(--border-color);
        }

        .wave-container {
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--accent-color);
            transform: translateY(-50%);
        }

        .wave-pulse {
            position: absolute;
            top: 0;
            left: 0;
            width: 100px;
            height: 100%;
            background: linear-gradient(90deg, transparent, var(--accent-color), transparent);
            animation: wavePulse 2s linear infinite;
        }

        @keyframes wavePulse {
            0% { left: -100px; }
            100% { left: 100%; }
        }

        /* 状态指示器 */
        .status-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 20px;
        }

        .status-card {
            padding: 15px;
            border-radius: 8px;
            border: 1px solid var(--border-color);
            text-align: center;
        }

        .status-value {
            font-size: 24px;
            font-weight: 700;
            color: var(--accent-color);
            margin-bottom: 5px;
        }

        .status-label {
            font-size: 12px;
            color: var(--text-secondary);
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .demo-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                position: fixed;
                z-index: 1000;
                height: 100vh;
            }
            
            .sidebar.open {
                transform: translateX(0);
            }
            
            .main-content {
                width: 100%;
            }
            
            .top-nav {
                padding: 0 15px;
            }
            
            .content-area {
                padding: 20px;
            }
        }

        /* 加载动画 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.5s ease;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 3px solid var(--border-color);
            border-top: 3px solid var(--accent-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-overlay.hidden {
            opacity: 0;
            pointer-events: none;
        }
    </style>
</head>
<body data-theme="medical">
    <!-- 加载动画 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- 顶部状态栏 -->
    <div class="top-status-bar">
        <div class="system-status">
            <div class="status-item">
                <div class="status-dot status-online"></div>
                <span>系统运行正常</span>
            </div>
            <div class="status-item">
                <div class="status-dot status-online"></div>
                <span>设备连接正常</span>
            </div>
            <div class="status-item">
                <div class="status-dot status-warning"></div>
                <span>数据同步中</span>
            </div>
        </div>
        <div>
            <span>NeuroKinect Pro v2.0 | 许可证：KFY160701 | 2025-06-13 15:16:23</span>
        </div>
    </div>

    <div class="main-container">
        <!-- 侧边栏 -->
        <nav class="sidebar" id="sidebar">
            <!-- Logo区域 -->
            <div class="logo-section">
                <a href="#" class="logo">
                    <div class="logo-icon">🧠</div>
                    <span class="logo-text">NeuroKinect</span>
                </a>
            </div>

            <!-- 导航菜单 -->
            <div class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">核心功能</div>
                    <a href="#" class="nav-item active">
                        <div class="nav-icon">📊</div>
                        <span class="nav-text">实时监测</span>
                    </a>
                    <a href="#" class="nav-item">
                        <div class="nav-icon">🧪</div>
                        <span class="nav-text">治疗训练</span>
                    </a>
                    <a href="#" class="nav-item">
                        <div class="nav-icon">👥</div>
                        <span class="nav-text">患者管理</span>
                    </a>
                    <a href="#" class="nav-item">
                        <div class="nav-icon">📈</div>
                        <span class="nav-text">数据分析</span>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">报告系统</div>
                    <a href="#" class="nav-item">
                        <div class="nav-icon">📋</div>
                        <span class="nav-text">训练报告</span>
                    </a>
                    <a href="#" class="nav-item">
                        <div class="nav-icon">📑</div>
                        <span class="nav-text">康复评估</span>
                    </a>
                    <a href="#" class="nav-item">
                        <div class="nav-icon">📊</div>
                        <span class="nav-text">统计分析</span>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">系统管理</div>
                    <a href="#" class="nav-item">
                        <div class="nav-icon">🔧</div>
                        <span class="nav-text">设备配置</span>
                    </a>
                    <a href="#" class="nav-item">
                        <div class="nav-icon">👤</div>
                        <span class="nav-text">用户管理</span>
                    </a>
                    <a href="#" class="nav-item">
                        <div class="nav-icon">⚙️</div>
                        <span class="nav-text">系统设置</span>
                    </a>
                </div>
            </div>

            <!-- 用户信息 -->
            <div class="user-section">
                <div class="user-avatar">A</div>
                <div class="user-info">
                    <div class="user-name">管理员</div>
                    <div class="user-role">系统管理员</div>
                </div>
            </div>
        </nav>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部导航栏 -->
            <header class="top-nav">
                <div class="nav-left">
                    <button class="sidebar-toggle" id="sidebarToggle">
                        <span>☰</span>
                    </button>
                    <div class="breadcrumb">
                        <span class="breadcrumb-item">脑机接口系统</span>
                        <span class="breadcrumb-item">></span>
                        <span class="breadcrumb-item active">实时监测</span>
                    </div>
                </div>

                <div class="nav-right">
                    <!-- 主题切换器 -->
                    <div class="theme-switcher">
                        <button class="theme-option active" data-theme="medical">医疗版</button>
                        <button class="theme-option" data-theme="bci">科技版</button>
                    </div>

                    <!-- 快捷操作 -->
                    <div class="action-buttons">
                        <button class="action-btn" title="通知">
                            <span>🔔</span>
                            <div class="notification-badge">3</div>
                        </button>
                        <button class="action-btn" title="帮助">
                            <span>❓</span>
                        </button>
                        <button class="action-btn" title="全屏">
                            <span>⛶</span>
                        </button>
                    </div>
                </div>
            </header>

            <!-- 内容区域 -->
            <div class="content-area">
                <div class="demo-grid">
                    <!-- 主监测区域 -->
                    <div class="demo-card">
                        <div class="card-title">
                            <div class="card-icon">🧠</div>
                            实时脑电信号监测
                        </div>
                        <div class="monitoring-area">
                            <div class="wave-container">
                                <div class="wave-pulse"></div>
                            </div>
                            <div style="position: absolute; bottom: 10px; left: 20px; font-size: 12px; color: var(--text-secondary);">
                                采样率: 1000Hz | 通道: 32 | 阻抗: < 5kΩ
                            </div>
                        </div>
                        <div style="margin-top: 15px; display: flex; justify-content: space-between; font-size: 14px;">
                            <span style="color: var(--text-secondary);">Alpha波: <span style="color: var(--accent-color); font-weight: 600;">8.2Hz</span></span>
                            <span style="color: var(--text-secondary);">Beta波: <span style="color: var(--accent-color); font-weight: 600;">15.7Hz</span></span>
                            <span style="color: var(--text-secondary);">Theta波: <span style="color: var(--accent-color); font-weight: 600;">6.1Hz</span></span>
                            <span style="color: var(--text-secondary);">Delta波: <span style="color: var(--accent-color); font-weight: 600;">2.3Hz</span></span>
                        </div>
                    </div>

                    <!-- 状态面板 -->
                    <div class="demo-card">
                        <div class="card-title">
                            <div class="card-icon">📊</div>
                            系统状态监控
                        </div>
                        <div class="status-grid">
                            <div class="status-card">
                                <div class="status-value">87%</div>
                                <div class="status-label">信号质量</div>
                            </div>
                            <div class="status-card">
                                <div class="status-value">98%</div>
                                <div class="status-label">设备连接</div>
                            </div>
                            <div class="status-card">
                                <div class="status-value">73%</div>
                                <div class="status-label">训练进度</div>
                            </div>
                            <div class="status-card">
                                <div class="status-value">15分钟</div>
                                <div class="status-label">剩余时间</div>
                            </div>
                        </div>
                        
                        <div style="margin-top: 25px; padding: 15px; background: var(--bg-primary); border-radius: 8px; border: 1px solid var(--border-color);">
                            <h4 style="color: var(--text-primary); margin-bottom: 10px; font-size: 14px;">当前患者</h4>
                            <div style="color: var(--text-secondary); font-size: 12px; line-height: 1.5;">
                                <div>姓名: 张三</div>
                                <div>年龄: 45岁</div>
                                <div>诊断: 运动功能障碍</div>
                                <div>训练: 第12次康复训练</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 主题切换功能
        const themeButtons = document.querySelectorAll('.theme-option');
        const body = document.body;

        themeButtons.forEach(button => {
            button.addEventListener('click', () => {
                const theme = button.dataset.theme;
                
                // 更新主题
                body.setAttribute('data-theme', theme);
                
                // 更新按钮状态
                themeButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');

                // 保存主题设置
                localStorage.setItem('bci-theme', theme);
            });
        });

        // 侧边栏切换
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebar = document.getElementById('sidebar');

        sidebarToggle.addEventListener('click', () => {
            sidebar.classList.toggle('collapsed');
        });

        // 导航项点击效果
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                
                // 移除所有活动状态
                navItems.forEach(nav => nav.classList.remove('active'));
                
                // 添加当前活动状态
                item.classList.add('active');
            });
        });

        // 加载动画
        window.addEventListener('load', () => {
            setTimeout(() => {
                document.getElementById('loadingOverlay').classList.add('hidden');
            }, 1500);
        });

        // 恢复保存的主题
        const savedTheme = localStorage.getItem('bci-theme');
        if (savedTheme) {
            body.setAttribute('data-theme', savedTheme);
            themeButtons.forEach(btn => {
                btn.classList.toggle('active', btn.dataset.theme === savedTheme);
            });
        }

        // 响应式侧边栏
        function handleResize() {
            if (window.innerWidth <= 768) {
                sidebar.classList.remove('collapsed');
            }
        }

        window.addEventListener('resize', handleResize);
        handleResize(); // 初始检查

        // 移动端侧边栏切换
        if (window.innerWidth <= 768) {
            sidebarToggle.addEventListener('click', (e) => {
                e.stopPropagation();
                sidebar.classList.toggle('open');
            });

            // 点击外部关闭侧边栏
            document.addEventListener('click', (e) => {
                if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                    sidebar.classList.remove('open');
                }
            });
        }
    </script>
</body>
</html>
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
治疗工作流程控制器
Treatment Workflow Controller

实现完整的治疗工作流程，包括UDP通信、实时分类、电刺激控制等
使用事件驱动的信号槽机制，简化状态管理

作者: AI Assistant
版本: 2.0.0
"""

import logging
import time
from typing import Optional, Dict, Any, Callable
from dataclasses import dataclass
from datetime import datetime

try:
    from PyQt6.QtCore import QObject, QTimer, pyqtSignal
    QT_AVAILABLE = True
except ImportError:
    try:
        from PyQt5.QtCore import QObject, QTimer, pyqtSignal
        QT_AVAILABLE = True
    except ImportError:
        QT_AVAILABLE = False
        logging.warning("PyQt未安装，治疗工作流程功能将不可用")

from core.udp_communicator import UDPCommunicator, UDPCommand, get_udp_communicator
from core.voice_prompt import get_voice_engine
from utils.app_config import AppConfig


@dataclass
class TreatmentSession:
    """治疗会话数据"""
    patient_id: str                    # 患者编号
    patient_name: str                  # 患者姓名
    start_time: datetime               # 治疗开始时间
    end_time: Optional[datetime]       # 治疗结束时间
    total_imagery_count: int           # 总想象次数
    successful_triggers: int           # 成功触发次数
    treatment_duration_minutes: int    # 治疗时长（分钟）
    treatment_score: int               # 治疗得分
    treatment_evaluation: str          # 治疗评价
    record_number: int                 # 记录编号


# 根据QT可用性选择基类
if QT_AVAILABLE:
    BaseClass = QObject
else:
    BaseClass = object


class TreatmentWorkflowController(BaseClass):
    """治疗工作流程控制器 - 使用事件驱动的信号槽机制"""

    # 定义信号（仅在QT可用时）
    if QT_AVAILABLE:
        progress_updated = pyqtSignal(dict)
        treatment_completed = pyqtSignal(object)

    def __init__(self):
        if QT_AVAILABLE:
            super().__init__()

        self.logger = logging.getLogger(__name__)

        # 配置参数
        self.config = AppConfig.TREATMENT_WORKFLOW_CONFIG
        self.motor_imagery_timeout = self.config['motor_imagery_timeout']  # 30秒超时

        # 核心组件
        self.udp_comm = get_udp_communicator()
        self.voice_engine = get_voice_engine()

        # 治疗状态（简化为基本标志）
        self.current_session: Optional[TreatmentSession] = None
        self.is_treatment_active = False
        self.is_stimulation_active = False  # 电刺激激活标志

        # QTimer定时器（如果QT可用）
        if QT_AVAILABLE:
            self.timeout_timer: Optional[QTimer] = None          # 30秒超时定时器
            self.treatment_timer: Optional[QTimer] = None        # 治疗总时长定时器
            self.stimulation_timer: Optional[QTimer] = None      # 刺激时长定时器
            self.rest_timer: Optional[QTimer] = None             # 休息定时器
        else:
            self.timeout_timer = None
            self.treatment_timer = None
            self.stimulation_timer = None
            self.rest_timer = None

        # 回调函数
        self.progress_update_callback: Optional[Callable[[Dict[str, Any]], None]] = None
        self.treatment_complete_callback: Optional[Callable[[TreatmentSession], None]] = None

        # 外部依赖（需要在启动前设置）
        self.eeg_device = None              # 脑电设备
        self.stimulation_device = None      # 电刺激设备
        self.ml_model = None                # EEGNet模型
        self.database_manager = None        # 数据库管理器
        self.http_client = None             # HTTP客户端

        # 治疗参数（从UI获取）
        self.treatment_duration_setting = 20    # 治疗总时长（分钟）
        self.stimulation_duration_setting = 10  # 单次刺激时长（秒）
        self.adaptive_learning_enabled = False  # 自适应学习开关

        self.logger.info("治疗工作流程控制器初始化完成（事件驱动模式）")

    def set_dependencies(self, eeg_device, stimulation_device, ml_model, database_manager, http_client, treatment_ui=None, auth_manager=None):
        """设置外部依赖"""
        self.eeg_device = eeg_device
        self.stimulation_device = stimulation_device
        self.ml_model = ml_model
        self.database_manager = database_manager
        self.http_client = http_client
        self.treatment_ui = treatment_ui  # 治疗界面引用，用于获取电流设置和患者信息
        self.auth_manager = auth_manager  # 认证管理器，用于获取当前用户信息
        self.logger.debug("治疗工作流程依赖设置完成")

    def set_treatment_parameters(self, duration_minutes: int, stimulation_duration: int, adaptive_learning: bool):
        """设置治疗参数"""
        self.treatment_duration_setting = duration_minutes
        self.stimulation_duration_setting = stimulation_duration
        self.adaptive_learning_enabled = adaptive_learning
        self.logger.debug(f"治疗参数已设置: 时长{duration_minutes}分钟, 刺激{stimulation_duration}秒, 自适应{adaptive_learning}")

    def set_callbacks(self, progress_callback=None, complete_callback=None):
        """设置回调函数"""
        if progress_callback:
            self.progress_update_callback = progress_callback
        if complete_callback:
            self.treatment_complete_callback = complete_callback

    def start_treatment(self, patient_id: str, patient_name: str) -> bool:
        """
        开始治疗工作流程

        Args:
            patient_id: 患者编号
            patient_name: 患者姓名

        Returns:
            bool: 启动是否成功
        """
        try:
            if self.is_treatment_active:
                self.logger.warning("治疗已在进行中")
                return False

            # 检查前置条件
            if not self._check_preconditions():
                return False

            # 初始化治疗会话
            self.current_session = TreatmentSession(
                patient_id=patient_id,
                patient_name=patient_name,
                start_time=datetime.now(),
                end_time=None,
                total_imagery_count=0,
                successful_triggers=0,
                treatment_duration_minutes=0,
                treatment_score=0,
                treatment_evaluation="",
                record_number=self._generate_record_number()
            )

            # 启动治疗工作流程
            self.is_treatment_active = True
            self.is_stimulation_active = False

            # 治疗初始化
            self._initialize_treatment()

            # 启动独立定时器
            self._start_timeout_timer()      # 30秒超时定时器
            self._start_treatment_timer()    # 治疗总时长定时器

            self.logger.info(f"治疗工作流程已启动 - 患者: {patient_name} ({patient_id})")
            return True

        except Exception as e:
            self.logger.error(f"启动治疗工作流程失败: {e}")
            self.is_treatment_active = False
            return False

    def stop_treatment(self, force: bool = False) -> bool:
        """
        停止治疗工作流程

        Args:
            force: 是否强制停止

        Returns:
            bool: 停止是否成功
        """
        try:
            if not self.is_treatment_active:
                self.logger.warning("治疗未在进行中")
                return True

            self.logger.info("正在停止治疗工作流程...")
            self.is_treatment_active = False
            self.is_stimulation_active = False

            # 停止所有定时器
            self._stop_all_timers()

            # 停止所有电刺激输出
            self._stop_all_stimulation()

            # 重置电流设置
            self._reset_stimulation_currents()

            # 发送UDP停止指令
            self.udp_comm.send_command_with_retry(UDPCommand.STOPALL)

            # 停止语音引擎（如果有正在播放的语音）
            if hasattr(self.voice_engine, 'stop'):
                self.voice_engine.stop()

            # 语音提示（只说一次）
            self.voice_engine.speak("治疗结束")

            # 处理治疗数据
            if self.current_session and not force:
                self._process_treatment_completion()

            self.logger.info("治疗工作流程已停止")
            return True

        except Exception as e:
            self.logger.error(f"停止治疗工作流程失败: {e}")
            return False

    def _check_preconditions(self) -> bool:
        """检查治疗前置条件"""
        # 检查脑电设备连接
        if not self.eeg_device or not self.eeg_device.is_connected():
            self.logger.error("脑电设备未连接")
            self._show_error_dialog("脑电设备未连接，请先连接脑电设备")
            return False

        # 检查电刺激设备连接
        if not self.stimulation_device or not self.stimulation_device.is_connected():
            self.logger.error("电刺激设备未连接")
            self._show_error_dialog("电刺激设备未连接，请先连接电刺激设备")
            return False

        # 检查EEGNet模型加载
        if not self.ml_model or not self.ml_model.is_trained:
            self.logger.error("EEGNet模型未加载")
            self._show_error_dialog("EEGNet模型未加载，请先加载模型")
            return False

        # 检查是否选择了患者
        if not hasattr(self, 'treatment_ui') or not self.treatment_ui:
            self.logger.error("治疗界面未设置")
            self._show_error_dialog("系统错误：治疗界面未设置")
            return False

        # 检查患者信息
        patient_info = getattr(self.treatment_ui, 'current_patient_info', None)
        if not patient_info:
            self.logger.error("未选择患者")
            self._show_error_dialog("请先选择患者")
            return False

        self.logger.info("治疗前置条件检查通过")
        return True

    def _initialize_treatment(self):
        """治疗初始化"""
        self.logger.info("开始治疗初始化...")

        # 发送UDP指令进入治疗模式
        self.udp_comm.send_command(UDPCommand.TREAT)

        # 语音提示
        self.voice_engine.speak("开始治疗，请想象运动")

        # 想象次数计数器+1
        self.current_session.total_imagery_count += 1

        # 更新进度
        self._update_progress()

        self.logger.info("治疗初始化完成")

    def _generate_record_number(self) -> int:
        """生成治疗记录编号"""
        # 简单实现：使用时间戳
        return int(time.time())

    # ==================== 新的事件驱动核心方法 ====================

    def on_motor_imagery_detected(self):
        """
        运动想象检测信号的槽函数
        当UI检测到运动想象状态时调用此方法
        """
        try:
            if not self.is_treatment_active:
                self.logger.debug("治疗未激活，忽略运动想象信号")
                return

            if self.is_stimulation_active:
                self.logger.debug("电刺激正在进行中，忽略运动想象信号")
                return

            self.logger.info("接收到运动想象检测信号，开始处理")
            self._handle_motor_imagery_trigger()

        except Exception as e:
            self.logger.error(f"处理运动想象检测信号失败: {e}")

    def _handle_motor_imagery_trigger(self):
        """处理运动想象触发"""
        try:
            # 设置电刺激激活标志
            self.is_stimulation_active = True

            # 成功触发次数+1
            self.current_session.successful_triggers += 1
            self._update_progress()

            self.logger.info(f"成功触发运动想象检测 - 第{self.current_session.successful_triggers}次")

            # 停止30秒超时定时器（电刺激期间不需要超时提示）
            # 使用QTimer.singleShot确保在主线程中执行
            if QT_AVAILABLE:
                QTimer.singleShot(0, self._stop_timeout_timer)
            else:
                self._stop_timeout_timer()

            # 发送UDP指令启动VR动画播放
            self.udp_comm.send_command(UDPCommand.START)
            self.logger.debug("已发送UDP START指令启动VR动画")

            # 立即启动电刺激
            if not self._start_stimulation():
                self.logger.warning("启动电刺激失败")

            # 启动刺激持续时长定时器
            self._start_stimulation_timer()

        except Exception as e:
            self.logger.error(f"处理运动想象触发失败: {e}")
            self.is_stimulation_active = False

    def _update_progress(self):
        """更新治疗进度"""
        if not self.current_session:
            return

        progress_data = {
            'total_imagery_count': self.current_session.total_imagery_count,
            'successful_triggers': self.current_session.successful_triggers,
            'is_active': self.is_treatment_active,
            'is_stimulation_active': self.is_stimulation_active,
            'elapsed_time': (datetime.now() - self.current_session.start_time).total_seconds()
        }

        # 调用进度更新回调
        if self.progress_update_callback:
            try:
                self.progress_update_callback(progress_data)
            except Exception as e:
                self.logger.error(f"进度更新回调失败: {e}")

    def _show_error_dialog(self, message: str):
        """显示错误对话框（需要UI实现）"""
        self.logger.error(f"治疗错误: {message}")
        # 这里需要UI层实现具体的对话框显示

    # ==================== 新的定时器管理方法 ====================

    def _stop_all_timers(self):
        """停止所有定时器"""
        try:
            if QT_AVAILABLE:
                # 停止30秒超时定时器
                self._stop_timeout_timer()

                # 停止刺激持续时长定时器
                self._stop_stimulation_timer()

                # 注意：治疗总时长定时器和休息定时器使用QTimer.singleShot，无法直接停止
                # 但会通过状态检查在回调中忽略

            self.logger.info("所有定时器已停止")
        except Exception as e:
            self.logger.error(f"停止定时器失败: {e}")

    def _stop_all_stimulation(self):
        """停止所有电刺激输出"""
        try:
            if self.stimulation_device:
                self.stimulation_device.stop_all_stimulation()
                self.logger.debug("所有电刺激已停止")
        except Exception as e:
            self.logger.error(f"停止电刺激失败: {e}")

    def _reset_stimulation_currents(self):
        """重置AB通道电流为0"""
        try:
            if hasattr(self, 'treatment_ui') and self.treatment_ui:
                # 重置A通道电流
                self.treatment_ui.channel_a_current.setValue(0)
                self.logger.info("A通道电流已重置为0")

                # 重置B通道电流
                self.treatment_ui.channel_b_current.setValue(0)
                self.logger.info("B通道电流已重置为0")

                # 添加日志记录
                if hasattr(self.treatment_ui, 'add_stimulation_log'):
                    self.treatment_ui.add_stimulation_log("治疗结束，AB通道电流已重置为0")

            else:
                self.logger.warning("无法访问治疗界面，跳过电流重置")

        except Exception as e:
            self.logger.error(f"重置电流失败: {e}")

    def _start_timeout_timer(self):
        """启动30秒超时定时器"""
        if not QT_AVAILABLE:
            return

        try:
            # 先停止现有的定时器
            self._stop_timeout_timer()

            # 创建新的定时器
            self.timeout_timer = QTimer()
            self.timeout_timer.setSingleShot(True)
            self.timeout_timer.timeout.connect(self._on_timeout)

            # 启动定时器
            timeout_ms = self.motor_imagery_timeout * 1000  # 转换为毫秒
            self.timeout_timer.start(timeout_ms)

            self.logger.debug(f"30秒超时定时器已启动")
        except Exception as e:
            self.logger.error(f"启动超时定时器失败: {e}")

    def _stop_timeout_timer(self):
        """停止30秒超时定时器"""
        try:
            if self.timeout_timer and self.timeout_timer.isActive():
                self.timeout_timer.stop()
                self.logger.debug("30秒超时定时器已停止")
        except Exception as e:
            self.logger.error(f"停止超时定时器失败: {e}")

    def _reset_timeout_timer(self):
        """重置30秒超时定时器"""
        if not QT_AVAILABLE:
            return

        # 直接启动新的定时器（QTimer.singleShot会自动处理）
        self._start_timeout_timer()
        self.logger.debug("30秒超时定时器已重置")

    def _start_treatment_timer(self):
        """启动治疗总时长定时器"""
        if not QT_AVAILABLE:
            return

        try:
            # 使用QTimer.singleShot在主线程中创建定时器
            timeout_ms = self.treatment_duration_setting * 60 * 1000  # 转换为毫秒
            QTimer.singleShot(timeout_ms, self._on_treatment_complete)

            self.logger.info(f"治疗总时长定时器已启动: {self.treatment_duration_setting}分钟")
        except Exception as e:
            self.logger.error(f"启动治疗定时器失败: {e}")

    def _start_stimulation_timer(self):
        """启动刺激持续时长定时器"""
        if not QT_AVAILABLE:
            return

        try:
            # 先停止现有的定时器
            self._stop_stimulation_timer()

            # 重置停止标志
            self.stimulation_timer_stopped = False

            # 使用QTimer.singleShot在主线程中创建定时器，并保存定时器ID
            timeout_ms = self.stimulation_duration_setting * 1000  # 转换为毫秒
            self.stimulation_timer_id = QTimer.singleShot(timeout_ms, self._on_stimulation_end)

            self.logger.info(f"刺激持续时长定时器已启动: {self.stimulation_duration_setting}秒")
        except Exception as e:
            self.logger.error(f"启动刺激定时器失败: {e}")

    def _stop_stimulation_timer(self):
        """停止刺激持续时长定时器"""
        try:
            # QTimer.singleShot创建的定时器无法直接停止，但我们可以设置标志
            self.stimulation_timer_stopped = True
            self.logger.debug("刺激持续时长定时器已标记停止")
        except Exception as e:
            self.logger.error(f"停止刺激定时器失败: {e}")

    def _start_rest_timer(self):
        """启动休息定时器（5秒）"""
        if not QT_AVAILABLE:
            return

        try:
            # 使用QTimer.singleShot在主线程中创建定时器
            QTimer.singleShot(5000, self._on_rest_complete)  # 5秒

            self.logger.info("休息定时器已启动: 5秒")
        except Exception as e:
            self.logger.error(f"启动休息定时器失败: {e}")

    # ==================== 定时器回调方法 ====================

    def _on_timeout(self):
        """30秒超时处理"""
        try:
            if not self.is_treatment_active:
                self.logger.debug("治疗未激活，忽略30秒超时")
                return

            # 检查是否正在电刺激中（双重保险）
            if self.is_stimulation_active:
                self.logger.debug("正在电刺激中，忽略30秒超时")
                return

            self.logger.info("30秒超时，语音提示继续努力")
            self.voice_engine.speak("继续努力，请想象运动")

            # 想象次数+1
            self.current_session.total_imagery_count += 1
            self._update_progress()

            # 重新启动超时定时器
            self._start_timeout_timer()

        except Exception as e:
            self.logger.error(f"30秒超时处理失败: {e}")

    def _on_treatment_complete(self):
        """治疗总时长到达处理"""
        try:
            self.logger.info("治疗时长到达，自动结束治疗")
            self.stop_treatment()
        except Exception as e:
            self.logger.error(f"治疗完成处理失败: {e}")

    def _on_stimulation_end(self):
        """刺激时长到达处理"""
        try:
            # 检查定时器是否被停止
            if hasattr(self, 'stimulation_timer_stopped') and self.stimulation_timer_stopped:
                self.logger.debug("刺激定时器已被停止，忽略刺激结束事件")
                return

            # 检查治疗是否仍在进行中（防止在停止治疗后仍执行）
            if not self.is_treatment_active:
                self.logger.debug("治疗已停止，忽略刺激结束事件")
                return

            self.logger.info("刺激时长到达，处理刺激结束")

            # 发送UDP指令停止VR动画
            self.udp_comm.send_command(UDPCommand.STOP)
            self.logger.debug("已发送UDP STOP指令停止VR动画")

            # 停止电刺激
            if self.stimulation_device:
                self.stimulation_device.stop_all_stimulation()
                self.logger.debug("已停止电刺激输出")

            # 取消电刺激激活标志
            self.is_stimulation_active = False

            # 再次检查治疗是否仍在进行中（双重保险）
            if not self.is_treatment_active:
                self.logger.debug("治疗已停止，跳过后续语音提示")
                return

            # 根据自适应学习选项执行不同的语音提示
            if self.adaptive_learning_enabled:
                # 自适应学习模式：休息5秒后继续
                self.voice_engine.speak("你做的很棒，休息，请保持放松状态")
                self._start_rest_timer()
            else:
                # 非自适应学习模式：直接继续
                self.voice_engine.speak("你做的很棒，请继续想象运动")
                # 想象次数+1（为下一轮检测做准备）
                self.current_session.total_imagery_count += 1
                self._update_progress()
                # 重新启动30秒超时定时器
                self._start_timeout_timer()

        except Exception as e:
            self.logger.error(f"处理刺激结束失败: {e}")

    def _on_rest_complete(self):
        """休息完成处理"""
        try:
            # 检查治疗是否仍在进行中
            if not self.is_treatment_active:
                self.logger.debug("治疗已停止，忽略休息完成事件")
                return

            self.logger.debug("休息完成，继续治疗")
            self.voice_engine.speak("请继续想象运动")
            # 想象次数+1（为下一轮检测做准备）
            self.current_session.total_imagery_count += 1
            self._update_progress()
            # 重新启动30秒超时定时器
            self._start_timeout_timer()

        except Exception as e:
            self.logger.error(f"休息完成处理失败: {e}")

    # ==================== 状态查询方法 ====================

    def get_current_session(self) -> Optional[TreatmentSession]:
        """获取当前治疗会话"""
        return self.current_session

    def is_active(self) -> bool:
        """检查治疗是否活跃"""
        return self.is_treatment_active

    def is_stimulation_running(self) -> bool:
        """检查电刺激是否正在运行"""
        return self.is_stimulation_active

    # ==================== 电刺激启动方法（保留并简化） ====================

    def _start_stimulation(self) -> bool:
        """
        启动电刺激（参考start_stimulation逻辑，简化不必要的检查）

        Returns:
            bool: 启动是否成功
        """
        try:
            if not self.stimulation_device:
                self.logger.warning("电刺激设备未连接")
                return False

            # 检查设备连接状态
            if not self.stimulation_device.is_connected():
                self.logger.warning("电刺激设备未连接")
                return False

            # 启动电刺激（完全按照手动点击"开始刺激"按钮的逻辑）
            success = False

            # 获取治疗界面的通道勾选状态和电流设置
            try:
                if not hasattr(self, 'treatment_ui') or not self.treatment_ui:
                    self.logger.error("无法获取治疗界面引用，无法启动电刺激")
                    return False

                # 检查是否有通道被选中（按照手动点击逻辑）
                channel_a_checked = self.treatment_ui.channel_a_checkbox.isChecked()
                channel_b_checked = self.treatment_ui.channel_b_checkbox.isChecked()

                if not (channel_a_checked or channel_b_checked):
                    self.logger.warning("没有选中任何通道，无法启动刺激")
                    return False

                # 收集需要启动的通道信息，并检查电流设置（完全按照手动点击逻辑）
                channels_to_start = []
                zero_current_channels = []

                if channel_a_checked:
                    current_value = self.treatment_ui.channel_a_current.value()
                    if current_value > 0:
                        channels_to_start.append((1, current_value, "A"))
                        self.logger.debug(f"A通道已选中，电流: {current_value}mA")
                    else:
                        zero_current_channels.append("A")
                        self.logger.warning(f"A通道已选中但电流为0mA")

                if channel_b_checked:
                    current_value = self.treatment_ui.channel_b_current.value()
                    if current_value > 0:
                        channels_to_start.append((2, current_value, "B"))
                        self.logger.debug(f"B通道已选中，电流: {current_value}mA")
                    else:
                        zero_current_channels.append("B")
                        self.logger.warning(f"B通道已选中但电流为0mA")

                # 检查是否有选中的通道电流为0
                if zero_current_channels:
                    channel_names = "、".join(zero_current_channels)
                    self.logger.warning(f"{channel_names}通道已选中但电流设置为0mA，无法启动刺激")
                    return False

                if not channels_to_start:
                    self.logger.warning("没有可启动的通道（选中且电流>0）")
                    return False

                # 按照手动点击逻辑启动刺激
                self.logger.info(f"准备启动{len(channels_to_start)}个通道的刺激")

                # 检查是否为双通道启动，使用快速启动方法（按照手动点击逻辑）
                if len(channels_to_start) == 2:
                    # 双通道快速启动
                    channel_a_current = 0
                    channel_b_current = 0

                    for channel_num, current_value, channel_name in channels_to_start:
                        if channel_num == 1:
                            channel_a_current = current_value
                        elif channel_num == 2:
                            channel_b_current = current_value

                    self.logger.info(f"使用快速双通道启动模式: A={channel_a_current}mA, B={channel_b_current}mA")
                    success = self.stimulation_device.fast_dual_channel_start(channel_a_current, channel_b_current)

                    if success:
                        self.logger.info(f"双通道快速启动成功: A={channel_a_current}mA, B={channel_b_current}mA")
                    else:
                        self.logger.error("双通道快速启动失败")
                else:
                    # 单通道启动（按照手动点击逻辑）
                    self.logger.info("使用单通道启动模式")
                    current_set_success = []

                    # 设置电流
                    for channel_num, current_value, channel_name in channels_to_start:
                        if self.stimulation_device.set_current(channel_num, current_value):
                            current_set_success.append((channel_num, current_value, channel_name))
                            self.logger.info(f"{channel_name}通道电流设置成功: {current_value}mA")
                        else:
                            self.logger.error(f"{channel_name}通道电流设置失败")

                    if not current_set_success:
                        self.logger.error("所有通道电流设置失败")
                        return False

                    # 启动通道（按照手动点击逻辑）
                    for channel_num, current_value, channel_name in current_set_success:
                        # 直接执行SwitchChannelState指令启动刺激
                        result = self.stimulation_device._safe_dll_call('SwitchChannelState', channel_num, 3)  # 3: 正常工作
                        if result == 0:
                            success = True
                            self.logger.info(f"{channel_name}通道启动成功")
                        else:
                            self.logger.error(f"{channel_name}通道启动失败，错误码: {result}")

            except Exception as e:
                self.logger.error(f"启动电刺激时发生异常: {e}")
                return False

            if success:
                self.logger.info("电刺激已启动")
            else:
                self.logger.warning("电刺激启动失败")

            return success

        except Exception as e:
            self.logger.error(f"启动电刺激失败: {e}")
            return False

    # ==================== 辅助方法 ====================

    def _update_progress(self):
        """更新治疗进度"""
        try:
            if not self.current_session:
                return

            progress_data = {
                'total_imagery_count': self.current_session.total_imagery_count,
                'successful_triggers': self.current_session.successful_triggers,
                'treatment_duration': self.current_session.treatment_duration_minutes,
                'treatment_score': self.current_session.treatment_score,
                'is_active': self.is_treatment_active,
                'is_stimulation_active': self.is_stimulation_active
            }

            # 发送信号（如果QT可用）
            if QT_AVAILABLE and hasattr(self, 'progress_updated'):
                self.progress_updated.emit(progress_data)

            # 调用回调函数
            if self.progress_update_callback:
                self.progress_update_callback(progress_data)

        except Exception as e:
            self.logger.error(f"更新治疗进度失败: {e}")

    # ==================== 数据处理方法（保留） ====================

    def _process_treatment_completion(self):
        """处理治疗完成后的数据处理和上传"""
        try:
            if not self.current_session:
                return

            # 计算治疗时间
            self.current_session.end_time = datetime.now()
            duration_seconds = (self.current_session.end_time - self.current_session.start_time).total_seconds()

            # 参考原QT项目：加0.6向下取整
            duration_minutes = int((duration_seconds / 60) + 0.6)
            self.current_session.treatment_duration_minutes = duration_minutes
            self.logger.info(f"治疗时长计算: {duration_seconds}秒 = {duration_minutes}分钟")

            # 检查是否达到最小治疗时长（从系统设置获取）
            min_duration = self._get_min_treatment_duration_from_settings()
            if duration_minutes >= min_duration:
                # 计算治疗得分（参考原QT项目公式）
                if self.current_session.total_imagery_count > 0:
                    score = round((self.current_session.successful_triggers / self.current_session.total_imagery_count) * 100)
                else:
                    score = 0

                self.current_session.treatment_score = score

                # 计算治疗评价（参考原QT项目标准）
                if score >= 80:
                    evaluation = "优"
                elif score >= 60:
                    evaluation = "良"
                elif score >= 45:
                    evaluation = "中"
                else:
                    evaluation = "差"

                self.current_session.treatment_evaluation = evaluation

                # 注意：数据保存和上传由原来的UI层负责，这里不重复处理
                self.logger.info("治疗数据将由UI层负责保存和上传")

            else:
                self.logger.info(f"治疗时长{duration_minutes}分钟小于最小时长{min_duration}分钟，不保存数据")

            # 调用治疗完成回调
            if self.treatment_complete_callback:
                self.logger.info(f"调用治疗完成回调函数 - 患者: {self.current_session.patient_id}")
                try:
                    self.treatment_complete_callback(self.current_session)
                    self.logger.info("治疗完成回调函数调用成功")
                except Exception as callback_error:
                    self.logger.error(f"治疗完成回调函数调用失败: {callback_error}")
            else:
                self.logger.warning("治疗完成回调函数未设置")

        except Exception as e:
            self.logger.error(f"处理治疗完成数据失败: {e}")



    def _get_min_treatment_duration_from_settings(self) -> int:
        """从系统设置获取治疗数据最小时长"""
        try:
            # 从配置文件获取治疗数据最小时长
            from utils.app_config import AppConfig
            min_duration = AppConfig.DATABASE_CONFIG.get('min_treatment_duration', 3)
            self.logger.debug(f"从配置获取最小治疗时长: {min_duration}分钟")
            return min_duration

        except Exception as e:
            self.logger.error(f"获取最小治疗时长设置失败: {e}")
            return 3  # 默认3分钟

    def _stop_all_stimulation(self):
        """停止所有电刺激输出（等同于点击"停止刺激"按钮）"""
        try:
            if self.stimulation_device and self.stimulation_device.is_connected():
                # 调用电刺激设备的停止方法
                self.stimulation_device.stop_all_stimulation()
                self.logger.info("所有电刺激输出已停止")
            else:
                self.logger.warning("电刺激设备未连接，无法停止刺激")

        except Exception as e:
            self.logger.error(f"停止电刺激失败: {e}")

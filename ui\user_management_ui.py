#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户管理界面模块
User Management UI Module

作者: AI Assistant
版本: 1.0.0
"""

import logging
from typing import Optional, List, Dict, Any

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                               QLabel, QLineEdit, QPushButton, QTableWidget,
                               QTableWidgetItem, QComboBox, QGroupBox,
                               QMessageBox, QHeaderView, QAbstractItemView,
                               QCheckBox, QDateTimeEdit, QTextEdit, QSplitter)
from PySide6.QtCore import Qt, QDateTime, Signal
from PySide6.QtGui import QFont

from core.auth_manager import AuthManager, UserRole, Permission, permission_required
from core.database_manager import DatabaseManager


class UserManagementWidget(QWidget):
    """用户管理界面"""

    # 信号定义
    user_created = Signal(str)  # 用户创建信号
    user_updated = Signal(str)  # 用户更新信号
    user_deleted = Signal(str)  # 用户删除信号

    def __init__(self, parent=None):
        super().__init__(parent)

        self.auth_manager: Optional[AuthManager] = None
        self.db_manager: Optional[DatabaseManager] = None
        self.logger = logging.getLogger(__name__)

        # 当前选中的用户
        self.current_user_id = None

        # 初始化界面
        self.init_ui()
        self.setup_connections()

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("用户管理")

        # 主布局
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)

        # 左侧：用户列表
        self.create_user_list_widget()
        splitter.addWidget(self.user_list_widget)

        # 右侧：用户详情和操作
        self.create_user_details_widget()
        splitter.addWidget(self.user_details_widget)

        # 设置分割器比例
        splitter.setStretchFactor(0, 1)
        splitter.setStretchFactor(1, 1)
        splitter.setSizes([400, 600])

    def create_user_list_widget(self):
        """创建用户列表部件"""
        self.user_list_widget = QGroupBox("用户列表")
        layout = QVBoxLayout(self.user_list_widget)

        # 搜索框
        search_layout = QHBoxLayout()
        search_label = QLabel("搜索:")
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("输入用户名搜索...")
        search_button = QPushButton("搜索")

        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_edit)
        search_layout.addWidget(search_button)
        layout.addLayout(search_layout)

        # 用户表格
        self.user_table = QTableWidget()
        self.user_table.setColumnCount(5)
        self.user_table.setHorizontalHeaderLabels([
            "ID", "用户名", "角色", "状态", "最后登录"
        ])

        # 设置表格属性
        self.user_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.user_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.user_table.setAlternatingRowColors(True)

        # 设置列宽
        header = self.user_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # ID
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # 用户名
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # 角色
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # 状态

        layout.addWidget(self.user_table)

        # 操作按钮
        button_layout = QHBoxLayout()
        self.refresh_button = QPushButton("刷新")
        self.add_user_button = QPushButton("添加用户")
        self.delete_user_button = QPushButton("停用用户")
        self.activate_user_button = QPushButton("激活用户")
        self.delete_user_button.setEnabled(False)
        self.activate_user_button.setEnabled(False)

        button_layout.addWidget(self.refresh_button)
        button_layout.addWidget(self.add_user_button)
        button_layout.addWidget(self.delete_user_button)
        button_layout.addWidget(self.activate_user_button)
        button_layout.addStretch()

        layout.addLayout(button_layout)

    def create_user_details_widget(self):
        """创建用户详情部件"""
        self.user_details_widget = QGroupBox("用户详情")
        layout = QVBoxLayout(self.user_details_widget)

        # 基本信息
        basic_group = QGroupBox("基本信息")
        basic_layout = QGridLayout(basic_group)

        # 用户名
        basic_layout.addWidget(QLabel("用户名:"), 0, 0)
        self.username_edit = QLineEdit()
        basic_layout.addWidget(self.username_edit, 0, 1)

        # 角色
        basic_layout.addWidget(QLabel("角色:"), 1, 0)
        self.role_combo = QComboBox()
        for role in UserRole:
            self.role_combo.addItem(role.value, role)
        basic_layout.addWidget(self.role_combo, 1, 1)

        # 状态
        basic_layout.addWidget(QLabel("状态:"), 2, 0)
        self.active_checkbox = QCheckBox("启用")
        self.active_checkbox.setChecked(True)
        basic_layout.addWidget(self.active_checkbox, 2, 1)

        # 创建时间
        basic_layout.addWidget(QLabel("创建时间:"), 3, 0)
        self.created_time_label = QLabel("--")
        basic_layout.addWidget(self.created_time_label, 3, 1)

        # 最后登录
        basic_layout.addWidget(QLabel("最后登录:"), 4, 0)
        self.last_login_label = QLabel("--")
        basic_layout.addWidget(self.last_login_label, 4, 1)

        layout.addWidget(basic_group)

        # 密码设置
        password_group = QGroupBox("密码设置")
        password_layout = QGridLayout(password_group)

        # 新密码
        password_layout.addWidget(QLabel("新密码:"), 0, 0)
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setPlaceholderText("留空表示不修改密码")
        password_layout.addWidget(self.password_edit, 0, 1)

        # 确认密码
        password_layout.addWidget(QLabel("确认密码:"), 1, 0)
        self.confirm_password_edit = QLineEdit()
        self.confirm_password_edit.setEchoMode(QLineEdit.Password)
        password_layout.addWidget(self.confirm_password_edit, 1, 1)

        layout.addWidget(password_group)

        # 权限设置
        permissions_group = QGroupBox("权限设置")
        permissions_layout = QVBoxLayout(permissions_group)

        # 权限说明
        permissions_info = QLabel("权限将根据角色自动分配")
        permissions_info.setStyleSheet("color: #7f8c8d; font-style: italic;")
        permissions_layout.addWidget(permissions_info)

        # 权限列表（只读显示）
        self.permissions_text = QTextEdit()
        self.permissions_text.setReadOnly(True)
        self.permissions_text.setMaximumHeight(100)
        permissions_layout.addWidget(self.permissions_text)

        layout.addWidget(permissions_group)

        # 操作按钮
        button_layout = QHBoxLayout()
        self.save_button = QPushButton("保存")
        self.cancel_button = QPushButton("取消")
        self.reset_password_button = QPushButton("重置密码")

        button_layout.addWidget(self.save_button)
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.reset_password_button)
        button_layout.addStretch()

        layout.addLayout(button_layout)

        # 初始状态禁用编辑
        self.set_edit_mode(False)

    def setup_connections(self):
        """设置信号连接"""
        # 表格选择变化
        self.user_table.itemSelectionChanged.connect(self.on_user_selection_changed)

        # 搜索
        self.search_edit.textChanged.connect(self.filter_users)

        # 按钮点击
        self.refresh_button.clicked.connect(self.refresh_user_list)
        self.add_user_button.clicked.connect(self.add_new_user)
        self.delete_user_button.clicked.connect(self.delete_selected_user)
        self.activate_user_button.clicked.connect(self.activate_selected_user)

        self.save_button.clicked.connect(self.save_user)
        self.cancel_button.clicked.connect(self.cancel_edit)
        self.reset_password_button.clicked.connect(self.reset_user_password)

        # 角色变化时更新权限显示
        self.role_combo.currentTextChanged.connect(self.update_permissions_display)

    def set_auth_manager(self, auth_manager: AuthManager):
        """设置权限管理器"""
        self.auth_manager = auth_manager
        self.update_ui_permissions()
        # 权限管理器设置后刷新用户列表（不检查权限，因为此时用户可能还没登录）
        self._refresh_user_list_internal()

    def set_database_manager(self, db_manager: DatabaseManager):
        """设置数据库管理器"""
        self.db_manager = db_manager

    def update_ui_permissions(self):
        """更新UI权限状态"""
        try:
            if not self.auth_manager:
                # 如果没有权限管理器，禁用所有按钮
                self.add_user_button.setEnabled(False)
                self.delete_user_button.setEnabled(False)
                self.save_button.setEnabled(False)
                self.reset_password_button.setEnabled(False)
                self.user_details_widget.setEnabled(False)
                self.user_details_widget.setTitle("用户详情 (权限管理器未初始化)")
                return

            # 检查是否已登录
            if not self.auth_manager.is_logged_in():
                # 未登录时禁用所有按钮
                self.add_user_button.setEnabled(False)
                self.delete_user_button.setEnabled(False)
                self.save_button.setEnabled(False)
                self.reset_password_button.setEnabled(False)
                self.user_details_widget.setEnabled(False)
                self.user_details_widget.setTitle("用户详情 (未登录)")
                return

            # 检查用户管理权限，确保返回布尔值
            try:
                has_user_manage = self.auth_manager.has_permission(Permission.USER_MANAGE)
                # 安全检查：确保返回值是布尔类型
                if has_user_manage is None:
                    has_user_manage = False
                has_user_manage = bool(has_user_manage)
            except Exception as e:
                self.logger.error(f"检查用户管理权限失败: {e}")
                has_user_manage = False

            # 检查是否是当前登录用户
            try:
                current_user = self.auth_manager.get_current_user()
                is_current_user = (current_user and
                                  self.current_user_id is not None and
                                  current_user['id'] == self.current_user_id)
                # 安全检查：确保返回值是布尔类型
                if is_current_user is None:
                    is_current_user = False
                is_current_user = bool(is_current_user)
            except Exception as e:
                self.logger.error(f"检查当前用户状态失败: {e}")
                is_current_user = False

            # 更新按钮状态，确保传入布尔值
            self.add_user_button.setEnabled(bool(has_user_manage))
            self.delete_user_button.setEnabled(bool(has_user_manage and self.current_user_id is not None))

            # 保存按钮：有用户管理权限 或者 是当前用户修改自己的信息
            self.save_button.setEnabled(bool(has_user_manage or is_current_user))

            self.reset_password_button.setEnabled(bool(has_user_manage and self.current_user_id is not None))

            # 更新激活用户按钮状态
            if hasattr(self, 'activate_user_button'):
                self.activate_user_button.setEnabled(bool(has_user_manage and self.current_user_id is not None))

        except Exception as e:
            self.logger.error(f"更新UI权限状态失败: {e}")
            # 出错时禁用所有按钮
            self.add_user_button.setEnabled(False)
            self.delete_user_button.setEnabled(False)
            self.save_button.setEnabled(False)
            self.reset_password_button.setEnabled(False)
            if hasattr(self, 'activate_user_button'):
                self.activate_user_button.setEnabled(False)
            self.user_details_widget.setEnabled(False)
            self.user_details_widget.setTitle("用户详情 (需要用户管理权限)")
        else:
            # 有用户管理权限 或者 是当前用户，则启用详情区域
            self.user_details_widget.setEnabled(True)
            if is_current_user and not has_user_manage:
                self.user_details_widget.setTitle("用户详情 (个人信息)")
            else:
                self.user_details_widget.setTitle("用户详情")

    def _refresh_user_list_internal(self):
        """内部刷新用户列表方法（不检查权限）"""
        if not self.auth_manager:
            return

        try:
            # 获取所有用户（包括停用的）
            users = self.auth_manager.get_all_users_without_permission_check()

            self.user_table.setRowCount(len(users))

            for row, user in enumerate(users):
                # ID
                self.user_table.setItem(row, 0, QTableWidgetItem(str(user['id'])))

                # 用户名
                self.user_table.setItem(row, 1, QTableWidgetItem(user['name']))

                # 角色
                self.user_table.setItem(row, 2, QTableWidgetItem(user['role']))

                # 状态
                status = "启用" if user['is_active'] else "禁用"
                self.user_table.setItem(row, 3, QTableWidgetItem(status))

                # 最后登录
                last_login = user.get('last_login', '--')
                if last_login and last_login != '--':
                    # 格式化时间显示
                    try:
                        from datetime import datetime
                        dt = datetime.fromisoformat(last_login.replace('Z', '+00:00'))
                        last_login = dt.strftime('%Y-%m-%d %H:%M')
                    except:
                        pass
                self.user_table.setItem(row, 4, QTableWidgetItem(str(last_login)))

            self.logger.info(f"用户列表已刷新，共 {len(users)} 个用户")

        except Exception as e:
            self.logger.error(f"刷新用户列表失败: {e}")
            QMessageBox.critical(self, "错误", f"刷新用户列表失败: {e}")

    @permission_required(Permission.USER_MANAGE)
    def refresh_user_list(self):
        """刷新用户列表（需要权限）"""
        self._refresh_user_list_internal()

    def filter_users(self):
        """过滤用户列表"""
        search_text = self.search_edit.text().lower()

        for row in range(self.user_table.rowCount()):
            username_item = self.user_table.item(row, 1)
            if username_item:
                username = username_item.text().lower()
                self.user_table.setRowHidden(row, search_text not in username)

    def on_user_selection_changed(self):
        """用户选择变化处理"""
        selected_items = self.user_table.selectedItems()
        if selected_items:
            row = selected_items[0].row()
            user_id_item = self.user_table.item(row, 0)
            status_item = self.user_table.item(row, 3)
            if user_id_item:
                self.current_user_id = int(user_id_item.text())
                self.load_user_details(self.current_user_id)

                # 更新UI权限状态
                self.update_ui_permissions()

                # 检查权限，确保返回布尔值
                try:
                    has_user_manage = self.auth_manager and self.auth_manager.has_permission(Permission.USER_MANAGE)
                    has_user_manage = bool(has_user_manage) if has_user_manage is not None else False
                except Exception as e:
                    self.logger.error(f"检查用户管理权限失败: {e}")
                    has_user_manage = False

                if has_user_manage:
                    # 检查是否是当前登录用户
                    try:
                        current_user = self.auth_manager.get_current_user()
                        is_current_user = (current_user and
                                          self.current_user_id is not None and
                                          current_user['id'] == self.current_user_id)
                        is_current_user = bool(is_current_user) if is_current_user is not None else False
                    except Exception as e:
                        self.logger.error(f"检查当前用户状态失败: {e}")
                        is_current_user = False

                    # 根据用户状态启用/禁用按钮
                    is_active = status_item.text() == "启用" if status_item else True
                    # 当前登录用户不能被停用
                    self.delete_user_button.setEnabled(bool(is_active and not is_current_user))
                    self.activate_user_button.setEnabled(bool(not is_active))
                    self.reset_password_button.setEnabled(bool(is_active))
                else:
                    # 没有权限时禁用所有操作按钮
                    self.delete_user_button.setEnabled(False)
                    self.activate_user_button.setEnabled(False)
                    self.reset_password_button.setEnabled(False)
        else:
            self.current_user_id = None
            self.clear_user_details()
            self.delete_user_button.setEnabled(False)
            self.activate_user_button.setEnabled(False)
            self.reset_password_button.setEnabled(False)
            # 更新UI权限状态
            self.update_ui_permissions()

    def load_user_details(self, user_id: int):
        """加载用户详情"""
        # 这里应该从数据库加载用户详情
        # 暂时使用表格中的信息
        try:
            for row in range(self.user_table.rowCount()):
                id_item = self.user_table.item(row, 0)
                if id_item and int(id_item.text()) == user_id:
                    # 加载基本信息
                    username = self.user_table.item(row, 1).text()
                    role = self.user_table.item(row, 2).text()
                    status = self.user_table.item(row, 3).text()
                    last_login = self.user_table.item(row, 4).text()

                    self.username_edit.setText(username)

                    # 设置角色
                    for i in range(self.role_combo.count()):
                        if self.role_combo.itemText(i) == role:
                            self.role_combo.setCurrentIndex(i)
                            break

                    self.active_checkbox.setChecked(status == "启用")
                    self.last_login_label.setText(last_login)

                    # 更新权限显示
                    self.update_permissions_display()

                    # 设置编辑模式
                    self.set_edit_mode(True)

                    # 检查是否是当前登录用户
                    try:
                        current_user = self.auth_manager.get_current_user() if self.auth_manager else None
                        is_current_user = (current_user and
                                          user_id is not None and
                                          current_user['id'] == user_id)
                        is_current_user = bool(is_current_user) if is_current_user is not None else False
                    except Exception as e:
                        self.logger.error(f"检查当前用户状态失败: {e}")
                        is_current_user = False

                    try:
                        has_user_manage = self.auth_manager.has_permission(Permission.USER_MANAGE) if self.auth_manager else False
                        has_user_manage = bool(has_user_manage) if has_user_manage is not None else False
                    except Exception as e:
                        self.logger.error(f"检查用户管理权限失败: {e}")
                        has_user_manage = False

                    if is_current_user:
                        # 如果是当前登录用户，根据权限设置控件状态
                        if has_user_manage:
                            # 有用户管理权限的当前用户可以修改所有信息
                            pass  # 保持所有控件启用
                        else:
                            # 没有用户管理权限的当前用户只能修改密码
                            self.username_edit.setEnabled(False)
                            self.role_combo.setEnabled(False)
                            self.active_checkbox.setEnabled(False)
                            # 密码可以修改
                            self.password_edit.setEnabled(True)
                            self.confirm_password_edit.setEnabled(True)
                            # 保存和取消按钮仍然可用
                            self.save_button.setEnabled(True)
                            self.cancel_button.setEnabled(True)
                    elif not has_user_manage:
                        # 不是当前用户且没有用户管理权限，禁用所有编辑
                        self.set_edit_mode(False)

                    break

        except Exception as e:
            self.logger.error(f"加载用户详情失败: {e}")

    def clear_user_details(self):
        """清空用户详情"""
        self.username_edit.clear()
        self.password_edit.clear()
        self.confirm_password_edit.clear()
        self.role_combo.setCurrentIndex(0)
        self.active_checkbox.setChecked(True)
        self.created_time_label.setText("--")
        self.last_login_label.setText("--")
        self.permissions_text.clear()

        self.set_edit_mode(False)

    def set_edit_mode(self, enabled: bool):
        """设置编辑模式"""
        self.username_edit.setEnabled(enabled)
        self.role_combo.setEnabled(enabled)
        self.active_checkbox.setEnabled(enabled)
        self.password_edit.setEnabled(enabled)
        self.confirm_password_edit.setEnabled(enabled)
        self.save_button.setEnabled(enabled)
        self.cancel_button.setEnabled(enabled)

    def update_permissions_display(self):
        """更新权限显示"""
        if not self.auth_manager:
            return

        try:
            role_text = self.role_combo.currentText()
            role = UserRole(role_text)

            permissions = self.auth_manager.role_permissions.get(role, [])

            permission_texts = []
            for perm in permissions:
                if perm == Permission.ALL:
                    permission_texts.append("所有权限")
                else:
                    permission_texts.append(perm.value)

            self.permissions_text.setText("\n".join(permission_texts))

            # 检查是否是当前登录用户
            current_user = self.auth_manager.get_current_user()
            is_current_user = (current_user and
                              self.current_user_id is not None and
                              current_user['id'] == self.current_user_id)

            # 如果是当前登录用户，权限显示为只读状态
            if is_current_user:
                self.permissions_text.setReadOnly(True)
                self.permissions_text.setStyleSheet("background-color: #f0f0f0;")  # 设置只读背景色
            else:
                # 权限文本始终是只读的，但这里设置样式表
                self.permissions_text.setStyleSheet("")

        except Exception as e:
            self.logger.error(f"更新权限显示失败: {e}")

    @permission_required(Permission.USER_MANAGE)
    def add_new_user(self):
        """添加新用户"""
        self.current_user_id = None
        self.clear_user_details()
        self.set_edit_mode(True)
        self.username_edit.setFocus()

    def save_user(self):
        """保存用户"""
        if not self.auth_manager:
            return

        try:
            # 验证输入
            username = self.username_edit.text().strip()
            if not username:
                QMessageBox.warning(self, "警告", "请输入用户名")
                return

            password = self.password_edit.text()
            confirm_password = self.confirm_password_edit.text()

            # 检查是否是当前登录用户
            current_user = self.auth_manager.get_current_user()
            is_current_user = (current_user and
                              self.current_user_id is not None and
                              current_user['id'] == self.current_user_id)

            # 权限检查
            has_user_manage = self.auth_manager.has_permission(Permission.USER_MANAGE)

            if self.current_user_id is None:  # 新用户
                # 创建新用户需要用户管理权限
                if not has_user_manage:
                    QMessageBox.warning(self, "权限不足", "您没有创建新用户的权限")
                    return

                if not password:
                    QMessageBox.warning(self, "警告", "请输入密码")
                    return

                if password != confirm_password:
                    QMessageBox.warning(self, "警告", "两次输入的密码不一致")
                    return

                # 创建新用户
                role = self.role_combo.currentData()
                success = self.auth_manager.create_user(username, password, role)

                if success:
                    QMessageBox.information(self, "成功", "用户创建成功")
                    self.user_created.emit(username)
                    self._refresh_user_list_internal()
                    self.clear_user_details()
                else:
                    QMessageBox.critical(self, "错误", "用户创建失败")

            else:  # 更新用户
                if is_current_user:
                    # 当前登录用户只能修改密码
                    if password:
                        if password != confirm_password:
                            QMessageBox.warning(self, "警告", "两次输入的密码不一致")
                            return

                        password_success = self.auth_manager.reset_user_password(
                            self.current_user_id,
                            password
                        )

                        if password_success:
                            QMessageBox.information(self, "成功", "密码更新成功")
                            self._refresh_user_list_internal()
                            self.clear_user_details()
                        else:
                            QMessageBox.critical(self, "错误", "密码更新失败")
                    else:
                        QMessageBox.information(self, "提示", "未进行任何修改")
                else:
                    # 更新其他用户的信息需要用户管理权限
                    if not has_user_manage:
                        QMessageBox.warning(self, "权限不足", "您没有修改其他用户信息的权限")
                        return

                    role = self.role_combo.currentData()
                    is_active = self.active_checkbox.isChecked()

                    # 更新用户基本信息
                    success = self.auth_manager.update_user(
                        self.current_user_id,
                        username,
                        role,
                        is_active
                    )

                    if success:
                        # 如果有新密码，则更新密码
                        if password:
                            if password != confirm_password:
                                QMessageBox.warning(self, "警告", "两次输入的密码不一致")
                                return

                            password_success = self.auth_manager.reset_user_password(
                                self.current_user_id,
                                password
                            )

                            if not password_success:
                                QMessageBox.warning(self, "警告", "用户信息更新成功，但密码更新失败")

                        QMessageBox.information(self, "成功", "用户信息更新成功")
                        self._refresh_user_list_internal()
                        self.clear_user_details()
                    else:
                        QMessageBox.critical(self, "错误", "用户信息更新失败")

        except Exception as e:
            self.logger.error(f"保存用户失败: {e}")
            QMessageBox.critical(self, "错误", f"保存用户失败: {e}")

    @permission_required(Permission.USER_MANAGE)
    def cancel_edit(self):
        """取消编辑"""
        if self.current_user_id:
            self.load_user_details(self.current_user_id)
        else:
            self.clear_user_details()

    @permission_required(Permission.USER_MANAGE)
    def delete_selected_user(self):
        """删除选中的用户"""
        if not self.current_user_id:
            return

        username = self.username_edit.text()

        # 安全检查：不能删除当前登录用户
        current_user = self.auth_manager.get_current_user()
        if current_user and current_user['id'] == self.current_user_id:
            QMessageBox.warning(
                self, "操作被拒绝",
                "不能删除当前登录的用户！\n请使用其他管理员账户来删除此用户。"
            )
            return

        # 安全检查：不能删除最后一个管理员
        if self.is_last_admin(self.current_user_id):
            QMessageBox.warning(
                self, "操作被拒绝",
                "不能删除最后一个管理员用户！\n系统必须至少保留一个管理员账户。"
            )
            return

        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要停用用户 '{username}' 吗？\n此操作可以通过重新激活来撤销。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                success = self.auth_manager.deactivate_user(self.current_user_id)
                if success:
                    QMessageBox.information(self, "成功", "用户已停用")
                    self.user_deleted.emit(username)
                    self._refresh_user_list_internal()
                    self.clear_user_details()
                else:
                    QMessageBox.critical(self, "错误", "用户停用失败")
            except Exception as e:
                self.logger.error(f"删除用户失败: {e}")
                QMessageBox.critical(self, "错误", f"删除用户失败: {e}")

    def is_last_admin(self, user_id: int) -> bool:
        """检查是否是最后一个管理员"""
        try:
            # 获取所有活跃的管理员用户
            active_admins = self.db_manager.execute_query(
                "SELECT COUNT(*) as count FROM operator WHERE role = 'admin' AND is_active = 1"
            )

            if not active_admins:
                return True

            admin_count = active_admins[0]['count']

            # 检查要删除的用户是否是管理员
            user_info = self.db_manager.execute_query(
                "SELECT role FROM operator WHERE id = ?",
                (user_id,)
            )

            if user_info and user_info[0]['role'] == 'admin':
                # 如果只有一个管理员且要删除的就是管理员，则不允许删除
                return admin_count <= 1

            return False

        except Exception as e:
            self.logger.error(f"检查最后管理员失败: {e}")
            return True  # 出错时保守处理，不允许删除

    @permission_required(Permission.USER_MANAGE)
    def activate_selected_user(self):
        """激活选中的用户"""
        if not self.current_user_id:
            return

        username = self.username_edit.text()

        reply = QMessageBox.question(
            self, "确认激活",
            f"确定要激活用户 '{username}' 吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                success = self.auth_manager.activate_user(self.current_user_id)
                if success:
                    QMessageBox.information(self, "成功", "用户已激活")
                    self._refresh_user_list_internal()
                    self.clear_user_details()
                else:
                    QMessageBox.critical(self, "错误", "用户激活失败")
            except Exception as e:
                self.logger.error(f"激活用户失败: {e}")
                QMessageBox.critical(self, "错误", f"激活用户失败: {e}")

    @permission_required(Permission.USER_MANAGE)
    def reset_user_password(self):
        """重置用户密码"""
        if not self.current_user_id:
            return

        username = self.username_edit.text()

        reply = QMessageBox.question(
            self, "确认重置",
            f"确定要重置用户 '{username}' 的密码吗？\n新密码将设置为默认密码 'admin123'。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # 重置为默认密码
                default_password = "admin123"
                success = self.auth_manager.reset_user_password(self.current_user_id, default_password)

                if success:
                    QMessageBox.information(
                        self, "成功",
                        f"用户 '{username}' 的密码已重置为默认密码 'admin123'。\n"
                        "请提醒用户尽快修改密码。"
                    )
                    self.logger.info(f"管理员重置用户密码成功 - {username}")
                else:
                    QMessageBox.critical(self, "错误", "密码重置失败！")

            except Exception as e:
                self.logger.error(f"重置用户密码失败: {e}")
                QMessageBox.critical(self, "错误", f"重置用户密码失败: {e}")

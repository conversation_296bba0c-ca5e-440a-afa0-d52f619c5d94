2025-05-26 09:34:42 - root - INFO - 日志系统初始化完成
2025-05-26 09:34:42 - system - INFO - 系统事件[系统启动] 详情: 快速启动演示开始
2025-05-26 09:34:42 - operation - INFO - 用户[demo_user] 执行操作[演示操作] 详情: 这是一个演示操作
2025-05-26 09:34:42 - data_processing - INFO - 数据处理[信号处理] 状态[成功] 详情: 处理了100个数据点
2025-05-26 09:34:42 - device - INFO - 设备[脑电设备] 状态[模拟连接] 详情: 演示模式
2025-05-26 09:34:42 - performance - INFO - 性能监控[数据处理] 耗时[0.123s] 详情: 演示性能记录
2025-05-26 09:34:42 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 09:34:42 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 09:34:42 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 09:34:42 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 09:38:47 - root - INFO - 日志系统初始化完成
2025-05-26 09:38:47 - system - INFO - 系统事件[测试事件] 详情: 这是一个测试事件
2025-05-26 09:38:47 - operation - INFO - 用户[test_user] 执行操作[测试操作] 详情: 这是一个测试操作
2025-05-26 09:39:09 - root - INFO - 日志系统初始化完成
2025-05-26 09:39:09 - system - INFO - 系统事件[系统启动] 详情: 快速启动演示开始
2025-05-26 09:39:09 - operation - INFO - 用户[demo_user] 执行操作[演示操作] 详情: 这是一个演示操作
2025-05-26 09:39:09 - data_processing - INFO - 数据处理[信号处理] 状态[成功] 详情: 处理了100个数据点
2025-05-26 09:39:09 - device - INFO - 设备[脑电设备] 状态[模拟连接] 详情: 演示模式
2025-05-26 09:39:09 - performance - INFO - 性能监控[数据处理] 耗时[0.123s] 详情: 演示性能记录
2025-05-26 09:39:09 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 09:39:09 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 09:39:09 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 09:39:09 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 10:07:25 - root - INFO - 日志系统初始化完成
2025-05-26 10:07:25 - system - INFO - 系统事件[测试事件] 详情: 这是一个测试事件
2025-05-26 10:07:25 - operation - INFO - 用户[test_user] 执行操作[测试操作] 详情: 这是一个测试操作
2025-05-26 10:07:25 - data_processing - INFO - 数据处理[信号处理] 状态[成功] 详情: 处理了100个数据点
2025-05-26 10:07:25 - device - INFO - 设备[脑电设备] 状态[连接] 详情: 设备正常
2025-05-26 10:07:25 - performance - INFO - 性能监控[数据处理] 耗时[0.123s] 详情: 性能测试
2025-05-26 10:07:25 - root - INFO - 日志系统初始化完成
2025-05-26 10:07:25 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 10:07:25 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 10:07:25 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 10:07:25 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 10:07:25 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 10:07:25 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 10:07:25 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 10:07:25 - core.database_manager - ERROR - 数据库操作异常: FOREIGN KEY constraint failed
2025-05-26 10:07:25 - core.database_manager - ERROR - 执行非查询失败: 
            INSERT INTO Edata (ebianhao, ename, channel_data, theta, alpha,
                             low_beta, high_beta, gamma, state)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        , 错误: FOREIGN KEY constraint failed
2025-05-26 10:07:25 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 10:07:25 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 10:07:25 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 10:07:25 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 10:07:25 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 10:07:25 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 10:07:25 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 10:07:25 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 10:07:25 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 10:07:25 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 10:07:25 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 10:07:25 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 10:07:25 - root - INFO - 日志系统初始化完成
2025-05-26 10:07:25 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 10:07:25 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 10:07:25 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 10:07:25 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 10:13:16 - root - INFO - 日志系统初始化完成
2025-05-26 10:13:16 - system - INFO - 系统事件[测试事件] 详情: 这是一个测试事件
2025-05-26 10:13:16 - operation - INFO - 用户[test_user] 执行操作[测试操作] 详情: 这是一个测试操作
2025-05-26 10:13:16 - data_processing - INFO - 数据处理[信号处理] 状态[成功] 详情: 处理了100个数据点
2025-05-26 10:13:16 - device - INFO - 设备[脑电设备] 状态[连接] 详情: 设备正常
2025-05-26 10:13:16 - performance - INFO - 性能监控[数据处理] 耗时[0.123s] 详情: 性能测试
2025-05-26 10:17:25 - root - INFO - 日志系统初始化完成
2025-05-26 10:17:25 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 10:17:25 - root - INFO - 系统版本: 1.0.0
2025-05-26 10:17:25 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 10:17:25 - root - INFO - 样式表加载成功
2025-05-26 10:17:25 - root - INFO - 应用程序基本设置完成
2025-05-26 10:17:25 - root - WARNING - 启动画面图片不存在
2025-05-26 10:17:25 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 10:17:25 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 10:17:25 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 10:17:25 - root - INFO - 数据库初始化成功
2025-05-26 10:17:25 - core.main_window - ERROR - 创建内容部件失败: name 'QStackedWidget' is not defined
2025-05-26 10:17:25 - core.main_window - ERROR - 创建中央部件失败: name 'QStackedWidget' is not defined
2025-05-26 10:17:25 - core.main_window - ERROR - 主窗口UI初始化失败: name 'QStackedWidget' is not defined
2025-05-26 10:17:25 - root - ERROR - 主窗口创建失败: name 'QStackedWidget' is not defined
2025-05-26 10:17:33 - root - INFO - 开始清理应用程序资源
2025-05-26 10:17:33 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 10:17:33 - root - INFO - 应用程序资源清理完成
2025-05-26 10:17:33 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 10:17:53 - root - INFO - 日志系统初始化完成
2025-05-26 10:17:53 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 10:17:53 - root - INFO - 系统版本: 1.0.0
2025-05-26 10:17:53 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 10:17:53 - root - INFO - 样式表加载成功
2025-05-26 10:17:53 - root - INFO - 应用程序基本设置完成
2025-05-26 10:17:53 - root - WARNING - 启动画面图片不存在
2025-05-26 10:17:53 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 10:17:53 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 10:17:53 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 10:17:53 - root - INFO - 数据库初始化成功
2025-05-26 10:17:54 - core.main_window - ERROR - 创建内容部件失败: name 'QStackedWidget' is not defined
2025-05-26 10:17:54 - core.main_window - ERROR - 创建中央部件失败: name 'QStackedWidget' is not defined
2025-05-26 10:17:54 - core.main_window - ERROR - 主窗口UI初始化失败: name 'QStackedWidget' is not defined
2025-05-26 10:17:54 - root - ERROR - 主窗口创建失败: name 'QStackedWidget' is not defined
2025-05-26 10:17:55 - root - INFO - 开始清理应用程序资源
2025-05-26 10:17:55 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 10:17:55 - root - INFO - 应用程序资源清理完成
2025-05-26 10:17:55 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 10:28:07 - root - INFO - 日志系统初始化完成
2025-05-26 10:28:07 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 10:28:07 - root - INFO - 系统版本: 1.0.0
2025-05-26 10:28:07 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 10:28:07 - root - INFO - 样式表加载成功
2025-05-26 10:28:07 - root - INFO - 应用程序基本设置完成
2025-05-26 10:28:07 - root - WARNING - 启动画面图片不存在
2025-05-26 10:28:07 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 10:28:07 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 10:28:07 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 10:28:07 - root - INFO - 数据库初始化成功
2025-05-26 10:28:08 - core.main_window - ERROR - 创建内容部件失败: name 'QStackedWidget' is not defined
2025-05-26 10:28:08 - core.main_window - ERROR - 创建中央部件失败: name 'QStackedWidget' is not defined
2025-05-26 10:28:08 - core.main_window - ERROR - 主窗口UI初始化失败: name 'QStackedWidget' is not defined
2025-05-26 10:28:08 - root - ERROR - 主窗口创建失败: name 'QStackedWidget' is not defined
2025-05-26 10:28:15 - root - INFO - 开始清理应用程序资源
2025-05-26 10:28:15 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 10:28:15 - root - INFO - 应用程序资源清理完成
2025-05-26 10:28:15 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 10:30:19 - root - INFO - 日志系统初始化完成
2025-05-26 10:30:19 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 10:30:19 - root - INFO - 系统版本: 1.0.0
2025-05-26 10:30:19 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 10:30:20 - root - INFO - 样式表加载成功
2025-05-26 10:30:20 - root - INFO - 应用程序基本设置完成
2025-05-26 10:30:20 - root - WARNING - 启动画面图片不存在
2025-05-26 10:30:20 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 10:30:20 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 10:30:20 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 10:30:20 - root - INFO - 数据库初始化成功
2025-05-26 10:30:20 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 10:30:20 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 10:30:20 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 10:30:20 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 10:30:20 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 10:30:20 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 10:30:20 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 10:30:20 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 10:30:20 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 10:30:20 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 10:30:20 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 10:30:20 - root - INFO - 主窗口创建成功
2025-05-26 10:30:20 - root - INFO - 主窗口显示成功
2025-05-26 10:30:20 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 10:30:32 - operation - INFO - 用户[unknown] 执行操作[切换到treatment页面]
2025-05-26 10:30:38 - operation - INFO - 用户[unknown] 执行操作[切换到report页面]
2025-05-26 10:30:40 - operation - INFO - 用户[unknown] 执行操作[切换到settings页面]
2025-05-26 10:30:57 - operation - INFO - 用户[unknown] 执行操作[切换到report页面]
2025-05-26 10:30:59 - operation - INFO - 用户[unknown] 执行操作[切换到treatment页面]
2025-05-26 10:31:09 - operation - INFO - 用户[unknown] 执行操作[切换到patient_management页面]
2025-05-26 10:31:24 - operation - INFO - 用户[unknown] 执行操作[切换到treatment页面]
2025-05-26 10:32:03 - operation - INFO - 用户[unknown] 执行操作[切换到report页面]
2025-05-26 10:32:15 - operation - INFO - 用户[unknown] 执行操作[切换到treatment页面]
2025-05-26 10:32:24 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 10:32:24 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 10:32:24 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 10:32:24 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 10:32:24 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 10:32:24 - root - INFO - 开始清理应用程序资源
2025-05-26 10:32:24 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 10:32:24 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 10:32:24 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 10:32:24 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 10:32:24 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 10:32:24 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 10:32:24 - root - INFO - 应用程序资源清理完成
2025-05-26 10:32:24 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 10:33:30 - root - INFO - 日志系统初始化完成
2025-05-26 10:33:30 - system - INFO - 系统事件[测试事件] 详情: 这是一个测试事件
2025-05-26 10:33:30 - operation - INFO - 用户[test_user] 执行操作[测试操作] 详情: 这是一个测试操作
2025-05-26 10:33:30 - data_processing - INFO - 数据处理[信号处理] 状态[成功] 详情: 处理了100个数据点
2025-05-26 10:33:30 - device - INFO - 设备[脑电设备] 状态[连接] 详情: 设备正常
2025-05-26 10:33:30 - performance - INFO - 性能监控[数据处理] 耗时[0.123s] 详情: 性能测试
2025-05-26 10:34:08 - root - INFO - 日志系统初始化完成
2025-05-26 10:34:08 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 10:34:08 - root - INFO - 系统版本: 1.0.0
2025-05-26 10:34:08 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 10:34:08 - root - INFO - 样式表加载成功
2025-05-26 10:34:08 - root - INFO - 应用程序基本设置完成
2025-05-26 10:34:08 - root - WARNING - 启动画面图片不存在
2025-05-26 10:34:08 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 10:34:08 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 10:34:08 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 10:34:08 - root - INFO - 数据库初始化成功
2025-05-26 10:34:09 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 10:34:09 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 10:34:09 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 10:34:09 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 10:34:09 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 10:34:09 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 10:34:09 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 10:34:09 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 10:34:09 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 10:34:09 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 10:34:09 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 10:34:09 - root - INFO - 主窗口创建成功
2025-05-26 10:34:09 - root - INFO - 主窗口显示成功
2025-05-26 10:34:09 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 10:34:34 - operation - INFO - 用户[current_user] 执行操作[添加患者] 详情: 患者: 222
2025-05-26 10:34:56 - operation - INFO - 用户[unknown] 执行操作[切换到settings页面]
2025-05-26 10:35:20 - operation - INFO - 用户[unknown] 执行操作[切换到report页面]
2025-05-26 10:35:27 - operation - INFO - 用户[unknown] 执行操作[切换到treatment页面]
2025-05-26 10:35:56 - root - INFO - 日志系统初始化完成
2025-05-26 10:35:56 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 10:35:56 - root - INFO - 系统版本: 1.0.0
2025-05-26 10:35:56 - root - INFO - Python版本: 3.9.0 (tags/v3.9.0:9cf6752, Oct  5 2020, 15:34:40) [MSC v.1927 64 bit (AMD64)]
2025-05-26 10:36:07 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 10:36:07 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 10:36:07 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 10:36:07 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 10:36:07 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 10:36:07 - root - INFO - 开始清理应用程序资源
2025-05-26 10:36:07 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 10:36:07 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 10:36:07 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 10:36:07 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 10:36:07 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 10:36:07 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 10:36:07 - root - INFO - 应用程序资源清理完成
2025-05-26 10:36:07 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 10:36:12 - root - INFO - 开始清理应用程序资源
2025-05-26 10:36:12 - root - INFO - 应用程序资源清理完成
2025-05-26 10:36:12 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 10:36:21 - root - INFO - 日志系统初始化完成
2025-05-26 10:36:21 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 10:36:21 - root - INFO - 系统版本: 1.0.0
2025-05-26 10:36:21 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 10:36:21 - root - INFO - 样式表加载成功
2025-05-26 10:36:21 - root - INFO - 应用程序基本设置完成
2025-05-26 10:36:21 - root - WARNING - 启动画面图片不存在
2025-05-26 10:36:21 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 10:36:21 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 10:36:21 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 10:36:21 - root - INFO - 数据库初始化成功
2025-05-26 10:36:21 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 10:36:21 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 10:36:21 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 10:36:21 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 10:36:21 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 10:36:21 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 10:36:21 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 10:36:21 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 10:36:21 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 10:36:21 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 10:36:21 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 10:36:21 - root - INFO - 主窗口创建成功
2025-05-26 10:36:21 - root - INFO - 主窗口显示成功
2025-05-26 10:36:21 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 10:36:27 - operation - INFO - 用户[unknown] 执行操作[切换到settings页面]
2025-05-26 10:36:51 - operation - INFO - 用户[unknown] 执行操作[切换到treatment页面]
2025-05-26 10:36:52 - operation - INFO - 用户[unknown] 执行操作[切换到report页面]
2025-05-26 10:36:52 - operation - INFO - 用户[unknown] 执行操作[切换到settings页面]
2025-05-26 10:37:00 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 10:37:00 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 10:37:00 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 10:37:00 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 10:37:00 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 10:37:00 - root - INFO - 开始清理应用程序资源
2025-05-26 10:37:00 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 10:37:00 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 10:37:00 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 10:37:00 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 10:37:00 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 10:37:00 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 10:37:00 - root - INFO - 应用程序资源清理完成
2025-05-26 10:37:00 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 10:38:05 - root - INFO - 日志系统初始化完成
2025-05-26 10:38:05 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 10:38:05 - root - INFO - 系统版本: 1.0.0
2025-05-26 10:38:05 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 10:38:05 - root - INFO - 样式表加载成功
2025-05-26 10:38:05 - root - INFO - 应用程序基本设置完成
2025-05-26 10:38:05 - root - WARNING - 启动画面图片不存在
2025-05-26 10:38:05 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 10:38:05 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 10:38:05 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 10:38:05 - root - INFO - 数据库初始化成功
2025-05-26 10:38:05 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 10:38:05 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 10:38:05 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 10:38:05 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 10:38:05 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 10:38:05 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 10:38:05 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 10:38:05 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 10:38:05 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 10:38:05 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 10:38:05 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 10:38:05 - root - INFO - 主窗口创建成功
2025-05-26 10:38:06 - root - INFO - 主窗口显示成功
2025-05-26 10:38:06 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 10:38:07 - operation - INFO - 用户[unknown] 执行操作[切换到treatment页面]
2025-05-26 10:38:32 - operation - INFO - 用户[unknown] 执行操作[切换到settings页面]
2025-05-26 10:38:50 - operation - INFO - 用户[unknown] 执行操作[切换到report页面]
2025-05-26 10:38:51 - operation - INFO - 用户[unknown] 执行操作[切换到treatment页面]
2025-05-26 10:39:00 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 10:39:00 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 10:39:00 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 10:39:00 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 10:39:00 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 10:39:00 - root - INFO - 开始清理应用程序资源
2025-05-26 10:39:00 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 10:39:00 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 10:39:00 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 10:39:00 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 10:39:00 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 10:39:00 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 10:39:00 - root - INFO - 应用程序资源清理完成
2025-05-26 10:39:00 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 10:59:07 - root - INFO - 日志系统初始化完成
2025-05-26 10:59:07 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 10:59:07 - root - INFO - 系统版本: 1.0.0
2025-05-26 10:59:07 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 10:59:07 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 10:59:07 - root - INFO - 应用程序基本设置完成
2025-05-26 10:59:07 - root - WARNING - 启动画面图片不存在
2025-05-26 10:59:07 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 10:59:07 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 10:59:07 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 10:59:07 - root - INFO - 数据库初始化成功
2025-05-26 10:59:07 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 10:59:07 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 10:59:07 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 10:59:07 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 10:59:07 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 10:59:07 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 10:59:07 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 10:59:07 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 10:59:07 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 10:59:07 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 10:59:07 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 10:59:07 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 10:59:07 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 10:59:07 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 10:59:07 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 10:59:07 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 10:59:07 - root - INFO - 主窗口创建成功
2025-05-26 10:59:07 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 10:59:07 - root - INFO - 主窗口显示成功
2025-05-26 10:59:07 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 10:59:08 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 10:59:09 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 10:59:10 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 10:59:22 - core.performance_optimizer - WARNING - CPU使用率过高: 96.9%
2025-05-26 10:59:22 - core.performance_optimizer - INFO - 降低工作线程数到: 7
2025-05-26 10:59:23 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 10:59:37 - operation - INFO - 用户[unknown] 执行操作[切换到treatment页面]
2025-05-26 10:59:50 - operation - INFO - 用户[unknown] 执行操作[切换到report页面]
2025-05-26 10:59:56 - operation - INFO - 用户[unknown] 执行操作[切换到settings页面]
2025-05-26 11:00:11 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 11:00:11 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 11:00:11 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 11:00:11 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 11:00:11 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 11:00:11 - root - INFO - 开始清理应用程序资源
2025-05-26 11:00:11 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 11:00:11 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 11:00:11 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 11:00:11 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 11:00:11 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 11:00:11 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 11:00:11 - root - INFO - 应用程序资源清理完成
2025-05-26 11:00:11 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 11:00:52 - root - INFO - 日志系统初始化完成
2025-05-26 11:00:52 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 11:00:52 - root - INFO - 系统版本: 1.0.0
2025-05-26 11:00:52 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 11:00:52 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 11:00:52 - root - INFO - 应用程序基本设置完成
2025-05-26 11:00:52 - root - WARNING - 启动画面图片不存在
2025-05-26 11:00:52 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 11:00:52 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 11:00:52 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 11:00:52 - root - INFO - 数据库初始化成功
2025-05-26 11:00:52 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 11:00:52 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 11:00:52 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 11:00:52 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 11:00:52 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 11:00:52 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 11:00:52 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 11:00:52 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 11:00:52 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 11:00:52 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 11:00:52 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 11:00:52 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 11:00:52 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 11:00:52 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 11:00:52 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 11:00:52 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 11:00:52 - root - INFO - 主窗口创建成功
2025-05-26 11:00:52 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 11:00:52 - root - INFO - 主窗口显示成功
2025-05-26 11:00:52 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 11:00:53 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 11:00:54 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 11:00:55 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 11:01:07 - core.performance_optimizer - WARNING - CPU使用率过高: 94.6%
2025-05-26 11:01:07 - core.performance_optimizer - INFO - 降低工作线程数到: 7
2025-05-26 11:01:08 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 11:02:19 - operation - INFO - 用户[unknown] 执行操作[切换到treatment页面]
2025-05-26 11:02:21 - operation - INFO - 用户[unknown] 执行操作[切换到report页面]
2025-05-26 11:02:22 - operation - INFO - 用户[unknown] 执行操作[切换到settings页面]
2025-05-26 11:02:23 - operation - INFO - 用户[unknown] 执行操作[切换到treatment页面]
2025-05-26 11:02:35 - operation - INFO - 用户[unknown] 执行操作[切换到patient_management页面]
2025-05-26 11:11:10 - operation - INFO - 用户[unknown] 执行操作[切换到settings页面]
2025-05-26 11:13:42 - operation - INFO - 用户[unknown] 执行操作[切换到treatment页面]
2025-05-26 11:20:12 - operation - INFO - 用户[unknown] 执行操作[切换到patient_management页面]
2025-05-26 11:22:07 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 11:22:07 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 11:22:07 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 11:22:07 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 11:22:07 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 11:22:07 - root - INFO - 开始清理应用程序资源
2025-05-26 11:22:07 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 11:22:07 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 11:22:07 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 11:22:07 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 11:22:07 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 11:22:07 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 11:22:07 - root - INFO - 应用程序资源清理完成
2025-05-26 11:22:07 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 11:22:15 - root - INFO - 日志系统初始化完成
2025-05-26 11:22:15 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 11:22:15 - root - INFO - 系统版本: 1.0.0
2025-05-26 11:22:15 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 11:22:15 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 11:22:15 - root - INFO - 应用程序基本设置完成
2025-05-26 11:22:15 - root - WARNING - 启动画面图片不存在
2025-05-26 11:22:15 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 11:22:15 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 11:22:15 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 11:22:15 - root - INFO - 数据库初始化成功
2025-05-26 11:22:15 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 11:22:15 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 11:22:15 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 11:22:15 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 11:22:15 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 11:22:15 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 11:22:15 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 11:22:15 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 11:22:15 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 11:22:15 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 11:22:15 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 11:22:15 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 11:22:15 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 11:22:15 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 11:22:15 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 11:22:15 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 11:22:15 - root - INFO - 主窗口创建成功
2025-05-26 11:22:15 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 11:22:15 - root - INFO - 主窗口显示成功
2025-05-26 11:22:15 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 11:22:16 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 11:22:17 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 11:22:18 - operation - INFO - 用户[unknown] 执行操作[切换到settings页面]
2025-05-26 11:22:18 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 11:22:31 - core.performance_optimizer - WARNING - CPU使用率过高: 93.8%
2025-05-26 11:22:31 - core.performance_optimizer - INFO - 降低工作线程数到: 7
2025-05-26 11:22:32 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 11:22:46 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 11:22:46 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 11:22:46 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 11:22:46 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 11:22:46 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 11:22:46 - root - INFO - 开始清理应用程序资源
2025-05-26 11:22:46 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 11:22:46 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 11:22:46 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 11:22:46 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 11:22:46 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 11:22:46 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 11:22:46 - root - INFO - 应用程序资源清理完成
2025-05-26 11:22:46 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 11:31:28 - root - INFO - 日志系统初始化完成
2025-05-26 11:31:28 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 11:31:28 - root - INFO - 系统版本: 1.0.0
2025-05-26 11:31:28 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 11:31:28 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 11:31:28 - root - INFO - 应用程序基本设置完成
2025-05-26 11:31:28 - root - WARNING - 启动画面图片不存在
2025-05-26 11:31:28 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 11:31:28 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 11:31:28 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 11:31:28 - root - INFO - 数据库初始化成功
2025-05-26 11:31:28 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 11:31:28 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 11:31:28 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 11:31:28 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 11:31:28 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 11:31:29 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 11:31:29 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 11:31:29 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 11:31:29 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 11:31:29 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 11:31:29 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 11:31:29 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 11:31:29 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 11:31:29 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 11:31:29 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 11:31:29 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 11:31:29 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 11:31:29 - root - INFO - 主窗口创建成功
2025-05-26 11:31:29 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 11:31:29 - root - INFO - 主窗口显示成功
2025-05-26 11:31:29 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 11:31:30 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 11:31:31 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 11:31:32 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 11:31:44 - core.performance_optimizer - WARNING - CPU使用率过高: 100.0%
2025-05-26 11:31:44 - core.performance_optimizer - INFO - 降低工作线程数到: 7
2025-05-26 11:31:45 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 11:32:20 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 11:32:20 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 11:32:20 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 11:32:20 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 11:32:20 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 11:32:20 - root - INFO - 开始清理应用程序资源
2025-05-26 11:32:20 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 11:32:20 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 11:32:20 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 11:32:20 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 11:32:20 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 11:32:20 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 11:32:20 - root - INFO - 应用程序资源清理完成
2025-05-26 11:32:20 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 11:32:37 - root - INFO - 日志系统初始化完成
2025-05-26 11:32:37 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 11:32:37 - root - INFO - 系统版本: 1.0.0
2025-05-26 11:32:37 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 11:35:22 - root - INFO - 日志系统初始化完成
2025-05-26 11:35:22 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 11:35:22 - root - INFO - 系统版本: 1.0.0
2025-05-26 11:35:22 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 11:35:23 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 11:35:23 - root - INFO - 应用程序基本设置完成
2025-05-26 11:35:23 - root - WARNING - 启动画面图片不存在
2025-05-26 11:35:23 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 11:35:23 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 11:35:23 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 11:35:23 - root - INFO - 数据库初始化成功
2025-05-26 11:35:23 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 11:35:23 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 11:35:23 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 11:35:23 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 11:35:23 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 11:35:23 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 11:35:23 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 11:35:23 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 11:35:23 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 11:35:23 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 11:35:23 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 11:35:23 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 11:35:23 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 11:35:23 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 11:35:23 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 11:35:23 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 11:35:23 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 11:35:23 - root - INFO - 主窗口创建成功
2025-05-26 11:35:23 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 11:35:23 - root - INFO - 主窗口显示成功
2025-05-26 11:35:23 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 11:35:24 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 11:35:25 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 11:35:26 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 11:35:38 - core.auth_manager - WARNING - 登录失败：用户不存在 - 1
2025-05-26 11:35:38 - core.performance_optimizer - WARNING - CPU使用率过高: 90.6%
2025-05-26 11:35:38 - core.performance_optimizer - INFO - 降低工作线程数到: 7
2025-05-26 11:35:40 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 11:35:47 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 11:35:47 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 11:35:47 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 11:35:47 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 11:35:47 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 11:35:47 - root - INFO - 开始清理应用程序资源
2025-05-26 11:35:47 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 11:35:47 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 11:35:47 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 11:35:47 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 11:35:47 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 11:35:47 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 11:35:47 - root - INFO - 应用程序资源清理完成
2025-05-26 11:35:47 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 11:42:34 - root - INFO - 日志系统初始化完成
2025-05-26 11:42:34 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 11:42:34 - root - INFO - 系统版本: 1.0.0
2025-05-26 11:42:34 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 11:42:34 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 11:42:34 - root - INFO - 应用程序基本设置完成
2025-05-26 11:42:34 - root - WARNING - 启动画面图片不存在
2025-05-26 11:42:34 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 11:42:34 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 11:42:34 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 11:42:34 - root - INFO - 数据库初始化成功
2025-05-26 11:42:34 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 11:42:34 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 11:42:34 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 11:42:34 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 11:42:34 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 11:42:34 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 11:42:34 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 11:42:34 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 11:42:34 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 11:42:34 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 11:42:34 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 11:42:34 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 11:42:34 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 11:42:34 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 11:42:34 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 11:42:34 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 11:42:34 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 11:42:34 - root - INFO - 主窗口创建成功
2025-05-26 11:42:34 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 11:42:34 - root - INFO - 主窗口显示成功
2025-05-26 11:42:34 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 11:42:35 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 11:42:36 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 11:42:37 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 11:42:50 - core.performance_optimizer - WARNING - CPU使用率过高: 85.7%
2025-05-26 11:47:59 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 11:47:59 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 11:47:59 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 11:47:59 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 11:47:59 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 11:47:59 - root - INFO - 开始清理应用程序资源
2025-05-26 11:47:59 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 11:47:59 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 11:47:59 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 11:47:59 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 11:47:59 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 11:47:59 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 11:47:59 - root - INFO - 应用程序资源清理完成
2025-05-26 11:47:59 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 11:48:53 - root - INFO - 日志系统初始化完成
2025-05-26 11:48:53 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 11:48:53 - root - INFO - 系统版本: 1.0.0
2025-05-26 11:48:53 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 11:48:53 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 11:48:53 - root - INFO - 应用程序基本设置完成
2025-05-26 11:48:53 - root - WARNING - 启动画面图片不存在
2025-05-26 11:48:53 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 11:48:53 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 11:48:53 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 11:48:53 - root - INFO - 数据库初始化成功
2025-05-26 11:48:53 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 11:48:53 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 11:48:53 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 11:48:53 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 11:48:53 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 11:48:53 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 11:48:53 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 11:48:53 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 11:48:53 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 11:48:53 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 11:48:53 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 11:48:53 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 11:48:53 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 11:48:53 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 11:48:53 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 11:48:53 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 11:48:53 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 11:48:53 - root - INFO - 主窗口创建成功
2025-05-26 11:48:53 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 11:48:53 - root - INFO - 主窗口显示成功
2025-05-26 11:48:53 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 11:48:54 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 11:48:55 - root - CRITICAL - 未捕获的异常
Traceback (most recent call last):
  File "D:\NK_QT\QT6\NK\NK\Python_NK_System\core\main_window.py", line 543, in show_login_dialog
    login_dialog = LoginDialog(self.auth_manager, self)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\NK_QT\QT6\NK\NK\Python_NK_System\ui\login_dialog.py", line 63, in __init__
    self.is_first_install = self.check_first_install()
                            ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'LoginDialog' object has no attribute 'check_first_install'
2025-05-26 11:48:56 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 11:48:56 - root - CRITICAL - 未捕获的异常
Traceback (most recent call last):
  File "D:\NK_QT\QT6\NK\NK\Python_NK_System\core\main_window.py", line 543, in show_login_dialog
    login_dialog = LoginDialog(self.auth_manager, self)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\NK_QT\QT6\NK\NK\Python_NK_System\ui\login_dialog.py", line 63, in __init__
    self.is_first_install = self.check_first_install()
                            ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'LoginDialog' object has no attribute 'check_first_install'
2025-05-26 11:48:57 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 11:48:59 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 11:48:59 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 11:48:59 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 11:48:59 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 11:48:59 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 11:48:59 - root - INFO - 开始清理应用程序资源
2025-05-26 11:48:59 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 11:48:59 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 11:48:59 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 11:48:59 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 11:48:59 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 11:48:59 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 11:48:59 - root - INFO - 应用程序资源清理完成
2025-05-26 11:48:59 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 11:53:31 - root - INFO - 日志系统初始化完成
2025-05-26 11:53:31 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 11:53:31 - root - INFO - 系统版本: 1.0.0
2025-05-26 11:53:31 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 11:53:31 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 11:53:31 - root - INFO - 应用程序基本设置完成
2025-05-26 11:53:31 - root - WARNING - 启动画面图片不存在
2025-05-26 11:53:31 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 11:53:31 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 11:53:31 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 11:53:31 - root - INFO - 数据库初始化成功
2025-05-26 11:53:31 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 11:53:31 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 11:53:31 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 11:53:31 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 11:53:31 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 11:53:31 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 11:53:31 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 11:53:31 - core.auth_manager - INFO - 权限系统初始化完成，当前有 1 个用户
2025-05-26 11:53:31 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 11:53:31 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 11:53:31 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 11:53:31 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 11:53:31 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 11:53:31 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 11:53:31 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 11:53:31 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 11:53:31 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 11:53:31 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 11:53:31 - root - INFO - 主窗口创建成功
2025-05-26 11:53:31 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 11:53:31 - root - INFO - 主窗口显示成功
2025-05-26 11:53:31 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 11:53:32 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 11:53:33 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 11:53:35 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 11:53:56 - core.auth_manager - WARNING - 登录失败：密码错误 - admin
2025-05-26 11:54:22 - core.auth_manager - WARNING - 登录失败：密码错误 - admin
2025-05-26 11:54:28 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 11:54:28 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 11:54:28 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 11:54:28 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 11:54:28 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 11:54:28 - root - INFO - 开始清理应用程序资源
2025-05-26 11:54:28 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 11:54:28 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 11:54:28 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 11:54:28 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 11:54:28 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 11:54:28 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 11:54:28 - root - INFO - 应用程序资源清理完成
2025-05-26 11:54:28 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 11:54:51 - root - INFO - 日志系统初始化完成
2025-05-26 11:54:51 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 11:54:51 - root - INFO - 系统版本: 1.0.0
2025-05-26 11:54:51 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 11:54:51 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 11:54:51 - root - INFO - 应用程序基本设置完成
2025-05-26 11:54:51 - root - WARNING - 启动画面图片不存在
2025-05-26 11:54:51 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 11:54:51 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 11:54:51 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 11:54:51 - root - INFO - 数据库初始化成功
2025-05-26 11:54:51 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 11:54:51 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 11:54:51 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 11:54:51 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 11:54:51 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 11:54:51 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 11:54:51 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 11:54:51 - core.auth_manager - INFO - 权限系统初始化完成，当前有 1 个用户
2025-05-26 11:54:51 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 11:54:51 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 11:54:51 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 11:54:51 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 11:54:51 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 11:54:51 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 11:54:51 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 11:54:51 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 11:54:51 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 11:54:51 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 11:54:51 - root - INFO - 主窗口创建成功
2025-05-26 11:54:51 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 11:54:51 - root - INFO - 主窗口显示成功
2025-05-26 11:54:51 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 11:54:53 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 11:54:54 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 11:54:55 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 11:55:05 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 11:55:05 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 11:55:05 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 11:55:05 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 11:55:05 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 11:55:05 - root - INFO - 开始清理应用程序资源
2025-05-26 11:55:05 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 11:55:05 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 11:55:05 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 11:55:05 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 11:55:05 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 11:55:05 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 11:55:05 - root - INFO - 应用程序资源清理完成
2025-05-26 11:55:05 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 14:35:27 - root - INFO - 日志系统初始化完成
2025-05-26 14:35:27 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 14:35:27 - root - INFO - 系统版本: 1.0.0
2025-05-26 14:35:27 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 14:35:27 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 14:35:27 - root - INFO - 应用程序基本设置完成
2025-05-26 14:35:27 - root - WARNING - 启动画面图片不存在
2025-05-26 14:35:27 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 14:35:27 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 14:35:27 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 14:35:27 - root - INFO - 数据库初始化成功
2025-05-26 14:35:27 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 14:35:27 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 14:35:27 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 14:35:27 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 14:35:27 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 14:35:28 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 14:35:28 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 14:35:28 - core.auth_manager - INFO - 权限系统初始化完成，当前有 1 个用户
2025-05-26 14:35:28 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 14:35:28 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 14:35:28 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 14:35:28 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 14:35:28 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 14:35:28 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 14:35:28 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 14:35:28 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 14:35:28 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 14:35:28 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 14:35:28 - root - INFO - 主窗口创建成功
2025-05-26 14:35:28 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 14:35:28 - root - INFO - 主窗口显示成功
2025-05-26 14:35:28 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 14:35:29 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 14:35:30 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 14:35:31 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 14:35:43 - core.performance_optimizer - WARNING - CPU使用率过高: 100.0%
2025-05-26 14:35:43 - core.performance_optimizer - INFO - 降低工作线程数到: 7
2025-05-26 14:35:45 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 14:36:17 - core.auth_manager - WARNING - 登录失败：密码错误 - admin
2025-05-26 14:37:29 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 14:37:29 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 14:37:29 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 14:37:29 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 14:37:29 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 14:37:29 - root - INFO - 开始清理应用程序资源
2025-05-26 14:37:29 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 14:37:29 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 14:37:29 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 14:37:29 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 14:37:29 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 14:37:29 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 14:37:29 - root - INFO - 应用程序资源清理完成
2025-05-26 14:37:29 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 15:48:14 - root - INFO - 日志系统初始化完成
2025-05-26 15:48:14 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 15:48:14 - root - INFO - 系统版本: 1.0.0
2025-05-26 15:48:14 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 15:48:14 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 15:48:14 - root - INFO - 应用程序基本设置完成
2025-05-26 15:48:14 - root - WARNING - 启动画面图片不存在
2025-05-26 15:48:14 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 15:48:14 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 15:48:14 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 15:48:14 - root - INFO - 数据库初始化成功
2025-05-26 15:48:14 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 15:48:14 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 15:48:14 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 15:48:14 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 15:48:14 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 15:48:15 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 15:48:15 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 15:48:15 - core.auth_manager - INFO - 权限系统初始化完成，当前有 1 个用户
2025-05-26 15:48:15 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 15:48:15 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 15:48:15 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 15:48:15 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 15:48:15 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 15:48:15 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 15:48:15 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 15:48:15 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 15:48:15 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 15:48:15 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 15:48:15 - root - INFO - 主窗口创建成功
2025-05-26 15:48:15 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 15:48:15 - root - INFO - 主窗口显示成功
2025-05-26 15:48:15 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 15:48:16 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 15:48:17 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 15:48:18 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 15:48:20 - core.auth_manager - WARNING - 登录失败：密码错误 - admin
2025-05-26 15:48:32 - core.auth_manager - WARNING - 登录失败：密码错误 - admin
2025-05-26 15:49:16 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 15:49:16 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 15:49:16 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 15:49:16 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 15:49:16 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 15:49:16 - root - INFO - 开始清理应用程序资源
2025-05-26 15:49:16 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 15:49:16 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 15:49:16 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 15:49:16 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 15:49:16 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 15:49:16 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 15:49:16 - root - INFO - 应用程序资源清理完成
2025-05-26 15:49:16 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 15:50:50 - root - INFO - 日志系统初始化完成
2025-05-26 15:50:50 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 15:50:50 - root - INFO - 系统版本: 1.0.0
2025-05-26 15:50:50 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 15:50:50 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 15:50:50 - root - INFO - 应用程序基本设置完成
2025-05-26 15:50:50 - root - WARNING - 启动画面图片不存在
2025-05-26 15:50:50 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 15:50:50 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 15:50:50 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 15:50:50 - root - INFO - 数据库初始化成功
2025-05-26 15:50:50 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 15:50:50 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 15:50:50 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 15:50:50 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 15:50:50 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 15:50:50 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 15:50:50 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 15:50:50 - core.auth_manager - INFO - 权限系统初始化完成，当前有 1 个用户
2025-05-26 15:50:50 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 15:50:50 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 15:50:50 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 15:50:50 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 15:50:50 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 15:50:50 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 15:50:50 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 15:50:50 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 15:50:50 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 15:50:50 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 15:50:50 - root - INFO - 主窗口创建成功
2025-05-26 15:50:51 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 15:50:51 - root - INFO - 主窗口显示成功
2025-05-26 15:50:51 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 15:50:52 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 15:50:53 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 15:50:54 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 15:52:17 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 15:52:17 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 15:52:17 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 15:52:17 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 15:52:17 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 15:52:17 - root - INFO - 开始清理应用程序资源
2025-05-26 15:52:17 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 15:52:17 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 15:52:17 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 15:52:17 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 15:52:17 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 15:52:17 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 15:52:17 - root - INFO - 应用程序资源清理完成
2025-05-26 15:52:17 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 15:56:17 - root - INFO - 日志系统初始化完成
2025-05-26 15:56:17 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 15:56:17 - root - INFO - 系统版本: 1.0.0
2025-05-26 15:56:17 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 15:58:31 - root - INFO - 日志系统初始化完成
2025-05-26 15:58:31 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 15:58:31 - root - INFO - 系统版本: 1.0.0
2025-05-26 15:58:31 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 15:58:31 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 15:58:31 - root - INFO - 应用程序基本设置完成
2025-05-26 15:58:31 - root - WARNING - 启动画面图片不存在
2025-05-26 15:58:31 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 15:58:31 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 15:58:31 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 15:58:31 - root - INFO - 数据库初始化成功
2025-05-26 15:58:31 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 15:58:31 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 15:58:31 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 15:58:31 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 15:58:31 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 15:58:31 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 15:58:31 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 15:58:31 - core.auth_manager - INFO - 权限系统初始化完成，当前有 1 个用户
2025-05-26 15:58:31 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 15:58:31 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 15:58:31 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 15:58:31 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 15:58:31 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 15:58:31 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 15:58:31 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 15:58:31 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 15:58:31 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 15:58:31 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 15:58:31 - root - INFO - 主窗口创建成功
2025-05-26 15:58:31 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 15:58:31 - root - INFO - 主窗口显示成功
2025-05-26 15:58:31 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 15:58:33 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 15:58:34 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 15:58:35 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 15:58:47 - core.performance_optimizer - WARNING - CPU使用率过高: 96.4%
2025-05-26 15:58:47 - core.performance_optimizer - INFO - 降低工作线程数到: 7
2025-05-26 15:58:48 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 15:58:57 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 15:58:57 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 15:58:57 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 15:58:57 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 15:58:57 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 15:58:57 - root - INFO - 开始清理应用程序资源
2025-05-26 15:58:57 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 15:58:57 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 15:58:57 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 15:58:57 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 15:58:57 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 15:58:57 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 15:58:57 - root - INFO - 应用程序资源清理完成
2025-05-26 15:58:57 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 15:59:20 - root - INFO - 日志系统初始化完成
2025-05-26 15:59:20 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 15:59:20 - root - INFO - 系统版本: 1.0.0
2025-05-26 15:59:20 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 15:59:20 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 15:59:20 - root - INFO - 应用程序基本设置完成
2025-05-26 15:59:20 - root - WARNING - 启动画面图片不存在
2025-05-26 15:59:20 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 15:59:20 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 15:59:20 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 15:59:20 - root - INFO - 数据库初始化成功
2025-05-26 15:59:20 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 15:59:20 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 15:59:20 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 15:59:20 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 15:59:20 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 15:59:20 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 15:59:20 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 15:59:20 - core.auth_manager - INFO - 权限系统初始化完成，当前有 1 个用户
2025-05-26 15:59:20 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 15:59:20 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 15:59:20 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 15:59:20 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 15:59:20 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 15:59:20 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 15:59:20 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 15:59:20 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 15:59:20 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 15:59:20 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 15:59:20 - root - INFO - 主窗口创建成功
2025-05-26 15:59:21 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 15:59:21 - root - INFO - 主窗口显示成功
2025-05-26 15:59:21 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 15:59:22 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 15:59:23 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 15:59:24 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 15:59:36 - core.performance_optimizer - WARNING - CPU使用率过高: 98.2%
2025-05-26 15:59:36 - core.performance_optimizer - INFO - 降低工作线程数到: 7
2025-05-26 15:59:37 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 15:59:54 - core.auth_manager - WARNING - 登录失败：密码错误 - admin
2025-05-26 16:00:12 - core.auth_manager - WARNING - 登录失败：密码错误 - admin
2025-05-26 16:00:25 - core.auth_manager - WARNING - 登录失败：密码错误 - admin
2025-05-26 16:00:37 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 16:00:37 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 16:00:37 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 16:00:37 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 16:00:37 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 16:00:37 - root - INFO - 开始清理应用程序资源
2025-05-26 16:00:37 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 16:00:37 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 16:00:37 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 16:00:37 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 16:00:37 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 16:00:37 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 16:00:37 - root - INFO - 应用程序资源清理完成
2025-05-26 16:00:37 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 16:00:45 - root - INFO - 日志系统初始化完成
2025-05-26 16:00:45 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 16:00:45 - root - INFO - 系统版本: 1.0.0
2025-05-26 16:00:45 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 16:00:45 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 16:00:45 - root - INFO - 应用程序基本设置完成
2025-05-26 16:00:45 - root - WARNING - 启动画面图片不存在
2025-05-26 16:00:45 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 16:00:45 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 16:00:45 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 16:00:45 - root - INFO - 数据库初始化成功
2025-05-26 16:00:45 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 16:00:45 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 16:00:45 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 16:00:45 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 16:00:45 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 16:00:45 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 16:00:45 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 16:00:45 - core.auth_manager - INFO - 权限系统初始化完成，当前有 1 个用户
2025-05-26 16:00:45 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 16:00:45 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 16:00:45 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 16:00:45 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 16:00:45 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 16:00:45 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 16:00:45 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 16:00:45 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 16:00:45 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 16:00:45 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 16:00:45 - root - INFO - 主窗口创建成功
2025-05-26 16:00:46 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 16:00:46 - root - INFO - 主窗口显示成功
2025-05-26 16:00:46 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 16:00:47 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 16:00:48 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 16:00:49 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 16:00:54 - core.auth_manager - WARNING - 登录失败：密码错误 - admin
2025-05-26 16:01:01 - core.performance_optimizer - WARNING - CPU使用率过高: 97.9%
2025-05-26 16:01:01 - core.performance_optimizer - INFO - 降低工作线程数到: 7
2025-05-26 16:01:01 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 16:01:01 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 16:01:01 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 16:01:01 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 16:01:01 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 16:01:01 - root - INFO - 开始清理应用程序资源
2025-05-26 16:01:01 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 16:01:01 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 16:01:01 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 16:01:01 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 16:01:01 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 16:01:01 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 16:01:01 - root - INFO - 应用程序资源清理完成
2025-05-26 16:01:01 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 16:03:16 - root - INFO - 日志系统初始化完成
2025-05-26 16:03:16 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 16:03:16 - root - INFO - 系统版本: 1.0.0
2025-05-26 16:03:16 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 16:03:16 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 16:03:16 - root - INFO - 应用程序基本设置完成
2025-05-26 16:03:16 - root - WARNING - 启动画面图片不存在
2025-05-26 16:03:16 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 16:03:16 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 16:03:16 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 16:03:16 - root - INFO - 数据库初始化成功
2025-05-26 16:03:16 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 16:03:16 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 16:03:16 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 16:03:16 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 16:03:16 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 16:03:17 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 16:03:17 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 16:03:17 - core.auth_manager - INFO - 权限系统初始化完成，当前有 1 个用户
2025-05-26 16:03:17 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 16:03:17 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 16:03:17 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 16:03:17 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 16:03:17 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 16:03:17 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 16:03:17 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 16:03:17 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 16:03:17 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 16:03:17 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 16:03:17 - root - INFO - 主窗口创建成功
2025-05-26 16:03:17 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 16:03:17 - root - INFO - 主窗口显示成功
2025-05-26 16:03:17 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 16:03:18 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 16:03:19 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 16:03:20 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 16:12:37 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 16:12:37 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 16:12:37 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 16:12:37 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 16:12:37 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 16:12:37 - root - INFO - 开始清理应用程序资源
2025-05-26 16:12:37 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 16:12:37 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 16:12:37 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 16:12:37 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 16:12:37 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 16:12:37 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 16:12:37 - root - INFO - 应用程序资源清理完成
2025-05-26 16:12:37 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 16:14:19 - root - INFO - 日志系统初始化完成
2025-05-26 16:14:19 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 16:14:19 - root - INFO - 系统版本: 1.0.0
2025-05-26 16:14:19 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 16:14:19 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 16:14:19 - root - INFO - 应用程序基本设置完成
2025-05-26 16:14:19 - root - WARNING - 启动画面图片不存在
2025-05-26 16:14:19 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 16:14:19 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 16:14:19 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 16:14:19 - root - INFO - 数据库初始化成功
2025-05-26 16:14:20 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 16:14:20 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 16:14:20 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 16:14:20 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 16:14:20 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 16:14:20 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 16:14:20 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 16:14:20 - core.auth_manager - INFO - 权限系统初始化完成，当前有 1 个用户
2025-05-26 16:14:20 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 16:14:20 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 16:14:20 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 16:14:20 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 16:14:20 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 16:14:20 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 16:14:20 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 16:14:20 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 16:14:20 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 16:14:20 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 16:14:20 - root - INFO - 主窗口创建成功
2025-05-26 16:14:20 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 16:14:20 - root - INFO - 主窗口显示成功
2025-05-26 16:14:20 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 16:14:21 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 16:14:22 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 16:14:23 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 16:14:32 - core.auth_manager - WARNING - 登录失败：密码错误 - admin
2025-05-26 16:14:32 - core.performance_optimizer - WARNING - CPU使用率过高: 91.7%
2025-05-26 16:14:32 - core.performance_optimizer - INFO - 降低工作线程数到: 7
2025-05-26 16:14:33 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 16:14:42 - core.auth_manager - WARNING - 登录失败：密码错误 - admin
2025-05-26 16:14:50 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 16:14:50 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 16:14:50 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 16:14:50 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 16:14:50 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 16:14:50 - root - INFO - 开始清理应用程序资源
2025-05-26 16:14:50 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 16:14:50 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 16:14:50 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 16:14:50 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 16:14:50 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 16:14:50 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 16:14:50 - root - INFO - 应用程序资源清理完成
2025-05-26 16:14:50 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 16:15:11 - root - INFO - 日志系统初始化完成
2025-05-26 16:15:11 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 16:15:11 - root - INFO - 系统版本: 1.0.0
2025-05-26 16:15:11 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 16:15:11 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 16:15:11 - root - INFO - 应用程序基本设置完成
2025-05-26 16:15:11 - root - WARNING - 启动画面图片不存在
2025-05-26 16:15:11 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 16:15:11 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 16:15:11 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 16:15:11 - root - INFO - 数据库初始化成功
2025-05-26 16:15:11 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 16:15:11 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 16:15:11 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 16:15:11 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 16:15:11 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 16:15:11 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 16:15:11 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 16:15:11 - core.auth_manager - INFO - 权限系统初始化完成，当前有 1 个用户
2025-05-26 16:15:11 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 16:15:11 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 16:15:11 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 16:15:11 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 16:15:11 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 16:15:11 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 16:15:11 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 16:15:11 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 16:15:11 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 16:15:11 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 16:15:11 - root - INFO - 主窗口创建成功
2025-05-26 16:15:11 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 16:15:11 - root - INFO - 主窗口显示成功
2025-05-26 16:15:11 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 16:15:13 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 16:15:14 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 16:15:15 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 16:15:21 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 16:15:21 - core.auth_manager - INFO - 数据库中存储的密码哈希=***********-05-26 16:15:21 - core.auth_manager - INFO - 密码验证结果: False
2025-05-26 16:15:21 - core.auth_manager - WARNING - 登录失败：密码错误 - admin
2025-05-26 16:15:27 - core.auth_manager - INFO - 验证密码: 输入密码=123456, 输入密码哈希=b3b4cd5c6ff6952c266b1235972af732a9fe84a04def1556f2da143b093f6870
2025-05-26 16:15:27 - core.auth_manager - INFO - 数据库中存储的密码哈希=***********-05-26 16:15:27 - core.auth_manager - INFO - 密码验证结果: False
2025-05-26 16:15:27 - core.auth_manager - WARNING - 登录失败：密码错误 - admin
2025-05-26 16:15:33 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 16:15:33 - core.auth_manager - INFO - 数据库中存储的密码哈希=***********-05-26 16:15:33 - core.auth_manager - INFO - 密码验证结果: False
2025-05-26 16:15:33 - core.auth_manager - WARNING - 登录失败：密码错误 - admin
2025-05-26 16:15:36 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 16:15:36 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 16:15:36 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 16:15:36 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 16:15:36 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 16:15:36 - root - INFO - 开始清理应用程序资源
2025-05-26 16:15:36 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 16:15:36 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 16:15:36 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 16:15:36 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 16:15:36 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 16:15:36 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 16:15:36 - root - INFO - 应用程序资源清理完成
2025-05-26 16:15:36 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 16:16:42 - root - INFO - 日志系统初始化完成
2025-05-26 16:16:42 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 16:16:42 - root - INFO - 系统版本: 1.0.0
2025-05-26 16:16:42 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 16:16:42 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 16:16:42 - root - INFO - 应用程序基本设置完成
2025-05-26 16:16:42 - root - WARNING - 启动画面图片不存在
2025-05-26 16:16:42 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 16:16:42 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 16:16:42 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 16:16:42 - root - INFO - 数据库初始化成功
2025-05-26 16:16:42 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 16:16:42 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 16:16:42 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 16:16:42 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 16:16:42 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 16:16:43 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 16:16:43 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 16:16:43 - core.auth_manager - INFO - 权限系统初始化完成，当前有 1 个用户
2025-05-26 16:16:43 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 16:16:43 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 16:16:43 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 16:16:43 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 16:16:43 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 16:16:43 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 16:16:43 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 16:16:43 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 16:16:43 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 16:16:43 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 16:16:43 - root - INFO - 主窗口创建成功
2025-05-26 16:16:43 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 16:16:43 - root - INFO - 主窗口显示成功
2025-05-26 16:16:43 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 16:16:44 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 16:16:45 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 16:16:46 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 16:16:51 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 16:16:51 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 16:16:51 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 16:16:51 - core.auth_manager - INFO - 数据库中存储的密码哈希=***********-05-26 16:16:51 - core.auth_manager - INFO - 密码验证结果: False
2025-05-26 16:16:51 - core.auth_manager - WARNING - 登录失败：密码错误 - admin
2025-05-26 16:17:01 - core.auth_manager - INFO - 计算密码哈希: 明文密码= admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 16:17:01 - core.auth_manager - INFO - 计算结果: 9e2224912920b5dd36aeccd36fb8d123d1262cf8c5fb21fa293c917bb871fa93
2025-05-26 16:17:01 - core.auth_manager - INFO - 验证密码: 输入密码= admin123, 输入密码哈希=9e2224912920b5dd36aeccd36fb8d123d1262cf8c5fb21fa293c917bb871fa93
2025-05-26 16:17:01 - core.auth_manager - INFO - 数据库中存储的密码哈希=***********-05-26 16:17:01 - core.auth_manager - INFO - 密码验证结果: False
2025-05-26 16:17:01 - core.auth_manager - WARNING - 登录失败：密码错误 - admin
2025-05-26 16:17:05 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 16:17:05 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 16:17:05 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 16:17:05 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 16:17:05 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 16:17:05 - root - INFO - 开始清理应用程序资源
2025-05-26 16:17:05 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 16:17:05 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 16:17:05 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 16:17:05 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 16:17:05 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 16:17:05 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 16:17:05 - root - INFO - 应用程序资源清理完成
2025-05-26 16:17:05 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 16:18:08 - root - INFO - 日志系统初始化完成
2025-05-26 16:18:08 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 16:18:08 - root - INFO - 系统版本: 1.0.0
2025-05-26 16:18:08 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 16:18:08 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 16:18:08 - root - INFO - 应用程序基本设置完成
2025-05-26 16:18:08 - root - WARNING - 启动画面图片不存在
2025-05-26 16:18:08 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 16:18:08 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 16:18:08 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 16:18:08 - root - INFO - 数据库初始化成功
2025-05-26 16:18:08 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 16:18:08 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 16:18:08 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 16:18:08 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 16:18:08 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 16:18:09 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 16:18:09 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 16:18:09 - core.auth_manager - INFO - 权限系统初始化完成，当前有 1 个用户
2025-05-26 16:18:09 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 16:18:09 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 16:18:09 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 16:18:09 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 16:18:09 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 16:18:09 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 16:18:09 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 16:18:09 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 16:18:09 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 16:18:09 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 16:18:09 - root - INFO - 主窗口创建成功
2025-05-26 16:18:09 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 16:18:09 - root - INFO - 主窗口显示成功
2025-05-26 16:18:09 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 16:18:10 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 16:18:11 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 16:18:12 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 16:18:18 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 16:18:18 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 16:18:18 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 16:18:18 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 16:18:18 - core.auth_manager - INFO - 密码验证结果: True
2025-05-26 16:18:18 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-26 16:18:18 - operation - INFO - 用户[admin] 执行操作[切换到patient_management页面]
2025-05-26 16:18:18 - core.main_window - INFO - 敏感内容已显示，用户已通过身份验证
2025-05-26 16:18:18 - operation - INFO - 用户[admin] 执行操作[用户登录]
2025-05-26 16:18:18 - operation - INFO - 用户[admin] 执行操作[用户登录成功]
2025-05-26 16:18:32 - operation - INFO - 用户[admin] 执行操作[切换到user_management页面]
2025-05-26 16:18:39 - ui.user_management_ui - INFO - 用户列表已刷新，共 1 个用户
2025-05-26 16:18:41 - ui.user_management_ui - INFO - 用户列表已刷新，共 1 个用户
2025-05-26 16:19:22 - operation - INFO - 用户[admin] 执行操作[切换到settings页面]
2025-05-26 16:19:33 - operation - INFO - 用户[admin] 执行操作[切换到user_management页面]
2025-05-26 16:19:34 - operation - INFO - 用户[admin] 执行操作[切换到report页面]
2025-05-26 16:19:36 - operation - INFO - 用户[admin] 执行操作[切换到treatment页面]
2025-05-26 16:19:38 - operation - INFO - 用户[admin] 执行操作[切换到patient_management页面]
2025-05-26 16:19:39 - operation - INFO - 用户[admin] 执行操作[切换到treatment页面]
2025-05-26 16:20:53 - operation - INFO - 用户[admin] 执行操作[切换到patient_management页面]
2025-05-26 16:21:01 - operation - INFO - 用户[admin] 执行操作[切换到treatment页面]
2025-05-26 16:21:05 - operation - INFO - 用户[admin] 执行操作[切换到report页面]
2025-05-26 16:21:17 - operation - INFO - 用户[admin] 执行操作[切换到user_management页面]
2025-05-26 16:23:49 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 16:23:49 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 16:23:49 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 16:23:49 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 16:23:49 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 16:23:49 - root - INFO - 开始清理应用程序资源
2025-05-26 16:23:49 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 16:23:49 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 16:23:49 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 16:23:49 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 16:23:49 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 16:23:49 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 16:23:49 - root - INFO - 应用程序资源清理完成
2025-05-26 16:23:49 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 16:25:34 - root - INFO - 日志系统初始化完成
2025-05-26 16:25:34 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 16:25:34 - root - INFO - 系统版本: 1.0.0
2025-05-26 16:25:34 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 16:25:34 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 16:25:34 - root - INFO - 应用程序基本设置完成
2025-05-26 16:25:34 - root - WARNING - 启动画面图片不存在
2025-05-26 16:25:34 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 16:25:34 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 16:25:34 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 16:25:34 - root - INFO - 数据库初始化成功
2025-05-26 16:25:34 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 16:25:34 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 16:25:34 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 16:25:34 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 16:25:34 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 16:25:34 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 16:25:34 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 16:25:34 - core.auth_manager - INFO - 权限系统初始化完成，当前有 1 个用户
2025-05-26 16:25:34 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 16:25:34 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 16:25:34 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 16:25:34 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 16:25:34 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 16:25:34 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 16:25:34 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 16:25:34 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 16:25:34 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 16:25:34 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 16:25:34 - root - INFO - 主窗口创建成功
2025-05-26 16:25:34 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 16:25:34 - root - INFO - 主窗口显示成功
2025-05-26 16:25:34 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 16:25:35 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 16:25:37 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 16:25:38 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 16:25:45 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 16:25:45 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 16:25:45 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 16:25:45 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 16:25:45 - core.auth_manager - INFO - 密码验证结果: True
2025-05-26 16:25:45 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-26 16:25:57 - root - INFO - 日志系统初始化完成
2025-05-26 16:25:57 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 16:25:57 - root - INFO - 系统版本: 1.0.0
2025-05-26 16:25:57 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 16:25:57 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 16:25:57 - root - INFO - 应用程序基本设置完成
2025-05-26 16:25:57 - root - WARNING - 启动画面图片不存在
2025-05-26 16:25:57 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 16:25:57 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 16:25:57 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 16:25:57 - root - INFO - 数据库初始化成功
2025-05-26 16:25:57 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 16:25:57 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 16:25:57 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 16:25:57 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 16:25:57 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 16:25:57 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 16:25:57 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 16:25:57 - core.auth_manager - INFO - 权限系统初始化完成，当前有 1 个用户
2025-05-26 16:25:57 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 16:25:57 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 16:25:57 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 16:25:57 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 16:25:57 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 16:25:57 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 16:25:57 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 16:25:57 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 16:25:57 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 16:25:57 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 16:25:57 - root - INFO - 主窗口创建成功
2025-05-26 16:25:57 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 16:25:57 - root - INFO - 主窗口显示成功
2025-05-26 16:25:57 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 16:25:58 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 16:25:59 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 16:26:00 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 16:26:06 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 16:26:06 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 16:26:06 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 16:26:06 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 16:26:06 - core.auth_manager - INFO - 密码验证结果: True
2025-05-26 16:26:06 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-26 16:26:06 - operation - INFO - 用户[admin] 执行操作[切换到patient_management页面]
2025-05-26 16:26:06 - core.main_window - INFO - 敏感内容已显示，用户已通过身份验证
2025-05-26 16:26:06 - operation - INFO - 用户[admin] 执行操作[用户登录]
2025-05-26 16:26:06 - operation - INFO - 用户[admin] 执行操作[用户登录成功]
2025-05-26 16:26:10 - operation - INFO - 用户[admin] 执行操作[切换到user_management页面]
2025-05-26 16:26:17 - ui.user_management_ui - INFO - 用户列表已刷新，共 1 个用户
2025-05-26 16:27:11 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 16:27:11 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 16:27:11 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 16:27:11 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 16:27:11 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 16:27:11 - root - INFO - 开始清理应用程序资源
2025-05-26 16:27:11 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 16:27:11 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 16:27:11 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 16:27:11 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 16:27:11 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 16:27:11 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 16:27:11 - root - INFO - 应用程序资源清理完成
2025-05-26 16:27:11 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 16:28:00 - root - INFO - 日志系统初始化完成
2025-05-26 16:28:00 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 16:28:00 - root - INFO - 系统版本: 1.0.0
2025-05-26 16:28:00 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 16:28:00 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 16:28:00 - root - INFO - 应用程序基本设置完成
2025-05-26 16:28:00 - root - WARNING - 启动画面图片不存在
2025-05-26 16:28:00 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 16:28:00 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 16:28:00 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 16:28:00 - root - INFO - 数据库初始化成功
2025-05-26 16:28:01 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 16:28:01 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 16:28:01 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 16:28:01 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 16:28:01 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 16:28:01 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 16:28:01 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 16:28:01 - core.auth_manager - INFO - 权限系统初始化完成，当前有 1 个用户
2025-05-26 16:28:01 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 16:28:01 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 16:28:01 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 16:28:01 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 16:28:01 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 16:28:01 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 16:28:01 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 16:28:01 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 16:28:01 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 16:28:01 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 16:28:01 - root - INFO - 主窗口创建成功
2025-05-26 16:28:01 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 16:28:01 - root - INFO - 主窗口显示成功
2025-05-26 16:28:01 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 16:28:02 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 16:28:03 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 16:28:04 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 16:28:10 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 16:28:10 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 16:28:10 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 16:28:10 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 16:28:10 - core.auth_manager - INFO - 密码验证结果: True
2025-05-26 16:28:10 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-26 16:28:10 - operation - INFO - 用户[admin] 执行操作[切换到patient_management页面]
2025-05-26 16:28:10 - core.main_window - INFO - 敏感内容已显示，用户已通过身份验证
2025-05-26 16:28:10 - operation - INFO - 用户[admin] 执行操作[用户登录]
2025-05-26 16:28:10 - operation - INFO - 用户[admin] 执行操作[用户登录成功]
2025-05-26 16:28:17 - operation - INFO - 用户[admin] 执行操作[切换到user_management页面]
2025-05-26 16:28:20 - ui.user_management_ui - INFO - 用户列表已刷新，共 1 个用户
2025-05-26 16:28:27 - operation - INFO - 用户[admin] 执行操作[切换到treatment页面]
2025-05-26 16:31:04 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 16:31:04 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 16:31:04 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 16:31:04 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 16:31:04 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 16:31:04 - root - INFO - 开始清理应用程序资源
2025-05-26 16:31:04 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 16:31:04 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 16:31:04 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 16:31:04 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 16:31:04 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 16:31:04 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 16:31:04 - root - INFO - 应用程序资源清理完成
2025-05-26 16:31:04 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 17:06:40 - root - INFO - 日志系统初始化完成
2025-05-26 17:06:40 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 17:06:40 - root - INFO - 系统版本: 1.0.0
2025-05-26 17:06:40 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 17:06:40 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 17:06:40 - root - INFO - 应用程序基本设置完成
2025-05-26 17:06:40 - root - WARNING - 启动画面图片不存在
2025-05-26 17:06:40 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 17:06:40 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 17:06:40 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 17:06:40 - root - INFO - 数据库初始化成功
2025-05-26 17:06:40 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 17:06:40 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 17:06:40 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 17:06:40 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 17:06:40 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 17:06:41 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 17:06:41 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 17:06:41 - core.auth_manager - INFO - 权限系统初始化完成，当前有 1 个用户
2025-05-26 17:06:41 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 17:06:41 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 17:06:41 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 17:06:41 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 17:06:41 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 17:06:41 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 17:06:41 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 17:06:41 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 17:06:41 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 17:06:41 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 17:06:41 - root - INFO - 主窗口创建成功
2025-05-26 17:06:41 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 17:06:41 - root - INFO - 主窗口显示成功
2025-05-26 17:06:41 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 17:06:42 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 17:06:43 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 17:06:44 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 17:06:49 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 17:06:49 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 17:06:49 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 17:06:49 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 17:06:49 - core.auth_manager - INFO - 密码验证结果: True
2025-05-26 17:06:49 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-26 17:16:49 - root - INFO - 日志系统初始化完成
2025-05-26 17:16:49 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 17:16:49 - root - INFO - 系统版本: 1.0.0
2025-05-26 17:16:49 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 17:16:49 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 17:16:49 - root - INFO - 应用程序基本设置完成
2025-05-26 17:16:49 - root - WARNING - 启动画面图片不存在
2025-05-26 17:16:49 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 17:16:49 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 17:16:49 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 17:16:49 - root - INFO - 数据库初始化成功
2025-05-26 17:16:49 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 17:16:49 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 17:16:49 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 17:16:49 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 17:16:49 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 17:16:50 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 17:16:50 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 17:16:50 - root - ERROR - 主窗口创建失败: name 'threading' is not defined
2025-05-26 17:16:54 - root - INFO - 开始清理应用程序资源
2025-05-26 17:16:54 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 17:16:54 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 17:16:54 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 17:16:54 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 17:16:54 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 17:16:54 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 17:16:54 - root - INFO - 应用程序资源清理完成
2025-05-26 17:16:54 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 17:17:11 - root - INFO - 日志系统初始化完成
2025-05-26 17:17:11 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 17:17:11 - root - INFO - 系统版本: 1.0.0
2025-05-26 17:17:11 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 17:17:11 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 17:17:11 - root - INFO - 应用程序基本设置完成
2025-05-26 17:17:11 - root - WARNING - 启动画面图片不存在
2025-05-26 17:17:11 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 17:17:11 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 17:17:11 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 17:17:11 - root - INFO - 数据库初始化成功
2025-05-26 17:17:11 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 17:17:11 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 17:17:11 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 17:17:11 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 17:17:11 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 17:17:11 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 17:17:11 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 17:17:11 - core.auth_manager - INFO - 权限系统初始化完成，当前有 1 个用户
2025-05-26 17:17:11 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 17:17:11 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 17:17:11 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 17:17:11 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 17:17:11 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 17:17:11 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 17:17:11 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 17:17:11 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 17:17:11 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 17:17:11 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 17:17:11 - root - INFO - 主窗口创建成功
2025-05-26 17:17:11 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 17:17:11 - root - INFO - 主窗口显示成功
2025-05-26 17:17:11 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 17:17:12 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 17:17:13 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 17:17:14 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 17:17:14 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 17:17:14 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 17:17:14 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 17:17:14 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 17:17:14 - root - INFO - 开始清理应用程序资源
2025-05-26 17:17:14 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 17:17:14 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 17:17:14 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 17:17:14 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 17:17:14 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 17:17:14 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 17:17:14 - root - INFO - 应用程序资源清理完成
2025-05-26 17:17:14 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 17:17:27 - root - INFO - 日志系统初始化完成
2025-05-26 17:17:27 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 17:17:27 - root - INFO - 系统版本: 1.0.0
2025-05-26 17:17:27 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 17:17:27 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 17:17:27 - root - INFO - 应用程序基本设置完成
2025-05-26 17:17:27 - root - WARNING - 启动画面图片不存在
2025-05-26 17:17:27 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 17:17:27 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 17:17:27 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 17:17:27 - root - INFO - 数据库初始化成功
2025-05-26 17:17:27 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 17:17:27 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 17:17:27 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 17:17:27 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 17:17:27 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 17:17:28 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 17:17:28 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 17:17:28 - core.auth_manager - INFO - 权限系统初始化完成，当前有 1 个用户
2025-05-26 17:17:28 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 17:17:28 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 17:17:28 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 17:17:28 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 17:17:28 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 17:17:28 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 17:17:28 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 17:17:28 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 17:17:28 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 17:17:28 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 17:17:28 - root - INFO - 主窗口创建成功
2025-05-26 17:17:28 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 17:17:28 - root - INFO - 主窗口显示成功
2025-05-26 17:17:28 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 17:17:29 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 17:17:30 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 17:17:31 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 17:17:34 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 17:17:34 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 17:17:34 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 17:17:34 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 17:17:34 - core.auth_manager - INFO - 密码验证结果: True
2025-05-26 17:17:34 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-26 17:45:49 - root - INFO - 日志系统初始化完成
2025-05-26 17:45:49 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 17:45:49 - root - INFO - 系统版本: 1.0.0
2025-05-26 17:45:49 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 17:45:57 - root - INFO - 日志系统初始化完成
2025-05-26 17:45:57 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 17:45:57 - root - INFO - 系统版本: 1.0.0
2025-05-26 17:45:57 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 17:45:58 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 17:45:58 - root - INFO - 应用程序基本设置完成
2025-05-26 17:45:58 - root - WARNING - 启动画面图片不存在
2025-05-26 17:45:58 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 17:45:58 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 17:45:58 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 17:45:58 - root - INFO - 数据库初始化成功
2025-05-26 17:45:58 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 17:45:58 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 17:45:58 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 17:45:58 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 17:45:58 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 17:45:58 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 17:45:58 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 17:45:58 - core.auth_manager - INFO - 权限系统初始化完成，当前有 1 个用户
2025-05-26 17:45:58 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 17:45:58 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 17:45:58 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 17:45:58 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 17:45:58 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 17:45:58 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 17:45:58 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 17:45:58 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 17:45:58 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 17:45:58 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 17:45:58 - root - INFO - 主窗口创建成功
2025-05-26 17:45:58 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 17:45:58 - root - INFO - 主窗口显示成功
2025-05-26 17:45:58 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 17:45:59 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 17:46:00 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 17:46:01 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 17:46:07 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 17:46:07 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 17:46:07 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 17:46:07 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 17:46:07 - core.auth_manager - INFO - 密码验证结果: True
2025-05-26 17:46:07 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-26 17:46:07 - operation - INFO - 用户[admin] 执行操作[切换到patient_management页面]
2025-05-26 17:46:07 - core.main_window - INFO - 敏感内容已显示，用户已通过身份验证
2025-05-26 17:46:07 - operation - INFO - 用户[admin] 执行操作[用户登录]
2025-05-26 17:46:07 - operation - INFO - 用户[admin] 执行操作[用户登录成功]
2025-05-26 17:46:17 - operation - INFO - 用户[admin] 执行操作[切换到user_management页面]
2025-05-26 17:46:22 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 17:46:22 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 17:46:22 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 17:46:22 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 17:46:22 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 17:46:22 - root - INFO - 开始清理应用程序资源
2025-05-26 17:46:22 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 17:46:22 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 17:46:22 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 17:46:22 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 17:46:22 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 17:46:22 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 17:46:22 - root - INFO - 应用程序资源清理完成
2025-05-26 17:46:22 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 17:47:27 - root - INFO - 日志系统初始化完成
2025-05-26 17:47:27 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 17:47:27 - root - INFO - 系统版本: 1.0.0
2025-05-26 17:47:27 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 17:47:57 - root - INFO - 日志系统初始化完成
2025-05-26 17:47:57 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 17:47:57 - root - INFO - 系统版本: 1.0.0
2025-05-26 17:47:57 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 17:48:46 - root - INFO - 日志系统初始化完成
2025-05-26 17:48:46 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 17:48:46 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 17:48:46 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 17:48:46 - core.auth_manager - INFO - 权限系统初始化完成，当前有 1 个用户
2025-05-26 17:48:46 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 17:48:46 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 17:48:46 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 17:48:46 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 17:48:46 - core.auth_manager - INFO - 密码验证结果: True
2025-05-26 17:48:46 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-26 17:48:46 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 17:49:28 - root - INFO - 日志系统初始化完成
2025-05-26 17:49:28 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 17:49:28 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 17:49:28 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 17:49:28 - core.auth_manager - INFO - 权限系统初始化完成，当前有 1 个用户
2025-05-26 17:49:28 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 17:49:28 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 17:49:28 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 17:49:28 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 17:49:28 - core.auth_manager - INFO - 密码验证结果: True
2025-05-26 17:49:28 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-26 17:49:28 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 17:51:31 - root - INFO - 日志系统初始化完成
2025-05-26 17:52:31 - root - INFO - 日志系统初始化完成
2025-05-26 17:52:31 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 17:52:31 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 17:52:31 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 17:52:31 - core.auth_manager - INFO - 检测到首次安装，创建默认管理员账户
2025-05-26 17:52:31 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 17:52:31 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 17:52:31 - core.auth_manager - INFO - 创建默认管理员账户，密码哈希: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 17:52:31 - core.auth_manager - INFO - 默认管理员账户创建成功: admin
2025-05-26 17:52:31 - core.auth_manager - INFO - 默认密码: admin123 (请登录后立即修改)
2025-05-26 17:52:31 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 17:52:31 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 17:52:31 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 17:52:31 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 17:52:31 - core.auth_manager - INFO - 密码验证结果: True
2025-05-26 17:52:31 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-26 17:52:31 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 17:52:49 - root - INFO - 日志系统初始化完成
2025-05-26 17:52:49 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 17:52:49 - root - INFO - 系统版本: 1.0.0
2025-05-26 17:52:49 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 17:52:49 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 17:52:49 - root - INFO - 应用程序基本设置完成
2025-05-26 17:52:49 - root - WARNING - 启动画面图片不存在
2025-05-26 17:52:49 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 17:52:49 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 17:52:49 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 17:52:49 - root - INFO - 数据库初始化成功
2025-05-26 17:52:49 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 17:52:49 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 17:52:49 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 17:52:49 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 17:52:49 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 17:52:49 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 17:52:49 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 17:52:49 - core.auth_manager - INFO - 权限系统初始化完成，当前有 1 个用户
2025-05-26 17:52:49 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 17:52:49 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 17:52:49 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 17:52:49 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 17:52:49 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 17:52:49 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 17:52:49 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 17:52:49 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 17:52:49 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 17:52:49 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 17:52:49 - root - INFO - 主窗口创建成功
2025-05-26 17:52:49 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 17:52:49 - root - INFO - 主窗口显示成功
2025-05-26 17:52:49 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 17:52:50 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 17:52:51 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 17:52:53 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 17:53:00 - core.auth_manager - INFO - 计算密码哈希: 明文密码=adin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 17:53:00 - core.auth_manager - INFO - 计算结果: 6a3fc29c6f625582a2cc10a5d12c6b349f3c387fbb90669e2eff0e9579372609
2025-05-26 17:53:00 - core.auth_manager - INFO - 验证密码: 输入密码=adin123, 输入密码哈希=6a3fc29c6f625582a2cc10a5d12c6b349f3c387fbb90669e2eff0e9579372609
2025-05-26 17:53:00 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 17:53:00 - core.auth_manager - INFO - 密码验证结果: False
2025-05-26 17:53:00 - core.auth_manager - WARNING - 登录失败：密码错误 - admin
2025-05-26 17:53:12 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 17:53:12 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 17:53:12 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 17:53:12 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 17:53:12 - core.auth_manager - INFO - 密码验证结果: True
2025-05-26 17:53:12 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-26 17:53:12 - operation - INFO - 用户[admin] 执行操作[切换到patient_management页面]
2025-05-26 17:53:12 - core.main_window - INFO - 敏感内容已显示，用户已通过身份验证
2025-05-26 17:53:12 - operation - INFO - 用户[admin] 执行操作[用户登录]
2025-05-26 17:53:12 - operation - INFO - 用户[admin] 执行操作[用户登录成功]
2025-05-26 17:54:15 - operation - INFO - 用户[admin] 执行操作[切换到treatment页面]
2025-05-26 17:54:16 - operation - INFO - 用户[admin] 执行操作[切换到report页面]
2025-05-26 17:54:20 - operation - INFO - 用户[admin] 执行操作[切换到user_management页面]
2025-05-26 17:54:24 - ui.user_management_ui - INFO - 用户列表已刷新，共 1 个用户
2025-05-26 17:54:29 - operation - INFO - 用户[admin] 执行操作[切换到report页面]
2025-05-26 17:54:29 - operation - INFO - 用户[admin] 执行操作[切换到treatment页面]
2025-05-26 17:54:30 - operation - INFO - 用户[admin] 执行操作[切换到patient_management页面]
2025-05-26 17:54:35 - operation - INFO - 用户[admin] 执行操作[切换到treatment页面]
2025-05-26 17:54:36 - operation - INFO - 用户[admin] 执行操作[切换到report页面]
2025-05-26 17:54:40 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 17:54:40 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 17:54:40 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 17:54:40 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 17:54:40 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 17:54:40 - root - INFO - 开始清理应用程序资源
2025-05-26 17:54:40 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 17:54:40 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 17:54:40 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 17:54:40 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 17:54:40 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 17:54:40 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 17:54:40 - root - INFO - 应用程序资源清理完成
2025-05-26 17:54:40 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 18:08:49 - root - INFO - 日志系统初始化完成
2025-05-26 18:08:49 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 18:08:49 - root - INFO - 系统版本: 1.0.0
2025-05-26 18:08:49 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 18:08:49 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 18:08:49 - root - INFO - 应用程序基本设置完成
2025-05-26 18:08:49 - root - WARNING - 启动画面图片不存在
2025-05-26 18:08:49 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 18:08:49 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 18:08:49 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 18:08:49 - root - INFO - 数据库初始化成功
2025-05-26 18:08:49 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 18:08:49 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 18:08:49 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 18:08:49 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 18:08:49 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 18:08:49 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 18:08:49 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 18:08:49 - core.auth_manager - INFO - 权限系统初始化完成，当前有 1 个用户
2025-05-26 18:08:49 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 18:08:49 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 18:08:49 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 18:08:49 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 18:08:49 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 18:08:49 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 18:08:49 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 18:08:49 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 18:08:49 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 18:08:49 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 18:08:49 - root - INFO - 主窗口创建成功
2025-05-26 18:08:49 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 18:08:49 - root - INFO - 主窗口显示成功
2025-05-26 18:08:49 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 18:08:51 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 18:08:52 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 18:08:53 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 18:09:04 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 18:09:04 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 18:09:04 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 18:09:04 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 18:09:04 - core.auth_manager - INFO - 密码验证结果: True
2025-05-26 18:09:04 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-26 18:09:04 - operation - INFO - 用户[admin] 执行操作[切换到patient_management页面]
2025-05-26 18:09:04 - core.main_window - INFO - 敏感内容已显示，用户已通过身份验证
2025-05-26 18:09:04 - operation - INFO - 用户[admin] 执行操作[用户登录]
2025-05-26 18:09:04 - operation - INFO - 用户[admin] 执行操作[用户登录成功]
2025-05-26 18:09:05 - core.performance_optimizer - WARNING - CPU使用率过高: 83.5%
2025-05-26 18:09:07 - operation - INFO - 用户[admin] 执行操作[切换到user_management页面]
2025-05-26 18:09:09 - ui.user_management_ui - INFO - 用户列表已刷新，共 1 个用户
2025-05-26 18:09:24 - core.auth_manager - INFO - 用户已停用 - ID: 2
2025-05-26 18:09:25 - ui.user_management_ui - INFO - 用户列表已刷新，共 1 个用户
2025-05-26 18:09:33 - ui.user_management_ui - INFO - 用户列表已刷新，共 1 个用户
2025-05-26 18:09:36 - core.auth_manager - INFO - 用户登出 - admin
2025-05-26 18:09:36 - operation - INFO - 用户[admin] 执行操作[用户登出]
2025-05-26 18:09:36 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 18:09:46 - core.auth_manager - WARNING - 登录失败：用户不存在 - admin
2025-05-26 18:10:00 - core.auth_manager - WARNING - 登录失败：用户不存在 - admin
2025-05-26 18:10:06 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 18:10:06 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 18:10:06 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 18:10:06 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 18:10:06 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 18:10:06 - root - INFO - 开始清理应用程序资源
2025-05-26 18:10:06 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 18:10:06 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 18:10:06 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 18:10:06 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 18:10:06 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 18:10:06 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 18:10:06 - root - INFO - 应用程序资源清理完成
2025-05-26 18:10:06 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 18:16:56 - root - INFO - 日志系统初始化完成
2025-05-26 18:16:56 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 18:16:56 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 18:16:56 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 18:16:57 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 18:20:06 - root - INFO - 日志系统初始化完成
2025-05-26 18:20:06 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 18:20:06 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 18:20:06 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 18:20:06 - core.auth_manager - INFO - 权限系统初始化完成，当前有 1 个用户
2025-05-26 18:20:06 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 18:20:06 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 18:20:06 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 18:20:06 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 18:20:06 - core.auth_manager - INFO - 密码验证结果: True
2025-05-26 18:20:06 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-26 18:20:06 - core.auth_manager - INFO - 计算密码哈希: 明文密码=password123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 18:20:06 - core.auth_manager - INFO - 计算结果: 4c0594bf8202b74436d18b091c143d7b356f82b8549872917be0202202bea135
2025-05-26 18:20:06 - core.auth_manager - INFO - 用户创建成功 - test_doctor (doctor)
2025-05-26 18:20:06 - core.auth_manager - WARNING - 停用用户失败：不能停用当前登录用户 - ID: 2
2025-05-26 18:20:06 - core.auth_manager - INFO - 用户已停用 - ID: 3
2025-05-26 18:20:06 - core.auth_manager - INFO - 用户已激活 - ID: 3
2025-05-26 18:20:06 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 18:22:16 - root - INFO - 日志系统初始化完成
2025-05-26 18:22:16 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 18:22:16 - root - INFO - 系统版本: 1.0.0
2025-05-26 18:22:16 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 18:22:16 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 18:22:16 - root - INFO - 应用程序基本设置完成
2025-05-26 18:22:16 - root - WARNING - 启动画面图片不存在
2025-05-26 18:22:16 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 18:22:16 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 18:22:16 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 18:22:16 - root - INFO - 数据库初始化成功
2025-05-26 18:22:16 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 18:22:16 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 18:22:16 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 18:22:16 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 18:22:16 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 18:22:16 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 18:22:16 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 18:22:16 - core.auth_manager - INFO - 权限系统初始化完成，当前有 2 个用户
2025-05-26 18:22:16 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 18:22:16 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 18:22:16 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 18:22:16 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 18:22:16 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 18:22:16 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 18:22:16 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 18:22:16 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 18:22:16 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 18:22:16 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 18:22:16 - root - INFO - 主窗口创建成功
2025-05-26 18:22:17 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 18:22:17 - root - INFO - 主窗口显示成功
2025-05-26 18:22:17 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 18:22:18 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 18:22:19 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 18:22:20 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 18:22:25 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 18:22:25 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 18:22:25 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 18:22:25 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 18:22:25 - core.auth_manager - INFO - 密码验证结果: True
2025-05-26 18:22:25 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-26 18:22:25 - operation - INFO - 用户[admin] 执行操作[切换到patient_management页面]
2025-05-26 18:22:25 - core.main_window - INFO - 敏感内容已显示，用户已通过身份验证
2025-05-26 18:22:25 - operation - INFO - 用户[admin] 执行操作[用户登录]
2025-05-26 18:22:25 - operation - INFO - 用户[admin] 执行操作[用户登录成功]
2025-05-26 18:22:30 - operation - INFO - 用户[admin] 执行操作[切换到user_management页面]
2025-05-26 18:22:33 - ui.user_management_ui - INFO - 用户列表已刷新，共 2 个用户
2025-05-26 18:23:02 - operation - INFO - 用户[admin] 执行操作[切换到settings页面]
2025-05-26 18:23:10 - operation - INFO - 用户[admin] 执行操作[切换到user_management页面]
2025-05-26 18:26:33 - core.auth_manager - INFO - 用户已停用 - ID: 3
2025-05-26 18:26:34 - ui.user_management_ui - INFO - 用户列表已刷新，共 2 个用户
2025-05-26 18:26:45 - core.auth_manager - INFO - 用户已激活 - ID: 3
2025-05-26 18:26:46 - ui.user_management_ui - INFO - 用户列表已刷新，共 2 个用户
2025-05-26 18:30:20 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 18:30:20 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 18:30:20 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 18:30:20 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 18:30:20 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 18:30:20 - root - INFO - 开始清理应用程序资源
2025-05-26 18:30:20 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 18:30:20 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 18:30:20 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 18:30:20 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 18:30:20 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 18:30:20 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 18:30:20 - root - INFO - 应用程序资源清理完成
2025-05-26 18:30:20 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 18:34:57 - root - INFO - 日志系统初始化完成
2025-05-26 18:34:57 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 18:34:57 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 18:34:57 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 18:34:57 - core.auth_manager - INFO - 权限系统初始化完成，当前有 2 个用户
2025-05-26 18:34:57 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 18:34:57 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 18:34:57 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 18:34:57 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 18:34:57 - core.auth_manager - INFO - 密码验证结果: True
2025-05-26 18:34:57 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-26 18:34:57 - core.auth_manager - WARNING - 创建用户失败：用户名已存在 - test_doctor
2025-05-26 18:34:57 - core.auth_manager - WARNING - 停用用户失败：不能停用当前登录用户 - ID: 2
2025-05-26 18:34:57 - core.auth_manager - INFO - 用户已停用 - ID: 3
2025-05-26 18:34:57 - core.auth_manager - INFO - 用户已激活 - ID: 3
2025-05-26 18:34:57 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 18:37:01 - root - INFO - 日志系统初始化完成
2025-05-26 18:37:01 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 18:37:01 - root - INFO - 系统版本: 1.0.0
2025-05-26 18:37:01 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 18:37:01 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 18:37:01 - root - INFO - 应用程序基本设置完成
2025-05-26 18:37:01 - root - WARNING - 启动画面图片不存在
2025-05-26 18:37:01 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 18:37:01 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 18:37:01 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 18:37:01 - root - INFO - 数据库初始化成功
2025-05-26 18:37:01 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 18:37:01 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 18:37:01 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 18:37:01 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 18:37:01 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 18:37:01 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 18:37:01 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 18:37:01 - core.auth_manager - INFO - 权限系统初始化完成，当前有 2 个用户
2025-05-26 18:37:01 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 18:37:01 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 18:37:01 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 18:37:01 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 18:37:01 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 18:37:01 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 18:37:01 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 18:37:01 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 18:37:01 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 18:37:01 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 18:37:01 - root - INFO - 主窗口创建成功
2025-05-26 18:37:01 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 18:37:01 - root - INFO - 主窗口显示成功
2025-05-26 18:37:01 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 18:37:02 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 18:37:03 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 18:37:04 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 18:37:07 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 18:37:07 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 18:37:07 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 18:37:07 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 18:37:07 - core.auth_manager - INFO - 密码验证结果: True
2025-05-26 18:37:07 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-26 18:41:17 - root - INFO - 日志系统初始化完成
2025-05-26 18:41:17 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 18:41:17 - root - INFO - 系统版本: 1.0.0
2025-05-26 18:41:17 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 18:41:17 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 18:41:17 - root - INFO - 应用程序基本设置完成
2025-05-26 18:41:17 - root - WARNING - 启动画面图片不存在
2025-05-26 18:41:17 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 18:41:17 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 18:41:17 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 18:41:17 - root - INFO - 数据库初始化成功
2025-05-26 18:41:17 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 18:41:17 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 18:41:17 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 18:41:17 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 18:41:17 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 18:41:17 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 18:41:17 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 18:41:17 - core.auth_manager - INFO - 权限系统初始化完成，当前有 9 个用户
2025-05-26 18:41:17 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 18:41:17 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 18:41:17 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 18:41:17 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 18:41:17 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 18:41:17 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 18:41:17 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 18:41:17 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 18:41:17 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 18:41:17 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 18:41:17 - root - INFO - 主窗口创建成功
2025-05-26 18:41:17 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 18:41:17 - root - INFO - 主窗口显示成功
2025-05-26 18:41:17 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 18:41:18 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 18:41:19 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 18:41:20 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 18:41:28 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 18:41:28 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 18:41:28 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 18:41:28 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 18:41:28 - core.auth_manager - INFO - 密码验证结果: True
2025-05-26 18:41:28 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-26 18:41:28 - operation - INFO - 用户[admin] 执行操作[切换到patient_management页面]
2025-05-26 18:41:28 - core.main_window - INFO - 敏感内容已显示，用户已通过身份验证
2025-05-26 18:41:28 - operation - INFO - 用户[admin] 执行操作[用户登录]
2025-05-26 18:41:28 - operation - INFO - 用户[admin] 执行操作[用户登录成功]
2025-05-26 18:41:32 - core.performance_optimizer - WARNING - CPU使用率过高: 95.9%
2025-05-26 18:41:32 - core.performance_optimizer - INFO - 降低工作线程数到: 7
2025-05-26 18:41:34 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 18:41:34 - operation - INFO - 用户[admin] 执行操作[切换到user_management页面]
2025-05-26 18:41:36 - ui.user_management_ui - INFO - 用户列表已刷新，共 9 个用户
2025-05-26 18:42:44 - operation - INFO - 用户[admin] 执行操作[切换到settings页面]
2025-05-26 18:42:50 - operation - INFO - 用户[admin] 执行操作[切换到patient_management页面]
2025-05-26 18:43:10 - operation - INFO - 用户[admin] 执行操作[切换到treatment页面]
2025-05-26 18:43:38 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 18:43:38 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 18:43:38 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 18:43:38 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 18:43:38 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 18:43:38 - root - INFO - 开始清理应用程序资源
2025-05-26 18:43:38 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 18:43:38 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 18:43:38 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 18:43:38 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 18:43:38 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 18:43:38 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 18:43:38 - root - INFO - 应用程序资源清理完成
2025-05-26 18:43:38 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 18:43:44 - root - INFO - 日志系统初始化完成
2025-05-26 18:43:44 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 18:43:44 - root - INFO - 系统版本: 1.0.0
2025-05-26 18:43:44 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 18:43:44 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 18:43:44 - root - INFO - 应用程序基本设置完成
2025-05-26 18:43:44 - root - WARNING - 启动画面图片不存在
2025-05-26 18:43:44 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 18:43:44 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 18:43:44 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 18:43:44 - root - INFO - 数据库初始化成功
2025-05-26 18:43:44 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 18:43:44 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 18:43:44 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 18:43:44 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 18:43:44 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 18:43:44 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 18:43:44 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 18:43:44 - core.auth_manager - INFO - 权限系统初始化完成，当前有 9 个用户
2025-05-26 18:43:44 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 18:43:44 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 18:43:44 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 18:43:44 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 18:43:44 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 18:43:44 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 18:43:44 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 18:43:44 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 18:43:44 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 18:43:44 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 18:43:44 - root - INFO - 主窗口创建成功
2025-05-26 18:43:44 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 18:43:44 - root - INFO - 主窗口显示成功
2025-05-26 18:43:44 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 18:43:45 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 18:43:46 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 18:43:47 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 18:43:50 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 18:43:50 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 18:43:50 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 18:43:50 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 18:43:50 - core.auth_manager - INFO - 密码验证结果: True
2025-05-26 18:43:50 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-26 18:49:48 - root - INFO - 日志系统初始化完成
2025-05-26 18:49:48 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 18:49:48 - root - INFO - 系统版本: 1.0.0
2025-05-26 18:49:48 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 18:49:48 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 18:49:48 - root - INFO - 应用程序基本设置完成
2025-05-26 18:49:48 - root - WARNING - 启动画面图片不存在
2025-05-26 18:49:48 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 18:49:48 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 18:49:48 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 18:49:48 - root - INFO - 数据库初始化成功
2025-05-26 18:49:48 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 18:49:48 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 18:49:48 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 18:49:48 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 18:49:48 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 18:49:48 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 18:49:48 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 18:49:48 - core.auth_manager - INFO - 权限系统初始化完成，当前有 9 个用户
2025-05-26 18:49:48 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 18:49:48 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 18:49:48 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 18:49:48 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 18:49:48 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 18:49:48 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 18:49:48 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 18:49:48 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 18:49:48 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 18:49:48 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 18:49:48 - root - INFO - 主窗口创建成功
2025-05-26 18:49:48 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 18:49:48 - root - INFO - 主窗口显示成功
2025-05-26 18:49:48 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 18:49:49 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 18:49:50 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 18:49:52 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 18:49:54 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 18:49:54 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 18:49:54 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 18:49:54 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 18:49:54 - core.auth_manager - INFO - 密码验证结果: True
2025-05-26 18:49:54 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-26 18:55:13 - root - INFO - 日志系统初始化完成
2025-05-26 18:55:13 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 18:55:13 - root - INFO - 系统版本: 1.0.0
2025-05-26 18:55:13 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 18:55:13 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 18:55:13 - root - INFO - 应用程序基本设置完成
2025-05-26 18:55:13 - root - WARNING - 启动画面图片不存在
2025-05-26 18:55:13 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 18:55:13 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 18:55:13 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 18:55:13 - root - INFO - 数据库初始化成功
2025-05-26 18:55:14 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 18:55:14 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 18:55:14 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 18:55:14 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 18:55:14 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 18:55:14 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 18:55:14 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 18:55:14 - core.auth_manager - INFO - 权限系统初始化完成，当前有 9 个用户
2025-05-26 18:55:14 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 18:55:14 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 18:55:14 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 18:55:14 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 18:55:14 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 18:55:14 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 18:55:14 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 18:55:14 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 18:55:14 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 18:55:14 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 18:55:14 - root - INFO - 主窗口创建成功
2025-05-26 18:55:14 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 18:55:14 - root - INFO - 主窗口显示成功
2025-05-26 18:55:14 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 18:55:15 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 18:55:16 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 18:55:17 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 18:55:20 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 18:55:20 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 18:55:20 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 18:55:20 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 18:55:20 - core.auth_manager - INFO - 密码验证结果: True
2025-05-26 18:55:20 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-26 18:55:42 - root - INFO - 日志系统初始化完成
2025-05-26 18:55:42 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 18:55:42 - root - INFO - 系统版本: 1.0.0
2025-05-26 18:55:42 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 18:55:42 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 18:55:42 - root - INFO - 应用程序基本设置完成
2025-05-26 18:55:42 - root - WARNING - 启动画面图片不存在
2025-05-26 18:55:42 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 18:55:42 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 18:55:42 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 18:55:42 - root - INFO - 数据库初始化成功
2025-05-26 18:55:42 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 18:55:42 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 18:55:42 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 18:55:42 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 18:55:42 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 18:55:42 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 18:55:42 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 18:55:42 - core.auth_manager - INFO - 权限系统初始化完成，当前有 9 个用户
2025-05-26 18:55:42 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 18:55:42 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 18:55:42 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 18:55:42 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 18:55:42 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 18:55:42 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 18:55:42 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 18:55:42 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 18:55:42 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 18:55:42 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 18:55:42 - root - INFO - 主窗口创建成功
2025-05-26 18:55:42 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 18:55:42 - root - INFO - 主窗口显示成功
2025-05-26 18:55:42 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 18:55:44 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 18:55:45 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 18:55:46 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 18:55:51 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 18:55:51 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 18:55:51 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 18:55:51 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 18:55:51 - core.auth_manager - INFO - 密码验证结果: True
2025-05-26 18:55:51 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-26 18:55:51 - operation - INFO - 用户[admin] 执行操作[切换到patient_management页面]
2025-05-26 18:55:51 - core.main_window - INFO - 敏感内容已显示，用户已通过身份验证
2025-05-26 18:55:51 - operation - INFO - 用户[admin] 执行操作[用户登录]
2025-05-26 18:55:51 - operation - INFO - 用户[admin] 执行操作[用户登录成功]
2025-05-26 18:55:51 - ui.login_dialog - INFO - 检测到admin用户使用默认密码，建议修改密码
2025-05-26 18:55:56 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 18:55:56 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 18:55:56 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 18:55:56 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 18:55:56 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 18:55:56 - root - INFO - 开始清理应用程序资源
2025-05-26 18:55:56 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 18:55:56 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 18:55:56 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 18:55:56 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 18:55:56 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 18:55:56 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 18:55:56 - root - INFO - 应用程序资源清理完成
2025-05-26 18:55:56 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 18:55:59 - root - INFO - 日志系统初始化完成
2025-05-26 18:55:59 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 18:55:59 - root - INFO - 系统版本: 1.0.0
2025-05-26 18:55:59 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 18:55:59 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 18:55:59 - root - INFO - 应用程序基本设置完成
2025-05-26 18:55:59 - root - WARNING - 启动画面图片不存在
2025-05-26 18:55:59 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 18:55:59 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 18:55:59 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 18:55:59 - root - INFO - 数据库初始化成功
2025-05-26 18:55:59 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 18:55:59 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 18:55:59 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 18:55:59 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 18:55:59 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 18:56:00 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 18:56:00 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 18:56:00 - core.auth_manager - INFO - 权限系统初始化完成，当前有 9 个用户
2025-05-26 18:56:00 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 18:56:00 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 18:56:00 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 18:56:00 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 18:56:00 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 18:56:00 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 18:56:00 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 18:56:00 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 18:56:00 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 18:56:00 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 18:56:00 - root - INFO - 主窗口创建成功
2025-05-26 18:56:00 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 18:56:00 - root - INFO - 主窗口显示成功
2025-05-26 18:56:00 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 18:56:01 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 18:56:02 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 18:56:03 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 18:56:14 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 18:56:14 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 18:56:14 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 18:56:14 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 18:56:14 - core.auth_manager - INFO - 密码验证结果: True
2025-05-26 18:56:14 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-26 18:56:14 - operation - INFO - 用户[admin] 执行操作[切换到patient_management页面]
2025-05-26 18:56:14 - core.main_window - INFO - 敏感内容已显示，用户已通过身份验证
2025-05-26 18:56:14 - operation - INFO - 用户[admin] 执行操作[用户登录]
2025-05-26 18:56:14 - operation - INFO - 用户[admin] 执行操作[用户登录成功]
2025-05-26 18:56:14 - ui.login_dialog - INFO - 检测到admin用户使用默认密码，建议修改密码
2025-05-26 18:56:15 - core.performance_optimizer - WARNING - CPU使用率过高: 80.6%
2025-05-26 18:56:17 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 18:56:17 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 18:56:17 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 18:56:17 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 18:56:17 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 18:56:17 - root - INFO - 开始清理应用程序资源
2025-05-26 18:56:17 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 18:56:17 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 18:56:17 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 18:56:17 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 18:56:17 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 18:56:17 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 18:56:17 - root - INFO - 应用程序资源清理完成
2025-05-26 18:56:17 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 18:56:20 - root - INFO - 日志系统初始化完成
2025-05-26 18:56:20 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 18:56:20 - root - INFO - 系统版本: 1.0.0
2025-05-26 18:56:20 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 18:56:20 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 18:56:20 - root - INFO - 应用程序基本设置完成
2025-05-26 18:56:20 - root - WARNING - 启动画面图片不存在
2025-05-26 18:56:20 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 18:56:20 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 18:56:20 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 18:56:20 - root - INFO - 数据库初始化成功
2025-05-26 18:56:20 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 18:56:20 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 18:56:20 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 18:56:20 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 18:56:20 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 18:56:20 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 18:56:20 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 18:56:20 - core.auth_manager - INFO - 权限系统初始化完成，当前有 9 个用户
2025-05-26 18:56:20 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 18:56:20 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 18:56:20 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 18:56:20 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 18:56:20 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 18:56:20 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 18:56:20 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 18:56:20 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 18:56:20 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 18:56:20 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 18:56:20 - root - INFO - 主窗口创建成功
2025-05-26 18:56:20 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 18:56:20 - root - INFO - 主窗口显示成功
2025-05-26 18:56:20 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 18:56:21 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 18:56:23 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 18:56:24 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 18:56:28 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 18:56:28 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 18:56:28 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 18:56:28 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 18:56:28 - core.auth_manager - INFO - 密码验证结果: True
2025-05-26 18:56:28 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-26 18:56:41 - root - INFO - 日志系统初始化完成
2025-05-26 18:56:41 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 18:56:41 - root - INFO - 系统版本: 1.0.0
2025-05-26 18:56:41 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 18:56:41 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 18:56:41 - root - INFO - 应用程序基本设置完成
2025-05-26 18:56:41 - root - WARNING - 启动画面图片不存在
2025-05-26 18:56:41 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 18:56:41 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 18:56:41 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 18:56:41 - root - INFO - 数据库初始化成功
2025-05-26 18:56:41 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 18:56:41 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 18:56:41 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 18:56:41 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 18:56:41 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 18:56:42 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 18:56:42 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 18:56:42 - core.auth_manager - INFO - 权限系统初始化完成，当前有 9 个用户
2025-05-26 18:56:42 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 18:56:42 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 18:56:42 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 18:56:42 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 18:56:42 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 18:56:42 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 18:56:42 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 18:56:42 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 18:56:42 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 18:56:42 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 18:56:42 - root - INFO - 主窗口创建成功
2025-05-26 18:56:42 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 18:56:42 - root - INFO - 主窗口显示成功
2025-05-26 18:56:42 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 18:56:43 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 18:56:44 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 18:56:45 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 18:57:18 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 18:57:18 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 18:57:18 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 18:57:18 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 18:57:18 - core.auth_manager - INFO - 密码验证结果: True
2025-05-26 18:57:18 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-26 18:58:07 - root - INFO - 日志系统初始化完成
2025-05-26 18:58:07 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 18:58:07 - root - INFO - 系统版本: 1.0.0
2025-05-26 18:58:07 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 18:58:07 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 18:58:07 - root - INFO - 应用程序基本设置完成
2025-05-26 18:58:07 - root - WARNING - 启动画面图片不存在
2025-05-26 18:58:07 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 18:58:07 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 18:58:07 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 18:58:07 - root - INFO - 数据库初始化成功
2025-05-26 18:58:07 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 18:58:07 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 18:58:07 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 18:58:07 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 18:58:07 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 18:58:08 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 18:58:08 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 18:58:08 - core.auth_manager - INFO - 权限系统初始化完成，当前有 9 个用户
2025-05-26 18:58:08 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 18:58:08 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 18:58:08 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 18:58:08 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 18:58:08 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 18:58:08 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 18:58:08 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 18:58:08 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 18:58:08 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 18:58:08 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 18:58:08 - root - INFO - 主窗口创建成功
2025-05-26 18:58:08 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 18:58:08 - root - INFO - 主窗口显示成功
2025-05-26 18:58:08 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 18:58:09 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 18:58:10 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 18:58:11 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 18:58:50 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 18:58:50 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 18:58:50 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 18:58:50 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 18:58:50 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 18:58:50 - root - INFO - 开始清理应用程序资源
2025-05-26 18:58:50 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 18:58:50 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 18:58:50 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 18:58:50 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 18:58:50 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 18:58:50 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 18:58:50 - root - INFO - 应用程序资源清理完成
2025-05-26 18:58:50 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 19:01:42 - root - INFO - 日志系统初始化完成
2025-05-26 19:01:42 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 19:01:42 - root - INFO - 系统版本: 1.0.0
2025-05-26 19:01:42 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 19:01:42 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 19:01:42 - root - INFO - 应用程序基本设置完成
2025-05-26 19:01:42 - root - WARNING - 启动画面图片不存在
2025-05-26 19:01:42 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 19:01:42 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 19:01:42 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 19:01:42 - root - INFO - 数据库初始化成功
2025-05-26 19:01:42 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 19:01:42 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 19:01:42 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 19:01:42 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 19:01:42 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 19:01:43 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 19:01:43 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 19:01:43 - core.auth_manager - INFO - 权限系统初始化完成，当前有 9 个用户
2025-05-26 19:01:43 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 19:01:43 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 19:01:43 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 19:01:43 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 19:01:43 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 19:01:43 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 19:01:43 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 19:01:43 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 19:01:43 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 19:01:43 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 19:01:43 - root - INFO - 主窗口创建成功
2025-05-26 19:01:43 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 19:01:43 - root - INFO - 主窗口显示成功
2025-05-26 19:01:43 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 19:01:44 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 19:01:45 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 19:01:46 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 19:01:49 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 19:01:49 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 19:01:49 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 19:01:49 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 19:01:49 - core.auth_manager - INFO - 密码验证结果: True
2025-05-26 19:01:49 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-26 19:02:01 - root - INFO - 日志系统初始化完成
2025-05-26 19:02:01 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 19:02:01 - root - INFO - 系统版本: 1.0.0
2025-05-26 19:02:01 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 19:02:01 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 19:02:01 - root - INFO - 应用程序基本设置完成
2025-05-26 19:02:01 - root - WARNING - 启动画面图片不存在
2025-05-26 19:02:01 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 19:02:01 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 19:02:01 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 19:02:01 - root - INFO - 数据库初始化成功
2025-05-26 19:02:01 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 19:02:01 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 19:02:01 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 19:02:01 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 19:02:01 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 19:02:01 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 19:02:01 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 19:02:01 - core.auth_manager - INFO - 权限系统初始化完成，当前有 9 个用户
2025-05-26 19:02:01 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 19:02:01 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 19:02:01 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 19:02:01 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 19:02:01 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 19:02:01 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 19:02:01 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 19:02:01 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 19:02:01 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 19:02:01 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 19:02:01 - root - INFO - 主窗口创建成功
2025-05-26 19:02:01 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 19:02:01 - root - INFO - 主窗口显示成功
2025-05-26 19:02:01 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 19:02:02 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 19:02:04 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 19:02:05 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 19:02:08 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 19:02:08 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 19:02:08 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 19:02:08 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 19:02:08 - core.auth_manager - INFO - 密码验证结果: True
2025-05-26 19:02:08 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-26 19:02:08 - operation - INFO - 用户[admin] 执行操作[切换到patient_management页面]
2025-05-26 19:02:08 - core.main_window - INFO - 敏感内容已显示，用户已通过身份验证
2025-05-26 19:02:08 - operation - INFO - 用户[admin] 执行操作[用户登录]
2025-05-26 19:02:08 - operation - INFO - 用户[admin] 执行操作[用户登录成功]
2025-05-26 19:02:08 - ui.login_dialog - INFO - 检测到admin用户使用默认密码，建议修改密码
2025-05-26 19:02:13 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 19:02:13 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 19:02:13 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 19:02:13 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 19:02:13 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 19:02:13 - root - INFO - 开始清理应用程序资源
2025-05-26 19:02:13 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 19:02:13 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 19:02:13 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 19:02:13 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 19:02:13 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 19:02:13 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 19:02:13 - root - INFO - 应用程序资源清理完成
2025-05-26 19:02:13 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 19:02:23 - root - INFO - 日志系统初始化完成
2025-05-26 19:02:23 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 19:02:23 - root - INFO - 系统版本: 1.0.0
2025-05-26 19:02:23 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 19:02:23 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 19:02:23 - root - INFO - 应用程序基本设置完成
2025-05-26 19:02:23 - root - WARNING - 启动画面图片不存在
2025-05-26 19:02:23 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 19:02:23 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 19:02:23 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 19:02:23 - root - INFO - 数据库初始化成功
2025-05-26 19:02:23 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 19:02:23 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 19:02:23 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 19:02:23 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 19:02:23 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 19:02:23 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 19:02:23 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 19:02:23 - core.auth_manager - INFO - 权限系统初始化完成，当前有 9 个用户
2025-05-26 19:02:23 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 19:02:23 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 19:02:23 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 19:02:23 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 19:02:23 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 19:02:23 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 19:02:23 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 19:02:23 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 19:02:23 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 19:02:23 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 19:02:23 - root - INFO - 主窗口创建成功
2025-05-26 19:02:24 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 19:02:24 - root - INFO - 主窗口显示成功
2025-05-26 19:02:24 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 19:02:25 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 19:02:26 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 19:02:27 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 19:02:29 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 19:02:29 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 19:02:29 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 19:02:29 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 19:02:29 - core.auth_manager - INFO - 密码验证结果: True
2025-05-26 19:02:29 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-26 19:02:34 - root - INFO - 日志系统初始化完成
2025-05-26 19:02:34 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 19:02:34 - root - INFO - 系统版本: 1.0.0
2025-05-26 19:02:34 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 19:02:34 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 19:02:34 - root - INFO - 应用程序基本设置完成
2025-05-26 19:02:34 - root - WARNING - 启动画面图片不存在
2025-05-26 19:02:34 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 19:02:34 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 19:02:34 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 19:02:34 - root - INFO - 数据库初始化成功
2025-05-26 19:02:34 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 19:02:34 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 19:02:34 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 19:02:34 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 19:02:34 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 19:02:34 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 19:02:34 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 19:02:34 - core.auth_manager - INFO - 权限系统初始化完成，当前有 9 个用户
2025-05-26 19:02:34 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 19:02:34 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 19:02:34 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 19:02:34 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 19:02:34 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 19:02:34 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 19:02:34 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 19:02:34 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 19:02:34 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 19:02:34 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 19:02:34 - root - INFO - 主窗口创建成功
2025-05-26 19:02:34 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 19:02:34 - root - INFO - 主窗口显示成功
2025-05-26 19:02:34 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 19:02:35 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 19:02:36 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 19:02:37 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 19:02:40 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 19:02:40 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 19:02:40 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 19:02:40 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 19:02:40 - core.auth_manager - INFO - 密码验证结果: True
2025-05-26 19:02:40 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-26 19:02:40 - operation - INFO - 用户[admin] 执行操作[切换到patient_management页面]
2025-05-26 19:02:40 - core.main_window - INFO - 敏感内容已显示，用户已通过身份验证
2025-05-26 19:02:40 - operation - INFO - 用户[admin] 执行操作[用户登录]
2025-05-26 19:02:40 - operation - INFO - 用户[admin] 执行操作[用户登录成功]
2025-05-26 19:02:40 - ui.login_dialog - INFO - 检测到admin用户使用默认密码，建议修改密码
2025-05-26 19:02:42 - operation - INFO - 用户[admin] 执行操作[切换到user_management页面]
2025-05-26 19:02:45 - ui.user_management_ui - INFO - 用户列表已刷新，共 9 个用户
2025-05-26 19:02:48 - operation - INFO - 用户[admin] 执行操作[切换到settings页面]
2025-05-26 19:02:55 - operation - INFO - 用户[admin] 执行操作[切换到user_management页面]
2025-05-26 19:03:00 - ui.user_management_ui - INFO - 用户列表已刷新，共 9 个用户
2025-05-26 19:03:19 - operation - INFO - 用户[admin] 执行操作[切换到treatment页面]
2025-05-26 19:03:23 - operation - INFO - 用户[admin] 执行操作[切换到patient_management页面]
2025-05-26 19:03:25 - operation - INFO - 用户[admin] 执行操作[切换到treatment页面]
2025-05-26 19:04:00 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 19:04:00 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 19:04:00 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 19:04:00 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 19:04:00 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 19:04:00 - root - INFO - 开始清理应用程序资源
2025-05-26 19:04:00 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 19:04:00 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 19:04:00 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 19:04:00 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 19:04:00 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 19:04:00 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 19:04:00 - root - INFO - 应用程序资源清理完成
2025-05-26 19:04:00 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 19:04:06 - root - INFO - 日志系统初始化完成
2025-05-26 19:04:06 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 19:04:06 - root - INFO - 系统版本: 1.0.0
2025-05-26 19:04:06 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 19:04:06 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 19:04:06 - root - INFO - 应用程序基本设置完成
2025-05-26 19:04:06 - root - WARNING - 启动画面图片不存在
2025-05-26 19:04:06 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 19:04:06 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 19:04:06 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 19:04:06 - root - INFO - 数据库初始化成功
2025-05-26 19:04:06 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 19:04:06 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 19:04:06 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 19:04:06 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 19:04:06 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 19:04:06 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 19:04:06 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 19:04:06 - core.auth_manager - INFO - 权限系统初始化完成，当前有 9 个用户
2025-05-26 19:04:06 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 19:04:06 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 19:04:06 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 19:04:06 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 19:04:06 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 19:04:06 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 19:04:06 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 19:04:06 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 19:04:06 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 19:04:06 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 19:04:06 - root - INFO - 主窗口创建成功
2025-05-26 19:04:06 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 19:04:06 - root - INFO - 主窗口显示成功
2025-05-26 19:04:06 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 19:04:07 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 19:04:09 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 19:04:10 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 19:04:13 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 19:04:13 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 19:04:13 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 19:04:13 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 19:04:13 - core.auth_manager - INFO - 密码验证结果: True
2025-05-26 19:04:13 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-26 19:08:26 - root - INFO - 日志系统初始化完成
2025-05-26 19:08:26 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 19:08:26 - root - INFO - 系统版本: 1.0.0
2025-05-26 19:08:26 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 19:08:26 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 19:08:26 - root - INFO - 应用程序基本设置完成
2025-05-26 19:08:26 - root - WARNING - 启动画面图片不存在
2025-05-26 19:08:26 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 19:08:26 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 19:08:26 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 19:08:26 - root - INFO - 数据库初始化成功
2025-05-26 19:08:26 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 19:08:26 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 19:08:26 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 19:08:26 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 19:08:26 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 19:08:27 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 19:08:27 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 19:08:27 - core.auth_manager - INFO - 权限系统初始化完成，当前有 9 个用户
2025-05-26 19:08:27 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 19:08:27 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 19:08:27 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 19:08:27 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 19:08:27 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 19:08:27 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 19:08:27 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 19:08:27 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 19:08:27 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 19:08:27 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 19:08:27 - root - INFO - 主窗口创建成功
2025-05-26 19:08:27 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 19:08:27 - root - INFO - 主窗口显示成功
2025-05-26 19:08:27 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 19:08:28 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 19:08:29 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 19:08:30 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 19:08:35 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 19:08:35 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 19:08:35 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 19:08:35 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 19:08:35 - core.auth_manager - INFO - 密码验证结果: True
2025-05-26 19:08:35 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-26 19:09:25 - root - INFO - 日志系统初始化完成
2025-05-26 19:09:25 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 19:09:25 - root - INFO - 系统版本: 1.0.0
2025-05-26 19:09:25 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 19:09:25 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 19:09:25 - root - INFO - 应用程序基本设置完成
2025-05-26 19:09:25 - root - WARNING - 启动画面图片不存在
2025-05-26 19:09:25 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 19:09:25 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 19:09:25 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 19:09:25 - root - INFO - 数据库初始化成功
2025-05-26 19:09:25 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 19:09:25 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 19:09:25 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 19:09:25 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 19:09:25 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 19:09:25 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 19:09:25 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 19:09:25 - core.auth_manager - INFO - 权限系统初始化完成，当前有 9 个用户
2025-05-26 19:09:25 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 19:09:25 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 19:09:25 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 19:09:25 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 19:09:25 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 19:09:25 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 19:09:25 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 19:09:25 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 19:09:25 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 19:09:25 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 19:09:25 - root - INFO - 主窗口创建成功
2025-05-26 19:09:26 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 19:09:26 - root - INFO - 主窗口显示成功
2025-05-26 19:09:26 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 19:09:27 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 19:09:28 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 19:09:29 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 19:09:51 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 19:09:51 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 19:09:51 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 19:09:51 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 19:09:51 - core.auth_manager - INFO - 密码验证结果: True
2025-05-26 19:09:51 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-26 19:10:12 - root - INFO - 日志系统初始化完成
2025-05-26 19:10:12 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 19:10:12 - root - INFO - 系统版本: 1.0.0
2025-05-26 19:10:12 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 19:10:12 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 19:10:12 - root - INFO - 应用程序基本设置完成
2025-05-26 19:10:12 - root - WARNING - 启动画面图片不存在
2025-05-26 19:10:12 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 19:10:12 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 19:10:12 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 19:10:12 - root - INFO - 数据库初始化成功
2025-05-26 19:10:12 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 19:10:12 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 19:10:12 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 19:10:12 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 19:10:12 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 19:10:12 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 19:10:12 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 19:10:12 - core.auth_manager - INFO - 权限系统初始化完成，当前有 9 个用户
2025-05-26 19:10:12 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 19:10:12 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 19:10:12 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 19:10:12 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 19:10:12 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 19:10:12 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 19:10:12 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 19:10:12 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 19:10:12 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 19:10:12 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 19:10:12 - root - INFO - 主窗口创建成功
2025-05-26 19:10:12 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 19:10:12 - root - INFO - 主窗口显示成功
2025-05-26 19:10:12 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 19:10:13 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 19:10:14 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 19:10:16 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 19:12:27 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 19:12:27 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 19:12:27 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 19:12:27 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 19:12:27 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 19:12:27 - root - INFO - 开始清理应用程序资源
2025-05-26 19:12:27 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 19:12:27 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 19:12:27 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 19:12:27 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 19:12:27 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 19:12:27 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 19:12:27 - root - INFO - 应用程序资源清理完成
2025-05-26 19:12:27 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 19:17:54 - root - INFO - 日志系统初始化完成
2025-05-26 19:17:54 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 19:17:54 - root - INFO - 系统版本: 1.0.0
2025-05-26 19:17:54 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 19:18:23 - root - INFO - 日志系统初始化完成
2025-05-26 19:18:23 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 19:18:23 - root - INFO - 系统版本: 1.0.0
2025-05-26 19:18:23 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 19:19:35 - root - INFO - 日志系统初始化完成
2025-05-26 19:19:35 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 19:19:35 - root - INFO - 系统版本: 1.0.0
2025-05-26 19:19:35 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 19:19:58 - root - INFO - 日志系统初始化完成
2025-05-26 19:19:58 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 19:19:58 - root - INFO - 系统版本: 1.0.0
2025-05-26 19:19:58 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 19:21:04 - root - INFO - 日志系统初始化完成
2025-05-26 19:21:04 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 19:21:04 - root - INFO - 系统版本: 1.0.0
2025-05-26 19:21:04 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 19:21:04 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 19:21:04 - root - INFO - 应用程序基本设置完成
2025-05-26 19:21:04 - root - WARNING - 启动画面图片不存在
2025-05-26 19:21:04 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 19:21:04 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 19:21:04 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 19:21:04 - root - INFO - 数据库初始化成功
2025-05-26 19:21:04 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 19:21:04 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 19:21:04 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 19:21:04 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 19:21:04 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 19:21:04 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 19:21:04 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 19:21:04 - core.auth_manager - INFO - 权限系统初始化完成，当前有 9 个用户
2025-05-26 19:21:04 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 19:21:04 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 19:21:04 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 19:21:04 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 19:21:04 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 19:21:04 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 19:21:04 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 19:21:04 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 19:21:04 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 19:21:04 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 19:21:04 - root - INFO - 主窗口创建成功
2025-05-26 19:21:04 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 19:21:05 - root - INFO - 主窗口显示成功
2025-05-26 19:21:05 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 19:21:06 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 19:21:07 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 19:21:08 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 19:21:14 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 19:21:14 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 19:21:14 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 19:21:14 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 19:21:14 - core.auth_manager - INFO - 密码验证结果: True
2025-05-26 19:21:14 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-26 19:21:14 - ui.login_dialog - INFO - 检测到admin用户使用默认密码，建议修改密码
2025-05-26 19:21:14 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 19:21:14 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 19:21:14 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 19:21:14 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 19:21:14 - core.auth_manager - INFO - 密码验证结果: True
2025-05-26 19:21:14 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-26 19:21:14 - ui.login_dialog - INFO - 检测到admin用户使用默认密码，建议修改密码
2025-05-26 19:21:14 - operation - INFO - 用户[admin] 执行操作[切换到patient_management页面]
2025-05-26 19:21:14 - core.main_window - INFO - 敏感内容已显示，用户已通过身份验证
2025-05-26 19:21:14 - operation - INFO - 用户[admin] 执行操作[用户登录成功]
2025-05-26 19:21:14 - operation - INFO - 用户[admin] 执行操作[切换到patient_management页面]
2025-05-26 19:21:14 - core.main_window - INFO - 敏感内容已显示，用户已通过身份验证
2025-05-26 19:21:14 - operation - INFO - 用户[admin] 执行操作[用户登录成功]
2025-05-26 19:21:14 - operation - INFO - 用户[admin] 执行操作[用户登录]
2025-05-26 19:21:14 - operation - INFO - 用户[admin] 执行操作[用户登录]
2025-05-26 19:21:46 - operation - INFO - 用户[admin] 执行操作[切换到user_management页面]
2025-05-26 19:21:48 - ui.user_management_ui - INFO - 用户列表已刷新，共 9 个用户
2025-05-26 19:22:27 - operation - INFO - 用户[admin] 执行操作[切换到treatment页面]
2025-05-26 19:23:32 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 19:23:32 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 19:23:32 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 19:23:32 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 19:23:32 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 19:23:32 - root - INFO - 开始清理应用程序资源
2025-05-26 19:23:32 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 19:23:32 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 19:23:32 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 19:23:32 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 19:23:32 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 19:23:32 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 19:23:32 - root - INFO - 应用程序资源清理完成
2025-05-26 19:23:32 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 19:26:33 - root - INFO - 日志系统初始化完成
2025-05-26 19:26:33 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 19:26:33 - root - INFO - 系统版本: 1.0.0
2025-05-26 19:26:33 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 19:26:40 - root - INFO - 日志系统初始化完成
2025-05-26 19:26:40 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 19:26:40 - root - INFO - 系统版本: 1.0.0
2025-05-26 19:26:40 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 19:26:40 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 19:26:40 - root - INFO - 应用程序基本设置完成
2025-05-26 19:26:40 - root - WARNING - 启动画面图片不存在
2025-05-26 19:26:40 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 19:26:40 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 19:26:40 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 19:26:40 - root - INFO - 数据库初始化成功
2025-05-26 19:26:40 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 19:26:40 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 19:26:40 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 19:26:40 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 19:26:40 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 19:26:40 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 19:26:40 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 19:26:40 - core.auth_manager - INFO - 权限系统初始化完成，当前有 9 个用户
2025-05-26 19:26:40 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 19:26:40 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 19:26:40 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 19:26:40 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 19:26:40 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 19:26:40 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 19:26:40 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 19:26:40 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 19:26:40 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 19:26:40 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 19:26:40 - root - INFO - 主窗口创建成功
2025-05-26 19:26:40 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 19:26:40 - root - INFO - 主窗口显示成功
2025-05-26 19:26:40 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 19:26:42 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 19:26:43 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 19:26:44 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 19:26:55 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 19:26:55 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 19:26:55 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 19:26:55 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 19:26:55 - core.auth_manager - INFO - 密码验证结果: True
2025-05-26 19:26:55 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-26 19:26:55 - ui.login_dialog - INFO - 检测到admin用户使用默认密码，建议修改密码
2025-05-26 19:26:55 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 19:26:55 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 19:26:55 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 19:26:55 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 19:26:55 - core.auth_manager - INFO - 密码验证结果: True
2025-05-26 19:26:55 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-26 19:26:55 - ui.login_dialog - INFO - 检测到admin用户使用默认密码，建议修改密码
2025-05-26 19:26:55 - operation - INFO - 用户[admin] 执行操作[切换到patient_management页面]
2025-05-26 19:26:55 - core.main_window - INFO - 敏感内容已显示，用户已通过身份验证
2025-05-26 19:26:55 - operation - INFO - 用户[admin] 执行操作[用户登录成功]
2025-05-26 19:26:55 - operation - INFO - 用户[admin] 执行操作[切换到patient_management页面]
2025-05-26 19:26:55 - core.main_window - INFO - 敏感内容已显示，用户已通过身份验证
2025-05-26 19:26:55 - operation - INFO - 用户[admin] 执行操作[用户登录成功]
2025-05-26 19:26:56 - operation - INFO - 用户[admin] 执行操作[用户登录]
2025-05-26 19:26:56 - operation - INFO - 用户[admin] 执行操作[用户登录]
2025-05-26 19:26:56 - core.performance_optimizer - WARNING - CPU使用率过高: 100.0%
2025-05-26 19:26:56 - core.performance_optimizer - INFO - 降低工作线程数到: 7
2025-05-26 19:26:57 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 19:27:04 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 19:27:04 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 19:27:04 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 19:27:04 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 19:27:04 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 19:27:04 - root - INFO - 开始清理应用程序资源
2025-05-26 19:27:04 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 19:27:04 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 19:27:04 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 19:27:04 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 19:27:04 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 19:27:04 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 19:27:04 - root - INFO - 应用程序资源清理完成
2025-05-26 19:27:04 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 19:27:46 - root - INFO - 日志系统初始化完成
2025-05-26 19:27:46 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 19:27:46 - root - INFO - 系统版本: 1.0.0
2025-05-26 19:27:46 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 19:27:56 - root - INFO - 日志系统初始化完成
2025-05-26 19:27:56 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 19:27:56 - root - INFO - 系统版本: 1.0.0
2025-05-26 19:27:56 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 19:29:06 - root - INFO - 日志系统初始化完成
2025-05-26 19:29:06 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 19:29:06 - root - INFO - 系统版本: 1.0.0
2025-05-26 19:29:06 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 19:29:32 - root - INFO - 日志系统初始化完成
2025-05-26 19:29:32 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 19:29:32 - root - INFO - 系统版本: 1.0.0
2025-05-26 19:29:32 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 19:29:32 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 19:29:32 - root - INFO - 应用程序基本设置完成
2025-05-26 19:29:32 - root - WARNING - 启动画面图片不存在
2025-05-26 19:29:32 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 19:29:32 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 19:29:32 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 19:29:32 - root - INFO - 数据库初始化成功
2025-05-26 19:29:32 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 19:29:32 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 19:29:32 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 19:29:32 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 19:29:32 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 19:29:32 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 19:29:32 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 19:29:32 - core.auth_manager - INFO - 权限系统初始化完成，当前有 9 个用户
2025-05-26 19:29:32 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 19:29:32 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 19:29:32 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 19:29:32 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 19:29:32 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 19:29:32 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 19:29:32 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 19:29:32 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 19:29:32 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 19:29:32 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 19:29:32 - root - INFO - 主窗口创建成功
2025-05-26 19:29:32 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 19:29:32 - root - INFO - 主窗口显示成功
2025-05-26 19:29:32 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 19:29:33 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 19:29:34 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 19:29:35 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 19:29:38 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 19:29:38 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 19:29:38 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 19:29:38 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 19:29:38 - core.auth_manager - INFO - 密码验证结果: True
2025-05-26 19:29:38 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-26 19:29:38 - ui.login_dialog - INFO - 检测到admin用户使用默认密码，建议修改密码
2025-05-26 19:29:38 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 19:29:38 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 19:29:38 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 19:29:38 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 19:29:38 - core.auth_manager - INFO - 密码验证结果: True
2025-05-26 19:29:38 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-26 19:29:38 - ui.login_dialog - INFO - 检测到admin用户使用默认密码，建议修改密码
2025-05-26 19:29:38 - operation - INFO - 用户[admin] 执行操作[切换到patient_management页面]
2025-05-26 19:29:38 - core.main_window - INFO - 敏感内容已显示，用户已通过身份验证
2025-05-26 19:29:38 - operation - INFO - 用户[admin] 执行操作[用户登录成功]
2025-05-26 19:29:38 - operation - INFO - 用户[admin] 执行操作[切换到patient_management页面]
2025-05-26 19:29:38 - core.main_window - INFO - 敏感内容已显示，用户已通过身份验证
2025-05-26 19:29:38 - operation - INFO - 用户[admin] 执行操作[用户登录成功]
2025-05-26 19:29:38 - operation - INFO - 用户[admin] 执行操作[用户登录]
2025-05-26 19:29:38 - operation - INFO - 用户[admin] 执行操作[用户登录]
2025-05-26 19:29:43 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 19:29:43 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 19:29:43 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 19:29:43 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 19:29:43 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 19:29:43 - root - INFO - 开始清理应用程序资源
2025-05-26 19:29:43 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 19:29:43 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 19:29:43 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 19:29:43 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 19:29:43 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 19:29:43 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 19:29:43 - root - INFO - 应用程序资源清理完成
2025-05-26 19:29:43 - root - INFO - === NK脑机接口系统退出 ===
2025-05-26 21:23:47 - root - INFO - 日志系统初始化完成
2025-05-26 21:23:47 - root - INFO - === NK脑机接口系统启动 ===
2025-05-26 21:23:47 - root - INFO - 系统版本: 1.0.0
2025-05-26 21:23:47 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-26 21:23:47 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-26 21:23:47 - root - INFO - 应用程序基本设置完成
2025-05-26 21:23:47 - root - WARNING - 启动画面图片不存在
2025-05-26 21:23:47 - core.database_manager - INFO - 数据库表创建成功
2025-05-26 21:23:47 - core.database_manager - INFO - 基础数据初始化成功
2025-05-26 21:23:47 - core.database_manager - INFO - 数据库初始化成功
2025-05-26 21:23:47 - root - INFO - 数据库初始化成功
2025-05-26 21:23:47 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-26 21:23:47 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-26 21:23:47 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-26 21:23:47 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-26 21:23:47 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-26 21:23:47 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-26 21:23:47 - core.main_window - INFO - 主窗口初始化完成
2025-05-26 21:23:47 - core.auth_manager - INFO - 权限系统初始化完成，当前有 9 个用户
2025-05-26 21:23:47 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-26 21:23:47 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-26 21:23:47 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-26 21:23:47 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-26 21:23:47 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-26 21:23:47 - core.main_window - INFO - 数据库管理器设置完成
2025-05-26 21:23:47 - core.main_window - INFO - 权限管理器设置完成
2025-05-26 21:23:47 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-26 21:23:47 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-26 21:23:47 - core.main_window - INFO - 性能优化器设置完成
2025-05-26 21:23:47 - root - INFO - 主窗口创建成功
2025-05-26 21:23:47 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-26 21:23:47 - root - INFO - 主窗口显示成功
2025-05-26 21:23:47 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-26 21:23:49 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-26 21:23:50 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-26 21:23:51 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-26 21:23:58 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin1123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 21:23:58 - core.auth_manager - INFO - 计算结果: 80bb2ca19441364158ae11a81fc5d8250e2579c4115f163c98a8698c589d1ba2
2025-05-26 21:23:58 - core.auth_manager - INFO - 验证密码: 输入密码=admin1123, 输入密码哈希=80bb2ca19441364158ae11a81fc5d8250e2579c4115f163c98a8698c589d1ba2
2025-05-26 21:23:58 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 21:23:58 - core.auth_manager - INFO - 密码验证结果: False
2025-05-26 21:23:58 - core.auth_manager - WARNING - 登录失败：密码错误 - admin
2025-05-26 21:24:04 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 21:24:04 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 21:24:04 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 21:24:04 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 21:24:04 - core.auth_manager - INFO - 密码验证结果: True
2025-05-26 21:24:04 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-26 21:24:04 - ui.login_dialog - INFO - 检测到admin用户使用默认密码，建议修改密码
2025-05-26 21:24:04 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-26 21:24:04 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 21:24:04 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 21:24:04 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-26 21:24:04 - core.auth_manager - INFO - 密码验证结果: True
2025-05-26 21:24:04 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-26 21:24:04 - ui.login_dialog - INFO - 检测到admin用户使用默认密码，建议修改密码
2025-05-26 21:24:04 - operation - INFO - 用户[admin] 执行操作[切换到patient_management页面]
2025-05-26 21:24:04 - core.main_window - INFO - 敏感内容已显示，用户已通过身份验证
2025-05-26 21:24:04 - operation - INFO - 用户[admin] 执行操作[用户登录成功]
2025-05-26 21:24:04 - operation - INFO - 用户[admin] 执行操作[切换到patient_management页面]
2025-05-26 21:24:04 - core.main_window - INFO - 敏感内容已显示，用户已通过身份验证
2025-05-26 21:24:04 - operation - INFO - 用户[admin] 执行操作[用户登录成功]
2025-05-26 21:24:05 - operation - INFO - 用户[admin] 执行操作[用户登录]
2025-05-26 21:24:05 - operation - INFO - 用户[admin] 执行操作[用户登录]
2025-05-26 21:24:08 - operation - INFO - 用户[admin] 执行操作[切换到treatment页面]
2025-05-26 21:24:10 - operation - INFO - 用户[admin] 执行操作[切换到user_management页面]
2025-05-26 21:24:12 - operation - INFO - 用户[admin] 执行操作[切换到settings页面]
2025-05-26 21:24:13 - operation - INFO - 用户[admin] 执行操作[切换到user_management页面]
2025-05-26 21:24:15 - ui.user_management_ui - INFO - 用户列表已刷新，共 9 个用户
2025-05-26 21:24:24 - operation - INFO - 用户[admin] 执行操作[切换到settings页面]
2025-05-26 21:24:34 - operation - INFO - 用户[admin] 执行操作[切换到patient_management页面]
2025-05-26 21:24:43 - operation - INFO - 用户[admin] 执行操作[切换到treatment页面]
2025-05-26 21:37:02 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 21:37:02 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 21:37:02 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 21:37:02 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 21:37:02 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 21:37:02 - root - INFO - 开始清理应用程序资源
2025-05-26 21:37:02 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-26 21:37:02 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-26 21:37:02 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-26 21:37:02 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-26 21:37:02 - core.main_window - INFO - 主窗口资源清理完成
2025-05-26 21:37:02 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-26 21:37:02 - root - INFO - 应用程序资源清理完成
2025-05-26 21:37:02 - root - INFO - === NK脑机接口系统退出 ===
2025-05-27 07:33:46 - root - INFO - 日志系统初始化完成
2025-05-27 07:33:46 - root - INFO - === NK脑机接口系统启动 ===
2025-05-27 07:33:46 - root - INFO - 系统版本: 1.0.0
2025-05-27 07:33:46 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-27 07:33:46 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-27 07:33:46 - root - INFO - 应用程序基本设置完成
2025-05-27 07:33:46 - root - WARNING - 启动画面图片不存在
2025-05-27 07:33:46 - core.database_manager - INFO - 数据库表创建成功
2025-05-27 07:33:46 - core.database_manager - INFO - 基础数据初始化成功
2025-05-27 07:33:46 - core.database_manager - INFO - 数据库初始化成功
2025-05-27 07:33:46 - root - INFO - 数据库初始化成功
2025-05-27 07:33:46 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-27 07:33:46 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-27 07:33:46 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-27 07:33:46 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-27 07:33:46 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-27 07:33:46 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-27 07:33:46 - core.main_window - INFO - 主窗口初始化完成
2025-05-27 07:33:46 - core.auth_manager - INFO - 权限系统初始化完成，当前有 9 个用户
2025-05-27 07:33:46 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-27 07:33:46 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-27 07:33:46 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-27 07:33:46 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-27 07:33:46 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-27 07:33:46 - core.main_window - INFO - 数据库管理器设置完成
2025-05-27 07:33:46 - core.main_window - INFO - 权限管理器设置完成
2025-05-27 07:33:46 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-27 07:33:46 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-27 07:33:46 - core.main_window - INFO - 性能优化器设置完成
2025-05-27 07:33:46 - root - INFO - 主窗口创建成功
2025-05-27 07:33:47 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-27 07:33:47 - root - INFO - 主窗口显示成功
2025-05-27 07:33:47 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-27 07:33:48 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-27 07:33:49 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-27 07:33:50 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-27 07:33:50 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-27 07:33:50 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-27 07:33:50 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-27 07:33:50 - core.main_window - INFO - 主窗口资源清理完成
2025-05-27 07:33:50 - root - INFO - 开始清理应用程序资源
2025-05-27 07:33:50 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-27 07:33:50 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-27 07:33:50 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-27 07:33:50 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-27 07:33:50 - core.main_window - INFO - 主窗口资源清理完成
2025-05-27 07:33:50 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-27 07:33:50 - root - INFO - 应用程序资源清理完成
2025-05-27 07:33:50 - root - INFO - === NK脑机接口系统退出 ===
2025-05-27 07:51:10 - root - INFO - 日志系统初始化完成
2025-05-27 07:51:10 - root - INFO - === NK脑机接口系统启动 ===
2025-05-27 07:51:10 - root - INFO - 系统版本: 1.0.0
2025-05-27 07:51:10 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-27 07:51:10 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-27 07:51:10 - root - INFO - 应用程序基本设置完成
2025-05-27 07:51:10 - root - WARNING - 启动画面图片不存在
2025-05-27 07:51:10 - core.database_manager - INFO - 数据库表创建成功
2025-05-27 07:51:10 - core.database_manager - INFO - 基础数据初始化成功
2025-05-27 07:51:10 - core.database_manager - INFO - 数据库初始化成功
2025-05-27 07:51:10 - root - INFO - 数据库初始化成功
2025-05-27 07:51:10 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-27 07:51:10 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-27 07:51:10 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-27 07:51:10 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-27 07:51:10 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-27 07:51:11 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-27 07:51:11 - core.main_window - INFO - 主窗口初始化完成
2025-05-27 07:51:11 - core.auth_manager - INFO - 权限系统初始化完成，当前有 9 个用户
2025-05-27 07:51:11 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-27 07:51:11 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-27 07:51:11 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-27 07:51:11 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-27 07:51:11 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-27 07:51:11 - core.main_window - INFO - 数据库管理器设置完成
2025-05-27 07:51:11 - core.main_window - INFO - 权限管理器设置完成
2025-05-27 07:51:11 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-27 07:51:11 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-27 07:51:11 - core.main_window - INFO - 性能优化器设置完成
2025-05-27 07:51:11 - root - INFO - 主窗口创建成功
2025-05-27 07:51:11 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-27 07:51:11 - root - INFO - 主窗口显示成功
2025-05-27 07:51:11 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-27 07:51:12 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-27 07:51:13 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-27 07:51:14 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-27 07:51:14 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-27 07:51:14 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-27 07:51:14 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-27 07:51:14 - core.main_window - INFO - 主窗口资源清理完成
2025-05-27 07:51:14 - root - INFO - 开始清理应用程序资源
2025-05-27 07:51:14 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-27 07:51:14 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-27 07:51:14 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-27 07:51:14 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-27 07:51:14 - core.main_window - INFO - 主窗口资源清理完成
2025-05-27 07:51:14 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-27 07:51:14 - root - INFO - 应用程序资源清理完成
2025-05-27 07:51:14 - root - INFO - === NK脑机接口系统退出 ===
2025-05-27 07:51:26 - root - INFO - 日志系统初始化完成
2025-05-27 07:51:26 - root - INFO - === NK脑机接口系统启动 ===
2025-05-27 07:51:26 - root - INFO - 系统版本: 1.0.0
2025-05-27 07:51:26 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-27 07:51:26 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-27 07:51:26 - root - INFO - 应用程序基本设置完成
2025-05-27 07:51:26 - root - WARNING - 启动画面图片不存在
2025-05-27 07:51:26 - core.database_manager - INFO - 数据库表创建成功
2025-05-27 07:51:26 - core.database_manager - INFO - 基础数据初始化成功
2025-05-27 07:51:26 - core.database_manager - INFO - 数据库初始化成功
2025-05-27 07:51:26 - root - INFO - 数据库初始化成功
2025-05-27 07:51:26 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-27 07:51:26 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-27 07:51:26 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-27 07:51:26 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-27 07:51:26 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-27 07:51:27 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-27 07:51:27 - core.main_window - INFO - 主窗口初始化完成
2025-05-27 07:51:27 - core.auth_manager - INFO - 权限系统初始化完成，当前有 9 个用户
2025-05-27 07:51:27 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-27 07:51:27 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-27 07:51:27 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-27 07:51:27 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-27 07:51:27 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-27 07:51:27 - core.main_window - INFO - 数据库管理器设置完成
2025-05-27 07:51:27 - core.main_window - INFO - 权限管理器设置完成
2025-05-27 07:51:27 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-27 07:51:27 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-27 07:51:27 - core.main_window - INFO - 性能优化器设置完成
2025-05-27 07:51:27 - root - INFO - 主窗口创建成功
2025-05-27 07:51:27 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-27 07:51:27 - root - INFO - 主窗口显示成功
2025-05-27 07:51:27 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-27 07:51:28 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-27 07:51:29 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-27 07:51:30 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-27 07:51:33 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-27 07:51:33 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-27 07:51:33 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-27 07:51:33 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-27 07:51:33 - core.auth_manager - INFO - 密码验证结果: True
2025-05-27 07:51:33 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-27 07:51:33 - ui.login_dialog - INFO - 检测到admin用户使用默认密码，建议修改密码
2025-05-27 07:51:33 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-27 07:51:33 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-27 07:51:33 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-27 07:51:33 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-27 07:51:33 - core.auth_manager - INFO - 密码验证结果: True
2025-05-27 07:51:33 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-27 07:51:33 - ui.login_dialog - INFO - 检测到admin用户使用默认密码，建议修改密码
2025-05-27 07:51:34 - operation - INFO - 用户[admin] 执行操作[切换到patient_management页面]
2025-05-27 07:51:34 - core.main_window - INFO - 敏感内容已显示，用户已通过身份验证
2025-05-27 07:51:34 - operation - INFO - 用户[admin] 执行操作[用户登录成功]
2025-05-27 07:51:34 - operation - INFO - 用户[admin] 执行操作[切换到patient_management页面]
2025-05-27 07:51:34 - core.main_window - INFO - 敏感内容已显示，用户已通过身份验证
2025-05-27 07:51:34 - operation - INFO - 用户[admin] 执行操作[用户登录成功]
2025-05-27 07:51:34 - operation - INFO - 用户[admin] 执行操作[用户登录]
2025-05-27 07:51:34 - operation - INFO - 用户[admin] 执行操作[用户登录]
2025-05-27 07:51:38 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-27 07:51:38 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-27 07:51:38 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-27 07:51:38 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-27 07:51:38 - core.main_window - INFO - 主窗口资源清理完成
2025-05-27 07:51:38 - root - INFO - 开始清理应用程序资源
2025-05-27 07:51:38 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-27 07:51:38 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-27 07:51:38 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-27 07:51:38 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-27 07:51:38 - core.main_window - INFO - 主窗口资源清理完成
2025-05-27 07:51:38 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-27 07:51:38 - root - INFO - 应用程序资源清理完成
2025-05-27 07:51:38 - root - INFO - === NK脑机接口系统退出 ===
2025-05-27 08:01:27 - root - INFO - 日志系统初始化完成
2025-05-27 08:01:27 - root - INFO - === NK脑机接口系统启动 ===
2025-05-27 08:01:27 - root - INFO - 系统版本: 1.0.0
2025-05-27 08:01:27 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-27 08:01:27 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-27 08:01:27 - root - INFO - 应用程序基本设置完成
2025-05-27 08:01:27 - root - WARNING - 启动画面图片不存在
2025-05-27 08:01:27 - core.database_manager - INFO - 数据库表创建成功
2025-05-27 08:01:27 - core.database_manager - INFO - 基础数据初始化成功
2025-05-27 08:01:27 - core.database_manager - INFO - 数据库初始化成功
2025-05-27 08:01:27 - root - INFO - 数据库初始化成功
2025-05-27 08:01:27 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-27 08:01:27 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-27 08:01:27 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-27 08:01:27 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-27 08:01:27 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-27 08:01:27 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-27 08:01:27 - core.main_window - INFO - 主窗口初始化完成
2025-05-27 08:01:27 - core.auth_manager - INFO - 权限系统初始化完成，当前有 9 个用户
2025-05-27 08:01:27 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-27 08:01:27 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-27 08:01:27 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-27 08:01:27 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-27 08:01:27 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-27 08:01:27 - core.main_window - INFO - 数据库管理器设置完成
2025-05-27 08:01:27 - core.main_window - INFO - 权限管理器设置完成
2025-05-27 08:01:27 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-27 08:01:27 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-27 08:01:27 - core.main_window - INFO - 性能优化器设置完成
2025-05-27 08:01:27 - root - INFO - 主窗口创建成功
2025-05-27 08:01:27 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-27 08:01:27 - root - INFO - 主窗口显示成功
2025-05-27 08:01:27 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-27 08:01:28 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-27 08:01:29 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-27 08:01:31 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-27 08:01:43 - core.performance_optimizer - WARNING - CPU使用率过高: 100.0%
2025-05-27 08:01:43 - core.performance_optimizer - INFO - 降低工作线程数到: 7
2025-05-27 08:01:44 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-27 08:04:14 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-27 08:04:14 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-27 08:04:14 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-27 08:04:14 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-27 08:04:14 - core.main_window - INFO - 主窗口资源清理完成
2025-05-27 08:04:14 - root - INFO - 开始清理应用程序资源
2025-05-27 08:04:14 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-27 08:04:14 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-27 08:04:14 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-27 08:04:14 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-27 08:04:14 - core.main_window - INFO - 主窗口资源清理完成
2025-05-27 08:04:14 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-27 08:04:14 - root - INFO - 应用程序资源清理完成
2025-05-27 08:04:14 - root - INFO - === NK脑机接口系统退出 ===
2025-05-27 08:04:43 - root - INFO - 日志系统初始化完成
2025-05-27 08:04:43 - root - INFO - === NK脑机接口系统启动 ===
2025-05-27 08:04:43 - root - INFO - 系统版本: 1.0.0
2025-05-27 08:04:43 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-27 08:12:49 - root - INFO - 日志系统初始化完成
2025-05-27 08:12:49 - root - INFO - === NK脑机接口系统启动 ===
2025-05-27 08:12:49 - root - INFO - 系统版本: 1.0.0
2025-05-27 08:12:49 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-27 08:12:49 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-27 08:12:49 - root - INFO - 应用程序基本设置完成
2025-05-27 08:12:49 - root - WARNING - 启动画面图片不存在
2025-05-27 08:12:49 - core.database_manager - INFO - 数据库表创建成功
2025-05-27 08:12:49 - core.database_manager - INFO - 基础数据初始化成功
2025-05-27 08:12:49 - core.database_manager - INFO - 数据库初始化成功
2025-05-27 08:12:49 - root - INFO - 数据库初始化成功
2025-05-27 08:12:49 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-27 08:12:49 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-27 08:12:49 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-27 08:12:49 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-27 08:12:49 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-27 08:12:49 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-27 08:12:49 - core.main_window - INFO - 主窗口初始化完成
2025-05-27 08:12:49 - core.auth_manager - INFO - 权限系统初始化完成，当前有 9 个用户
2025-05-27 08:12:49 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-27 08:12:49 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-27 08:12:49 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-27 08:12:49 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-27 08:12:49 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-27 08:12:49 - core.main_window - INFO - 数据库管理器设置完成
2025-05-27 08:12:49 - core.main_window - INFO - 权限管理器设置完成
2025-05-27 08:12:49 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-27 08:12:49 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-27 08:12:49 - core.main_window - INFO - 性能优化器设置完成
2025-05-27 08:12:49 - root - INFO - 主窗口创建成功
2025-05-27 08:12:49 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-27 08:12:49 - root - INFO - 主窗口显示成功
2025-05-27 08:12:49 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-27 08:12:50 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-27 08:12:52 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-27 08:12:53 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-27 08:13:03 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-27 08:13:03 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-27 08:13:03 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-27 08:13:03 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-27 08:13:03 - core.auth_manager - INFO - 密码验证结果: True
2025-05-27 08:13:03 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-27 08:13:03 - ui.login_dialog - INFO - 检测到admin用户使用默认密码，建议修改密码
2025-05-27 08:13:03 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-27 08:13:03 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-27 08:13:03 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-27 08:13:03 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-27 08:13:03 - core.auth_manager - INFO - 密码验证结果: True
2025-05-27 08:13:03 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-27 08:13:03 - ui.login_dialog - INFO - 检测到admin用户使用默认密码，建议修改密码
2025-05-27 08:13:03 - operation - INFO - 用户[admin] 执行操作[切换到patient_management页面]
2025-05-27 08:13:03 - core.main_window - INFO - 敏感内容已显示，用户已通过身份验证
2025-05-27 08:13:03 - operation - INFO - 用户[admin] 执行操作[用户登录成功]
2025-05-27 08:13:03 - operation - INFO - 用户[admin] 执行操作[切换到patient_management页面]
2025-05-27 08:13:03 - core.main_window - INFO - 敏感内容已显示，用户已通过身份验证
2025-05-27 08:13:03 - operation - INFO - 用户[admin] 执行操作[用户登录成功]
2025-05-27 08:13:04 - operation - INFO - 用户[admin] 执行操作[用户登录]
2025-05-27 08:13:04 - operation - INFO - 用户[admin] 执行操作[用户登录]
2025-05-27 08:13:05 - core.performance_optimizer - WARNING - CPU使用率过高: 95.8%
2025-05-27 08:13:05 - core.performance_optimizer - INFO - 降低工作线程数到: 7
2025-05-27 08:13:06 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-27 08:13:20 - operation - INFO - 用户[admin] 执行操作[切换到treatment页面]
2025-05-27 08:13:21 - operation - INFO - 用户[admin] 执行操作[切换到patient_management页面]
2025-05-27 08:13:27 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-27 08:13:27 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-27 08:13:27 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-27 08:13:27 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-27 08:13:27 - core.main_window - INFO - 主窗口资源清理完成
2025-05-27 08:13:27 - root - INFO - 开始清理应用程序资源
2025-05-27 08:13:27 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-27 08:13:27 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-27 08:13:27 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-27 08:13:27 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-27 08:13:27 - core.main_window - INFO - 主窗口资源清理完成
2025-05-27 08:13:27 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-27 08:13:27 - root - INFO - 应用程序资源清理完成
2025-05-27 08:13:27 - root - INFO - === NK脑机接口系统退出 ===
2025-05-27 08:13:29 - root - INFO - 日志系统初始化完成
2025-05-27 08:13:29 - root - INFO - === NK脑机接口系统启动 ===
2025-05-27 08:13:29 - root - INFO - 系统版本: 1.0.0
2025-05-27 08:13:29 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-27 08:13:30 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-27 08:13:30 - root - INFO - 应用程序基本设置完成
2025-05-27 08:13:30 - root - WARNING - 启动画面图片不存在
2025-05-27 08:13:30 - core.database_manager - INFO - 数据库表创建成功
2025-05-27 08:13:30 - core.database_manager - INFO - 基础数据初始化成功
2025-05-27 08:13:30 - core.database_manager - INFO - 数据库初始化成功
2025-05-27 08:13:30 - root - INFO - 数据库初始化成功
2025-05-27 08:13:30 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-27 08:13:30 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-27 08:13:30 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-27 08:13:30 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-27 08:13:30 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-27 08:13:30 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-27 08:13:30 - core.main_window - INFO - 主窗口初始化完成
2025-05-27 08:13:30 - core.auth_manager - INFO - 权限系统初始化完成，当前有 9 个用户
2025-05-27 08:13:30 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-27 08:13:30 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-27 08:13:30 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-27 08:13:30 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-27 08:13:30 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-27 08:13:30 - core.main_window - INFO - 数据库管理器设置完成
2025-05-27 08:13:30 - core.main_window - INFO - 权限管理器设置完成
2025-05-27 08:13:30 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-27 08:13:30 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-27 08:13:30 - core.main_window - INFO - 性能优化器设置完成
2025-05-27 08:13:30 - root - INFO - 主窗口创建成功
2025-05-27 08:13:30 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-27 08:13:30 - root - INFO - 主窗口显示成功
2025-05-27 08:13:30 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-27 08:13:31 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-27 08:13:32 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-27 08:13:33 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-27 08:13:35 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-27 08:13:35 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-27 08:13:35 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-27 08:13:35 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-27 08:13:35 - core.auth_manager - INFO - 密码验证结果: True
2025-05-27 08:13:35 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-27 08:13:35 - ui.login_dialog - INFO - 检测到admin用户使用默认密码，建议修改密码
2025-05-27 08:13:35 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-27 08:13:35 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-27 08:13:35 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-27 08:13:35 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-27 08:13:35 - core.auth_manager - INFO - 密码验证结果: True
2025-05-27 08:13:35 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-27 08:13:35 - ui.login_dialog - INFO - 检测到admin用户使用默认密码，建议修改密码
2025-05-27 08:13:35 - operation - INFO - 用户[admin] 执行操作[切换到patient_management页面]
2025-05-27 08:13:35 - core.main_window - INFO - 敏感内容已显示，用户已通过身份验证
2025-05-27 08:13:35 - operation - INFO - 用户[admin] 执行操作[用户登录成功]
2025-05-27 08:13:35 - operation - INFO - 用户[admin] 执行操作[切换到patient_management页面]
2025-05-27 08:13:35 - core.main_window - INFO - 敏感内容已显示，用户已通过身份验证
2025-05-27 08:13:35 - operation - INFO - 用户[admin] 执行操作[用户登录成功]
2025-05-27 08:13:36 - operation - INFO - 用户[admin] 执行操作[用户登录]
2025-05-27 08:13:36 - operation - INFO - 用户[admin] 执行操作[用户登录]
2025-05-27 08:13:42 - operation - INFO - 用户[admin] 执行操作[切换到treatment页面]
2025-05-27 08:13:46 - core.performance_optimizer - WARNING - CPU使用率过高: 100.0%
2025-05-27 08:13:46 - core.performance_optimizer - INFO - 降低工作线程数到: 7
2025-05-27 08:13:47 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-27 08:21:44 - operation - INFO - 用户[admin] 执行操作[切换到settings页面]
2025-05-27 08:21:51 - operation - INFO - 用户[admin] 执行操作[切换到treatment页面]
2025-05-27 08:32:04 - core.performance_optimizer - WARNING - CPU使用率过高: 100.0%
2025-05-27 08:32:04 - core.performance_optimizer - INFO - 降低工作线程数到: 7
2025-05-27 08:32:05 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-27 08:34:04 - core.performance_optimizer - WARNING - CPU使用率过高: 100.0%
2025-05-27 08:34:04 - core.performance_optimizer - INFO - 降低工作线程数到: 7
2025-05-27 08:34:05 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-27 08:35:20 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-27 08:35:20 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-27 08:35:20 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-27 08:35:20 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-27 08:35:20 - core.main_window - INFO - 主窗口资源清理完成
2025-05-27 08:35:20 - root - INFO - 开始清理应用程序资源
2025-05-27 08:35:20 - ui.patient_management_ui - INFO - 患者管理界面资源清理完成
2025-05-27 08:35:20 - ui.treatment_ui - INFO - 治疗系统界面资源清理完成
2025-05-27 08:35:20 - ui.report_ui - INFO - 报告分析界面资源清理完成
2025-05-27 08:35:20 - ui.settings_ui - INFO - 系统设置界面资源清理完成
2025-05-27 08:35:20 - core.main_window - INFO - 主窗口资源清理完成
2025-05-27 08:35:20 - core.database_manager - INFO - 数据库管理器已关闭
2025-05-27 08:35:20 - root - INFO - 应用程序资源清理完成
2025-05-27 08:35:20 - root - INFO - === NK脑机接口系统退出 ===
2025-05-27 08:35:40 - root - INFO - 日志系统初始化完成
2025-05-27 08:35:40 - root - INFO - === NK脑机接口系统启动 ===
2025-05-27 08:35:40 - root - INFO - 系统版本: 1.0.0
2025-05-27 08:35:40 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-27 08:36:21 - root - INFO - 日志系统初始化完成
2025-05-27 08:36:21 - root - INFO - === NK脑机接口系统启动 ===
2025-05-27 08:36:21 - root - INFO - 系统版本: 1.0.0
2025-05-27 08:36:21 - root - INFO - Python版本: 3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC v.1929 64 bit (AMD64)]
2025-05-27 08:36:21 - root - WARNING - 样式表文件不存在，使用默认样式
2025-05-27 08:36:21 - root - INFO - 应用程序基本设置完成
2025-05-27 08:36:21 - root - WARNING - 启动画面图片不存在
2025-05-27 08:36:21 - core.database_manager - INFO - 数据库表创建成功
2025-05-27 08:36:21 - core.database_manager - INFO - 基础数据初始化成功
2025-05-27 08:36:21 - core.database_manager - INFO - 数据库初始化成功
2025-05-27 08:36:21 - root - INFO - 数据库初始化成功
2025-05-27 08:36:21 - ui.patient_management_ui - INFO - 患者管理界面初始化完成
2025-05-27 08:36:21 - ui.treatment_ui - INFO - 治疗界面信号连接设置完成
2025-05-27 08:36:21 - ui.treatment_ui - INFO - 治疗系统界面初始化完成
2025-05-27 08:36:21 - ui.report_ui - INFO - 报告分析界面初始化完成
2025-05-27 08:36:21 - ui.settings_ui - INFO - 系统设置界面初始化完成
2025-05-27 08:36:21 - core.main_window - INFO - 主窗口UI初始化完成
2025-05-27 08:36:21 - core.main_window - INFO - 敏感内容已隐藏，符合医疗隐私保护要求
2025-05-27 08:36:21 - core.main_window - INFO - 主窗口初始化完成
2025-05-27 08:36:21 - core.auth_manager - INFO - 权限系统初始化完成，当前有 9 个用户
2025-05-27 08:36:21 - core.performance_optimizer - INFO - 自动内存清理已启动
2025-05-27 08:36:21 - ui.patient_management_ui - INFO - 患者管理界面数据库管理器设置完成
2025-05-27 08:36:21 - ui.treatment_ui - INFO - 治疗系统界面数据库管理器设置完成
2025-05-27 08:36:21 - ui.report_ui - INFO - 报告分析界面数据库管理器设置完成
2025-05-27 08:36:21 - ui.settings_ui - INFO - 系统设置界面数据库管理器设置完成
2025-05-27 08:36:21 - core.main_window - INFO - 数据库管理器设置完成
2025-05-27 08:36:21 - core.main_window - INFO - 权限管理器设置完成
2025-05-27 08:36:21 - core.performance_optimizer - INFO - 性能监控已启动
2025-05-27 08:36:21 - core.performance_optimizer - INFO - 性能优化器已启动
2025-05-27 08:36:21 - core.main_window - INFO - 性能优化器设置完成
2025-05-27 08:36:21 - root - INFO - 主窗口创建成功
2025-05-27 08:36:21 - core.performance_optimizer - INFO - 增加工作线程数到: 5
2025-05-27 08:36:21 - root - INFO - 主窗口显示成功
2025-05-27 08:36:21 - root - INFO - 应用程序启动完成，进入事件循环
2025-05-27 08:36:22 - core.performance_optimizer - INFO - 增加工作线程数到: 6
2025-05-27 08:36:23 - core.performance_optimizer - INFO - 增加工作线程数到: 7
2025-05-27 08:36:24 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-27 08:36:37 - core.performance_optimizer - WARNING - CPU使用率过高: 100.0%
2025-05-27 08:36:37 - core.performance_optimizer - INFO - 降低工作线程数到: 7
2025-05-27 08:36:38 - core.performance_optimizer - INFO - 增加工作线程数到: 8
2025-05-27 08:36:42 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-27 08:36:42 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-27 08:36:42 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-27 08:36:42 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-27 08:36:42 - core.auth_manager - INFO - 密码验证结果: True
2025-05-27 08:36:42 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-27 08:36:42 - ui.login_dialog - INFO - 检测到admin用户使用默认密码，建议修改密码
2025-05-27 08:36:42 - core.auth_manager - INFO - 计算密码哈希: 明文密码=admin123, 使用盐=NK_BCI_SYSTEM_2024
2025-05-27 08:36:42 - core.auth_manager - INFO - 计算结果: c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-27 08:36:42 - core.auth_manager - INFO - 验证密码: 输入密码=admin123, 输入密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-27 08:36:42 - core.auth_manager - INFO - 数据库中存储的密码哈希=c816beb62bb0281615f43fad1ac64c245a261e5915cb595c41d5833d0cda39a0
2025-05-27 08:36:42 - core.auth_manager - INFO - 密码验证结果: True
2025-05-27 08:36:42 - core.auth_manager - INFO - 用户登录成功 - admin
2025-05-27 08:36:42 - ui.login_dialog - INFO - 检测到admin用户使用默认密码，建议修改密码
2025-05-27 08:36:42 - operation - INFO - 用户[admin] 执行操作[切换到patient_management页面]
2025-05-27 08:36:42 - core.main_window - INFO - 敏感内容已显示，用户已通过身份验证
2025-05-27 08:36:42 - operation - INFO - 用户[admin] 执行操作[用户登录成功]
2025-05-27 08:36:43 - operation - INFO - 用户[admin] 执行操作[切换到patient_management页面]
2025-05-27 08:36:43 - core.main_window - INFO - 敏感内容已显示，用户已通过身份验证
2025-05-27 08:36:43 - operation - INFO - 用户[admin] 执行操作[用户登录成功]
2025-05-27 08:36:43 - operation - INFO - 用户[admin] 执行操作[用户登录]
2025-05-27 08:36:43 - operation - INFO - 用户[admin] 执行操作[用户登录]
2025-05-27 08:36:45 - operation - INFO - 用户[admin] 执行操作[切换到treatment页面]
2025-05-27 08:36:51 - ui.treatment_ui - INFO - 开始连接脑电设备
2025-05-27 08:36:51 - core.eeg_device - INFO - ADS1299设备控制器初始化完成
2025-05-27 08:36:51 - core.eeg_device - INFO - 数据回调函数已设置
2025-05-27 08:36:51 - core.eeg_device - INFO - 正在连接脑电设备 COM8
2025-05-27 08:36:51 - core.eeg_device - INFO - 发送START命令: b'START'
2025-05-27 08:36:51 - core.eeg_device - INFO - 接收到有效数据包，连接验证成功
2025-05-27 08:36:51 - core.eeg_device - INFO - 脑电设备连接成功
2025-05-27 08:36:51 - core.eeg_device - INFO - 开始数据接收循环
2025-05-27 08:36:51 - core.eeg_device - INFO - 数据接收线程已启动
2025-05-27 08:36:51 - ui.treatment_ui - ERROR - 添加训练日志失败: 'PySide6.QtGui.QTextCursor' object has no attribute 'End'
2025-05-27 08:36:51 - ui.treatment_ui - INFO - 脑电设备连接成功
2025-05-27 08:36:51 - ui.treatment_ui - ERROR - 添加训练日志失败: 'PySide6.QtGui.QTextCursor' object has no attribute 'End'
2025-05-27 08:36:56 - ui.treatment_ui - ERROR - 添加训练日志失败: 'PySide6.QtGui.QTextCursor' object has no attribute 'End'
2025-05-27 08:37:01 - ui.treatment_ui - ERROR - 添加训练日志失败: 'PySide6.QtGui.QTextCursor' object has no attribute 'End'
2025-05-27 08:37:06 - ui.treatment_ui - ERROR - 添加训练日志失败: 'PySide6.QtGui.QTextCursor' object has no attribute 'End'
2025-05-27 08:37:11 - ui.treatment_ui - ERROR - 添加训练日志失败: 'PySide6.QtGui.QTextCursor' object has no attribute 'End'
2025-05-27 08:37:16 - ui.treatment_ui - ERROR - 添加训练日志失败: 'PySide6.QtGui.QTextCursor' object has no attribute 'End'
2025-05-27 08:37:21 - ui.treatment_ui - ERROR - 添加训练日志失败: 'PySide6.QtGui.QTextCursor' object has no attribute 'End'
2025-05-27 08:37:26 - ui.treatment_ui - ERROR - 添加训练日志失败: 'PySide6.QtGui.QTextCursor' object has no attribute 'End'
2025-05-27 08:37:28 - ui.treatment_ui - INFO - 开始断开脑电设备连接
2025-05-27 08:37:28 - core.eeg_device - INFO - 正在断开脑电设备连接
2025-05-27 08:37:28 - core.eeg_device - INFO - 数据接收循环结束
2025-05-27 08:37:28 - core.eeg_device - INFO - 数据接收线程已停止
2025-05-27 08:37:28 - core.eeg_device - INFO - 发送STOP命令: b'STOP'
2025-05-27 08:37:28 - core.eeg_device - INFO - 脑电设备连接已断开
2025-05-27 08:37:28 - ui.treatment_ui - ERROR - 添加训练日志失败: 'PySide6.QtGui.QTextCursor' object has no attribute 'End'
2025-05-27 08:37:28 - ui.treatment_ui - INFO - 脑电设备断开连接成功
2025-05-27 08:37:47 - ui.treatment_ui - INFO - 开始连接脑电设备
2025-05-27 08:37:47 - core.eeg_device - INFO - 正在连接脑电设备 COM8
2025-05-27 08:37:47 - core.eeg_device - INFO - 发送START命令: b'START'
2025-05-27 08:37:47 - core.eeg_device - INFO - 接收到有效数据包，连接验证成功
2025-05-27 08:37:47 - core.eeg_device - INFO - 脑电设备连接成功
2025-05-27 08:37:47 - core.eeg_device - INFO - 开始数据接收循环
2025-05-27 08:37:47 - core.eeg_device - INFO - 数据接收线程已启动
2025-05-27 08:37:47 - ui.treatment_ui - ERROR - 添加训练日志失败: 'PySide6.QtGui.QTextCursor' object has no attribute 'End'
2025-05-27 08:37:47 - ui.treatment_ui - INFO - 脑电设备连接成功
2025-05-27 08:37:47 - ui.treatment_ui - ERROR - 添加训练日志失败: 'PySide6.QtGui.QTextCursor' object has no attribute 'End'
2025-05-27 08:37:52 - ui.treatment_ui - ERROR - 添加训练日志失败: 'PySide6.QtGui.QTextCursor' object has no attribute 'End'
2025-05-27 08:37:57 - ui.treatment_ui - ERROR - 添加训练日志失败: 'PySide6.QtGui.QTextCursor' object has no attribute 'End'
2025-05-27 08:38:02 - ui.treatment_ui - ERROR - 添加训练日志失败: 'PySide6.QtGui.QTextCursor' object has no attribute 'End'
2025-05-27 08:38:06 - ui.treatment_ui - INFO - 开始断开脑电设备连接
2025-05-27 08:38:06 - core.eeg_device - INFO - 正在断开脑电设备连接
2025-05-27 08:38:06 - core.eeg_device - INFO - 数据接收循环结束
2025-05-27 08:38:06 - core.eeg_device - INFO - 数据接收线程已停止
2025-05-27 08:38:06 - core.eeg_device - INFO - 发送STOP命令: b'STOP'
2025-05-27 08:38:06 - core.eeg_device - INFO - 脑电设备连接已断开
2025-05-27 08:38:06 - ui.treatment_ui - ERROR - 添加训练日志失败: 'PySide6.QtGui.QTextCursor' object has no attribute 'End'
2025-05-27 08:38:06 - ui.treatment_ui - INFO - 脑电设备断开连接成功
2025-05-27 08:44:50 - core.performance_optimizer - WARNING - CPU使用率过高: 92.9%
2025-05-27 08:44:50 - core.performance_optimizer - INFO - 降低工作线程数到: 7
2025-05-27 08:44:51 - core.performance_optimizer - INFO - 增加工作线程数到: 8

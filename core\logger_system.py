#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志系统模块
Logging System Module

作者: AI Assistant
版本: 1.0.0
"""

import logging
import logging.handlers
import os
import sys
import traceback
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any
from enum import Enum

from utils.app_config import AppConfig


class LogLevel(Enum):
    """日志级别枚举"""
    DEBUG = logging.DEBUG
    INFO = logging.INFO
    WARNING = logging.WARNING
    ERROR = logging.ERROR
    CRITICAL = logging.CRITICAL


class LoggerSystem:
    """日志系统类"""
    
    def __init__(self):
        self.loggers: Dict[str, logging.Logger] = {}
        self.handlers: Dict[str, logging.Handler] = {}
        self.is_setup = False
        
    def setup_logging(self):
        """设置日志系统"""
        if self.is_setup:
            return
            
        try:
            # 获取日志配置
            log_config = AppConfig.get_config('log')
            
            # 确保日志目录存在
            log_file_path = Path(log_config['file_path'])
            log_file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 设置根日志器
            root_logger = logging.getLogger()
            root_logger.setLevel(getattr(logging, log_config['level']))
            
            # 清除现有处理器
            root_logger.handlers.clear()
            
            # 创建文件处理器
            self._create_file_handler(log_config)
            
            # 创建控制台处理器
            if log_config.get('console_output', True):
                self._create_console_handler(log_config)
            
            # 创建错误日志处理器
            self._create_error_handler(log_config)
            
            # 设置异常钩子
            sys.excepthook = self._handle_exception
            
            self.is_setup = True
            logging.info("日志系统初始化完成")
            
        except Exception as e:
            print(f"日志系统初始化失败: {e}")
            raise
    
    def _create_file_handler(self, log_config: Dict[str, Any]):
        """创建文件日志处理器"""
        try:
            # 使用RotatingFileHandler实现日志轮转
            file_handler = logging.handlers.RotatingFileHandler(
                filename=log_config['file_path'],
                maxBytes=log_config.get('max_file_size', 10 * 1024 * 1024),
                backupCount=log_config.get('backup_count', 5),
                encoding='utf-8'
            )
            
            # 设置格式
            formatter = logging.Formatter(
                log_config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s'),
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            file_handler.setFormatter(formatter)
            
            # 添加到根日志器
            logging.getLogger().addHandler(file_handler)
            self.handlers['file'] = file_handler
            
        except Exception as e:
            print(f"创建文件日志处理器失败: {e}")
            raise
    
    def _create_console_handler(self, log_config: Dict[str, Any]):
        """创建控制台日志处理器"""
        try:
            console_handler = logging.StreamHandler(sys.stdout)
            
            # 控制台使用简化格式
            formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s',
                datefmt='%H:%M:%S'
            )
            console_handler.setFormatter(formatter)
            
            # 控制台只显示INFO及以上级别
            console_handler.setLevel(logging.INFO)
            
            # 添加到根日志器
            logging.getLogger().addHandler(console_handler)
            self.handlers['console'] = console_handler
            
        except Exception as e:
            print(f"创建控制台日志处理器失败: {e}")
            raise
    
    def _create_error_handler(self, log_config: Dict[str, Any]):
        """创建错误日志处理器"""
        try:
            # 错误日志单独文件
            error_log_path = log_config['file_path'].parent / 'error.log'
            
            error_handler = logging.handlers.RotatingFileHandler(
                filename=error_log_path,
                maxBytes=log_config.get('max_file_size', 10 * 1024 * 1024),
                backupCount=log_config.get('backup_count', 5),
                encoding='utf-8'
            )
            
            # 只记录ERROR及以上级别
            error_handler.setLevel(logging.ERROR)
            
            # 设置详细格式
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            error_handler.setFormatter(formatter)
            
            # 添加到根日志器
            logging.getLogger().addHandler(error_handler)
            self.handlers['error'] = error_handler
            
        except Exception as e:
            print(f"创建错误日志处理器失败: {e}")
            raise
    
    def _handle_exception(self, exc_type, exc_value, exc_traceback):
        """处理未捕获的异常"""
        if issubclass(exc_type, KeyboardInterrupt):
            # 键盘中断不记录到日志
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        # 记录异常到日志
        logging.critical(
            "未捕获的异常",
            exc_info=(exc_type, exc_value, exc_traceback)
        )
    
    def get_logger(self, name: str) -> logging.Logger:
        """获取指定名称的日志器"""
        if name not in self.loggers:
            logger = logging.getLogger(name)
            self.loggers[name] = logger
        return self.loggers[name]
    
    def log_operation(self, user_id: str, operation: str, details: str = "", 
                     level: LogLevel = LogLevel.INFO):
        """记录用户操作日志"""
        logger = self.get_logger('operation')
        message = f"用户[{user_id}] 执行操作[{operation}]"
        if details:
            message += f" 详情: {details}"
        
        logger.log(level.value, message)
    
    def log_system_event(self, event: str, details: str = "", 
                        level: LogLevel = LogLevel.INFO):
        """记录系统事件日志"""
        logger = self.get_logger('system')
        message = f"系统事件[{event}]"
        if details:
            message += f" 详情: {details}"
        
        logger.log(level.value, message)
    
    def log_data_processing(self, process: str, status: str, details: str = ""):
        """记录数据处理日志"""
        logger = self.get_logger('data_processing')
        message = f"数据处理[{process}] 状态[{status}]"
        if details:
            message += f" 详情: {details}"
        
        logger.info(message)
    
    def log_device_status(self, device: str, status: str, details: str = ""):
        """记录设备状态日志"""
        logger = self.get_logger('device')
        message = f"设备[{device}] 状态[{status}]"
        if details:
            message += f" 详情: {details}"
        
        logger.info(message)
    
    def log_error(self, error_type: str, error_message: str, 
                  exception: Optional[Exception] = None):
        """记录错误日志"""
        logger = self.get_logger('error')
        message = f"错误类型[{error_type}] 错误信息[{error_message}]"
        
        if exception:
            logger.error(message, exc_info=exception)
        else:
            logger.error(message)
    
    def log_performance(self, operation: str, duration: float, details: str = ""):
        """记录性能日志"""
        logger = self.get_logger('performance')
        message = f"性能监控[{operation}] 耗时[{duration:.3f}s]"
        if details:
            message += f" 详情: {details}"
        
        logger.info(message)
    
    def log_security(self, event: str, user_id: str = "", ip_address: str = "", 
                    level: LogLevel = LogLevel.WARNING):
        """记录安全日志"""
        logger = self.get_logger('security')
        message = f"安全事件[{event}]"
        if user_id:
            message += f" 用户[{user_id}]"
        if ip_address:
            message += f" IP[{ip_address}]"
        
        logger.log(level.value, message)
    
    def create_audit_log(self, user_id: str, action: str, resource: str, 
                        result: str, details: str = ""):
        """创建审计日志"""
        logger = self.get_logger('audit')
        timestamp = datetime.now().isoformat()
        message = (f"审计记录 时间[{timestamp}] 用户[{user_id}] "
                  f"操作[{action}] 资源[{resource}] 结果[{result}]")
        if details:
            message += f" 详情: {details}"
        
        logger.info(message)
    
    def set_log_level(self, level: LogLevel):
        """设置日志级别"""
        logging.getLogger().setLevel(level.value)
        logging.info(f"日志级别已设置为: {level.name}")
    
    def flush_logs(self):
        """刷新所有日志处理器"""
        for handler in self.handlers.values():
            if hasattr(handler, 'flush'):
                handler.flush()
    
    def close_handlers(self):
        """关闭所有日志处理器"""
        for handler in self.handlers.values():
            if hasattr(handler, 'close'):
                handler.close()
        self.handlers.clear()
    
    def get_log_statistics(self) -> Dict[str, Any]:
        """获取日志统计信息"""
        try:
            log_config = AppConfig.get_config('log')
            log_file_path = Path(log_config['file_path'])
            
            stats = {
                'log_file_exists': log_file_path.exists(),
                'log_file_size': 0,
                'error_count': 0,
                'warning_count': 0,
                'info_count': 0,
            }
            
            if log_file_path.exists():
                stats['log_file_size'] = log_file_path.stat().st_size
                
                # 简单统计（实际应用中可能需要更高效的方法）
                try:
                    with open(log_file_path, 'r', encoding='utf-8') as f:
                        for line in f:
                            if ' - ERROR - ' in line:
                                stats['error_count'] += 1
                            elif ' - WARNING - ' in line:
                                stats['warning_count'] += 1
                            elif ' - INFO - ' in line:
                                stats['info_count'] += 1
                except Exception:
                    pass  # 忽略读取错误
            
            return stats
            
        except Exception as e:
            logging.error(f"获取日志统计信息失败: {e}")
            return {}


# 全局日志系统实例
_logger_system = None


def get_logger_system() -> LoggerSystem:
    """获取全局日志系统实例"""
    global _logger_system
    if _logger_system is None:
        _logger_system = LoggerSystem()
    return _logger_system


def setup_logging():
    """设置全局日志系统"""
    logger_system = get_logger_system()
    logger_system.setup_logging()


# 便捷函数
def log_info(message: str, logger_name: str = 'main'):
    """记录信息日志"""
    logging.getLogger(logger_name).info(message)


def log_warning(message: str, logger_name: str = 'main'):
    """记录警告日志"""
    logging.getLogger(logger_name).warning(message)


def log_error(message: str, exception: Optional[Exception] = None, 
              logger_name: str = 'main'):
    """记录错误日志"""
    logger = logging.getLogger(logger_name)
    if exception:
        logger.error(message, exc_info=exception)
    else:
        logger.error(message)

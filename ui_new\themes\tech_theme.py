#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
科技主题配置
Tech Theme Configuration

作者: AI Assistant
版本: 2.0.0
"""

from typing import Dict, Any


class TechTheme:
    """科技主题配置类"""
    
    # 颜色系统
    COLORS = {
        'primary': '#06b6d4',           # 主要青色
        'secondary': '#0891b2',         # 深青色
        'accent': '#00d4ff',            # 强调荧光青
        'success': '#00ff88',           # 成功荧光绿
        'warning': '#ffaa00',           # 警告金色
        'danger': '#ff3366',            # 危险粉红
        'bg_primary': '#0f172a',        # 主背景（深蓝黑）
        'bg_secondary': '#1e293b',      # 次背景（深灰蓝）
        'bg_tertiary': '#334155',       # 三级背景
        'text_primary': '#f1f5f9',      # 主文字色（浅色）
        'text_secondary': '#94a3b8',    # 次文字色
        'border': '#475569',            # 边框色
        'shadow': 'rgba(0, 255, 255, 0.1)', # 阴影色（荧光青）
        'shadow_lg': 'rgba(0, 255, 255, 0.2)', # 大阴影色
    }
    
    # 字体系统（与医疗主题相同）
    FONTS = {
        'primary': 'Segoe UI, -apple-system, BlinkMacSystemFont, sans-serif',
        'mono': 'Consolas, Monaco, monospace',
        'sizes': {
            'xs': 12,
            'sm': 14,
            'base': 16,
            'lg': 18,
            'xl': 20,
            'xxl': 24,
            'xxxl': 32,
        }
    }
    
    # 间距系统（与医疗主题相同）
    SPACING = {
        'xs': 4,
        'sm': 8,
        'base': 16,
        'lg': 24,
        'xl': 32,
        'xxl': 48,
    }
    
    # 圆角系统（与医疗主题相同）
    BORDER_RADIUS = {
        'small': 6,
        'base': 8,
        'large': 12,
        'xl': 16,
        'full': 9999,  # 完全圆角
    }
    
    # 阴影系统（荧光效果）
    SHADOWS = {
        'sm': '0 1px 2px 0 rgba(0, 255, 255, 0.1)',
        'base': '0 4px 6px -1px rgba(0, 255, 255, 0.1)',
        'lg': '0 10px 25px -3px rgba(0, 255, 255, 0.2)',
        'xl': '0 20px 25px -5px rgba(0, 255, 255, 0.3)',
        'glow': '0 0 10px rgba(0, 212, 255, 0.5)',  # 发光效果
    }
    
    @classmethod
    def get_theme_config(cls) -> Dict[str, Any]:
        """获取完整主题配置"""
        return {
            'name': 'tech',
            'display_name': '科技主题',
            'colors': cls.COLORS,
            'fonts': cls.FONTS,
            'spacing': cls.SPACING,
            'border_radius': cls.BORDER_RADIUS,
            'shadows': cls.SHADOWS,
        }
    
    @classmethod
    def get_color(cls, color_name: str) -> str:
        """获取指定颜色值"""
        return cls.COLORS.get(color_name, '#ffffff')
    
    @classmethod
    def get_font_size(cls, size_name: str) -> int:
        """获取指定字体大小"""
        return cls.FONTS['sizes'].get(size_name, 16)
    
    @classmethod
    def get_spacing(cls, spacing_name: str) -> int:
        """获取指定间距值"""
        return cls.SPACING.get(spacing_name, 16)
    
    @classmethod
    def get_border_radius(cls, radius_name: str) -> int:
        """获取指定圆角值"""
        return cls.BORDER_RADIUS.get(radius_name, 8)

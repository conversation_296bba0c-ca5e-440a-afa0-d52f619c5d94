#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脑电原始数据管理器
EEG Raw Data Manager

负责脑电原始数据的采集、存储和管理
支持HDF5格式存储，包含完整的标签信息

作者: AI Assistant
版本: 1.0.0
"""

import logging
import h5py
import numpy as np
import json
import hashlib
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, Dict, List, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum

from utils.app_config import AppConfig


class TrialType(Enum):
    """试验类型枚举"""
    REST = 0          # 休息状态
    MOTOR_IMAGERY = 1 # 运动想象


@dataclass
class TrialMetadata:
    """试验元数据"""
    trial_id: int
    trial_type: TrialType
    start_time: datetime
    duration_seconds: float
    sampling_rate: int
    channels: int
    channel_names: List[str]
    data_quality: float
    patient_id: int
    session_id: int
    notes: str = ""


@dataclass
class SessionInfo:
    """会话信息"""
    session_id: int
    patient_id: int
    treatment_id: Optional[int]
    session_type: str
    start_time: datetime
    end_time: Optional[datetime]
    total_trials: int
    successful_trials: int
    data_directory: str
    notes: str = ""


class EEGRawDataManager:
    """脑电原始数据管理器"""
    
    def __init__(self, db_manager=None):
        """初始化数据管理器"""
        self.logger = logging.getLogger(__name__)
        self.db_manager = db_manager
        
        # 配置信息
        self.config = AppConfig.get_config('raw_data') or self._get_default_config()
        
        # 数据根目录
        self.data_root = Path(AppConfig.get_config('paths')['data']) / 'raw_eeg_data'
        self.data_root.mkdir(parents=True, exist_ok=True)
        
        # 当前会话信息
        self.current_session: Optional[SessionInfo] = None
        self.current_file_path: Optional[Path] = None
        self.current_trial_count = 0
        
        # 初始化数据库表
        self._init_database_tables()
        
        self.logger.info("脑电原始数据管理器初始化完成")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'enabled': True,
            'storage_format': 'hdf5',
            'compression': True,
            'compression_level': 6,
            'max_file_size_mb': 100,
            'auto_backup': True,
            'backup_interval_hours': 24,
            'data_retention_days': 365,
            'quality_threshold': 0.7,
        }
    
    def _init_database_tables(self):
        """初始化数据库表"""
        if not self.db_manager:
            self.logger.warning("数据库管理器未提供，跳过表初始化")
            return
        
        try:
            # 创建原始数据记录表
            self.db_manager.execute_non_query("""
                CREATE TABLE IF NOT EXISTS eeg_raw_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    patient_id INTEGER NOT NULL,
                    session_id INTEGER NOT NULL,
                    trial_id INTEGER NOT NULL,
                    file_path TEXT NOT NULL,
                    file_format TEXT DEFAULT 'hdf5',
                    start_time TIMESTAMP NOT NULL,
                    duration_seconds REAL NOT NULL,
                    sampling_rate INTEGER DEFAULT 125,
                    channels INTEGER DEFAULT 8,
                    label INTEGER NOT NULL,
                    data_quality REAL,
                    file_size_bytes INTEGER,
                    checksum TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (patient_id) REFERENCES bingren(bianhao)
                )
            """)
            
            # 创建会话记录表
            self.db_manager.execute_non_query("""
                CREATE TABLE IF NOT EXISTS eeg_sessions (
                    session_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    patient_id INTEGER NOT NULL,
                    treatment_id INTEGER,
                    session_type TEXT DEFAULT 'training',
                    start_time TIMESTAMP NOT NULL,
                    end_time TIMESTAMP,
                    total_trials INTEGER DEFAULT 0,
                    successful_trials INTEGER DEFAULT 0,
                    session_notes TEXT,
                    data_directory TEXT,
                    FOREIGN KEY (patient_id) REFERENCES bingren(bianhao),
                    FOREIGN KEY (treatment_id) REFERENCES zhiliao(zhiliaobh)
                )
            """)
            
            self.logger.info("数据库表初始化完成")
            
        except Exception as e:
            self.logger.error(f"数据库表初始化失败: {e}")
    
    def start_session(self, patient_id: int, treatment_id: Optional[int] = None,
                     session_type: str = 'training') -> int:
        """开始新的数据采集会话"""
        try:
            if not self.config['enabled']:
                self.logger.info("原始数据存储已禁用")
                return -1
            
            # 创建会话信息
            session_info = SessionInfo(
                session_id=0,  # 将由数据库生成
                patient_id=patient_id,
                treatment_id=treatment_id,
                session_type=session_type,
                start_time=datetime.now(),
                end_time=None,
                total_trials=0,
                successful_trials=0,
                data_directory="",
                notes=""
            )
            
            # 创建数据目录
            date_str = session_info.start_time.strftime('%Y-%m-%d')
            time_str = session_info.start_time.strftime('%H%M%S')
            
            # 确保patient_id是字符串格式，用于目录名
            patient_id_str = str(patient_id)
            patient_dir = self.data_root / 'patients' / f'patient_{patient_id_str}'
            session_dir = patient_dir / 'sessions' / date_str
            session_dir.mkdir(parents=True, exist_ok=True)

            session_info.data_directory = str(session_dir)

            # 创建HDF5文件
            file_name = f'P{patient_id_str}_{date_str}_{time_str}_{session_type}.h5'
            self.current_file_path = session_dir / file_name
            
            # 保存会话到数据库
            if self.db_manager:
                session_id = self._save_session_to_db(session_info)
                session_info.session_id = session_id
            else:
                session_info.session_id = int(datetime.now().timestamp())
            
            self.current_session = session_info
            self.current_trial_count = 0
            
            # 创建HDF5文件并写入元数据
            self._create_hdf5_file()
            
            self.logger.info(f"开始数据采集会话: 患者{patient_id}, 会话ID{session_info.session_id}")
            return session_info.session_id
            
        except Exception as e:
            self.logger.error(f"开始会话失败: {e}")
            return -1
    
    def save_trial_data(self, eeg_data: np.ndarray, label: int, 
                       trial_metadata: Optional[Dict[str, Any]] = None) -> bool:
        """保存单次试验数据"""
        try:
            if not self.config['enabled'] or not self.current_session:
                return False
            
            # 验证数据格式
            if eeg_data.ndim != 2 or eeg_data.shape[0] != 8:
                self.logger.error(f"数据格式错误: {eeg_data.shape}")
                return False
            
            # 使用当前试验计数作为trial_id
            trial_id = self.current_trial_count

            # 创建试验元数据
            trial_meta = TrialMetadata(
                trial_id=trial_id,
                trial_type=TrialType(label),
                start_time=datetime.now(),
                duration_seconds=eeg_data.shape[1] / 125.0,  # 假设125Hz采样率
                sampling_rate=125,
                channels=8,
                channel_names=['PZ', 'P3', 'P4', 'C3', 'CZ', 'C4', 'F3', 'F4'],
                data_quality=self._calculate_data_quality(eeg_data),
                patient_id=self.current_session.patient_id,
                session_id=self.current_session.session_id,
                notes=trial_metadata.get('notes', '') if trial_metadata else ""
            )

            # 保存到HDF5文件
            success = self._save_trial_to_hdf5(eeg_data, trial_meta)

            if success:
                # 保存到数据库
                self._save_trial_to_db(trial_meta)

                # 只有保存成功后才递增计数器
                self.current_trial_count += 1
                self.current_session.total_trials += 1

                # 如果数据质量达标，增加成功计数
                if trial_meta.data_quality >= self.config['quality_threshold']:
                    self.current_session.successful_trials += 1

                self.logger.debug(f"保存试验数据: 试验{trial_meta.trial_id}, 标签{label}, 质量{trial_meta.data_quality:.3f}")
            else:
                self.logger.error(f"试验数据保存失败: 试验{trial_id}, 标签{label}")

            return success
            
        except Exception as e:
            self.logger.error(f"保存试验数据失败: {e}")
            return False
    
    def end_session(self) -> bool:
        """结束当前会话"""
        try:
            if not self.current_session:
                return True
            
            # 更新会话结束时间
            self.current_session.end_time = datetime.now()
            
            # 更新数据库记录
            if self.db_manager:
                self._update_session_in_db()
            
            # 关闭HDF5文件
            if hasattr(self, '_current_hdf5_file') and self._current_hdf5_file:
                self._current_hdf5_file.close()
                self._current_hdf5_file = None
            
            # 计算文件校验和
            if self.current_file_path and self.current_file_path.exists():
                checksum = self._calculate_file_checksum(self.current_file_path)
                self._update_file_checksum(checksum)
            
            self.logger.info(f"结束数据采集会话: {self.current_session.session_id}, "
                           f"总试验{self.current_session.total_trials}, "
                           f"成功{self.current_session.successful_trials}")
            
            # 清理当前会话
            self.current_session = None
            self.current_file_path = None
            self.current_trial_count = 0
            
            return True
            
        except Exception as e:
            self.logger.error(f"结束会话失败: {e}")
            return False
    
    def _create_hdf5_file(self):
        """创建HDF5文件并写入元数据"""
        try:
            self._current_hdf5_file = h5py.File(self.current_file_path, 'w')
            
            # 写入会话元数据
            session_group = self._current_hdf5_file.create_group('session_metadata')
            session_group.attrs['session_id'] = self.current_session.session_id
            session_group.attrs['patient_id'] = self.current_session.patient_id
            session_group.attrs['session_type'] = self.current_session.session_type
            session_group.attrs['start_time'] = self.current_session.start_time.isoformat()
            session_group.attrs['sampling_rate'] = 125
            session_group.attrs['channels'] = 8
            session_group.attrs['channel_names'] = ['PZ', 'P3', 'P4', 'C3', 'CZ', 'C4', 'F3', 'F4']
            
            # 创建试验数据组
            self._current_hdf5_file.create_group('trials')
            
        except Exception as e:
            self.logger.error(f"创建HDF5文件失败: {e}")
            raise
    
    def _save_trial_to_hdf5(self, eeg_data: np.ndarray, trial_meta: TrialMetadata) -> bool:
        """保存试验数据到HDF5文件"""
        try:
            if not hasattr(self, '_current_hdf5_file') or not self._current_hdf5_file:
                self.logger.error("HDF5文件未打开")
                return False

            # 创建试验组 - 检查是否已存在
            trial_group_name = f'trials/trial_{trial_meta.trial_id:03d}'

            # 如果组已存在，删除它（覆盖模式）
            if trial_group_name in self._current_hdf5_file:
                try:
                    del self._current_hdf5_file[trial_group_name]
                    self.logger.debug(f"删除已存在的试验组: {trial_group_name}")
                except Exception as e:
                    self.logger.warning(f"删除已存在试验组失败: {e}")
                    # 尝试使用不同的名称
                    import time
                    timestamp = int(time.time() * 1000) % 10000
                    trial_group_name = f'trials/trial_{trial_meta.trial_id:03d}_{timestamp}'
                    self.logger.info(f"使用新的试验组名称: {trial_group_name}")

            # 创建试验组
            trial_group = self._current_hdf5_file.create_group(trial_group_name)

            # 保存原始数据（使用压缩）
            compression_opts = self.config['compression_level'] if self.config['compression'] else None
            trial_group.create_dataset(
                'eeg_data',
                data=eeg_data.astype(np.float32),
                compression='gzip' if self.config['compression'] else None,
                compression_opts=compression_opts
            )

            # 保存元数据
            trial_group.attrs['trial_id'] = trial_meta.trial_id
            trial_group.attrs['label'] = trial_meta.trial_type.value
            trial_group.attrs['start_time'] = trial_meta.start_time.isoformat()
            trial_group.attrs['duration_seconds'] = trial_meta.duration_seconds
            trial_group.attrs['data_quality'] = trial_meta.data_quality
            trial_group.attrs['notes'] = trial_meta.notes

            # 强制写入磁盘
            self._current_hdf5_file.flush()

            self.logger.debug(f"成功保存试验到HDF5: {trial_group_name}")
            return True

        except Exception as e:
            self.logger.error(f"保存试验到HDF5失败: {e}")
            # 记录更详细的错误信息
            import traceback
            self.logger.debug(f"HDF5保存错误详情: {traceback.format_exc()}")
            return False
    
    def _calculate_data_quality(self, eeg_data: np.ndarray) -> float:
        """计算数据质量评分"""
        try:
            # 简单的数据质量评估
            # 基于信号幅度、方差和异常值检测
            
            # 检查数据范围（正常脑电信号范围）
            amplitude_score = 1.0
            if np.max(np.abs(eeg_data)) > 200000:  # 异常大的幅度
                amplitude_score = 0.5
            
            # 检查信号方差（太小可能是设备问题）
            variance_score = 1.0
            channel_vars = np.var(eeg_data, axis=1)
            if np.any(channel_vars < 100):  # 方差太小
                variance_score = 0.7
            
            # 检查饱和度（信号是否达到ADC极限）
            saturation_score = 1.0
            max_val = 2**23 - 1  # 24位ADC最大值
            if np.any(np.abs(eeg_data) > max_val * 0.95):
                saturation_score = 0.3
            
            # 综合评分
            quality_score = (amplitude_score + variance_score + saturation_score) / 3.0
            
            return min(1.0, max(0.0, quality_score))
            
        except Exception as e:
            self.logger.error(f"计算数据质量失败: {e}")
            return 0.5  # 默认中等质量
    
    def _save_session_to_db(self, session_info: SessionInfo) -> int:
        """保存会话信息到数据库"""
        try:
            sql = """
                INSERT INTO eeg_sessions (patient_id, treatment_id, session_type, 
                                        start_time, total_trials, successful_trials, 
                                        session_notes, data_directory)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """
            params = (
                session_info.patient_id,
                session_info.treatment_id,
                session_info.session_type,
                session_info.start_time.isoformat(),
                session_info.total_trials,
                session_info.successful_trials,
                session_info.notes,
                session_info.data_directory
            )
            
            if self.db_manager.execute_non_query(sql, params):
                # 获取插入的ID
                result = self.db_manager.execute_query("SELECT last_insert_rowid()")
                if result:
                    return result[0]['last_insert_rowid()']
            
            return -1
            
        except Exception as e:
            self.logger.error(f"保存会话到数据库失败: {e}")
            return -1

    def _save_trial_to_db(self, trial_meta: TrialMetadata):
        """保存试验记录到数据库"""
        try:
            if not self.db_manager or not self.current_file_path:
                return

            file_size = self.current_file_path.stat().st_size if self.current_file_path.exists() else 0

            sql = """
                INSERT INTO eeg_raw_data (patient_id, session_id, trial_id, file_path,
                                        file_format, start_time, duration_seconds,
                                        sampling_rate, channels, label, data_quality,
                                        file_size_bytes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            params = (
                trial_meta.patient_id,
                trial_meta.session_id,
                trial_meta.trial_id,
                str(self.current_file_path),
                'hdf5',
                trial_meta.start_time.isoformat(),
                trial_meta.duration_seconds,
                trial_meta.sampling_rate,
                trial_meta.channels,
                trial_meta.trial_type.value,
                trial_meta.data_quality,
                file_size
            )

            self.db_manager.execute_non_query(sql, params)

        except Exception as e:
            self.logger.error(f"保存试验记录到数据库失败: {e}")

    def _update_session_in_db(self):
        """更新数据库中的会话信息"""
        try:
            if not self.db_manager or not self.current_session:
                return

            sql = """
                UPDATE eeg_sessions
                SET end_time = ?, total_trials = ?, successful_trials = ?
                WHERE session_id = ?
            """
            params = (
                self.current_session.end_time.isoformat() if self.current_session.end_time else None,
                self.current_session.total_trials,
                self.current_session.successful_trials,
                self.current_session.session_id
            )

            self.db_manager.execute_non_query(sql, params)

        except Exception as e:
            self.logger.error(f"更新会话信息失败: {e}")

    def _calculate_file_checksum(self, file_path: Path) -> str:
        """计算文件校验和"""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            self.logger.error(f"计算文件校验和失败: {e}")
            return ""

    def _update_file_checksum(self, checksum: str):
        """更新文件校验和"""
        try:
            if not self.db_manager or not self.current_file_path:
                return

            sql = """
                UPDATE eeg_raw_data
                SET checksum = ?
                WHERE file_path = ?
            """
            params = (checksum, str(self.current_file_path))
            self.db_manager.execute_non_query(sql, params)

        except Exception as e:
            self.logger.error(f"更新文件校验和失败: {e}")

    def get_patient_sessions(self, patient_id: int,
                           start_date: Optional[datetime] = None,
                           end_date: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """获取患者的会话列表"""
        try:
            if not self.db_manager:
                return []

            sql = """
                SELECT * FROM eeg_sessions
                WHERE patient_id = ?
            """
            params = [patient_id]

            if start_date:
                sql += " AND start_time >= ?"
                params.append(start_date.isoformat())

            if end_date:
                sql += " AND start_time <= ?"
                params.append(end_date.isoformat())

            sql += " ORDER BY start_time DESC"

            return self.db_manager.execute_query(sql, params) or []

        except Exception as e:
            self.logger.error(f"获取患者会话失败: {e}")
            return []

    def get_session_trials(self, session_id: int) -> List[Dict[str, Any]]:
        """获取会话的试验列表"""
        try:
            if not self.db_manager:
                return []

            sql = """
                SELECT * FROM eeg_raw_data
                WHERE session_id = ?
                ORDER BY trial_id
            """

            return self.db_manager.execute_query(sql, [session_id]) or []

        except Exception as e:
            self.logger.error(f"获取会话试验失败: {e}")
            return []

    def load_trial_data(self, file_path: str, trial_id: int) -> Optional[Tuple[np.ndarray, Dict[str, Any]]]:
        """加载特定试验的数据"""
        try:
            if not Path(file_path).exists():
                self.logger.error(f"文件不存在: {file_path}")
                return None

            with h5py.File(file_path, 'r') as f:
                trial_group_name = f'trials/trial_{trial_id:03d}'

                if trial_group_name not in f:
                    self.logger.error(f"试验数据不存在: {trial_group_name}")
                    return None

                trial_group = f[trial_group_name]

                # 加载数据
                eeg_data = trial_group['eeg_data'][:]

                # 加载元数据
                metadata = {}
                for key in trial_group.attrs.keys():
                    metadata[key] = trial_group.attrs[key]

                return eeg_data, metadata

        except Exception as e:
            self.logger.error(f"加载试验数据失败: {e}")
            return None

    def export_patient_data(self, patient_id: int, output_dir: str,
                          format_type: str = 'hdf5') -> bool:
        """导出患者的所有数据"""
        try:
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)

            # 获取患者的所有会话
            sessions = self.get_patient_sessions(patient_id)

            if not sessions:
                self.logger.warning(f"患者{patient_id}没有数据")
                return False

            export_info = {
                'patient_id': patient_id,
                'export_time': datetime.now().isoformat(),
                'sessions': []
            }

            for session in sessions:
                session_id = session['session_id']
                trials = self.get_session_trials(session_id)

                session_info = {
                    'session_id': session_id,
                    'session_type': session['session_type'],
                    'start_time': session['start_time'],
                    'total_trials': len(trials),
                    'trials': []
                }

                # 复制数据文件
                for trial in trials:
                    source_file = Path(trial['file_path'])
                    if source_file.exists():
                        # 生成导出文件名
                        export_filename = f"patient_{patient_id}_session_{session_id}_trial_{trial['trial_id']}.h5"
                        dest_file = output_path / export_filename

                        # 复制文件
                        import shutil
                        shutil.copy2(source_file, dest_file)

                        session_info['trials'].append({
                            'trial_id': trial['trial_id'],
                            'label': trial['label'],
                            'data_quality': trial['data_quality'],
                            'file_name': export_filename
                        })

                export_info['sessions'].append(session_info)

            # 保存导出信息
            info_file = output_path / f'patient_{patient_id}_export_info.json'
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(export_info, f, indent=2, ensure_ascii=False)

            self.logger.info(f"患者{patient_id}数据导出完成: {output_path}")
            return True

        except Exception as e:
            self.logger.error(f"导出患者数据失败: {e}")
            return False

    def cleanup_old_data(self, retention_days: Optional[int] = None) -> int:
        """清理过期数据"""
        try:
            if retention_days is None:
                retention_days = self.config['data_retention_days']

            cutoff_date = datetime.now() - timedelta(days=retention_days)

            # 查找过期的会话
            sql = """
                SELECT session_id, data_directory FROM eeg_sessions
                WHERE start_time < ?
            """

            old_sessions = self.db_manager.execute_query(sql, [cutoff_date.isoformat()]) or []

            cleaned_count = 0
            for session in old_sessions:
                session_id = session['session_id']
                data_dir = session['data_directory']

                # 删除数据文件
                if data_dir and Path(data_dir).exists():
                    import shutil
                    shutil.rmtree(data_dir)

                # 删除数据库记录
                self.db_manager.execute_non_query(
                    "DELETE FROM eeg_raw_data WHERE session_id = ?", [session_id]
                )
                self.db_manager.execute_non_query(
                    "DELETE FROM eeg_sessions WHERE session_id = ?", [session_id]
                )

                cleaned_count += 1

            self.logger.info(f"清理了{cleaned_count}个过期会话的数据")
            return cleaned_count

        except Exception as e:
            self.logger.error(f"清理过期数据失败: {e}")
            return 0

    def get_storage_statistics(self) -> Dict[str, Any]:
        """获取存储统计信息"""
        try:
            stats = {
                'total_sessions': 0,
                'total_trials': 0,
                'total_size_bytes': 0,
                'patients_count': 0,
                'average_quality': 0.0,
                'storage_path': str(self.data_root)
            }

            if not self.db_manager:
                return stats

            # 统计会话数
            result = self.db_manager.execute_query("SELECT COUNT(*) as count FROM eeg_sessions")
            if result:
                stats['total_sessions'] = result[0]['count']

            # 统计试验数和总大小
            result = self.db_manager.execute_query("""
                SELECT COUNT(*) as count,
                       SUM(file_size_bytes) as total_size,
                       AVG(data_quality) as avg_quality
                FROM eeg_raw_data
            """)
            if result and result[0]['count']:
                stats['total_trials'] = result[0]['count']
                stats['total_size_bytes'] = result[0]['total_size'] or 0
                stats['average_quality'] = result[0]['avg_quality'] or 0.0

            # 统计患者数
            result = self.db_manager.execute_query("""
                SELECT COUNT(DISTINCT patient_id) as count FROM eeg_sessions
            """)
            if result:
                stats['patients_count'] = result[0]['count']

            return stats

        except Exception as e:
            self.logger.error(f"获取存储统计失败: {e}")
            return {}

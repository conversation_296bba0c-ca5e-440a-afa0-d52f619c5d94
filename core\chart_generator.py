#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图表生成器模块
Chart Generator Module

作者: AI Assistant
版本: 1.0.0
"""

import logging
import io
import base64
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.font_manager import FontProperties
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class ChartGenerator:
    """图表生成器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 设置图表样式
        plt.style.use('default')
        self.colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', 
                      '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf']
    
    def generate_treatment_trend_chart(self, treatment_data: List[Dict[str, Any]], 
                                     width: int = 10, height: int = 6) -> str:
        """生成治疗趋势图"""
        try:
            if not treatment_data:
                return self._create_empty_chart("暂无治疗数据", width, height)
            
            # 准备数据
            dates = []
            scores = []
            
            for record in reversed(treatment_data):  # 按时间正序
                try:
                    date_str = record.get('rq', '')
                    score = float(record.get('defen', 0))
                    
                    if date_str and score > 0:
                        date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                        dates.append(date_obj)
                        scores.append(score)
                except (ValueError, TypeError):
                    continue
            
            if not dates:
                return self._create_empty_chart("无有效治疗数据", width, height)
            
            # 创建图表
            fig, ax = plt.subplots(figsize=(width, height))
            
            # 绘制趋势线
            ax.plot(dates, scores, marker='o', linewidth=2, markersize=6, 
                   color=self.colors[0], label='治疗得分')
            
            # 添加趋势线
            if len(dates) > 1:
                z = np.polyfit(range(len(dates)), scores, 1)
                p = np.poly1d(z)
                trend_scores = p(range(len(dates)))
                ax.plot(dates, trend_scores, '--', color=self.colors[1], 
                       alpha=0.7, label='趋势线')
            
            # 设置图表属性
            ax.set_title('治疗效果趋势图', fontsize=14, fontweight='bold')
            ax.set_xlabel('治疗日期', fontsize=12)
            ax.set_ylabel('治疗得分 (%)', fontsize=12)
            ax.grid(True, alpha=0.3)
            ax.legend()
            
            # 设置日期格式
            if len(dates) > 10:
                ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
                ax.xaxis.set_major_locator(mdates.WeekdayLocator())
            else:
                ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
            
            plt.xticks(rotation=45)
            plt.tight_layout()
            
            return self._fig_to_base64(fig)
            
        except Exception as e:
            self.logger.error(f"生成治疗趋势图失败: {e}")
            return self._create_empty_chart("图表生成失败", width, height)
    
    def generate_score_distribution_chart(self, treatment_data: List[Dict[str, Any]], 
                                        width: int = 8, height: int = 6) -> str:
        """生成得分分布柱状图"""
        try:
            if not treatment_data:
                return self._create_empty_chart("暂无治疗数据", width, height)
            
            # 统计治疗效果分布
            evaluation_counts = {'优': 0, '良': 0, '中': 0, '差': 0}
            
            for record in treatment_data:
                evaluation = record.get('zlms', '')
                if evaluation in evaluation_counts:
                    evaluation_counts[evaluation] += 1
            
            # 检查是否有数据
            if sum(evaluation_counts.values()) == 0:
                return self._create_empty_chart("无治疗评价数据", width, height)
            
            # 创建图表
            fig, ax = plt.subplots(figsize=(width, height))
            
            categories = list(evaluation_counts.keys())
            counts = list(evaluation_counts.values())
            colors = ['#2ecc71', '#3498db', '#f39c12', '#e74c3c']  # 绿、蓝、橙、红
            
            bars = ax.bar(categories, counts, color=colors, alpha=0.8)
            
            # 添加数值标签
            for bar, count in zip(bars, counts):
                if count > 0:
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                           f'{count}次', ha='center', va='bottom', fontsize=11)
            
            # 设置图表属性
            ax.set_title('治疗效果分布图', fontsize=14, fontweight='bold')
            ax.set_xlabel('治疗评价', fontsize=12)
            ax.set_ylabel('治疗次数', fontsize=12)
            ax.grid(True, alpha=0.3, axis='y')
            
            plt.tight_layout()
            
            return self._fig_to_base64(fig)
            
        except Exception as e:
            self.logger.error(f"生成得分分布图失败: {e}")
            return self._create_empty_chart("图表生成失败", width, height)
    
    def generate_daily_statistics_chart(self, daily_stats: List[Dict[str, Any]], 
                                      width: int = 12, height: int = 8) -> str:
        """生成日统计图表"""
        try:
            if not daily_stats:
                return self._create_empty_chart("暂无统计数据", width, height)
            
            # 准备数据
            dates = []
            treatment_counts = []
            patient_counts = []
            avg_scores = []
            
            for stat in daily_stats:
                try:
                    date_str = stat.get('date', '')
                    treatment_stat = stat.get('treatment_stats', {})
                    
                    if date_str:
                        date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                        dates.append(date_obj)
                        treatment_counts.append(treatment_stat.get('treatment_count', 0))
                        patient_counts.append(treatment_stat.get('patient_count', 0))
                        avg_scores.append(treatment_stat.get('avg_score', 0) or 0)
                except (ValueError, TypeError):
                    continue
            
            if not dates:
                return self._create_empty_chart("无有效统计数据", width, height)
            
            # 创建子图
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(width, height))
            
            # 1. 治疗次数趋势
            ax1.plot(dates, treatment_counts, marker='o', color=self.colors[0])
            ax1.set_title('每日治疗次数', fontweight='bold')
            ax1.set_ylabel('治疗次数')
            ax1.grid(True, alpha=0.3)
            
            # 2. 患者人数趋势
            ax2.plot(dates, patient_counts, marker='s', color=self.colors[1])
            ax2.set_title('每日治疗患者数', fontweight='bold')
            ax2.set_ylabel('患者人数')
            ax2.grid(True, alpha=0.3)
            
            # 3. 平均得分趋势
            ax3.plot(dates, avg_scores, marker='^', color=self.colors[2])
            ax3.set_title('每日平均得分', fontweight='bold')
            ax3.set_ylabel('平均得分 (%)')
            ax3.grid(True, alpha=0.3)
            
            # 4. 综合柱状图
            x_pos = np.arange(len(dates))
            width_bar = 0.35
            
            ax4.bar(x_pos - width_bar/2, treatment_counts, width_bar, 
                   label='治疗次数', color=self.colors[0], alpha=0.7)
            ax4.bar(x_pos + width_bar/2, patient_counts, width_bar, 
                   label='患者人数', color=self.colors[1], alpha=0.7)
            
            ax4.set_title('治疗量统计对比', fontweight='bold')
            ax4.set_ylabel('数量')
            ax4.set_xticks(x_pos)
            ax4.set_xticklabels([d.strftime('%m-%d') for d in dates], rotation=45)
            ax4.legend()
            ax4.grid(True, alpha=0.3)
            
            # 设置日期格式
            for ax in [ax1, ax2, ax3]:
                ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
                plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
            
            plt.tight_layout()
            
            return self._fig_to_base64(fig)
            
        except Exception as e:
            self.logger.error(f"生成日统计图表失败: {e}")
            return self._create_empty_chart("图表生成失败", width, height)
    
    def generate_eeg_features_chart(self, eeg_data: List[Dict[str, Any]], 
                                  width: int = 10, height: int = 6) -> str:
        """生成脑电特征图表"""
        try:
            if not eeg_data:
                return self._create_empty_chart("暂无脑电数据", width, height)
            
            # 准备数据
            features = ['θ波', 'α波', '低β波', '高β波', 'γ波']
            avg_powers = []
            
            # 计算平均功率
            theta_avg = np.mean([d.get('theta', 0) for d in eeg_data if d.get('theta')])
            alpha_avg = np.mean([d.get('alpha', 0) for d in eeg_data if d.get('alpha')])
            low_beta_avg = np.mean([d.get('low_beta', 0) for d in eeg_data if d.get('low_beta')])
            high_beta_avg = np.mean([d.get('high_beta', 0) for d in eeg_data if d.get('high_beta')])
            gamma_avg = np.mean([d.get('gamma', 0) for d in eeg_data if d.get('gamma')])
            
            avg_powers = [theta_avg, alpha_avg, low_beta_avg, high_beta_avg, gamma_avg]
            
            # 创建图表
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(width, height))
            
            # 1. 柱状图
            bars = ax1.bar(features, avg_powers, color=self.colors[:5], alpha=0.8)
            ax1.set_title('脑电频带功率分布', fontweight='bold')
            ax1.set_ylabel('功率 (μV²)')
            ax1.grid(True, alpha=0.3, axis='y')
            
            # 添加数值标签
            for bar, power in zip(bars, avg_powers):
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                        f'{power:.2f}', ha='center', va='bottom', fontsize=10)
            
            # 2. 饼图
            ax2.pie(avg_powers, labels=features, colors=self.colors[:5], 
                   autopct='%1.1f%%', startangle=90)
            ax2.set_title('脑电频带功率占比', fontweight='bold')
            
            plt.tight_layout()
            
            return self._fig_to_base64(fig)
            
        except Exception as e:
            self.logger.error(f"生成脑电特征图表失败: {e}")
            return self._create_empty_chart("图表生成失败", width, height)
    
    def _create_empty_chart(self, message: str, width: int, height: int) -> str:
        """创建空图表"""
        fig, ax = plt.subplots(figsize=(width, height))
        ax.text(0.5, 0.5, message, ha='center', va='center', 
               fontsize=16, transform=ax.transAxes)
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        
        return self._fig_to_base64(fig)
    
    def generate_weekly_statistics_chart(self, weekly_data: Dict[str, Any],
                                       width: int = 12, height: int = 8) -> str:
        """生成周统计图表"""
        try:
            weekly_stats = weekly_data.get('weekly_stats', [])
            if not weekly_stats:
                return self._create_empty_chart("暂无周统计数据", width, height)

            # 准备数据
            weeks = [stat.get('week', '') for stat in weekly_stats]
            treatment_counts = [stat.get('treatment_count', 0) for stat in weekly_stats]
            avg_scores = [stat.get('avg_score', 0) or 0 for stat in weekly_stats]
            patient_counts = [stat.get('patient_count', 0) for stat in weekly_stats]

            # 创建子图
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(width, height))

            # 1. 周治疗次数趋势
            ax1.plot(weeks, treatment_counts, marker='o', linewidth=2, color=self.colors[0])
            ax1.set_title('周治疗次数趋势', fontweight='bold')
            ax1.set_ylabel('治疗次数')
            ax1.grid(True, alpha=0.3)
            ax1.tick_params(axis='x', rotation=45)

            # 2. 周平均得分趋势
            ax2.plot(weeks, avg_scores, marker='s', linewidth=2, color=self.colors[1])
            ax2.set_title('周平均得分趋势', fontweight='bold')
            ax2.set_ylabel('平均得分 (%)')
            ax2.grid(True, alpha=0.3)
            ax2.tick_params(axis='x', rotation=45)

            # 3. 周患者数趋势
            ax3.bar(weeks, patient_counts, color=self.colors[2], alpha=0.7)
            ax3.set_title('周治疗患者数', fontweight='bold')
            ax3.set_ylabel('患者人数')
            ax3.grid(True, alpha=0.3, axis='y')
            ax3.tick_params(axis='x', rotation=45)

            # 4. 综合对比
            x_pos = np.arange(len(weeks))
            width_bar = 0.35

            ax4.bar(x_pos - width_bar/2, treatment_counts, width_bar,
                   label='治疗次数', color=self.colors[0], alpha=0.7)
            ax4.bar(x_pos + width_bar/2, patient_counts, width_bar,
                   label='患者人数', color=self.colors[2], alpha=0.7)

            ax4.set_title('周度综合统计', fontweight='bold')
            ax4.set_ylabel('数量')
            ax4.set_xticks(x_pos)
            ax4.set_xticklabels(weeks, rotation=45)
            ax4.legend()
            ax4.grid(True, alpha=0.3)

            plt.tight_layout()

            return self._fig_to_base64(fig)

        except Exception as e:
            self.logger.error(f"生成周统计图表失败: {e}")
            return self._create_empty_chart("图表生成失败", width, height)

    def generate_monthly_statistics_chart(self, monthly_data: Dict[str, Any],
                                        width: int = 12, height: int = 8) -> str:
        """生成月统计图表"""
        try:
            monthly_stats = monthly_data.get('monthly_stats', [])
            improvement_rates = monthly_data.get('improvement_rates', [])

            if not monthly_stats:
                return self._create_empty_chart("暂无月统计数据", width, height)

            # 准备数据
            months = [stat.get('month', '') for stat in monthly_stats]
            treatment_counts = [stat.get('treatment_count', 0) for stat in monthly_stats]
            avg_scores = [stat.get('avg_score', 0) or 0 for stat in monthly_stats]
            improvement_rates_values = [rate.get('improvement_rate', 0) for rate in improvement_rates]

            # 创建子图
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(width, height))

            # 1. 月治疗次数趋势
            ax1.plot(months, treatment_counts, marker='o', linewidth=3, color=self.colors[0])
            ax1.fill_between(months, treatment_counts, alpha=0.3, color=self.colors[0])
            ax1.set_title('月治疗次数趋势', fontweight='bold')
            ax1.set_ylabel('治疗次数')
            ax1.grid(True, alpha=0.3)
            ax1.tick_params(axis='x', rotation=45)

            # 2. 月平均得分趋势
            ax2.plot(months, avg_scores, marker='s', linewidth=3, color=self.colors[1])
            ax2.fill_between(months, avg_scores, alpha=0.3, color=self.colors[1])
            ax2.set_title('月平均得分趋势', fontweight='bold')
            ax2.set_ylabel('平均得分 (%)')
            ax2.grid(True, alpha=0.3)
            ax2.tick_params(axis='x', rotation=45)

            # 3. 月改善率
            if improvement_rates_values:
                ax3.bar(months, improvement_rates_values, color=self.colors[2], alpha=0.8)
                ax3.set_title('月康复改善率', fontweight='bold')
                ax3.set_ylabel('改善率 (%)')
                ax3.grid(True, alpha=0.3, axis='y')
                ax3.tick_params(axis='x', rotation=45)
            else:
                ax3.text(0.5, 0.5, '暂无改善率数据', ha='center', va='center',
                        transform=ax3.transAxes, fontsize=12)
                ax3.set_title('月康复改善率', fontweight='bold')

            # 4. 效果分布饼图
            excellent_total = sum(stat.get('excellent_count', 0) for stat in monthly_stats)
            good_total = sum(stat.get('good_count', 0) for stat in monthly_stats)
            fair_total = sum(stat.get('fair_count', 0) for stat in monthly_stats)
            poor_total = sum(stat.get('poor_count', 0) for stat in monthly_stats)

            if excellent_total + good_total + fair_total + poor_total > 0:
                labels = ['优', '良', '中', '差']
                sizes = [excellent_total, good_total, fair_total, poor_total]
                colors_pie = ['#2ecc71', '#3498db', '#f39c12', '#e74c3c']

                ax4.pie(sizes, labels=labels, colors=colors_pie, autopct='%1.1f%%', startangle=90)
                ax4.set_title('月度治疗效果分布', fontweight='bold')
            else:
                ax4.text(0.5, 0.5, '暂无效果数据', ha='center', va='center',
                        transform=ax4.transAxes, fontsize=12)
                ax4.set_title('月度治疗效果分布', fontweight='bold')

            plt.tight_layout()

            return self._fig_to_base64(fig)

        except Exception as e:
            self.logger.error(f"生成月统计图表失败: {e}")
            return self._create_empty_chart("图表生成失败", width, height)

    def generate_patient_statistics_chart(self, patient_data: Dict[str, Any],
                                        width: int = 12, height: int = 8) -> str:
        """生成患者统计图表"""
        try:
            patient_rankings = patient_data.get('patient_rankings', [])
            age_group_stats = patient_data.get('age_group_stats', [])

            if not patient_rankings:
                return self._create_empty_chart("暂无患者统计数据", width, height)

            # 创建子图
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(width, height))

            # 1. 患者表现排名（前10名）
            top_patients = patient_rankings[:10]
            patient_names = [f"{p.get('name', '')}({p.get('bianh', '')})" for p in top_patients]
            avg_scores = [p.get('avg_score', 0) for p in top_patients]

            ax1.barh(patient_names, avg_scores, color=self.colors[0], alpha=0.8)
            ax1.set_title('患者表现排名 (前10名)', fontweight='bold')
            ax1.set_xlabel('平均得分 (%)')
            ax1.grid(True, alpha=0.3, axis='x')

            # 2. 患者进步幅度
            improvements = [p.get('improvement', 0) for p in top_patients]
            ax2.bar(range(len(top_patients)), improvements, color=self.colors[1], alpha=0.8)
            ax2.set_title('患者进步幅度 (前10名)', fontweight='bold')
            ax2.set_ylabel('进步幅度 (分)')
            ax2.set_xticks(range(len(top_patients)))
            ax2.set_xticklabels([p.get('name', '') for p in top_patients], rotation=45)
            ax2.grid(True, alpha=0.3, axis='y')

            # 3. 年龄组分析
            if age_group_stats:
                age_groups = [stat.get('age_group', '') for stat in age_group_stats]
                age_scores = [stat.get('avg_score', 0) for stat in age_group_stats]

                ax3.bar(age_groups, age_scores, color=self.colors[2], alpha=0.8)
                ax3.set_title('年龄组平均得分', fontweight='bold')
                ax3.set_ylabel('平均得分 (%)')
                ax3.tick_params(axis='x', rotation=45)
                ax3.grid(True, alpha=0.3, axis='y')
            else:
                ax3.text(0.5, 0.5, '暂无年龄组数据', ha='center', va='center',
                        transform=ax3.transAxes, fontsize=12)
                ax3.set_title('年龄组平均得分', fontweight='bold')

            # 4. 治疗次数分布
            treatment_counts = [p.get('treatment_count', 0) for p in patient_rankings]
            ax4.hist(treatment_counts, bins=10, color=self.colors[3], alpha=0.7, edgecolor='black')
            ax4.set_title('患者治疗次数分布', fontweight='bold')
            ax4.set_xlabel('治疗次数')
            ax4.set_ylabel('患者人数')
            ax4.grid(True, alpha=0.3, axis='y')

            plt.tight_layout()

            return self._fig_to_base64(fig)

        except Exception as e:
            self.logger.error(f"生成患者统计图表失败: {e}")
            return self._create_empty_chart("图表生成失败", width, height)

    def _fig_to_base64(self, fig) -> str:
        """将matplotlib图表转换为base64字符串"""
        try:
            buffer = io.BytesIO()
            fig.savefig(buffer, format='png', dpi=100, bbox_inches='tight')
            buffer.seek(0)

            # 转换为base64
            image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')

            plt.close(fig)  # 释放内存
            buffer.close()

            return f"data:image/png;base64,{image_base64}"

        except Exception as e:
            self.logger.error(f"图表转换base64失败: {e}")
            plt.close(fig)
            return ""

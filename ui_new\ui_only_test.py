#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
纯UI测试启动文件（无业务逻辑依赖）
Pure UI Test Launcher (No Business Logic Dependencies)

作者: AI Assistant
版本: 2.0.0
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QPushButton, QHBoxLayout
from PySide6.QtCore import Qt

# 导入新UI组件
from ui_new.components.sidebar import ModernSidebar
from ui_new.components.top_bar import ModernTopBar
from ui_new.themes.theme_manager import ThemeManager


class UITestWindow(QWidget):
    """UI测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.theme_manager = ThemeManager()
        self.init_ui()
        
        # 应用默认主题
        self.theme_manager.apply_theme_styles()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("NK脑机接口康复系统 - UI组件测试")
        self.setMinimumSize(1200, 800)
        
        # 主布局
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建侧边栏
        self.sidebar = ModernSidebar()
        main_layout.addWidget(self.sidebar)
        
        # 创建右侧内容区
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(0)
        
        # 创建顶部栏
        self.top_bar = ModernTopBar()
        right_layout.addWidget(self.top_bar)
        
        # 创建内容区
        content_widget = QWidget()
        content_widget.setObjectName("content_area")
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(24, 24, 24, 24)
        content_layout.setSpacing(24)
        
        # 测试内容
        self.create_test_content(content_layout)
        
        right_layout.addWidget(content_widget)
        main_layout.addWidget(right_widget)
        
        # 连接信号
        self.setup_connections()
    
    def create_test_content(self, layout):
        """创建测试内容"""
        # 标题
        title = QLabel("UI组件测试")
        title.setProperty("class", "text-xxxl font-bold")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 测试说明
        description = QLabel("""
        这是NK脑机接口康复系统的新UI框架测试。
        
        测试功能：
        • 主题切换（顶部栏右侧开关）
        • 侧边栏折叠（顶部栏左侧菜单按钮）
        • 导航切换（侧边栏菜单项）
        • 现代化卡片式布局
        • 专业医疗设备UI风格
        """)
        description.setProperty("class", "text-base")
        description.setAlignment(Qt.AlignCenter)
        layout.addWidget(description)
        
        # 测试卡片
        self.create_test_cards(layout)
        
        layout.addStretch()
    
    def create_test_cards(self, layout):
        """创建测试卡片"""
        cards_layout = QHBoxLayout()
        cards_layout.setSpacing(24)
        
        # 卡片1：主题测试
        card1 = QWidget()
        card1.setProperty("class", "card")
        card1.setFixedHeight(200)
        
        card1_layout = QVBoxLayout(card1)
        card1_layout.setContentsMargins(20, 20, 20, 20)
        
        card1_title = QLabel("主题系统")
        card1_title.setProperty("class", "card-title")
        card1_layout.addWidget(card1_title)
        
        card1_desc = QLabel("当前主题：医疗主题\n点击顶部栏开关切换到科技主题")
        card1_desc.setProperty("class", "text-sm")
        card1_layout.addWidget(card1_desc)
        
        card1_layout.addStretch()
        
        # 卡片2：导航测试
        card2 = QWidget()
        card2.setProperty("class", "card")
        card2.setFixedHeight(200)
        
        card2_layout = QVBoxLayout(card2)
        card2_layout.setContentsMargins(20, 20, 20, 20)
        
        card2_title = QLabel("导航系统")
        card2_title.setProperty("class", "card-title")
        card2_layout.addWidget(card2_title)
        
        card2_desc = QLabel("侧边栏包含分组导航\n点击菜单项测试页面切换")
        card2_desc.setProperty("class", "text-sm")
        card2_layout.addWidget(card2_desc)
        
        card2_layout.addStretch()
        
        # 卡片3：状态测试
        card3 = QWidget()
        card3.setProperty("class", "card")
        card3.setFixedHeight(200)
        
        card3_layout = QVBoxLayout(card3)
        card3_layout.setContentsMargins(20, 20, 20, 20)
        
        card3_title = QLabel("状态系统")
        card3_title.setProperty("class", "card-title")
        card3_layout.addWidget(card3_title)
        
        card3_desc = QLabel("顶部栏显示实时状态\n设备、信号、系统状态监控")
        card3_desc.setProperty("class", "text-sm")
        card3_layout.addWidget(card3_desc)
        
        card3_layout.addStretch()
        
        cards_layout.addWidget(card1)
        cards_layout.addWidget(card2)
        cards_layout.addWidget(card3)
        
        layout.addLayout(cards_layout)
    
    def setup_connections(self):
        """设置信号连接"""
        # 顶部栏信号
        self.top_bar.menu_toggle_clicked.connect(self.toggle_sidebar)
        self.top_bar.theme_changed.connect(self.change_theme)
        
        # 侧边栏信号
        self.sidebar.page_changed.connect(self.on_page_changed)
        self.sidebar.collapse_toggled.connect(self.on_sidebar_collapsed)
    
    def toggle_sidebar(self):
        """切换侧边栏"""
        self.sidebar.toggle_collapse()
    
    def change_theme(self, theme_name):
        """切换主题"""
        self.theme_manager.switch_theme(theme_name)
        print(f"主题已切换到: {theme_name}")
    
    def on_page_changed(self, page_id):
        """页面切换"""
        print(f"导航到页面: {page_id}")
        
        # 更新页面标题
        page_titles = {
            'dashboard': '实时监测仪表板',
            'patients': '患者管理',
            'treatment': '治疗系统',
            'data_management': '数据管理',
            'reports': '报告分析',
            'users': '用户管理',
            'settings': '系统设置',
            'logout': '退出系统'
        }
        
        title = page_titles.get(page_id, '未知页面')
        self.top_bar.set_page_title(title)
        
        if page_id == 'logout':
            print("退出系统请求")
    
    def on_sidebar_collapsed(self, is_collapsed):
        """侧边栏折叠状态变化"""
        status = "折叠" if is_collapsed else "展开"
        print(f"侧边栏已{status}")


def main():
    """主函数"""
    print("=" * 50)
    print("NK脑机接口康复系统 - 纯UI组件测试")
    print("=" * 50)
    
    try:
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("NK脑机接口康复系统 - UI测试")
        
        # 创建测试窗口
        window = UITestWindow()
        window.show()
        
        print("UI测试窗口已启动")
        print("\n测试功能：")
        print("1. 主题切换 - 点击顶部栏右侧的主题开关")
        print("2. 侧边栏折叠 - 点击顶部栏左侧的菜单按钮")
        print("3. 导航切换 - 点击侧边栏的菜单项")
        print("4. 状态监控 - 观察顶部栏的状态指示器")
        print("\n关闭窗口退出测试")
        
        # 运行应用程序
        return app.exec()
        
    except Exception as e:
        print(f"UI测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())

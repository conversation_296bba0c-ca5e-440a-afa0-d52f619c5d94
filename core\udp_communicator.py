#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UDP通信模块
UDP Communication Module

用于与VR系统进行UDP通信，参考原QT项目UDP.cpp实现

作者: AI Assistant
版本: 1.0.0
"""

import logging
import socket
import threading
import time
from typing import Optional, Callable
from enum import Enum

from utils.app_config import AppConfig


class UDPCommand(Enum):
    """UDP指令枚举（参考原QT项目）"""
    CALM = "calm"        # 平静状态
    START = "start"      # 开启VR软件动画的播放
    TRAIN = "train"      # 训练模式
    TREAT = "treat"      # 治疗模式
    STOP = "stop"        # 停止当前动画
    STOPALL = "stopall"  # 结束治疗
    EXIT = "exit"        # 退出VR软件


class UDPCommunicator:
    """UDP通信器 - 参考原QT项目UDP类实现"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 从配置获取UDP参数
        udp_config = AppConfig.NETWORK_CONFIG['udp']
        workflow_config = AppConfig.TREATMENT_WORKFLOW_CONFIG
        
        self.local_port = udp_config['local_port']      # 3005 - 本地绑定端口
        self.target_host = udp_config['vr_host']        # 127.0.0.1
        self.target_port = udp_config['vr_port']        # 3004 - VR系统端口
        self.retry_count = workflow_config['udp_retry_count']    # 重发次数
        self.retry_delay = workflow_config['udp_retry_delay']    # 重发间隔
        
        # UDP套接字
        self.socket = None
        self.is_listening = False
        self.listen_thread = None
        
        # 消息处理回调
        self.message_callback: Optional[Callable[[str, str, int], None]] = None
        
        self.logger.info(f"UDP通信器初始化 - 本地端口: {self.local_port}, 目标: {self.target_host}:{self.target_port}")

    def start_listening(self) -> bool:
        """
        开始监听UDP消息（参考原QT项目bind和connect逻辑）
        
        Returns:
            bool: 启动是否成功
        """
        try:
            if self.is_listening:
                self.logger.warning("UDP监听已在运行")
                return True
                
            # 创建UDP套接字
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            
            # 绑定本地端口（参考原QT: udpSocket->bind(3005)）
            self.socket.bind(('', self.local_port))
            self.socket.settimeout(1.0)  # 设置超时，便于线程退出
            
            # 启动监听线程
            self.is_listening = True
            self.listen_thread = threading.Thread(target=self._listen_worker, daemon=True)
            self.listen_thread.start()
            
            self.logger.info(f"UDP监听已启动，绑定端口: {self.local_port}")
            return True
            
        except Exception as e:
            self.logger.error(f"启动UDP监听失败: {e}")
            self.is_listening = False
            if self.socket:
                self.socket.close()
                self.socket = None
            return False

    def stop_listening(self):
        """停止UDP监听"""
        try:
            self.is_listening = False
            
            if self.listen_thread and self.listen_thread.is_alive():
                self.listen_thread.join(timeout=2.0)
                
            if self.socket:
                self.socket.close()
                self.socket = None
                
            self.logger.info("UDP监听已停止")
            
        except Exception as e:
            self.logger.error(f"停止UDP监听失败: {e}")

    def _listen_worker(self):
        """UDP监听工作线程（参考原QT项目dealMsg方法）"""
        while self.is_listening:
            try:
                if not self.socket:
                    break
                    
                # 接收数据（参考原QT: readDatagram）
                data, addr = self.socket.recvfrom(1024)
                
                if data:
                    message = data.decode('utf-8', errors='ignore')
                    host, port = addr
                    
                    # 格式化消息（参考原QT格式: [IP:PORT] MESSAGE）
                    formatted_msg = f"[{host}:{port}] {message}"
                    self.logger.debug(f"收到UDP消息: {formatted_msg}")
                    
                    # 调用回调函数处理消息
                    if self.message_callback:
                        try:
                            self.message_callback(message, host, port)
                        except Exception as e:
                            self.logger.error(f"处理UDP消息回调失败: {e}")
                            
            except socket.timeout:
                continue  # 超时继续循环
            except Exception as e:
                if self.is_listening:  # 只在监听状态下记录错误
                    self.logger.error(f"UDP监听线程错误: {e}")
                break

    def send_command(self, command: UDPCommand) -> bool:
        """
        发送UDP指令到VR系统（参考原QT项目senddata方法）
        
        Args:
            command: UDP指令枚举
            
        Returns:
            bool: 发送是否成功
        """
        try:
            command_str = command.value
            
            # 参考原QT项目：udpSocket->writeDatagram(buf.toUtf8(),QHostAddress("127.0.0.1"),3004)
            success = self._send_udp_data(command_str)
            
            if success:
                self.logger.debug(f"UDP指令发送成功: {command_str}")
            else:
                self.logger.warning(f"UDP指令发送失败: {command_str}")
                
            return success
            
        except Exception as e:
            self.logger.error(f"发送UDP指令异常: {e}")
            return False

    def send_command_with_retry(self, command: UDPCommand) -> bool:
        """
        发送UDP指令并重试（参考原QT项目中多次发送stopall的逻辑）
        
        Args:
            command: UDP指令枚举
            
        Returns:
            bool: 发送是否成功
        """
        for attempt in range(self.retry_count):
            if self.send_command(command):
                return True
                
            if attempt < self.retry_count - 1:  # 不是最后一次尝试
                time.sleep(self.retry_delay)
                
        self.logger.error(f"UDP指令重试{self.retry_count}次后仍失败: {command.value}")
        return False

    def _send_udp_data(self, data: str) -> bool:
        """
        发送UDP数据的底层实现
        
        Args:
            data: 要发送的数据字符串
            
        Returns:
            bool: 发送是否成功
        """
        try:
            # 创建临时套接字发送数据
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as send_socket:
                send_socket.settimeout(1.0)
                
                # 发送数据到目标地址
                data_bytes = data.encode('utf-8')
                send_socket.sendto(data_bytes, (self.target_host, self.target_port))
                
            return True
            
        except Exception as e:
            self.logger.error(f"UDP数据发送失败: {e}")
            return False

    def set_message_callback(self, callback: Callable[[str, str, int], None]):
        """
        设置消息处理回调函数
        
        Args:
            callback: 回调函数，参数为 (message, host, port)
        """
        self.message_callback = callback
        self.logger.debug("UDP消息回调函数已设置")

    def is_connected(self) -> bool:
        """
        检查UDP连接状态
        
        Returns:
            bool: 是否正在监听
        """
        return self.is_listening and self.socket is not None

    def __del__(self):
        """析构函数，确保资源清理"""
        self.stop_listening()


# 全局UDP通信器实例
_udp_communicator: Optional[UDPCommunicator] = None

def get_udp_communicator() -> UDPCommunicator:
    """获取全局UDP通信器实例"""
    global _udp_communicator
    if _udp_communicator is None:
        _udp_communicator = UDPCommunicator()
    return _udp_communicator

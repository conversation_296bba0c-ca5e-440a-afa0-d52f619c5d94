#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的主程序入口
"""

import os
import sys
import time
from pathlib import Path

# 启动优化设置
os.environ['PYTHONOPTIMIZE'] = '1'  # 启用Python优化
os.environ['QT_LOGGING_RULES'] = '*.debug=false'  # 禁用Qt调试日志

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """优化的主函数"""
    print("🚀 启动NK脑机接口康复训练系统 (优化版)")
    
    start_time = time.time()
    
    try:
        # 1. 应用启动优化
        from startup_optimizer import StartupOptimizer
        optimizer = StartupOptimizer()
        optimizer.apply_all_optimizations()
        
        # 2. 导入主程序
        from main import NKSystemApp
        
        # 3. 运行应用
        app = NKSystemApp()
        result = app.run()
        
        total_time = time.time() - start_time
        print(f"✅ 系统启动完成，总耗时: {total_time:.2f}s")
        
        return result
        
    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())


# 测试模式支持
if "--test-mode" in sys.argv:
    print("启动测试模式...")
    
    # 模拟启动过程但不显示界面
    class TestNKApplication(NKApplication):
        def show_splash_screen(self):
            pass  # 跳过启动画面
            
        def show_main_window(self):
            # 记录启动完成时间
            total_startup_time = time.time() - self.startup_time
            print(f"测试模式启动完成: {total_startup_time:.2f}s")
            
            # 立即退出
            if self.app:
                self.app.quit()
    
    # 使用测试版本
    def test_main():
        nk_app = TestNKApplication()
        try:
            exit_code = nk_app.run()
        except KeyboardInterrupt:
            exit_code = 0
        except Exception as e:
            print(f"测试启动失败: {e}")
            exit_code = 1
        finally:
            nk_app.cleanup()
        sys.exit(exit_code)
    
    if __name__ == "__main__":
        test_main()

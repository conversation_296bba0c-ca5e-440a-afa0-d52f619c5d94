#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脑电信号预处理模块
EEG Signal Processing Module

作者: AI Assistant
版本: 1.0.0
"""

import numpy as np
import logging
from typing import List, Tuple, Optional, Dict, Any
from dataclasses import dataclass
from scipy import signal
from scipy.signal import butter, filtfilt, welch
from sklearn.decomposition import FastICA
import warnings

warnings.filterwarnings('ignore')


@dataclass
class SignalQuality:
    """信号质量评估结果"""
    overall_quality: float  # 总体质量 0-1
    noise_level: float     # 噪声水平
    artifact_ratio: float  # 伪迹比例
    channel_quality: List[float]  # 各通道质量
    is_usable: bool       # 是否可用


class EEGSignalProcessor:
    """脑电信号预处理器"""

    def __init__(self, sample_rate: float = 125.0, channels: int = 8):
        """初始化信号处理器"""
        self.logger = logging.getLogger(__name__)
        self.sample_rate = sample_rate
        self.channels = channels

        # 滤波器参数
        self.lowcut = 8.0    # 低频截止 (mu节律下限)
        self.highcut = 30.0  # 高频截止 (beta节律上限)
        self.notch_freq = 50.0  # 工频陷波

        # 信号质量阈值
        self.quality_threshold = 0.6
        self.artifact_threshold = 0.3

        # 缓存滤波器系数
        self._filter_cache = {}

        self.logger.info("脑电信号处理器初始化完成")

    def preprocess_signal(self, raw_data: np.ndarray) -> Tuple[np.ndarray, SignalQuality]:
        """
        预处理脑电信号

        Args:
            raw_data: 原始脑电数据 [channels, samples]

        Returns:
            processed_data: 预处理后的数据
            quality: 信号质量评估
        """
        try:
            # 输入验证
            if raw_data.shape[0] != self.channels:
                raise ValueError(f"通道数不匹配: 期望{self.channels}, 实际{raw_data.shape[0]}")

            # 1. 基线校正
            processed_data = self._baseline_correction(raw_data)

            # 2. 工频陷波滤波
            processed_data = self._notch_filter(processed_data)

            # 3. 带通滤波
            processed_data = self._bandpass_filter(processed_data)

            # 4. 伪迹去除
            processed_data, artifact_ratio = self._artifact_removal(processed_data)

            # 5. 空间滤波 (CAR - Common Average Reference)
            processed_data = self._spatial_filter(processed_data)

            # 6. 信号质量评估
            quality = self._assess_signal_quality(processed_data, artifact_ratio)

            self.logger.debug(f"信号预处理完成，质量评分: {quality.overall_quality:.3f}")

            return processed_data, quality

        except Exception as e:
            self.logger.error(f"信号预处理失败: {e}")
            # 返回原始数据和低质量评估
            quality = SignalQuality(0.0, 1.0, 1.0, [0.0] * self.channels, False)
            return raw_data, quality

    def extract_features(self, data: np.ndarray, window_size: float = 1.0) -> Dict[str, np.ndarray]:
        """
        提取运动想象特征

        Args:
            data: 预处理后的脑电数据 [channels, samples]
            window_size: 窗口大小(秒)

        Returns:
            features: 特征字典
        """
        try:
            window_samples = int(window_size * self.sample_rate)
            n_windows = data.shape[1] // window_samples

            features = {
                'power_spectral_density': [],
                'band_power': [],
                'spectral_entropy': [],
                'hjorth_parameters': []
            }

            for i in range(n_windows):
                start_idx = i * window_samples
                end_idx = start_idx + window_samples
                window_data = data[:, start_idx:end_idx]

                # 功率谱密度特征
                psd_features = self._extract_psd_features(window_data)
                features['power_spectral_density'].append(psd_features)

                # 频带功率特征
                band_power = self._extract_band_power(window_data)
                features['band_power'].append(band_power)

                # 谱熵特征
                spectral_entropy = self._extract_spectral_entropy(window_data)
                features['spectral_entropy'].append(spectral_entropy)

                # Hjorth参数
                hjorth_params = self._extract_hjorth_parameters(window_data)
                features['hjorth_parameters'].append(hjorth_params)

            # 转换为numpy数组
            for key in features:
                features[key] = np.array(features[key])

            self.logger.debug(f"特征提取完成，窗口数: {n_windows}")
            return features

        except Exception as e:
            self.logger.error(f"特征提取失败: {e}")
            return {}

    def _baseline_correction(self, data: np.ndarray) -> np.ndarray:
        """基线校正"""
        return data - np.mean(data, axis=1, keepdims=True)

    def _notch_filter(self, data: np.ndarray) -> np.ndarray:
        """工频陷波滤波"""
        try:
            # 检查数据长度是否足够进行滤波
            min_samples_required = 40  # 为filtfilt提供足够的数据长度
            if data.shape[1] < min_samples_required:
                self.logger.debug(f"数据长度不足({data.shape[1]} < {min_samples_required})，跳过陷波滤波")
                return data

            # 设计陷波滤波器
            nyquist = self.sample_rate / 2
            low = (self.notch_freq - 1) / nyquist
            high = (self.notch_freq + 1) / nyquist

            b, a = butter(4, [low, high], btype='bandstop')

            # 应用滤波器
            filtered_data = np.zeros_like(data)
            for ch in range(data.shape[0]):
                try:
                    filtered_data[ch] = filtfilt(b, a, data[ch])
                except ValueError as ve:
                    if "padlen" in str(ve):
                        self.logger.debug(f"通道{ch}数据长度不足进行陷波滤波，使用原始数据")
                        filtered_data[ch] = data[ch]
                    else:
                        raise ve

            return filtered_data

        except Exception as e:
            self.logger.warning(f"陷波滤波失败: {e}")
            return data

    def _bandpass_filter(self, data: np.ndarray) -> np.ndarray:
        """带通滤波"""
        try:
            # 检查数据长度是否足够进行滤波
            min_samples_required = 50  # 为filtfilt提供足够的数据长度
            if data.shape[1] < min_samples_required:
                self.logger.debug(f"数据长度不足({data.shape[1]} < {min_samples_required})，跳过带通滤波")
                return data

            # 设计带通滤波器
            nyquist = self.sample_rate / 2
            low = self.lowcut / nyquist
            high = self.highcut / nyquist

            b, a = butter(5, [low, high], btype='band')

            # 应用滤波器
            filtered_data = np.zeros_like(data)
            for ch in range(data.shape[0]):
                try:
                    filtered_data[ch] = filtfilt(b, a, data[ch])
                except ValueError as ve:
                    if "padlen" in str(ve):
                        self.logger.debug(f"通道{ch}数据长度不足进行滤波，使用原始数据")
                        filtered_data[ch] = data[ch]
                    else:
                        raise ve

            return filtered_data

        except Exception as e:
            self.logger.warning(f"带通滤波失败: {e}")
            return data

    def _artifact_removal(self, data: np.ndarray) -> Tuple[np.ndarray, float]:
        """改进的伪迹去除算法"""
        try:
            # 多维度伪迹检测
            channel_artifacts = []
            cleaned_data = data.copy()

            for ch in range(data.shape[0]):
                channel_data = data[ch]
                artifact_score = self._detect_channel_artifacts(channel_data)
                channel_artifacts.append(artifact_score)

                # 如果伪迹严重，尝试简单修复
                if artifact_score > 0.7:
                    cleaned_data[ch] = self._simple_artifact_correction(channel_data)

            # 计算平均伪迹比例
            avg_artifact_ratio = np.mean(channel_artifacts)

            # 如果整体伪迹严重，尝试空间滤波修复
            if avg_artifact_ratio > 0.6:
                cleaned_data = self._spatial_artifact_correction(cleaned_data)

            return cleaned_data, avg_artifact_ratio

        except Exception as e:
            self.logger.warning(f"改进伪迹检测失败: {e}")
            return data, 0.0

    def _detect_channel_artifacts(self, channel_data: np.ndarray) -> float:
        """检测单通道伪迹"""
        try:
            artifact_indicators = []

            # 1. 幅值异常检测
            signal_std = np.std(channel_data)
            if signal_std > 5000.0:
                artifact_indicators.append(0.9)
            elif signal_std > 2000.0:
                artifact_indicators.append(0.4)
            elif signal_std < 1.0:
                artifact_indicators.append(0.6)
            else:
                artifact_indicators.append(0.0)

            # 2. 饱和检测
            max_val = np.max(np.abs(channel_data))
            if max_val > 32000:  # 接近ADC饱和
                artifact_indicators.append(0.8)
            else:
                artifact_indicators.append(0.0)

            # 3. 跳跃检测
            diff_signal = np.diff(channel_data)
            jump_threshold = 5 * np.std(diff_signal)
            large_jumps = np.sum(np.abs(diff_signal) > jump_threshold)
            jump_ratio = large_jumps / len(diff_signal)
            artifact_indicators.append(min(1.0, jump_ratio * 10))

            # 4. 平坦段检测
            flat_threshold = 0.1 * signal_std
            flat_samples = np.sum(np.abs(diff_signal) < flat_threshold)
            flat_ratio = flat_samples / len(diff_signal)
            if flat_ratio > 0.8:  # 80%以上的样本变化很小
                artifact_indicators.append(0.7)
            else:
                artifact_indicators.append(0.0)

            # 5. 频域异常检测
            try:
                freqs, psd = welch(channel_data, self.sample_rate, nperseg=min(256, len(channel_data)//4))

                # 检查高频噪声
                high_freq_mask = freqs > 50
                if np.any(high_freq_mask):
                    high_freq_power = np.mean(psd[high_freq_mask])
                    total_power = np.mean(psd)
                    if high_freq_power > 0.3 * total_power:
                        artifact_indicators.append(0.5)
                    else:
                        artifact_indicators.append(0.0)
                else:
                    artifact_indicators.append(0.0)

            except:
                artifact_indicators.append(0.0)

            # 综合评分
            return np.mean(artifact_indicators)

        except Exception as e:
            self.logger.warning(f"通道伪迹检测失败: {e}")
            return 0.5

    def _simple_artifact_correction(self, channel_data: np.ndarray) -> np.ndarray:
        """简单伪迹修复"""
        try:
            corrected_data = channel_data.copy()

            # 1. 饱和修复 - 限幅
            max_reasonable = 30000
            corrected_data = np.clip(corrected_data, -max_reasonable, max_reasonable)

            # 2. 跳跃修复 - 中值滤波
            from scipy.signal import medfilt
            if len(corrected_data) > 5:
                kernel_size = min(5, len(corrected_data) // 10)
                if kernel_size % 2 == 0:
                    kernel_size += 1
                corrected_data = medfilt(corrected_data, kernel_size=kernel_size)

            return corrected_data

        except Exception as e:
            self.logger.warning(f"简单伪迹修复失败: {e}")
            return channel_data

    def _spatial_artifact_correction(self, data: np.ndarray) -> np.ndarray:
        """空间伪迹修复 - 基于通道间相关性"""
        try:
            corrected_data = data.copy()

            # 计算通道间相关性
            correlation_matrix = np.corrcoef(data)

            for ch in range(data.shape[0]):
                # 找到与当前通道相关性最高的其他通道
                correlations = correlation_matrix[ch, :]
                correlations[ch] = 0  # 排除自身

                # 如果当前通道与其他通道相关性都很低，可能有问题
                max_corr = np.max(np.abs(correlations))
                if max_corr < 0.3:  # 相关性阈值
                    # 使用相邻通道的平均值进行修复
                    neighbor_indices = []
                    if ch > 0:
                        neighbor_indices.append(ch - 1)
                    if ch < data.shape[0] - 1:
                        neighbor_indices.append(ch + 1)

                    if neighbor_indices:
                        neighbor_data = data[neighbor_indices, :]
                        corrected_data[ch, :] = np.mean(neighbor_data, axis=0)

            return corrected_data

        except Exception as e:
            self.logger.warning(f"空间伪迹修复失败: {e}")
            return data

    def _spatial_filter(self, data: np.ndarray) -> np.ndarray:
        """空间滤波 - 共同平均参考"""
        try:
            # 计算所有通道的平均值
            car = np.mean(data, axis=0, keepdims=True)
            # 从每个通道减去平均值
            return data - car

        except Exception as e:
            self.logger.warning(f"空间滤波失败: {e}")
            return data

    def _assess_signal_quality(self, data: np.ndarray, artifact_ratio: float) -> SignalQuality:
        """
        评估信号质量 - 简化版

        基于实际测试结果，采用简化的评估方法：
        1. 信号幅值检测 - 主要指标，检查信号强度
        2. 连接状态检测 - 检查电极连接质量
        3. 伪迹检测 - 检查信号是否过大

        质量分数计算：
        - 优秀 (0.8-1.0): 信号幅值在理想范围，连接良好
        - 良好 (0.6-0.8): 信号幅值适中，连接正常
        - 一般 (0.4-0.6): 信号幅值较小，连接一般
        - 较差 (0.0-0.4): 信号幅值很小或很大，连接不良
        """
        try:
            # 1. 计算各通道的质量指标
            channel_quality = []
            channel_amplitude = []

            for ch in range(data.shape[0]):
                channel_data = data[ch]
                signal_std = np.std(channel_data)
                signal_var = np.var(channel_data)

                # 基于幅值的质量评估
                if signal_std < 5.0:
                    # 信号太小，可能是电极脱落或设备未连接
                    amplitude_score = 0.1
                elif signal_std < 20.0:
                    # 信号较小，可能是连接不良
                    amplitude_score = 0.3
                elif signal_std < 100.0:
                    # 信号适中，质量一般
                    amplitude_score = 0.6
                elif signal_std < 1000.0:
                    # 合理的信号幅值范围，质量良好
                    amplitude_score = 0.8
                elif signal_std < 5000.0:
                    # 较大的信号，可能包含一些伪迹但仍可用
                    amplitude_score = 0.6
                else:
                    # 信号过大，可能是严重伪迹或饱和
                    amplitude_score = 0.2

                # 基于方差的连接状态检查
                if signal_var < 1.0:
                    # 信号几乎没有变化，连接很差
                    connection_factor = 0.1
                elif signal_var < 10.0:
                    # 信号变化很小，连接不良
                    connection_factor = 0.5
                else:
                    # 信号有正常变化，连接良好
                    connection_factor = 1.0

                # 综合评分
                channel_score = amplitude_score * connection_factor

                channel_quality.append(channel_score)
                channel_amplitude.append(signal_std)

            # 2. 总体质量评估
            avg_channel_quality = np.mean(channel_quality)
            avg_amplitude = np.mean(channel_amplitude)

            # 伪迹惩罚 - 减少权重
            artifact_penalty = artifact_ratio * 0.1

            # 最终质量分数
            overall_quality = max(0.0, avg_channel_quality - artifact_penalty)

            # 噪声水平评估 - 基于信号幅值
            if avg_amplitude < 10.0:
                noise_level = 0.9  # 信号太小，噪声水平高
            elif avg_amplitude < 100.0:
                noise_level = 0.5  # 信号适中，噪声水平中等
            else:
                noise_level = 0.2  # 信号较大，噪声水平低

            # 可用性判断 - 调整标准
            is_usable = (overall_quality > 0.5 and  # 降低质量阈值
                        artifact_ratio < self.artifact_threshold and
                        avg_amplitude > 30.0)  # 信号幅值至少要大于30

            self.logger.debug(f"信号质量评估: 总体={overall_quality:.3f}, 平均幅值={avg_amplitude:.1f}, 伪迹={artifact_ratio:.3f}")

            return SignalQuality(
                overall_quality=overall_quality,
                noise_level=noise_level,
                artifact_ratio=artifact_ratio,
                channel_quality=channel_quality,
                is_usable=is_usable
            )

        except Exception as e:
            self.logger.error(f"信号质量评估失败: {e}")
            return SignalQuality(0.0, 1.0, 1.0, [0.0] * self.channels, False)

    def _assess_signal_amplitude(self, data: np.ndarray) -> float:
        """评估信号幅值质量"""
        try:
            # 计算信号的标准差作为幅值指标
            signal_std = np.std(data)
            signal_range = np.ptp(data)  # peak-to-peak

            # 检查信号是否在合理范围内
            # ADS1299的ADC值范围通常在几千到几万之间
            if signal_std < 5.0:
                # 信号太小，可能是电极脱落或设备未连接
                amplitude_score = 0.1
            elif signal_std < 20.0:
                # 信号较小，可能是连接不良
                amplitude_score = 0.3
            elif signal_std < 100.0:
                # 信号较小，但可能有一定质量
                amplitude_score = 0.5
            elif signal_std < 1000.0:
                # 合理的信号幅值范围
                amplitude_score = 0.8
            elif signal_std < 5000.0:
                # 较大的信号，可能包含一些伪迹但仍可用
                amplitude_score = 0.6
            else:
                # 信号过大，可能是严重伪迹或饱和
                amplitude_score = 0.2

            return amplitude_score

        except Exception as e:
            self.logger.warning(f"幅值评估失败: {e}")
            return 0.3  # 默认较低质量

    def _assess_signal_stability(self, data: np.ndarray) -> float:
        """评估信号稳定性"""
        try:
            # 将信号分段，检查各段的一致性
            segment_length = len(data) // 4
            if segment_length < 10:
                return 0.5

            segment_stds = []
            for i in range(4):
                start_idx = i * segment_length
                end_idx = start_idx + segment_length
                segment = data[start_idx:end_idx]
                segment_stds.append(np.std(segment))

            # 计算各段标准差的变异系数
            mean_std = np.mean(segment_stds)
            std_of_stds = np.std(segment_stds)

            if mean_std > 0:
                cv = std_of_stds / mean_std
                # 变异系数越小，信号越稳定
                stability_score = max(0.0, 1.0 - cv)
            else:
                stability_score = 0.1  # 信号过于平坦

            return stability_score

        except Exception as e:
            self.logger.warning(f"稳定性评估失败: {e}")
            return 0.5

    def _assess_connection_status(self, data: np.ndarray) -> float:
        """评估电极连接状态"""
        try:
            # 检查信号是否过于平坦（可能表示电极脱落）
            signal_var = np.var(data)

            if signal_var < 1.0:
                # 信号几乎没有变化，可能是电极脱落
                connection_score = 0.0
            elif signal_var < 10.0:
                # 信号变化很小，连接可能不良
                connection_score = 0.3
            else:
                # 信号有正常变化，连接良好
                connection_score = 1.0

            # 检查是否有异常的直流偏移
            signal_mean = np.abs(np.mean(data))
            if signal_mean > np.std(data) * 3:
                # 有较大的直流偏移，可能是连接问题
                connection_score *= 0.5

            return connection_score

        except Exception as e:
            self.logger.warning(f"连接状态评估失败: {e}")
            return 0.5

    def _assess_frequency_features(self, data: np.ndarray) -> float:
        """评估频域特征质量 - 改进版"""
        try:
            from scipy.signal import welch

            # 计算功率谱
            freqs, psd = welch(data, self.sample_rate, nperseg=min(256, len(data)//4))

            # 检查是否有明显的频域结构（而不是白噪声）
            # 白噪声的功率谱应该是平坦的
            psd_normalized = psd / np.sum(psd)

            # 计算功率谱的熵 - 白噪声的熵较高
            spectral_entropy = -np.sum(psd_normalized * np.log2(psd_normalized + 1e-10))
            max_entropy = np.log2(len(psd))
            normalized_entropy = spectral_entropy / max_entropy

            # 熵越低，说明频域结构越明显
            structure_score = 1.0 - normalized_entropy

            # 检查脑电相关频带的相对功率
            alpha_band = (8, 13)
            beta_band = (13, 30)

            alpha_mask = (freqs >= alpha_band[0]) & (freqs <= alpha_band[1])
            beta_mask = (freqs >= beta_band[0]) & (freqs <= beta_band[1])

            alpha_power = np.sum(psd[alpha_mask])
            beta_power = np.sum(psd[beta_mask])
            total_power = np.sum(psd)

            # 脑电频带功率占比
            brain_power_ratio = (alpha_power + beta_power) / (total_power + 1e-10)

            # 综合频域质量评分
            freq_quality = 0.6 * structure_score + 0.4 * min(1.0, brain_power_ratio * 2.0)

            return freq_quality

        except Exception as e:
            self.logger.warning(f"频域特征评估失败: {e}")
            return 0.3  # 默认较低质量



    def _extract_psd_features(self, data: np.ndarray) -> np.ndarray:
        """提取功率谱密度特征"""
        try:
            psd_features = []
            for ch in range(data.shape[0]):
                freqs, psd = welch(data[ch], self.sample_rate, nperseg=min(256, data.shape[1]//4))
                # 选择感兴趣的频率范围
                freq_mask = (freqs >= self.lowcut) & (freqs <= self.highcut)
                psd_features.extend(psd[freq_mask])

            return np.array(psd_features)

        except Exception as e:
            self.logger.warning(f"PSD特征提取失败: {e}")
            return np.zeros(self.channels * 10)  # 返回默认特征

    def _extract_band_power(self, data: np.ndarray) -> np.ndarray:
        """提取频带功率特征"""
        try:
            # 定义频带
            bands = {
                'alpha': (8, 13),
                'beta': (13, 30),
                'mu': (8, 12)
            }

            band_powers = []
            for ch in range(data.shape[0]):
                for band_name, (low, high) in bands.items():
                    freqs, psd = welch(data[ch], self.sample_rate, nperseg=min(256, data.shape[1]//4))
                    freq_mask = (freqs >= low) & (freqs <= high)
                    band_power = np.sum(psd[freq_mask])
                    band_powers.append(band_power)

            return np.array(band_powers)

        except Exception as e:
            self.logger.warning(f"频带功率特征提取失败: {e}")
            return np.zeros(self.channels * 3)

    def _extract_spectral_entropy(self, data: np.ndarray) -> np.ndarray:
        """提取谱熵特征"""
        try:
            entropies = []
            for ch in range(data.shape[0]):
                freqs, psd = welch(data[ch], self.sample_rate, nperseg=min(256, data.shape[1]//4))
                # 归一化PSD
                psd_norm = psd / np.sum(psd)
                # 计算谱熵
                entropy = -np.sum(psd_norm * np.log2(psd_norm + 1e-10))
                entropies.append(entropy)

            return np.array(entropies)

        except Exception as e:
            self.logger.warning(f"谱熵特征提取失败: {e}")
            return np.zeros(self.channels)

    def _extract_hjorth_parameters(self, data: np.ndarray) -> np.ndarray:
        """提取Hjorth参数"""
        try:
            hjorth_params = []
            for ch in range(data.shape[0]):
                signal_data = data[ch]

                # Activity (variance)
                activity = np.var(signal_data)

                # Mobility
                diff1 = np.diff(signal_data)
                mobility = np.sqrt(np.var(diff1) / activity)

                # Complexity
                diff2 = np.diff(diff1)
                complexity = np.sqrt(np.var(diff2) / np.var(diff1)) / mobility

                hjorth_params.extend([activity, mobility, complexity])

            return np.array(hjorth_params)

        except Exception as e:
            self.logger.warning(f"Hjorth参数提取失败: {e}")
            return np.zeros(self.channels * 3)

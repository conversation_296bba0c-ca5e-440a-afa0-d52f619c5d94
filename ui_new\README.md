# NK脑机接口康复系统 - 现代化UI框架

## 概述

这是NK脑机接口康复系统的全新现代化UI框架，采用直接迁移模式重构，提供专业的医疗设备界面体验。

## 核心特性

### 🎨 双主题系统
- **医疗主题**: 专业的蓝白色调，符合医疗设备标准
- **科技主题**: 现代化深色荧光风格，科技感十足
- **动态切换**: 实时主题切换，无需重启

### 📱 现代化架构
- **单页应用**: 流畅的页面切换体验
- **组件化设计**: 高度模块化，易于维护
- **响应式布局**: 适配不同屏幕尺寸

### 🏥 医疗专业性
- **权限管理**: 完整的用户权限控制
- **操作日志**: 符合医疗器械软件规范
- **数据安全**: 患者隐私保护机制

### ⚡ 性能优化
- **延迟加载**: 按需加载页面组件
- **直接迁移**: 无适配器开销
- **内存优化**: 智能资源管理

## 项目结构

```
ui_new/
├── __init__.py                 # 模块初始化
├── main_window_new.py          # 现代化主窗口
├── components/                 # UI组件
│   ├── __init__.py
│   ├── sidebar.py             # 侧边栏组件
│   └── top_bar.py             # 顶部栏组件
├── pages/                     # 页面模块
│   ├── __init__.py
│   ├── base_page.py           # 页面基类
│   └── dashboard_page.py      # 实时监测页面
├── themes/                    # 主题系统
│   ├── __init__.py
│   ├── theme_manager.py       # 主题管理器
│   ├── medical_theme.py       # 医疗主题配置
│   └── tech_theme.py          # 科技主题配置
├── styles/                    # 样式文件
│   ├── __init__.py
│   ├── base_styles.qss        # 基础样式
│   ├── medical_theme.qss      # 医疗主题样式
│   └── tech_theme.qss         # 科技主题样式
├── test_new_ui.py             # 完整测试启动器
├── simple_test.py             # 简化测试启动器
└── README.md                  # 本文件
```

## 快速开始

### 1. 简化测试
运行基本UI功能测试：
```bash
cd ui_new
python simple_test.py
```

### 2. 完整测试
运行包含业务逻辑的完整测试：
```bash
cd ui_new
python test_new_ui.py
```

### 3. 集成到现有系统
```python
from ui_new.main_window_new import ModernMainWindow
from ui_new.themes.theme_manager import ThemeManager

# 创建现代化主窗口
main_window = ModernMainWindow()

# 设置业务组件（直接迁移）
main_window.set_database_manager(db_manager)
main_window.set_auth_manager(auth_manager)

# 显示窗口
main_window.show()
```

## 主要组件

### 1. ModernMainWindow
现代化主窗口，采用单页应用架构：
- 左侧：可折叠导航栏
- 右侧：顶部状态栏 + 动态内容区

### 2. ModernSidebar
现代化侧边栏组件：
- 分组导航菜单
- 用户信息显示
- 折叠/展开动画

### 3. ModernTopBar
现代化顶部栏组件：
- 菜单切换按钮
- 页面标题显示
- 主题切换开关
- 实时状态指示器

### 4. ThemeManager
主题管理器：
- 双主题配置
- 动态主题切换
- 样式表管理

### 5. BasePage
页面基类：
- 统一的页面生命周期
- 权限检查机制
- 数据加载管理
- 错误处理

## 主题系统

### 医疗主题 (Medical Theme)
```css
/* 主要颜色 */
--primary-color: #2563eb;      /* 主要蓝色 */
--secondary-color: #1e40af;    /* 深蓝色 */
--accent-color: #0ea5e9;       /* 强调蓝色 */
--bg-primary: #f8fafc;         /* 主背景 */
--bg-secondary: #ffffff;       /* 次背景 */
```

### 科技主题 (Tech Theme)
```css
/* 主要颜色 */
--primary-color: #06b6d4;      /* 主要青色 */
--secondary-color: #0891b2;    /* 深青色 */
--accent-color: #00d4ff;       /* 强调荧光青 */
--bg-primary: #0f172a;         /* 主背景 */
--bg-secondary: #1e293b;       /* 次背景 */
```

## 页面开发指南

### 1. 创建新页面
```python
from ui_new.pages.base_page import BasePage

class MyPage(BasePage):
    def setup_ui(self):
        # 实现UI布局
        pass
    
    def setup_connections(self):
        # 实现信号连接
        pass
    
    def load_data(self):
        # 实现数据加载
        pass
```

### 2. 注册页面
在 `main_window_new.py` 中添加：
```python
from .pages.my_page import MyPage

self.page_classes = {
    'my_page': MyPage,
    # ... 其他页面
}
```

### 3. 添加导航
在 `sidebar.py` 中添加导航项：
```python
("my_page", "我的页面", "🏠"),
```

## 业务逻辑迁移

### 直接迁移模式
新UI框架采用直接迁移模式，无适配器开销：

```python
# 直接使用现有业务组件
page.set_database_manager(db_manager)
page.set_auth_manager(auth_manager)

# 直接调用现有方法
patients = db_manager.get_patients()
has_permission = auth_manager.has_permission(Permission.PATIENT_VIEW)
```

## 测试功能

### 基本功能测试
1. **主题切换**: 顶部栏右侧开关
2. **侧边栏折叠**: 顶部栏左侧菜单按钮
3. **导航切换**: 侧边栏菜单项
4. **登录功能**: 点击登录按钮
5. **实时监测**: 登录后默认页面

### 高级功能测试
1. **权限管理**: 不同用户角色的功能限制
2. **数据加载**: 页面数据的异步加载
3. **状态管理**: 实时状态指示器更新
4. **错误处理**: 异常情况的用户友好提示

## 开发规范

### 1. 代码风格
- 使用类型提示
- 完整的异常处理
- 详细的日志记录
- 清晰的注释文档

### 2. 组件设计
- 单一职责原则
- 松耦合设计
- 可复用性
- 可测试性

### 3. 样式规范
- 使用QSS样式表
- 遵循主题系统
- 响应式设计
- 无障碍访问

## 下一步计划

1. **页面迁移**: 逐步迁移所有现有页面
2. **功能完善**: 添加更多现代化功能
3. **性能优化**: 进一步提升响应速度
4. **测试覆盖**: 完善自动化测试

## 技术支持

如有问题或建议，请查看：
1. 代码注释和文档
2. 测试文件示例
3. 日志输出信息

---

**版本**: 2.0.0  
**作者**: AI Assistant  
**更新时间**: 2024年

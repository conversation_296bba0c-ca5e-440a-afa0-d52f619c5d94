#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新UI框架配置文件
New UI Framework Configuration

作者: AI Assistant
版本: 2.0.0
"""

from typing import Dict, Any
from pathlib import Path


class UIConfig:
    """新UI框架配置类"""
    
    # 版本信息
    VERSION = "2.0.0"
    NAME = "NK脑机接口康复系统 - 现代化版本"
    
    # 主题配置
    THEME_CONFIG = {
        'default_theme': 'medical',  # 默认主题
        'available_themes': ['medical', 'tech'],  # 可用主题
        'theme_switch_animation': True,  # 主题切换动画
        'remember_theme': True,  # 记住用户主题选择
    }
    
    # 布局配置
    LAYOUT_CONFIG = {
        'sidebar_width': 280,  # 侧边栏宽度
        'sidebar_collapsed_width': 80,  # 折叠后宽度
        'top_bar_height': 72,  # 顶部栏高度
        'content_padding': 24,  # 内容区域内边距
        'card_spacing': 24,  # 卡片间距
        'animation_duration': 300,  # 动画持续时间(ms)
        'animation_easing': 'OutCubic',  # 动画缓动类型
    }
    
    # 窗口配置
    WINDOW_CONFIG = {
        'min_width': 1400,  # 最小宽度
        'min_height': 900,  # 最小高度
        'default_width': 1600,  # 默认宽度
        'default_height': 1000,  # 默认高度
        'remember_size': True,  # 记住窗口大小
        'remember_position': True,  # 记住窗口位置
        'center_on_startup': True,  # 启动时居中
    }
    
    # 性能配置
    PERFORMANCE_CONFIG = {
        'lazy_loading': True,  # 延迟加载页面
        'auto_refresh_interval': 30000,  # 自动刷新间隔(ms)
        'status_update_interval': 1000,  # 状态更新间隔(ms)
        'auto_save_interval': 300,  # 自动保存间隔(s)
        'max_cached_pages': 10,  # 最大缓存页面数
        'enable_animations': True,  # 启用动画效果
    }
    
    # 日志配置
    LOGGING_CONFIG = {
        'level': 'INFO',  # 日志级别
        'file_logging': True,  # 文件日志
        'console_logging': True,  # 控制台日志
        'max_log_files': 10,  # 最大日志文件数
        'log_file_size': 10 * 1024 * 1024,  # 日志文件大小(10MB)
        'log_format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    }
    
    # 安全配置
    SECURITY_CONFIG = {
        'auto_logout_timeout': 3600,  # 自动登出时间(s)
        'session_timeout_warning': 300,  # 会话超时警告(s)
        'max_login_attempts': 3,  # 最大登录尝试次数
        'password_complexity': True,  # 密码复杂度检查
        'audit_logging': True,  # 审计日志
    }
    
    # 数据配置
    DATA_CONFIG = {
        'auto_backup': True,  # 自动备份
        'backup_interval': 24 * 3600,  # 备份间隔(s)
        'max_backup_files': 30,  # 最大备份文件数
        'data_compression': True,  # 数据压缩
        'export_formats': ['csv', 'xlsx', 'pdf'],  # 支持的导出格式
    }
    
    # 界面文本配置
    UI_TEXT = {
        'app_title': 'NK脑机接口康复系统',
        'login_title': '用户登录',
        'logout_confirm': '确定要退出系统吗？',
        'save_confirm': '是否保存当前更改？',
        'delete_confirm': '确定要删除选中的项目吗？',
        'error_title': '错误',
        'warning_title': '警告',
        'info_title': '提示',
        'success_title': '成功',
    }
    
    # 页面配置
    PAGE_CONFIG = {
        'dashboard': {
            'title': '实时监测仪表板',
            'icon': '📊',
            'auto_refresh': True,
            'refresh_interval': 2000,  # 2秒
        },
        'patients': {
            'title': '患者管理',
            'icon': '👥',
            'auto_refresh': False,
            'page_size': 20,  # 分页大小
        },
        'treatment': {
            'title': '治疗系统',
            'icon': '⚕️',
            'auto_refresh': True,
            'refresh_interval': 1000,  # 1秒
        },
        'data_management': {
            'title': '数据管理',
            'icon': '💾',
            'auto_refresh': False,
            'export_enabled': True,
        },
        'reports': {
            'title': '报告分析',
            'icon': '📈',
            'auto_refresh': False,
            'chart_types': ['line', 'bar', 'pie'],
        },
        'users': {
            'title': '用户管理',
            'icon': '👤',
            'auto_refresh': False,
            'permission_required': 'USER_MANAGE',
        },
        'settings': {
            'title': '系统设置',
            'icon': '⚙️',
            'auto_refresh': False,
            'permission_required': 'SYSTEM_CONFIG',
        },
    }
    
    # 状态指示器配置
    STATUS_CONFIG = {
        'device': {
            'normal': {'text': '设备在线', 'color': 'success'},
            'warning': {'text': '设备连接中', 'color': 'warning'},
            'error': {'text': '设备离线', 'color': 'error'},
        },
        'signal': {
            'normal': {'text': '信号良好', 'color': 'success'},
            'warning': {'text': '信号微弱', 'color': 'warning'},
            'error': {'text': '信号中断', 'color': 'error'},
        },
        'system': {
            'normal': {'text': '系统正常', 'color': 'success'},
            'warning': {'text': '系统警告', 'color': 'warning'},
            'error': {'text': '系统错误', 'color': 'error'},
        },
    }
    
    # 开发配置
    DEVELOPMENT_CONFIG = {
        'debug_mode': False,  # 调试模式
        'show_debug_info': False,  # 显示调试信息
        'enable_hot_reload': False,  # 热重载
        'mock_data': False,  # 使用模拟数据
        'performance_monitoring': False,  # 性能监控
    }
    
    @classmethod
    def get_config(cls, section: str = None) -> Dict[str, Any]:
        """获取配置
        
        Args:
            section: 配置节名称，None表示获取所有配置
            
        Returns:
            配置字典
        """
        if section is None:
            return {
                'theme': cls.THEME_CONFIG,
                'layout': cls.LAYOUT_CONFIG,
                'window': cls.WINDOW_CONFIG,
                'performance': cls.PERFORMANCE_CONFIG,
                'logging': cls.LOGGING_CONFIG,
                'security': cls.SECURITY_CONFIG,
                'data': cls.DATA_CONFIG,
                'ui_text': cls.UI_TEXT,
                'page': cls.PAGE_CONFIG,
                'status': cls.STATUS_CONFIG,
                'development': cls.DEVELOPMENT_CONFIG,
            }
        
        config_map = {
            'theme': cls.THEME_CONFIG,
            'layout': cls.LAYOUT_CONFIG,
            'window': cls.WINDOW_CONFIG,
            'performance': cls.PERFORMANCE_CONFIG,
            'logging': cls.LOGGING_CONFIG,
            'security': cls.SECURITY_CONFIG,
            'data': cls.DATA_CONFIG,
            'ui_text': cls.UI_TEXT,
            'page': cls.PAGE_CONFIG,
            'status': cls.STATUS_CONFIG,
            'development': cls.DEVELOPMENT_CONFIG,
        }
        
        return config_map.get(section, {})
    
    @classmethod
    def get_page_config(cls, page_id: str) -> Dict[str, Any]:
        """获取页面配置
        
        Args:
            page_id: 页面ID
            
        Returns:
            页面配置字典
        """
        return cls.PAGE_CONFIG.get(page_id, {})
    
    @classmethod
    def get_status_config(cls, status_type: str) -> Dict[str, Any]:
        """获取状态配置
        
        Args:
            status_type: 状态类型
            
        Returns:
            状态配置字典
        """
        return cls.STATUS_CONFIG.get(status_type, {})
    
    @classmethod
    def is_debug_mode(cls) -> bool:
        """是否为调试模式"""
        return cls.DEVELOPMENT_CONFIG.get('debug_mode', False)
    
    @classmethod
    def get_theme_list(cls) -> list:
        """获取可用主题列表"""
        return cls.THEME_CONFIG.get('available_themes', ['medical'])
    
    @classmethod
    def get_default_theme(cls) -> str:
        """获取默认主题"""
        return cls.THEME_CONFIG.get('default_theme', 'medical')


# 全局配置实例
ui_config = UIConfig()

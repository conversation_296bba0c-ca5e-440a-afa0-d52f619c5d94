#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ADS1299脑电设备通信模块
ADS1299 EEG Device Communication Module

作者: AI Assistant
版本: 1.0.0
"""

import logging
import struct
import threading
import time
from typing import Optional, Callable, List, Tuple, Dict, Any
from dataclasses import dataclass
from enum import Enum

try:
    import serial
    SERIAL_AVAILABLE = True
except ImportError:
    SERIAL_AVAILABLE = False
    logging.warning("pyserial未安装，脑电设备功能将不可用")

from utils.app_config import AppConfig


class EEGDeviceStatus(Enum):
    """脑电设备状态枚举"""
    DISCONNECTED = "未连接"
    CONNECTING = "连接中"
    CONNECTED = "已连接"
    ERROR = "错误"
    TIMEOUT = "超时"


@dataclass
class EEGDataPacket:
    """脑电数据包"""
    timestamp: float
    channel_data: List[List[int]]  # 4组数据，每组8个通道
    packet_number: int
    is_valid: bool


class ADS1299Device:
    """ADS1299脑电设备控制器"""

    def __init__(self):
        """初始化ADS1299设备"""
        self.logger = logging.getLogger(__name__)

        # 设备配置
        self.config = AppConfig.EEG_CONFIG
        self.serial_port: Optional[serial.Serial] = None
        self.status = EEGDeviceStatus.DISCONNECTED

        # 数据接收
        self.data_callback: Optional[Callable[[EEGDataPacket], None]] = None
        self.receive_thread: Optional[threading.Thread] = None
        self.stop_receiving = threading.Event()

        # 统计信息
        self.packet_count = 0
        self.error_count = 0
        self.last_packet_time = 0.0

        # 数据缓冲
        self.receive_buffer = bytearray()

        self.logger.info("ADS1299设备控制器初始化完成")

    def connect(self) -> bool:
        """连接脑电设备"""
        if not SERIAL_AVAILABLE:
            self.logger.error("pyserial未安装，无法连接脑电设备")
            self.status = EEGDeviceStatus.ERROR
            return False

        try:
            self.status = EEGDeviceStatus.CONNECTING
            self.logger.info(f"正在连接脑电设备 {self.config['serial_port']}")

            # 关闭已存在的连接
            if self.serial_port and self.serial_port.is_open:
                self.serial_port.close()

            # 创建串口连接
            self.serial_port = serial.Serial(
                port=self.config['serial_port'],
                baudrate=self.config['baud_rate'],
                timeout=self.config['timeout'],
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE
            )

            # 清空缓冲区
            self.serial_port.reset_input_buffer()
            self.serial_port.reset_output_buffer()

            # 发送START命令
            start_command = self.config['start_command'].encode('ascii')
            self.serial_port.write(start_command)
            self.logger.info(f"发送START命令: {start_command}")

            # 等待并验证数据包
            if self._verify_connection():
                self.status = EEGDeviceStatus.CONNECTED
                self.logger.info("脑电设备连接成功")

                # 启动数据接收线程
                self._start_data_receiving()
                return True
            else:
                self.status = EEGDeviceStatus.TIMEOUT
                self.logger.error("脑电设备连接验证失败")
                self.disconnect()
                return False

        except serial.SerialException as e:
            self.logger.error(f"串口连接失败: {e}")
            self.status = EEGDeviceStatus.ERROR
            return False
        except Exception as e:
            self.logger.error(f"连接脑电设备时发生未知错误: {e}")
            self.status = EEGDeviceStatus.ERROR
            return False

    def disconnect(self) -> bool:
        """断开脑电设备连接"""
        try:
            self.logger.info("正在断开脑电设备连接")

            # 停止数据接收
            self._stop_data_receiving()

            # 发送STOP命令
            if self.serial_port and self.serial_port.is_open:
                try:
                    stop_command = self.config['stop_command'].encode('ascii')
                    self.serial_port.write(stop_command)
                    self.logger.info(f"发送STOP命令: {stop_command}")
                    time.sleep(0.1)  # 等待命令发送完成
                except Exception as e:
                    self.logger.warning(f"发送STOP命令失败: {e}")

                # 关闭串口
                self.serial_port.close()
                self.serial_port = None

            self.status = EEGDeviceStatus.DISCONNECTED
            self.logger.info("脑电设备连接已断开")
            return True

        except Exception as e:
            self.logger.error(f"断开脑电设备连接时发生错误: {e}")
            self.status = EEGDeviceStatus.ERROR
            return False

    def is_connected(self) -> bool:
        """检查设备是否已连接"""
        return (self.status == EEGDeviceStatus.CONNECTED and
                self.serial_port and
                self.serial_port.is_open)

    def get_status(self) -> EEGDeviceStatus:
        """获取设备状态"""
        return self.status

    def set_data_callback(self, callback: Callable[[EEGDataPacket], None]):
        """设置数据回调函数"""
        self.data_callback = callback
        self.logger.info("数据回调函数已设置")

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'packet_count': self.packet_count,
            'error_count': self.error_count,
            'last_packet_time': self.last_packet_time,
            'status': self.status.value,
            'is_connected': self.is_connected()
        }

    def _verify_connection(self) -> bool:
        """验证连接是否成功"""
        try:
            timeout_time = time.time() + self.config['connection_timeout']

            while time.time() < timeout_time:
                # 尝试读取数据包
                if self.serial_port.in_waiting >= self.config['packet_size']:
                    data = self.serial_port.read(self.config['packet_size'])
                    if self._validate_packet(data):
                        self.logger.info("接收到有效数据包，连接验证成功")
                        return True

                time.sleep(0.1)  # 短暂等待

            self.logger.warning("连接验证超时")
            return False

        except Exception as e:
            self.logger.error(f"连接验证失败: {e}")
            return False

    def _validate_packet(self, data: bytes) -> bool:
        """验证数据包格式"""
        if len(data) != self.config['packet_size']:
            return False

        # 检查包头
        header = data[:2]
        if header != self.config['packet_header']:
            return False

        # 检查包尾
        footer = data[-2:]
        if footer != self.config['packet_footer']:
            return False

        # 检查数据长度
        data_length = len(data) - 4  # 减去包头包尾
        expected_length = (self.config['data_groups_per_packet'] *
                          self.config['bytes_per_group'])

        return data_length == expected_length

    def _start_data_receiving(self):
        """启动数据接收线程"""
        if self.receive_thread and self.receive_thread.is_alive():
            return

        self.stop_receiving.clear()
        self.receive_thread = threading.Thread(
            target=self._data_receiving_loop,
            name="EEG_DataReceiver",
            daemon=True
        )
        self.receive_thread.start()
        self.logger.info("数据接收线程已启动")

    def _stop_data_receiving(self):
        """停止数据接收线程"""
        if self.receive_thread and self.receive_thread.is_alive():
            self.stop_receiving.set()
            self.receive_thread.join(timeout=2.0)
            if self.receive_thread.is_alive():
                self.logger.warning("数据接收线程未能正常停止")
            else:
                self.logger.info("数据接收线程已停止")

    def _data_receiving_loop(self):
        """数据接收循环"""
        self.logger.info("开始数据接收循环")

        while not self.stop_receiving.is_set():
            try:
                if not self.serial_port or not self.serial_port.is_open:
                    break

                # 读取可用数据
                if self.serial_port.in_waiting > 0:
                    new_data = self.serial_port.read(self.serial_port.in_waiting)
                    self.receive_buffer.extend(new_data)

                # 处理缓冲区中的数据包
                self._process_buffer()

                time.sleep(0.001)  # 1ms延迟，避免CPU占用过高

            except Exception as e:
                self.logger.error(f"数据接收循环错误: {e}")
                self.error_count += 1
                time.sleep(0.1)

        self.logger.info("数据接收循环结束")

    def _process_buffer(self):
        """处理接收缓冲区"""
        while len(self.receive_buffer) >= self.config['packet_size']:
            # 查找包头
            header_pos = self.receive_buffer.find(self.config['packet_header'])

            if header_pos == -1:
                # 没有找到包头，清空缓冲区
                self.receive_buffer.clear()
                break

            # 移除包头之前的数据
            if header_pos > 0:
                self.receive_buffer = self.receive_buffer[header_pos:]

            # 检查是否有完整的数据包
            if len(self.receive_buffer) < self.config['packet_size']:
                break

            # 提取数据包
            packet_data = bytes(self.receive_buffer[:self.config['packet_size']])

            # 验证数据包
            if self._validate_packet(packet_data):
                # 解析数据包
                eeg_packet = self._parse_packet(packet_data)
                if eeg_packet and self.data_callback:
                    self.data_callback(eeg_packet)

                self.packet_count += 1
                self.last_packet_time = time.time()
            else:
                self.error_count += 1
                # 只在错误数量是100的倍数时记录日志，避免日志过多
                if self.error_count % 100 == 0:
                    self.logger.warning(f"接收到无效数据包，累计错误: {self.error_count}")

            # 移除已处理的数据包
            self.receive_buffer = self.receive_buffer[self.config['packet_size']:]

    def _parse_packet(self, data: bytes) -> Optional[EEGDataPacket]:
        """解析数据包"""
        try:
            # 跳过包头，提取数据部分
            data_part = data[2:-2]  # 去掉包头包尾

            # 解析4组数据
            channel_data = []
            offset = 0

            for group in range(self.config['data_groups_per_packet']):
                group_data = []

                # 每组包含8个通道，每个通道3字节（24位补码）
                for channel in range(self.config['channels']):
                    # 提取3字节数据
                    channel_bytes = data_part[offset:offset+3]
                    if len(channel_bytes) != 3:
                        return None

                    # 转换为24位补码整数
                    value = struct.unpack('>I', b'\x00' + channel_bytes)[0]
                    if value & 0x800000:  # 负数
                        value -= 0x1000000

                    group_data.append(value)
                    offset += 3

                # 跳过剩余字节（如果有的话）
                remaining_bytes = self.config['bytes_per_group'] - (self.config['channels'] * 3)
                if remaining_bytes > 0:
                    offset += remaining_bytes

                channel_data.append(group_data)

            return EEGDataPacket(
                timestamp=time.time(),
                channel_data=channel_data,
                packet_number=self.packet_count,
                is_valid=True
            )

        except Exception as e:
            self.logger.error(f"解析数据包失败: {e}")
            return None

    def cleanup(self):
        """清理资源"""
        try:
            self.disconnect()
            self.logger.info("ADS1299设备资源清理完成")
        except Exception as e:
            self.logger.error(f"ADS1299设备资源清理失败: {e}")

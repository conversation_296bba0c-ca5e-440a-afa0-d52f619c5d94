#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EEGNet深度学习模型
EEGNet Deep Learning Model for Motor Imagery Classification

基于EEGNet架构的运动想象分类系统，专为8导ADS1299脑电设备和脑卒中患者优化

作者: AI Assistant
版本: 2.0.0
"""

import numpy as np
import logging
import os
import time
import pickle
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional, Callable
from dataclasses import dataclass

# 深度学习框架
try:
    # 设置环境变量来避免optree冲突
    import os
    os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'  # 减少TensorFlow日志

    import tensorflow as tf
    from tensorflow import keras
    from tensorflow.keras import layers, Model
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau

    # 设置TensorFlow日志级别 - 屏蔽警告和信息
    tf.get_logger().setLevel('ERROR')

    # 屏蔽TensorFlow的C++警告
    import os
    os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'  # 0=INFO, 1=WARNING, 2=ERROR, 3=FATAL

    # 屏蔽特定的NodeDef警告
    import warnings
    warnings.filterwarnings('ignore', category=UserWarning, module='tensorflow')

    # 设置随机种子以支持确定性操作
    try:
        # 设置全局随机种子
        tf.random.set_seed(42)
        # 启用确定性操作
        tf.config.experimental.enable_op_determinism()
    except Exception:
        # 如果确定性操作有问题，只设置种子
        try:
            tf.random.set_seed(42)
        except Exception:
            pass  # 如果失败就忽略

    TF_AVAILABLE = True
    logging.info(f"TensorFlow版本: {tf.__version__}")

except ImportError as e:
    TF_AVAILABLE = False
    logging.error(f"TensorFlow未安装，无法使用EEGNet模型: {e}")
except Exception as e:
    TF_AVAILABLE = False
    logging.error(f"TensorFlow初始化失败: {e}")
    logging.warning("将尝试使用简化模式")

# 信号处理
try:
    import mne
    from scipy import signal
    from scipy.signal import butter, filtfilt
    MNE_AVAILABLE = True
except ImportError:
    MNE_AVAILABLE = False
    logging.warning("MNE或scipy未安装，将使用简化的信号处理")

from core.signal_processor import EEGSignalProcessor
from utils.app_config import AppConfig


@dataclass
class ModelPerformance:
    """模型性能评估结果"""
    accuracy: float
    val_accuracy: float
    loss: float
    val_loss: float
    training_history: Dict[str, List[float]]
    confusion_matrix: Optional[np.ndarray] = None


@dataclass
class ModelInfo:
    """EEGNet深度学习模型信息"""
    name: str
    version: int
    created_time: float
    last_updated: float
    training_rounds: int
    total_samples: int
    performance: Optional[ModelPerformance]
    # 深度学习训练参数
    epochs: int = 50
    batch_size: int = 32
    learning_rate: float = 0.001
    # 深度学习推理参数
    temperature: float = 1.0          # 温度缩放参数
    decision_threshold: float = 0.5   # 激活阈值（神经网络输出阈值）
    confidence_threshold: float = 0.6 # 置信度阈值
    # 深度学习特有参数
    class_weight_ratio: float = 1.0   # 类别权重比例
    smoothing_window: int = 3         # 预测平滑窗口
    adaptive_learning: bool = False   # 自适应学习开关
    dropout_rate: float = 0.25        # Dropout率
    # 迁移学习参数
    transfer_learning: bool = False   # 迁移学习开关
    finetune_layers: int = 3          # 微调层数
    pretrained_model_path: str = ""   # 预训练模型路径
    # 兼容性参数（深度学习中的敏感度级别）
    difficulty_level: int = 1         # 敏感度级别 (1-10)
    # 数据平衡
    label_balance_ratio: float = 1.0  # 标签平衡比例


@dataclass
class TrainingConfig:
    """训练配置"""
    epochs: int = 50
    batch_size: int = 32
    learning_rate: float = 0.001
    validation_split: float = 0.2
    early_stopping_patience: int = 10
    reduce_lr_patience: int = 5
    use_data_augmentation: bool = True
    dropout_rate: float = 0.25


def create_eegnet_model(n_channels: int = 8, n_samples: int = 250, n_classes: int = 2, 
                       dropout_rate: float = 0.25) -> Model:
    """
    创建EEGNet模型架构
    
    参数:
        n_channels: 脑电通道数 (默认8，适配ADS1299)
        n_samples: 时间采样点数 (默认250，对应2秒@125Hz)
        n_classes: 分类类别数 (默认2，运动想象vs平静)
        dropout_rate: Dropout比例
    
    返回:
        编译好的Keras模型
    """
    if not TF_AVAILABLE:
        raise ImportError("TensorFlow未安装，无法创建EEGNet模型")
    
    # 输入层
    input_layer = layers.Input(shape=(n_channels, n_samples, 1))
    
    # Block 1: 时域卷积
    conv1 = layers.Conv2D(16, (1, 64), padding='same', use_bias=False)(input_layer)
    conv1 = layers.BatchNormalization()(conv1)
    
    # Block 2: 深度卷积 (空间滤波)
    conv2 = layers.DepthwiseConv2D((n_channels, 1), use_bias=False, 
                                   depth_multiplier=1, 
                                   depthwise_constraint=keras.constraints.max_norm(1.))(conv1)
    conv2 = layers.BatchNormalization()(conv2)
    conv2 = layers.Activation('elu')(conv2)
    conv2 = layers.AveragePooling2D((1, 4))(conv2)
    conv2 = layers.Dropout(dropout_rate)(conv2)
    
    # Block 3: 可分离卷积 (时域特征)
    conv3 = layers.SeparableConv2D(32, (1, 16), use_bias=False, padding='same')(conv2)
    conv3 = layers.BatchNormalization()(conv3)
    conv3 = layers.Activation('elu')(conv3)
    conv3 = layers.AveragePooling2D((1, 8))(conv3)
    conv3 = layers.Dropout(dropout_rate)(conv3)
    
    # 分类层
    flatten = layers.Flatten()(conv3)
    dense = layers.Dense(n_classes, use_bias=False, 
                        kernel_constraint=keras.constraints.max_norm(0.25))(flatten)
    output = layers.Activation('softmax')(dense)
    
    # 创建模型
    model = Model(inputs=input_layer, outputs=output)
    
    # 编译模型
    model.compile(
        optimizer=Adam(learning_rate=0.001),
        loss='sparse_categorical_crossentropy',
        metrics=['accuracy']
    )
    
    return model


class EEGNetModel:
    """基于EEGNet的运动想象分类模型"""

    def __init__(self, model_name: str = "EEGNet_default"):
        """初始化EEGNet模型"""
        self.logger = logging.getLogger(__name__)
        self.model_name = model_name
        
        # 检查TensorFlow可用性
        if not TF_AVAILABLE:
            raise ImportError("TensorFlow未安装，无法使用EEGNet模型")

        # 模型参数
        self.n_channels = 8      # ADS1299 8导
        self.n_samples = 250     # 2秒@125Hz
        self.n_classes = 2       # 二分类
        
        # 模型组件
        self.model: Optional[Model] = None
        self.is_trained = False
        
        # 训练数据
        self.training_data: List[np.ndarray] = []
        self.training_labels: List[int] = []
        
        # 模型信息
        self.model_info = ModelInfo(
            name=model_name,
            version=2,
            created_time=time.time(),
            last_updated=0,
            training_rounds=0,
            total_samples=0,
            performance=None
        )
        
        # 信号处理器
        self.signal_processor = EEGSignalProcessor()
        
        self.logger.info(f"EEGNet模型初始化完成: {model_name}")

    @property
    def name(self) -> str:
        """获取模型名称"""
        return self.model_name

    def add_training_data(self, data, labels=None) -> bool:
        """添加训练数据 - 适配EEGNet格式"""
        try:
            # 处理单个样本的情况
            if isinstance(data, np.ndarray) and labels is not None and not hasattr(labels, '__len__'):
                # 单个样本
                if data.shape[0] != self.n_channels:
                    self.logger.warning(f"样本通道数不正确: {data.shape[0]}, 期望: {self.n_channels}")
                    return False

                # 确保时间维度正确
                if data.shape[1] < self.n_samples:
                    self.logger.warning(f"样本时间点不足: {data.shape[1]}, 期望: {self.n_samples}")
                    return False
                
                # 截取到固定长度
                processed_data = data[:, :self.n_samples]
                
                # 添加到训练集
                self.training_data.append(processed_data)
                self.training_labels.append(int(labels))

                self.model_info.total_samples = len(self.training_data)
                self.logger.debug(f"添加单个训练样本，总计: {self.model_info.total_samples}")
                return True

            # 处理批量样本的情况
            elif hasattr(data, '__len__') and hasattr(labels, '__len__'):
                if len(data) != len(labels):
                    raise ValueError("数据和标签数量不匹配")

                # 验证数据格式
                for i, sample in enumerate(data):
                    if sample.shape[0] != self.n_channels:
                        self.logger.warning(f"样本 {i} 通道数不正确: {sample.shape[0]}")
                        continue
                    
                    if sample.shape[1] < self.n_samples:
                        self.logger.warning(f"样本 {i} 时间点不足: {sample.shape[1]}")
                        continue

                    # 截取到固定长度
                    processed_data = sample[:, :self.n_samples]
                    
                    # 添加到训练集
                    self.training_data.append(processed_data)
                    self.training_labels.append(int(labels[i]))

                self.model_info.total_samples = len(self.training_data)
                self.logger.info(f"添加训练数据: {len(data)} 个样本，总计: {self.model_info.total_samples}")
                return True

            else:
                raise ValueError("数据格式不正确")

        except Exception as e:
            self.logger.error(f"添加训练数据失败: {e}")
            return False

    def train_model(self, config: TrainingConfig = None, progress_callback: Callable = None) -> bool:
        """训练EEGNet模型"""
        try:
            # 设置随机种子确保可重复性
            if TF_AVAILABLE:
                try:
                    tf.random.set_seed(42)
                    import numpy as np
                    np.random.seed(42)
                except Exception:
                    pass

            if len(self.training_data) < 10:
                self.logger.warning("训练数据不足，至少需要10个样本")
                return False

            # 使用默认配置
            if config is None:
                config = TrainingConfig()

            # 检查是否启用迁移学习
            if self.model_info.transfer_learning:
                self.logger.info(f"使用迁移学习训练EEGNet模型，样本数: {len(self.training_data)}")
                return self._train_with_transfer_learning(config, progress_callback)
            else:
                self.logger.info(f"从头训练EEGNet模型，样本数: {len(self.training_data)}")
                return self._train_from_scratch(config, progress_callback)

        except Exception as e:
            self.logger.error(f"EEGNet训练失败: {e}")
            return False

    def _train_with_transfer_learning(self, config: TrainingConfig, progress_callback: Callable = None) -> bool:
        """使用智能的迁移学习训练模型"""
        try:
            if progress_callback:
                progress_callback("初始化迁移学习...", 5)

            # 智能选择基础模型
            base_model = self._select_base_model_for_transfer_learning()
            if base_model is None:
                self.logger.warning("无法获取基础模型，回退到普通训练")
                return self._train_from_scratch(config, progress_callback)

            if progress_callback:
                progress_callback("准备微调数据...", 20)

            # 准备训练数据
            X, y = self._prepare_eegnet_data()
            if X is None or y is None:
                self.logger.error("训练数据准备失败")
                return False

            if progress_callback:
                progress_callback("配置迁移学习模型...", 40)

            # 冻结底层特征提取层
            freeze_layers = getattr(self.model_info, 'finetune_layers', 3)
            total_layers = len(base_model.layers)

            for i, layer in enumerate(base_model.layers):
                # 冻结前面的层，只训练最后几层
                layer.trainable = (i >= total_layers - freeze_layers)

            # 重新编译模型（使用较小的学习率）
            base_model.compile(
                optimizer=tf.keras.optimizers.Adam(learning_rate=config.learning_rate * 0.1),
                loss='sparse_categorical_crossentropy',
                metrics=['accuracy']
            )

            if progress_callback:
                progress_callback("开始微调训练...", 60)

            # 微调训练
            history = base_model.fit(
                X, y,
                epochs=min(config.epochs, 20),  # 限制微调轮次
                batch_size=min(config.batch_size, 16),  # 使用较小批次
                validation_split=config.validation_split,
                verbose=1,
                callbacks=[
                    tf.keras.callbacks.EarlyStopping(
                        monitor='val_accuracy',
                        patience=5,
                        restore_best_weights=True
                    )
                ]
            )

            # 使用微调后的模型
            self.model = base_model
            self.is_trained = True

            # 更新模型信息
            self.model_info.training_rounds += 1
            self.model_info.last_updated = time.time()
            self.model_info.total_samples = len(self.training_data)

            if progress_callback:
                progress_callback("评估模型性能...", 90)

            # 评估模型性能
            performance = self._evaluate_eegnet_model(X, y, history)
            self.model_info.performance = performance

            # 计算决策阈值
            self._calculate_eegnet_thresholds(X, y)

            if progress_callback:
                progress_callback("迁移学习训练完成", 100)

            # 记录迁移学习使用信息
            self.model_info.used_transfer_learning = True
            if hasattr(self, '_current_pretrained_path'):
                self.model_info.pretrained_model_path = self._current_pretrained_path
            else:
                self.model_info.pretrained_model_path = "预训练模型"

            self.logger.info(f"🎯 迁移学习训练完成！")
            self.logger.info(f"   ✅ 使用预训练模型: {self.model_info.pretrained_model_path}")
            self.logger.info(f"   📊 最终准确率: {performance.accuracy:.3f}")
            self.logger.info(f"   🚀 微调层数: {self.model_info.finetune_layers}")

            return True

        except Exception as e:
            self.logger.error(f"迁移学习训练失败: {e}")
            self.logger.warning("回退到普通训练")
            return self._train_from_scratch(config, progress_callback)

    def _select_base_model_for_transfer_learning(self):
        """智能选择迁移学习的基础模型"""
        try:
            # 策略1: 如果当前模型已经训练过，优先使用当前模型（继续训练）
            if self.model is not None and self.is_trained:
                # 检查是否是多轮训练的情况
                if hasattr(self.model_info, 'training_rounds') and self.model_info.training_rounds > 0:
                    self.logger.info("检测到多轮训练，使用当前模型继续训练")
                    self._current_pretrained_path = f"当前模型第{self.model_info.training_rounds}轮"
                    return self.model

            # 策略2: 使用预训练模型（首次训练或重新开始）
            pretrained_model = self._load_simple_pretrained_model()
            if pretrained_model is not None:
                self.logger.info("使用预训练模型进行迁移学习")
                return pretrained_model

            # 策略3: 无可用模型
            self.logger.warning("无可用的基础模型")
            return None

        except Exception as e:
            self.logger.error(f"选择基础模型失败: {e}")
            return None

    def _load_simple_pretrained_model(self):
        """简化的预训练模型加载"""
        try:
            # 预训练模型目录
            models_dir = Path("pretrained_models")

            if not models_dir.exists():
                self.logger.warning("预训练模型目录不存在")
                return None

            # 优先查找兼容的预训练模型
            compatible_files = list(models_dir.glob("eegnet_compatible_mi_vs_rest_*.keras"))
            bci_files = list(models_dir.glob("eegnet_bci_pretrained_*.keras"))

            # 按优先级排序：兼容模型 > BCI模型
            all_model_files = compatible_files + bci_files

            if not all_model_files:
                self.logger.warning("未找到预训练模型文件")
                return None

            # 按文件名排序，获取最新的模型
            latest_model = sorted(all_model_files)[-1]

            try:
                # 加载预训练模型
                model = tf.keras.models.load_model(str(latest_model))
                self._current_pretrained_path = str(latest_model.name)
                self.logger.info(f"成功加载预训练模型: {latest_model.name}")

                # 检查模型结构是否兼容
                expected_input_shape = (None, self.n_channels, self.n_samples, 1)
                actual_input_shape = model.input_shape

                if actual_input_shape == expected_input_shape:
                    self.logger.info(f"预训练模型完全兼容: {actual_input_shape}")
                    return model
                else:
                    self.logger.warning(f"预训练模型输入形状不匹配: 期望{expected_input_shape}, 实际{actual_input_shape}")
                    # 尝试适配
                    if "compatible" in latest_model.name:
                        self.logger.info("兼容模型形状不匹配，跳过适配")
                        return None
                    else:
                        # 尝试适配BCI模型到8通道
                        model = self._adapt_pretrained_model_to_8ch(model)
                        return model

            except Exception as e:
                self.logger.error(f"加载预训练模型失败: {e}")
                return None

        except Exception as e:
            self.logger.error(f"预训练模型加载过程失败: {e}")
            return None

    def _adapt_pretrained_model_to_8ch(self, pretrained_model):
        """将3通道预训练模型适配到8通道"""
        try:
            self.logger.info("适配预训练模型到8通道...")

            # 创建新的8通道模型
            adapted_model = create_eegnet_model(
                n_channels=8,
                n_samples=500,  # 适配到您系统的采样点数
                n_classes=2,
                dropout_rate=0.25
            )

            # 复制可兼容的权重
            pretrained_layers = {layer.name: layer for layer in pretrained_model.layers}

            for layer in adapted_model.layers:
                if layer.name in pretrained_layers:
                    pretrained_layer = pretrained_layers[layer.name]

                    # 跳过输入层和形状不兼容的层
                    if 'input' in layer.name.lower():
                        continue

                    try:
                        # 尝试复制权重
                        if hasattr(layer, 'get_weights') and hasattr(pretrained_layer, 'get_weights'):
                            pretrained_weights = pretrained_layer.get_weights()
                            if pretrained_weights:
                                # 检查权重形状是否兼容
                                layer_weights = layer.get_weights()
                                if len(pretrained_weights) == len(layer_weights):
                                    compatible_weights = []
                                    for pw, lw in zip(pretrained_weights, layer_weights):
                                        if pw.shape == lw.shape:
                                            compatible_weights.append(pw)
                                        else:
                                            # 形状不匹配，保持原始权重
                                            compatible_weights.append(lw)

                                    layer.set_weights(compatible_weights)
                                    self.logger.debug(f"复制权重到层: {layer.name}")

                    except Exception as e:
                        self.logger.debug(f"跳过层 {layer.name}: {e}")
                        continue

            self.logger.info("预训练模型适配完成")
            return adapted_model

        except Exception as e:
            self.logger.error(f"模型适配失败: {e}")
            return None

    def _train_from_scratch(self, config: TrainingConfig, progress_callback: Callable = None) -> bool:
        """从头训练模型"""
        try:
            if progress_callback:
                progress_callback("准备训练数据...", 0)

            # 准备训练数据
            X, y = self._prepare_eegnet_data()
            if X is None or y is None:
                self.logger.error("训练数据准备失败")
                return False

            # 检查数据平衡性
            unique_labels, label_counts = np.unique(y, return_counts=True)
            self.logger.info(f"标签分布: {dict(zip(unique_labels, label_counts))}")

            if len(unique_labels) < 2:
                self.logger.error("训练数据只有一个类别，无法训练分类器")
                return False

            # 记录数据平衡性
            label_ratio = min(label_counts) / max(label_counts)
            self.model_info.label_balance_ratio = label_ratio
            if label_ratio < 0.5:
                self.logger.warning(f"数据不平衡，比例: {label_ratio:.2f}")

            if progress_callback:
                progress_callback("准备EEGNet模型...", 10)

            # 智能模型创建策略
            if self.model is None or not self.is_trained:
                # 首次训练：创建新模型
                self.logger.info("创建新的EEGNet模型")
                self.model = create_eegnet_model(
                    n_channels=self.n_channels,
                    n_samples=self.n_samples,
                    n_classes=self.n_classes,
                    dropout_rate=config.dropout_rate
                )
            else:
                # 后续训练：复用现有模型继续训练
                self.logger.info("复用现有EEGNet模型继续训练")
                # 重新编译模型以确保使用新的训练配置
                self.model.compile(
                    optimizer=tf.keras.optimizers.Adam(learning_rate=config.learning_rate),
                    loss='sparse_categorical_crossentropy',
                    metrics=['accuracy']
                )

            # 更新模型配置
            self.model_info.epochs = config.epochs
            self.model_info.batch_size = config.batch_size
            self.model_info.learning_rate = config.learning_rate

            if progress_callback:
                progress_callback("开始训练...", 20)

            # 设置回调函数
            callbacks = []

            # 早停
            early_stopping = EarlyStopping(
                monitor='val_loss',
                patience=config.early_stopping_patience,
                restore_best_weights=True,
                verbose=1
            )
            callbacks.append(early_stopping)

            # 学习率调整
            reduce_lr = ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=config.reduce_lr_patience,
                min_lr=1e-6,
                verbose=1
            )
            callbacks.append(reduce_lr)

            # 训练EEGNet深度学习模型
            try:
                if progress_callback:
                    progress_callback("开始EEGNet深度学习训练...", 20)

                history = self.model.fit(
                    X, y,
                    epochs=config.epochs,
                    batch_size=config.batch_size,
                    validation_split=config.validation_split,
                    callbacks=callbacks,
                    verbose=1
                )

                self.logger.info("EEGNet深度学习训练成功完成")

            except Exception as train_error:
                self.logger.error(f"EEGNet训练失败: {train_error}")

                if progress_callback:
                    progress_callback("EEGNet训练失败", 0)

                self.logger.error("EEGNet深度学习训练失败，请检查数据质量和TensorFlow环境")
                return False

            # 标记为已训练
            self.is_trained = True

            if progress_callback:
                progress_callback("评估模型性能...", 90)

            # 评估模型性能
            performance = self._evaluate_eegnet_model(X, y, history)
            self.model_info.performance = performance
            self.model_info.training_rounds += 1
            self.model_info.last_updated = time.time()

            # 计算决策阈值
            self._calculate_eegnet_thresholds(X, y)

            if progress_callback:
                progress_callback("训练完成", 100)

            # 记录普通训练信息
            self.model_info.used_transfer_learning = False
            self.model_info.pretrained_model_path = None

            self.logger.info(f"🎯 普通训练完成！")
            self.logger.info(f"   📊 最终准确率: {performance.accuracy:.3f}")
            self.logger.info(f"   🔧 训练方式: 普通训练")

            return True

        except Exception as e:
            self.logger.error(f"EEGNet训练失败: {e}")
            return False

    def predict(self, data: np.ndarray) -> Tuple[int, float]:
        """EEGNet预测单个样本"""
        try:
            if not self.is_trained or self.model is None:
                raise ValueError("EEGNet模型未训练")

            # 预处理数据
            processed_data, quality = self.signal_processor.preprocess_signal(data)

            if not quality.is_usable:
                self.logger.warning("输入数据质量较低")

            # 准备EEGNet输入格式
            X = self._prepare_single_sample(processed_data)

            # 预测
            predictions = self.model.predict(X, verbose=0)
            probabilities = predictions[0]

            # 获取预测类别和置信度
            prediction = int(np.argmax(probabilities))
            confidence = float(np.max(probabilities))

            return prediction, confidence

        except Exception as e:
            self.logger.error(f"EEGNet预测失败: {e}")
            return 0, 0.0

    def predict_with_adjustment(self, data: np.ndarray) -> Tuple[int, float, str]:
        """带调整机制的EEGNet预测"""
        try:
            if not self.is_trained or self.model is None:
                raise ValueError("EEGNet模型未训练")

            # 预处理数据
            processed_data, quality = self.signal_processor.preprocess_signal(data)

            if not quality.is_usable:
                self.logger.warning("输入数据质量较低")

            # 准备EEGNet输入格式
            X = self._prepare_single_sample(processed_data)

            # 预测
            predictions = self.model.predict(X, verbose=0)
            probabilities = predictions[0]

            # 获取原始预测
            raw_prediction = int(np.argmax(probabilities))
            raw_confidence = float(np.max(probabilities))

            # 应用温度缩放
            scaled_probs = self._apply_temperature_scaling(probabilities)

            # 应用阈值调整
            adjusted_prediction, status = self._apply_eegnet_threshold_adjustment(scaled_probs, raw_confidence)

            return int(adjusted_prediction), float(raw_confidence), status

        except Exception as e:
            self.logger.error(f"EEGNet带调整预测失败: {e}")
            return 0, 0.0, "error"

    def _prepare_eegnet_data(self) -> Tuple[np.ndarray, np.ndarray]:
        """准备EEGNet训练数据"""
        try:
            if len(self.training_data) == 0:
                return None, None

            # 转换为EEGNet格式: (samples, channels, time_points, 1)
            X = []
            y = []

            for i, data in enumerate(self.training_data):
                # 确保数据形状正确
                if data.shape[0] != self.n_channels or data.shape[1] < self.n_samples:
                    continue

                # 截取到固定长度并添加通道维度
                processed_data = data[:, :self.n_samples]
                processed_data = processed_data.reshape(self.n_channels, self.n_samples, 1)

                X.append(processed_data)
                y.append(self.training_labels[i])

            if len(X) == 0:
                return None, None

            X = np.array(X)
            y = np.array(y)

            self.logger.info(f"EEGNet数据准备完成: X.shape={X.shape}, y.shape={y.shape}")
            return X, y

        except Exception as e:
            self.logger.error(f"EEGNet数据准备失败: {e}")
            return None, None

    def _prepare_single_sample(self, data: np.ndarray) -> np.ndarray:
        """准备单个样本用于EEGNet预测"""
        try:
            # 确保数据形状正确
            if data.shape[0] != self.n_channels:
                raise ValueError(f"通道数不匹配: {data.shape[0]} vs {self.n_channels}")

            if data.shape[1] < self.n_samples:
                raise ValueError(f"时间点不足: {data.shape[1]} vs {self.n_samples}")

            # 截取到固定长度并添加维度
            processed_data = data[:, :self.n_samples]
            processed_data = processed_data.reshape(1, self.n_channels, self.n_samples, 1)

            return processed_data

        except Exception as e:
            self.logger.error(f"单样本数据准备失败: {e}")
            # 返回零数据作为备用
            return np.zeros((1, self.n_channels, self.n_samples, 1))

    def _evaluate_eegnet_model(self, X: np.ndarray, y: np.ndarray, history) -> ModelPerformance:
        """评估EEGNet模型性能"""
        try:
            # 获取训练历史
            training_history = {
                'loss': history.history['loss'],
                'accuracy': history.history['accuracy'],
                'val_loss': history.history['val_loss'],
                'val_accuracy': history.history['val_accuracy']
            }

            # 获取最终性能指标
            final_accuracy = history.history['accuracy'][-1]
            final_val_accuracy = history.history['val_accuracy'][-1]
            final_loss = history.history['loss'][-1]
            final_val_loss = history.history['val_loss'][-1]

            # 计算混淆矩阵
            predictions = self.model.predict(X, verbose=0)
            y_pred = np.argmax(predictions, axis=1)

            from sklearn.metrics import confusion_matrix
            cm = confusion_matrix(y, y_pred)

            performance = ModelPerformance(
                accuracy=final_accuracy,
                val_accuracy=final_val_accuracy,
                loss=final_loss,
                val_loss=final_val_loss,
                training_history=training_history,
                confusion_matrix=cm
            )

            self.logger.info(f"EEGNet评估完成 - 训练准确率: {final_accuracy:.3f}, 验证准确率: {final_val_accuracy:.3f}")

            return performance

        except Exception as e:
            self.logger.error(f"EEGNet模型评估失败: {e}")
            return ModelPerformance(0.0, 0.0, 1.0, 1.0, {}, None)

    def _calculate_eegnet_thresholds(self, X: np.ndarray, y: np.ndarray):
        """计算EEGNet决策阈值"""
        try:
            # 获取训练数据的预测概率
            predictions = self.model.predict(X, verbose=0)

            # 计算最优决策阈值 (使用ROC曲线)
            from sklearn.metrics import roc_curve
            fpr, tpr, thresholds = roc_curve(y, predictions[:, 1])

            # 找到最优阈值 (Youden's J statistic)
            optimal_idx = np.argmax(tpr - fpr)
            optimal_threshold = thresholds[optimal_idx]

            # 设置决策阈值
            self.model_info.decision_threshold = float(optimal_threshold)

            # 计算置信度阈值 (基于训练数据的置信度分布)
            all_confidences = np.max(predictions, axis=1)
            confidence_threshold = np.percentile(all_confidences, 25)  # 25%分位数
            self.model_info.confidence_threshold = max(0.5, float(confidence_threshold))

            self.logger.info(f"EEGNet决策阈值: {self.model_info.decision_threshold:.3f}")
            self.logger.info(f"EEGNet置信度阈值: {self.model_info.confidence_threshold:.3f}")

        except Exception as e:
            self.logger.warning(f"计算EEGNet阈值失败: {e}")
            # 使用默认值
            self.model_info.decision_threshold = 0.5
            self.model_info.confidence_threshold = 0.6

    def _apply_temperature_scaling(self, probabilities: np.ndarray) -> np.ndarray:
        """应用温度缩放"""
        try:
            temperature = self.model_info.temperature
            if temperature != 1.0:
                # 应用温度缩放
                scaled_logits = np.log(probabilities + 1e-8) / temperature
                # 重新归一化
                exp_logits = np.exp(scaled_logits)
                scaled_probs = exp_logits / np.sum(exp_logits)
                return scaled_probs
            return probabilities
        except Exception as e:
            self.logger.warning(f"温度缩放失败: {e}")
            return probabilities

    def _apply_eegnet_threshold_adjustment(self, probabilities: np.ndarray, confidence: float) -> Tuple[int, str]:
        """应用EEGNet阈值调整机制"""
        try:
            # 获取决策阈值
            threshold = self.model_info.decision_threshold
            confidence_threshold = self.model_info.confidence_threshold

            # 基础预测
            prob_class_1 = probabilities[1]

            # 置信度检查
            if confidence < confidence_threshold:
                return 0, "low_confidence"

            # 应用动态阈值
            difficulty_factor = self._get_difficulty_factor()
            adjusted_threshold = threshold + (difficulty_factor - 1) * 0.05

            if prob_class_1 > adjusted_threshold:
                return 1, "active"
            else:
                return 0, "rest"

        except Exception as e:
            self.logger.warning(f"EEGNet阈值调整失败: {e}")
            return int(np.argmax(probabilities)), "default"

    def _get_difficulty_factor(self) -> float:
        """获取难度系数"""
        difficulty_level = self.model_info.difficulty_level
        # 难度等级1-5对应系数0.8-1.2
        return 0.8 + (difficulty_level - 1) * 0.1

    def adjust_difficulty(self, level: int):
        """调整难度等级"""
        if 1 <= level <= 5:
            self.model_info.difficulty_level = level
            self.logger.info(f"难度等级调整为: {level}")
        else:
            self.logger.warning(f"无效的难度等级: {level}")

    def adjust_temperature(self, temperature: float):
        """调整温度缩放参数"""
        if 0.1 <= temperature <= 5.0:
            self.model_info.temperature = temperature
            self.logger.info(f"温度参数调整为: {temperature}")
        else:
            self.logger.warning(f"无效的温度参数: {temperature}")

    def adjust_thresholds(self, decision_threshold: float = None, confidence_threshold: float = None):
        """调整决策阈值"""
        if decision_threshold is not None:
            if 0.1 <= decision_threshold <= 0.9:
                self.model_info.decision_threshold = decision_threshold
                self.logger.info(f"决策阈值调整为: {decision_threshold}")
            else:
                self.logger.warning(f"无效的决策阈值: {decision_threshold}")

        if confidence_threshold is not None:
            if 0.1 <= confidence_threshold <= 0.9:
                self.model_info.confidence_threshold = confidence_threshold
                self.logger.info(f"置信度阈值调整为: {confidence_threshold}")
            else:
                self.logger.warning(f"无效的置信度阈值: {confidence_threshold}")

    def save_model(self, filepath: str = None) -> bool:
        """保存EEGNet模型"""
        try:
            if filepath is None:
                models_dir = os.path.join(AppConfig.get_config('paths')['data'], 'models')
                os.makedirs(models_dir, exist_ok=True)
                filepath = os.path.join(models_dir, f"{self.model_name}.h5")

            # 保存Keras模型 - 使用新的.keras格式
            if self.model is not None:
                model_path = filepath.replace('.h5', '_model.keras')
                self.model.save(model_path)

                # 保存模型信息和训练数据
                info_path = filepath.replace('.h5', '_info.pkl')
                model_data = {
                    'model_info': self.model_info,
                    'training_data': self.training_data,
                    'training_labels': self.training_labels,
                    'is_trained': self.is_trained,
                    'n_channels': self.n_channels,
                    'n_samples': self.n_samples,
                    'n_classes': self.n_classes
                }

                with open(info_path, 'wb') as f:
                    pickle.dump(model_data, f)

                self.logger.info(f"EEGNet模型已保存: {model_path}, {info_path}")
                return True
            else:
                self.logger.warning("模型未训练，无法保存")
                return False

        except Exception as e:
            self.logger.error(f"保存EEGNet模型失败: {e}")
            return False

    def load_model(self, filepath: str) -> bool:
        """加载EEGNet模型"""
        try:
            # 优先尝试新的.keras格式，然后回退到.h5格式
            model_path_keras = filepath.replace('.h5', '_model.keras')
            model_path_h5 = filepath.replace('.h5', '_model.h5')
            info_path = filepath.replace('.h5', '_info.pkl')

            # 检查文件存在性
            if os.path.exists(model_path_keras):
                model_path = model_path_keras
            elif os.path.exists(model_path_h5):
                model_path = model_path_h5
            else:
                self.logger.error(f"模型文件不存在: {model_path_keras} 或 {model_path_h5}")
                return False

            if not os.path.exists(info_path):
                self.logger.error(f"模型信息文件不存在: {info_path}")
                return False

            # 加载Keras模型
            self.model = keras.models.load_model(model_path)

            # 加载模型信息
            with open(info_path, 'rb') as f:
                model_data = pickle.load(f)

            # 恢复模型状态
            self.model_info = model_data.get('model_info', self.model_info)
            self.training_data = model_data.get('training_data', [])
            self.training_labels = model_data.get('training_labels', [])
            self.is_trained = model_data.get('is_trained', False)
            self.n_channels = model_data.get('n_channels', 8)
            self.n_samples = model_data.get('n_samples', 250)
            self.n_classes = model_data.get('n_classes', 2)

            # 更新模型名称
            self.model_name = self.model_info.name

            self.logger.info(f"EEGNet模型已加载: {model_path}")
            return True

        except Exception as e:
            self.logger.error(f"加载EEGNet模型失败: {e}")
            return False

    def get_model_info(self) -> ModelInfo:
        """获取模型信息"""
        return self.model_info

    def clear_training_data(self):
        """清空训练数据"""
        self.training_data.clear()
        self.training_labels.clear()
        self.model_info.total_samples = 0
        self.logger.info("训练数据已清空")

    def calibrate_model(self, recent_predictions: List[Tuple[int, float]],
                       expected_balance: float = 0.3) -> bool:
        """模型校准 - 基于最近预测结果调整阈值"""
        try:
            if len(recent_predictions) < 10:
                self.logger.warning("校准数据不足")
                return False

            # 分析预测分布
            predictions = [pred for pred, conf in recent_predictions]
            confidences = [conf for pred, conf in recent_predictions]

            active_ratio = sum(predictions) / len(predictions)
            avg_confidence = np.mean(confidences)

            self.logger.info(f"当前活跃比例: {active_ratio:.2f}, 平均置信度: {avg_confidence:.2f}")

            # 调整决策阈值
            if active_ratio < expected_balance * 0.5:
                # 活跃预测太少，降低阈值
                new_threshold = max(0.3, self.model_info.decision_threshold - 0.05)
                self.model_info.decision_threshold = new_threshold
                self.logger.info("降低决策阈值以增加活跃预测")
            elif active_ratio > expected_balance * 2:
                # 活跃预测太多，提高阈值
                new_threshold = min(0.8, self.model_info.decision_threshold + 0.05)
                self.model_info.decision_threshold = new_threshold
                self.logger.info("提高决策阈值以减少活跃预测")

            # 调整置信度阈值
            if avg_confidence < 0.6:
                new_conf_threshold = max(0.4, self.model_info.confidence_threshold - 0.05)
                self.model_info.confidence_threshold = new_conf_threshold
                self.logger.info("降低置信度阈值")
            elif avg_confidence > 0.9:
                new_conf_threshold = min(0.8, self.model_info.confidence_threshold + 0.05)
                self.model_info.confidence_threshold = new_conf_threshold
                self.logger.info("提高置信度阈值")

            return True

        except Exception as e:
            self.logger.error(f"EEGNet模型校准失败: {e}")
            return False


# 为了向后兼容，创建别名
MotorImageryModel = EEGNetModel


class ModelManager:
    """EEGNet模型管理器"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.models_dir = os.path.join(AppConfig.get_config('paths')['data'], 'models')
        os.makedirs(self.models_dir, exist_ok=True)

        self.current_model: Optional[EEGNetModel] = None

    def create_model(self, model_name: str) -> EEGNetModel:
        """创建新的EEGNet模型"""
        model = EEGNetModel(model_name)
        model.model_info.created_time = time.time()
        return model

    def save_model(self, model: EEGNetModel, model_name: str = None) -> bool:
        """保存EEGNet模型"""
        if model_name:
            model.model_name = model_name
            model.model_info.name = model_name

        filepath = os.path.join(self.models_dir, f"{model.model_name}.h5")
        return model.save_model(filepath)

    def load_model(self, model_name: str) -> Optional[EEGNetModel]:
        """加载EEGNet模型"""
        filepath = os.path.join(self.models_dir, f"{model_name}.h5")
        model = EEGNetModel(model_name)

        if model.load_model(filepath):
            return model
        return None

    def list_models(self) -> List[dict]:
        """列出所有可用的EEGNet模型"""
        try:
            model_files = [f for f in os.listdir(self.models_dir) if f.endswith('_info.pkl')]
            models_info = []

            for model_file in model_files:
                model_name = model_file.replace('_info.pkl', '')
                filepath = os.path.join(self.models_dir, model_file)

                try:
                    # 尝试加载模型信息
                    with open(filepath, 'rb') as f:
                        model_data = pickle.load(f)

                    model_info = model_data.get('model_info')
                    if model_info:
                        accuracy = 0.0
                        if model_info.performance:
                            accuracy = model_info.performance.accuracy

                        models_info.append({
                            'name': model_name,
                            'created_time': time.strftime('%Y-%m-%d %H:%M:%S',
                                                        time.localtime(model_info.created_time)),
                            'accuracy': accuracy,
                            'training_rounds': model_info.training_rounds,
                            'total_samples': model_info.total_samples,
                            'type': 'EEGNet'
                        })

                except Exception as e:
                    self.logger.warning(f"读取EEGNet模型信息失败 {model_name}: {e}")

            return models_info

        except Exception as e:
            self.logger.error(f"列出EEGNet模型失败: {e}")
            return []

    def delete_model(self, model_name: str) -> bool:
        """删除EEGNet模型"""
        try:
            model_path_keras = os.path.join(self.models_dir, f"{model_name}_model.keras")
            model_path_h5 = os.path.join(self.models_dir, f"{model_name}_model.h5")
            info_path = os.path.join(self.models_dir, f"{model_name}_info.pkl")

            deleted = False
            # 删除.keras格式模型
            if os.path.exists(model_path_keras):
                os.remove(model_path_keras)
                deleted = True
            # 删除.h5格式模型
            if os.path.exists(model_path_h5):
                os.remove(model_path_h5)
                deleted = True
            # 删除模型信息
            if os.path.exists(info_path):
                os.remove(info_path)
                deleted = True

            if deleted:
                self.logger.info(f"EEGNet模型已删除: {model_name}")
                return True
            return False
        except Exception as e:
            self.logger.error(f"删除EEGNet模型失败: {e}")
            return False

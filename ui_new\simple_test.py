#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的新UI测试启动文件
Simple Test Launcher for New UI

作者: AI Assistant
版本: 2.0.0
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt

# 导入新UI组件
from ui_new.main_window_new import ModernMainWindow
from ui_new.themes.theme_manager import ThemeManager


def test_basic_ui():
    """测试基本UI功能"""
    try:
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("NK脑机接口康复系统 - 新UI测试")
        
        # 设置高DPI支持（Qt6中已默认启用）
        # 移除已弃用的设置
        
        # 创建主窗口
        main_window = ModernMainWindow()
        
        # 显示主窗口
        main_window.show()
        main_window.raise_()
        main_window.activateWindow()
        
        print("新UI框架基本测试启动成功")
        print("功能测试项目：")
        print("1. 主题切换（顶部栏右侧开关）")
        print("2. 侧边栏折叠（顶部栏左侧菜单按钮）")
        print("3. 导航切换（侧边栏菜单项）")
        print("4. 登录功能（点击登录按钮）")
        print("5. 实时监测页面（登录后默认页面）")
        
        # 运行应用程序
        return app.exec()
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


def test_theme_system():
    """测试主题系统"""
    try:
        print("测试主题系统...")
        
        # 创建主题管理器
        theme_manager = ThemeManager()
        
        # 测试主题配置
        medical_config = theme_manager.get_theme_config("medical")
        tech_config = theme_manager.get_theme_config("tech")
        
        print(f"医疗主题: {medical_config['display_name']}")
        print(f"科技主题: {tech_config['display_name']}")
        
        # 测试颜色获取
        medical_primary = theme_manager.get_color("primary", "medical")
        tech_primary = theme_manager.get_color("primary", "tech")
        
        print(f"医疗主题主色: {medical_primary}")
        print(f"科技主题主色: {tech_primary}")
        
        print("主题系统测试通过")
        return True
        
    except Exception as e:
        print(f"主题系统测试失败: {e}")
        return False


def test_components():
    """测试组件系统"""
    try:
        print("测试组件系统...")
        
        # 创建应用程序（组件需要QApplication）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 测试侧边栏组件
        from ui_new.components.sidebar import ModernSidebar
        sidebar = ModernSidebar()
        print("侧边栏组件创建成功")
        
        # 测试顶部栏组件
        from ui_new.components.top_bar import ModernTopBar
        top_bar = ModernTopBar()
        print("顶部栏组件创建成功")
        
        # 测试页面基类
        from ui_new.pages.dashboard_page import DashboardPage
        dashboard = DashboardPage()
        print("仪表板页面创建成功")
        
        print("组件系统测试通过")
        return True
        
    except Exception as e:
        print(f"组件系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("=" * 50)
    print("NK脑机接口康复系统 - 新UI框架测试")
    print("=" * 50)
    
    # 测试主题系统
    if not test_theme_system():
        print("主题系统测试失败，退出")
        return 1
    
    print()
    
    # 测试组件系统
    if not test_components():
        print("组件系统测试失败，退出")
        return 1
    
    print()
    
    # 测试基本UI
    print("启动UI界面测试...")
    return test_basic_ui()


if __name__ == "__main__":
    sys.exit(main())

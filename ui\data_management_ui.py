#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
原始数据管理界面
Raw Data Management UI

提供脑电原始数据的查看、分析、导出功能

作者: AI Assistant
版本: 1.0.0
"""

import logging
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, Dict, Any, List
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QGroupBox, QLabel,
    QPushButton, QTableWidget, QTableWidgetItem, QComboBox, QSpinBox,
    QDateEdit, QTextEdit, QProgressBar, QMessageBox, QFileDialog,
    QTabWidget, QSplitter, QHeaderView, QCheckBox, QDoubleSpinBox
)
from PySide6.QtCore import Qt, QDate, QThread, Signal, QTimer
from PySide6.QtGui import QFont

from core.database_manager import DatabaseManager
from core.eeg_data_loader import EEGDataLoader
from core.training_data_integration import TrainingDataIntegration
from utils.app_config import AppConfig


class DataExportWorker(QThread):
    """数据导出工作线程"""
    progress_updated = Signal(int, str)
    export_completed = Signal(bool, str)
    
    def __init__(self, data_loader, export_config):
        super().__init__()
        self.data_loader = data_loader
        self.export_config = export_config
    
    def run(self):
        try:
            self.progress_updated.emit(10, "准备导出数据...")

            # 获取数据
            patient_ids = self.export_config['patient_ids']
            output_dir = self.export_config['output_dir']
            format_type = self.export_config['format']
            include_metadata = self.export_config.get('include_metadata', True)
            compress = self.export_config.get('compress', True)

            self.progress_updated.emit(20, "创建输出目录...")

            # 创建输出目录
            from pathlib import Path
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)

            self.progress_updated.emit(30, "查询数据...")

            # 导出每个患者的数据
            total_patients = len(patient_ids)
            exported_files = []

            for i, patient_id in enumerate(patient_ids):
                self.progress_updated.emit(
                    30 + (i * 60) // total_patients,
                    f"导出患者 {patient_id} 数据..."
                )

                # 调用实际的导出方法
                success, files = self._export_patient_data(
                    patient_id, output_path, format_type, include_metadata, compress
                )

                if success:
                    exported_files.extend(files)
                else:
                    self.export_completed.emit(False, f"导出患者 {patient_id} 数据失败")
                    return

            self.progress_updated.emit(100, "导出完成")

            if exported_files:
                file_list = "\n".join([f"- {f}" for f in exported_files])
                message = f"成功导出 {len(exported_files)} 个文件到: {output_dir}\n\n文件列表:\n{file_list}"
            else:
                message = f"未找到可导出的数据"

            self.export_completed.emit(len(exported_files) > 0, message)

        except Exception as e:
            import traceback
            error_msg = f"导出失败: {str(e)}\n\n详细错误:\n{traceback.format_exc()}"
            self.export_completed.emit(False, error_msg)

    def _export_patient_data(self, patient_id, output_path, format_type, include_metadata, compress):
        """导出单个患者的数据"""
        try:
            exported_files = []

            # 查询患者的所有试验数据
            criteria = {
                'patient_ids': [patient_id],
                'limit': 10000  # 大数量限制
            }

            trials = self.data_loader.search_trials(criteria)

            if not trials:
                return True, []  # 没有数据但不算失败

            if format_type.lower() == 'csv':
                # CSV格式导出
                exported_files.extend(self._export_to_csv(patient_id, trials, output_path, include_metadata))
            elif format_type.lower() == 'hdf5':
                # HDF5格式导出
                exported_files.extend(self._export_to_hdf5(patient_id, trials, output_path, include_metadata, compress))
            elif format_type.lower() == 'mat':
                # MAT格式导出
                exported_files.extend(self._export_to_mat(patient_id, trials, output_path, include_metadata))

            return True, exported_files

        except Exception as e:
            print(f"导出患者 {patient_id} 数据失败: {e}")
            return False, []

    def _export_to_csv(self, patient_id, trials, output_path, include_metadata):
        """导出为CSV格式"""
        import csv
        from datetime import datetime

        exported_files = []

        # 导出试验元数据
        metadata_file = output_path / f"patient_{patient_id}_metadata.csv"
        with open(metadata_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)

            # 写入表头
            headers = [
                'trial_id', 'session_id', 'patient_id', 'label', 'label_text',
                'start_time', 'duration_seconds', 'data_quality', 'file_size_bytes',
                'file_path', 'sampling_rate', 'channels'
            ]
            writer.writerow(headers)

            # 写入数据
            for trial in trials:
                label = trial.get('label', 0)
                label_text = "运动想象" if label == 1 else "休息" if label == 0 else "未知"

                row = [
                    trial.get('trial_id', ''),
                    trial.get('session_id', ''),
                    trial.get('patient_id', ''),
                    label,
                    label_text,
                    trial.get('start_time', ''),
                    trial.get('duration_seconds', 0),
                    trial.get('data_quality', 0),
                    trial.get('file_size_bytes', 0),
                    trial.get('file_path', ''),
                    trial.get('sampling_rate', 125),
                    trial.get('channels', 8)
                ]
                writer.writerow(row)

        exported_files.append(metadata_file.name)

        # 如果需要，导出原始数据（简化版本）
        if include_metadata:
            summary_file = output_path / f"patient_{patient_id}_summary.csv"
            with open(summary_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)

                # 统计信息
                total_trials = len(trials)
                motor_imagery_count = sum(1 for t in trials if t.get('label', 0) == 1)
                rest_count = total_trials - motor_imagery_count
                avg_quality = sum(t.get('data_quality', 0) for t in trials) / total_trials if total_trials > 0 else 0
                total_duration = sum(t.get('duration_seconds', 0) for t in trials)

                writer.writerow(['统计项目', '数值'])
                writer.writerow(['患者ID', patient_id])
                writer.writerow(['总试验数', total_trials])
                writer.writerow(['运动想象试验', motor_imagery_count])
                writer.writerow(['休息试验', rest_count])
                writer.writerow(['平均质量', f"{avg_quality:.3f}"])
                writer.writerow(['总时长(秒)', f"{total_duration:.2f}"])
                writer.writerow(['导出时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S')])

            exported_files.append(summary_file.name)

        return exported_files

    def _export_to_hdf5(self, patient_id, trials, output_path, include_metadata, compress):
        """导出为HDF5格式"""
        # 这里可以实现HDF5导出逻辑
        # 由于原始数据已经是HDF5格式，可以直接复制文件
        import shutil
        from pathlib import Path

        exported_files = []

        # 获取唯一的文件路径
        unique_files = set()
        for trial in trials:
            file_path = trial.get('file_path', '')
            if file_path:
                unique_files.add(file_path)

        # 复制文件
        for file_path in unique_files:
            source_path = Path(file_path)
            if source_path.exists():
                dest_name = f"patient_{patient_id}_{source_path.name}"
                dest_path = output_path / dest_name
                shutil.copy2(source_path, dest_path)
                exported_files.append(dest_name)

        return exported_files

    def _export_to_mat(self, patient_id, trials, output_path, include_metadata):
        """导出为MAT格式"""
        try:
            import scipy.io as sio
            import numpy as np

            exported_files = []

            # 准备数据结构
            export_data = {
                'patient_id': patient_id,
                'trials': [],
                'metadata': {
                    'total_trials': len(trials),
                    'export_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
            }

            # 添加试验信息
            for trial in trials:
                trial_info = {
                    'trial_id': trial.get('trial_id', 0),
                    'session_id': trial.get('session_id', 0),
                    'label': trial.get('label', 0),
                    'start_time': trial.get('start_time', ''),
                    'duration_seconds': trial.get('duration_seconds', 0),
                    'data_quality': trial.get('data_quality', 0),
                    'file_path': trial.get('file_path', '')
                }
                export_data['trials'].append(trial_info)

            # 保存MAT文件
            mat_file = output_path / f"patient_{patient_id}_data.mat"
            sio.savemat(str(mat_file), export_data)
            exported_files.append(mat_file.name)

            return exported_files

        except ImportError:
            # 如果没有scipy，回退到CSV格式
            return self._export_to_csv(patient_id, trials, output_path, include_metadata)


class DataManagementWidget(QWidget):
    """原始数据管理界面"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        
        # 数据管理器
        self.db_manager: Optional[DatabaseManager] = None
        self.data_loader: Optional[EEGDataLoader] = None
        self.training_integration: Optional[TrainingDataIntegration] = None
        
        # 当前数据
        self.current_data = []
        self.current_statistics = {}
        
        # 导出工作线程
        self.export_worker: Optional[DataExportWorker] = None
        
        # 初始化界面
        self.init_ui()
        
        # 定时器用于自动刷新
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_data)
        self.refresh_timer.start(30000)  # 30秒刷新一次
        
        self.logger.info("原始数据管理界面初始化完成")
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        
        # 创建标签页
        tab_widget = QTabWidget()
        
        # 数据概览标签页
        overview_tab = self.create_overview_tab()
        tab_widget.addTab(overview_tab, "📊 数据概览")
        
        # 数据查询标签页
        query_tab = self.create_query_tab()
        tab_widget.addTab(query_tab, "🔍 数据查询")
        
        # 数据分析标签页
        analysis_tab = self.create_analysis_tab()
        tab_widget.addTab(analysis_tab, "📈 数据分析")
        
        # 数据导出标签页
        export_tab = self.create_export_tab()
        tab_widget.addTab(export_tab, "📤 数据导出")
        
        layout.addWidget(tab_widget)
    
    def create_overview_tab(self):
        """创建数据概览标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 统计信息组
        stats_group = QGroupBox("📊 数据统计")
        stats_layout = QGridLayout(stats_group)
        
        # 统计标签
        self.total_patients_label = QLabel("0")
        self.total_sessions_label = QLabel("0")
        self.total_trials_label = QLabel("0")
        self.total_size_label = QLabel("0 MB")
        self.avg_quality_label = QLabel("0.000")
        
        stats_layout.addWidget(QLabel("总患者数:"), 0, 0)
        stats_layout.addWidget(self.total_patients_label, 0, 1)
        stats_layout.addWidget(QLabel("总会话数:"), 0, 2)
        stats_layout.addWidget(self.total_sessions_label, 0, 3)
        
        stats_layout.addWidget(QLabel("总试验数:"), 1, 0)
        stats_layout.addWidget(self.total_trials_label, 1, 1)
        stats_layout.addWidget(QLabel("数据大小:"), 1, 2)
        stats_layout.addWidget(self.total_size_label, 1, 3)
        
        stats_layout.addWidget(QLabel("平均质量:"), 2, 0)
        stats_layout.addWidget(self.avg_quality_label, 2, 1)
        
        layout.addWidget(stats_group)
        
        # 最近数据组
        recent_group = QGroupBox("🕒 最近数据")
        recent_layout = QVBoxLayout(recent_group)
        
        # 最近数据表格
        self.recent_table = QTableWidget()
        self.recent_table.setColumnCount(6)
        self.recent_table.setHorizontalHeaderLabels([
            "患者ID", "会话类型", "试验数", "数据质量", "创建时间", "文件大小"
        ])
        self.recent_table.horizontalHeader().setStretchLastSection(True)
        
        recent_layout.addWidget(self.recent_table)
        layout.addWidget(recent_group)
        
        # 刷新按钮
        refresh_button = QPushButton("🔄 刷新数据")
        refresh_button.clicked.connect(self.refresh_data)
        layout.addWidget(refresh_button)
        
        return widget
    
    def create_query_tab(self):
        """创建数据查询标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 查询条件组
        query_group = QGroupBox("🔍 查询条件")
        query_layout = QGridLayout(query_group)
        
        # 患者选择
        query_layout.addWidget(QLabel("患者ID:"), 0, 0)
        self.patient_combo = QComboBox()
        self.patient_combo.setEditable(True)
        self.patient_combo.addItem("全部患者", None)
        query_layout.addWidget(self.patient_combo, 0, 1)
        
        # 会话类型
        query_layout.addWidget(QLabel("会话类型:"), 0, 2)
        self.session_type_combo = QComboBox()
        self.session_type_combo.addItems(["全部", "training", "treatment"])
        query_layout.addWidget(self.session_type_combo, 0, 3)
        
        # 日期范围
        query_layout.addWidget(QLabel("开始日期:"), 1, 0)
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        self.start_date.setCalendarPopup(True)
        query_layout.addWidget(self.start_date, 1, 1)
        
        query_layout.addWidget(QLabel("结束日期:"), 1, 2)
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setCalendarPopup(True)
        query_layout.addWidget(self.end_date, 1, 3)
        
        # 质量过滤
        query_layout.addWidget(QLabel("最小质量:"), 2, 0)
        self.min_quality_spin = QDoubleSpinBox()
        self.min_quality_spin.setRange(0.0, 1.0)
        self.min_quality_spin.setSingleStep(0.1)
        self.min_quality_spin.setValue(0.0)
        query_layout.addWidget(self.min_quality_spin, 2, 1)
        
        # 查询按钮
        query_button = QPushButton("🔍 查询")
        query_button.clicked.connect(self.query_data)
        query_layout.addWidget(query_button, 2, 2)
        
        layout.addWidget(query_group)
        
        # 查询结果表格
        results_group = QGroupBox("📋 查询结果")
        results_layout = QVBoxLayout(results_group)
        
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(8)
        self.results_table.setHorizontalHeaderLabels([
            "患者ID", "会话ID", "试验ID", "标签", "质量", "时长(秒)", "创建时间", "文件路径"
        ])
        self.results_table.horizontalHeader().setStretchLastSection(True)
        self.results_table.setSelectionBehavior(QTableWidget.SelectRows)
        
        results_layout.addWidget(self.results_table)
        layout.addWidget(results_group)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        view_button = QPushButton("👁️ 查看详情")
        view_button.clicked.connect(self.view_trial_details)
        button_layout.addWidget(view_button)
        
        export_selected_button = QPushButton("📤 导出选中")
        export_selected_button.clicked.connect(self.export_selected_trials)
        button_layout.addWidget(export_selected_button)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        return widget

    def create_analysis_tab(self):
        """创建数据分析标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 分析选项组
        analysis_group = QGroupBox("📈 分析选项")
        analysis_layout = QGridLayout(analysis_group)

        # 分析类型
        analysis_layout.addWidget(QLabel("分析类型:"), 0, 0)
        self.analysis_type_combo = QComboBox()
        self.analysis_type_combo.addItems([
            "数据质量分析", "标签分布分析", "时间趋势分析", "患者对比分析"
        ])
        analysis_layout.addWidget(self.analysis_type_combo, 0, 1)

        # 分析按钮
        analyze_button = QPushButton("📊 开始分析")
        analyze_button.clicked.connect(self.start_analysis)
        analysis_layout.addWidget(analyze_button, 0, 2)

        layout.addWidget(analysis_group)

        # 分析结果显示
        results_group = QGroupBox("📊 分析结果")
        results_layout = QVBoxLayout(results_group)

        self.analysis_text = QTextEdit()
        self.analysis_text.setReadOnly(True)
        self.analysis_text.setFont(QFont("Consolas", 10))
        results_layout.addWidget(self.analysis_text)

        layout.addWidget(results_group)

        return widget

    def create_export_tab(self):
        """创建数据导出标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 导出配置组
        export_group = QGroupBox("📤 导出配置")
        export_layout = QGridLayout(export_group)

        # 患者选择
        export_layout.addWidget(QLabel("选择患者:"), 0, 0)
        self.export_patient_combo = QComboBox()
        self.export_patient_combo.setEditable(True)
        export_layout.addWidget(self.export_patient_combo, 0, 1)

        # 导出格式
        export_layout.addWidget(QLabel("导出格式:"), 0, 2)
        self.export_format_combo = QComboBox()
        self.export_format_combo.addItems(["HDF5", "CSV", "MAT"])
        export_layout.addWidget(self.export_format_combo, 0, 3)

        # 输出目录
        export_layout.addWidget(QLabel("输出目录:"), 1, 0)
        self.output_dir_label = QLabel("未选择")
        export_layout.addWidget(self.output_dir_label, 1, 1, 1, 2)

        browse_button = QPushButton("📁 浏览")
        browse_button.clicked.connect(self.browse_output_dir)
        export_layout.addWidget(browse_button, 1, 3)

        # 导出选项
        export_layout.addWidget(QLabel("包含元数据:"), 2, 0)
        self.include_metadata_check = QCheckBox()
        self.include_metadata_check.setChecked(True)
        export_layout.addWidget(self.include_metadata_check, 2, 1)

        export_layout.addWidget(QLabel("压缩数据:"), 2, 2)
        self.compress_data_check = QCheckBox()
        self.compress_data_check.setChecked(True)
        export_layout.addWidget(self.compress_data_check, 2, 3)

        layout.addWidget(export_group)

        # 导出进度
        progress_group = QGroupBox("📊 导出进度")
        progress_layout = QVBoxLayout(progress_group)

        self.export_progress = QProgressBar()
        self.export_status_label = QLabel("就绪")

        progress_layout.addWidget(self.export_progress)
        progress_layout.addWidget(self.export_status_label)

        layout.addWidget(progress_group)

        # 导出按钮
        button_layout = QHBoxLayout()

        export_button = QPushButton("🚀 开始导出")
        export_button.clicked.connect(self.start_export)
        button_layout.addWidget(export_button)

        cancel_button = QPushButton("❌ 取消导出")
        cancel_button.clicked.connect(self.cancel_export)
        button_layout.addWidget(cancel_button)

        button_layout.addStretch()
        layout.addLayout(button_layout)

        return widget

    def set_database_manager(self, db_manager: DatabaseManager):
        """设置数据库管理器"""
        self.db_manager = db_manager

        # 初始化数据加载器和集成管理器
        self.data_loader = EEGDataLoader(db_manager)
        self.training_integration = TrainingDataIntegration(db_manager)

        # 加载患者列表
        self.load_patient_list()

        # 刷新数据
        self.refresh_data()

        self.logger.info("数据管理界面数据库管理器设置完成")

    def load_patient_list(self):
        """加载患者列表"""
        try:
            if not self.db_manager:
                return

            # 获取所有患者
            patients = self.db_manager.execute_query("SELECT bianhao, name FROM bingren ORDER BY bianhao")

            # 清空现有选项
            self.patient_combo.clear()
            self.export_patient_combo.clear()

            # 添加"全部"选项
            self.patient_combo.addItem("全部患者", None)
            self.export_patient_combo.addItem("全部患者", None)

            # 添加患者选项
            if patients:
                for patient in patients:
                    patient_id = patient['bianhao']
                    patient_name = patient.get('name', f'患者{patient_id}')
                    display_text = f"{patient_id} - {patient_name}"

                    self.patient_combo.addItem(display_text, patient_id)
                    self.export_patient_combo.addItem(display_text, patient_id)

        except Exception as e:
            self.logger.error(f"加载患者列表失败: {e}")

    def refresh_data(self):
        """刷新数据"""
        try:
            if not self.data_loader:
                return

            # 获取统计信息
            stats = self.data_loader.get_data_statistics()
            self.current_statistics = stats

            # 更新统计标签
            self.total_patients_label.setText(str(stats.get('total_patients', 0)))
            self.total_sessions_label.setText(str(stats.get('total_sessions', 0)))
            self.total_trials_label.setText(str(stats.get('total_trials', 0)))

            total_size = stats.get('total_size', 0) or 0
            self.total_size_label.setText(f"{total_size / 1024 / 1024:.2f} MB")

            avg_quality = stats.get('avg_quality') or 0
            self.avg_quality_label.setText(f"{avg_quality:.3f}")

            # 更新最近数据表格
            self.update_recent_data_table()

        except Exception as e:
            self.logger.error(f"刷新数据失败: {e}")

    def update_recent_data_table(self):
        """更新最近数据表格"""
        try:
            if not self.data_loader:
                return

            # 获取最近的会话数据
            criteria = {
                'limit': 10
            }
            recent_trials = self.data_loader.search_trials(criteria)

            # 按会话分组
            sessions = {}
            for trial in recent_trials:
                session_id = trial['session_id']
                if session_id not in sessions:
                    sessions[session_id] = {
                        'patient_id': trial['patient_id'],
                        'session_type': trial.get('session_type', 'unknown'),
                        'trials': [],
                        'start_time': trial.get('session_start', trial.get('start_time', '')),
                        'total_size': 0
                    }
                sessions[session_id]['trials'].append(trial)
                sessions[session_id]['total_size'] += trial.get('file_size_bytes', 0)

            # 更新表格
            self.recent_table.setRowCount(len(sessions))

            for row, (session_id, session_data) in enumerate(sessions.items()):
                # 患者ID
                self.recent_table.setItem(row, 0, QTableWidgetItem(str(session_data['patient_id'])))

                # 会话类型
                self.recent_table.setItem(row, 1, QTableWidgetItem(session_data['session_type']))

                # 试验数
                self.recent_table.setItem(row, 2, QTableWidgetItem(str(len(session_data['trials']))))

                # 平均质量
                avg_quality = np.mean([t.get('data_quality', 0) for t in session_data['trials']])
                self.recent_table.setItem(row, 3, QTableWidgetItem(f"{avg_quality:.3f}"))

                # 创建时间
                self.recent_table.setItem(row, 4, QTableWidgetItem(session_data['start_time']))

                # 文件大小
                size_mb = session_data['total_size'] / 1024 / 1024
                self.recent_table.setItem(row, 5, QTableWidgetItem(f"{size_mb:.2f} MB"))

        except Exception as e:
            self.logger.error(f"更新最近数据表格失败: {e}")

    def query_data(self):
        """查询数据"""
        try:
            if not self.data_loader:
                QMessageBox.warning(self, "警告", "数据加载器未初始化")
                return

            # 构建查询条件
            criteria = {}

            # 患者ID
            patient_data = self.patient_combo.currentData()
            if patient_data:
                criteria['patient_ids'] = [patient_data]

            # 会话类型
            session_type = self.session_type_combo.currentText()
            if session_type != "全部":
                criteria['session_types'] = [session_type]

            # 日期范围
            start_date = self.start_date.date().toPython()
            end_date = self.end_date.date().toPython()
            criteria['date_range'] = (
                datetime.combine(start_date, datetime.min.time()),
                datetime.combine(end_date, datetime.max.time())
            )

            # 质量过滤
            min_quality = self.min_quality_spin.value()
            if min_quality > 0:
                criteria['min_quality'] = min_quality

            # 限制结果数量
            criteria['limit'] = 1000

            # 执行查询
            results = self.data_loader.search_trials(criteria)
            self.current_data = results

            # 更新结果表格
            self.update_results_table(results)

            self.logger.info(f"查询完成，找到 {len(results)} 条记录")

        except Exception as e:
            self.logger.error(f"查询数据失败: {e}")
            QMessageBox.critical(self, "错误", f"查询数据失败: {e}")

    def update_results_table(self, results: List[Dict[str, Any]]):
        """更新结果表格"""
        try:
            self.results_table.setRowCount(len(results))

            for row, trial in enumerate(results):
                # 患者ID
                self.results_table.setItem(row, 0, QTableWidgetItem(str(trial.get('patient_id', ''))))

                # 会话ID
                self.results_table.setItem(row, 1, QTableWidgetItem(str(trial.get('session_id', ''))))

                # 试验ID
                self.results_table.setItem(row, 2, QTableWidgetItem(str(trial.get('trial_id', ''))))

                # 标签
                label = trial.get('label', 0)
                label_text = "运动想象" if label == 1 else "休息"
                self.results_table.setItem(row, 3, QTableWidgetItem(label_text))

                # 质量
                quality = trial.get('data_quality', 0) or 0
                self.results_table.setItem(row, 4, QTableWidgetItem(f"{quality:.3f}"))

                # 时长
                duration = trial.get('duration_seconds', 0) or 0
                self.results_table.setItem(row, 5, QTableWidgetItem(f"{duration:.1f}"))

                # 创建时间
                start_time = trial.get('start_time', '')
                self.results_table.setItem(row, 6, QTableWidgetItem(start_time))

                # 文件路径
                file_path = trial.get('file_path', '')
                self.results_table.setItem(row, 7, QTableWidgetItem(file_path))

        except Exception as e:
            self.logger.error(f"更新结果表格失败: {e}")

    def view_trial_details(self):
        """查看试验详情"""
        try:
            current_row = self.results_table.currentRow()
            if current_row < 0 or current_row >= len(self.current_data):
                QMessageBox.information(self, "提示", "请选择一个试验")
                return

            trial = self.current_data[current_row]

            # 创建详情对话框
            details = f"""
试验详情:
========
患者ID: {trial.get('patient_id', '')}
会话ID: {trial.get('session_id', '')}
试验ID: {trial.get('trial_id', '')}
标签: {'运动想象' if trial.get('label', 0) == 1 else '休息'}
数据质量: {trial.get('data_quality', 0):.3f}
持续时间: {trial.get('duration_seconds', 0):.1f} 秒
采样率: {trial.get('sampling_rate', 125)} Hz
通道数: {trial.get('channels', 8)}
文件大小: {(trial.get('file_size_bytes', 0) or 0) / 1024:.2f} KB
创建时间: {trial.get('start_time', '')}
文件路径: {trial.get('file_path', '')}
校验和: {trial.get('checksum', '无')}
"""

            QMessageBox.information(self, "试验详情", details)

        except Exception as e:
            self.logger.error(f"查看试验详情失败: {e}")
            QMessageBox.critical(self, "错误", f"查看试验详情失败: {e}")

    def export_selected_trials(self):
        """导出选中的试验"""
        try:
            selected_rows = set()
            for item in self.results_table.selectedItems():
                selected_rows.add(item.row())

            if not selected_rows:
                QMessageBox.information(self, "提示", "请选择要导出的试验")
                return

            # 选择输出目录
            output_dir = QFileDialog.getExistingDirectory(self, "选择导出目录")
            if not output_dir:
                return

            # 导出选中的试验
            exported_count = 0
            for row in selected_rows:
                if row < len(self.current_data):
                    trial = self.current_data[row]
                    # 这里可以实现具体的导出逻辑
                    exported_count += 1

            QMessageBox.information(self, "导出完成", f"已导出 {exported_count} 个试验到: {output_dir}")

        except Exception as e:
            self.logger.error(f"导出选中试验失败: {e}")
            QMessageBox.critical(self, "错误", f"导出选中试验失败: {e}")

    def start_analysis(self):
        """开始数据分析"""
        try:
            if not self.data_loader:
                QMessageBox.warning(self, "警告", "数据加载器未初始化")
                return

            analysis_type = self.analysis_type_combo.currentText()

            self.analysis_text.clear()
            self.analysis_text.append(f"开始 {analysis_type}...")
            self.analysis_text.append("=" * 50)

            if analysis_type == "数据质量分析":
                self._analyze_data_quality()
            elif analysis_type == "标签分布分析":
                self._analyze_label_distribution()
            elif analysis_type == "时间趋势分析":
                self._analyze_time_trends()
            elif analysis_type == "患者对比分析":
                self._analyze_patient_comparison()

            self.analysis_text.append("\n分析完成!")

        except Exception as e:
            self.logger.error(f"数据分析失败: {e}")
            self.analysis_text.append(f"\n分析失败: {e}")

    def _analyze_data_quality(self):
        """分析数据质量"""
        try:
            stats = self.current_statistics

            self.analysis_text.append("数据质量分析结果:")
            self.analysis_text.append("-" * 30)

            avg_quality = stats.get('avg_quality') or 0
            min_quality = stats.get('min_quality') or 0
            max_quality = stats.get('max_quality') or 0

            self.analysis_text.append(f"平均质量: {avg_quality:.3f}")
            self.analysis_text.append(f"最低质量: {min_quality:.3f}")
            self.analysis_text.append(f"最高质量: {max_quality:.3f}")

            # 质量分布
            quality_dist = stats.get('quality_distribution', {})
            if quality_dist:
                self.analysis_text.append("\n质量分布:")
                for quality_level, count in quality_dist.items():
                    percentage = (count / stats.get('total_trials', 1)) * 100
                    self.analysis_text.append(f"  {quality_level}: {count} ({percentage:.1f}%)")

            # 质量建议
            self.analysis_text.append("\n质量评估:")
            if avg_quality >= 0.8:
                self.analysis_text.append("✅ 数据质量优秀")
            elif avg_quality >= 0.6:
                self.analysis_text.append("⚠️ 数据质量良好，建议优化采集环境")
            else:
                self.analysis_text.append("❌ 数据质量较差，需要检查设备和采集流程")

        except Exception as e:
            self.analysis_text.append(f"质量分析失败: {e}")

    def _analyze_label_distribution(self):
        """分析标签分布"""
        try:
            stats = self.current_statistics

            self.analysis_text.append("标签分布分析结果:")
            self.analysis_text.append("-" * 30)

            label_dist = stats.get('label_distribution', {})
            total_trials = stats.get('total_trials', 0)

            if label_dist and total_trials > 0:
                for label, count in label_dist.items():
                    label_name = "运动想象" if label == "1" else "休息"
                    percentage = (count / total_trials) * 100
                    self.analysis_text.append(f"{label_name}: {count} ({percentage:.1f}%)")

                # 平衡性分析
                self.analysis_text.append("\n平衡性分析:")
                if len(label_dist) == 2:
                    counts = list(label_dist.values())
                    ratio = max(counts) / min(counts) if min(counts) > 0 else float('inf')

                    if ratio <= 1.2:
                        self.analysis_text.append("✅ 标签分布平衡")
                    elif ratio <= 2.0:
                        self.analysis_text.append("⚠️ 标签分布轻微不平衡")
                    else:
                        self.analysis_text.append("❌ 标签分布严重不平衡，建议重新采集")
                else:
                    self.analysis_text.append("⚠️ 标签类别不完整")
            else:
                self.analysis_text.append("无标签分布数据")

        except Exception as e:
            self.analysis_text.append(f"标签分布分析失败: {e}")

    def _analyze_time_trends(self):
        """分析时间趋势"""
        try:
            self.analysis_text.append("时间趋势分析结果:")
            self.analysis_text.append("-" * 30)

            # 获取最近30天的数据
            criteria = {
                'date_range': (
                    datetime.now() - timedelta(days=30),
                    datetime.now()
                ),
                'limit': 10000
            }

            recent_data = self.data_loader.search_trials(criteria)

            if recent_data:
                # 按日期分组
                daily_counts = {}
                daily_quality = {}

                for trial in recent_data:
                    date_str = trial.get('start_time', '')[:10]  # 取日期部分
                    if date_str:
                        if date_str not in daily_counts:
                            daily_counts[date_str] = 0
                            daily_quality[date_str] = []

                        daily_counts[date_str] += 1
                        quality = trial.get('data_quality', 0)
                        if quality:
                            daily_quality[date_str].append(quality)

                # 显示趋势
                self.analysis_text.append("最近7天数据量:")
                sorted_dates = sorted(daily_counts.keys())[-7:]
                for date in sorted_dates:
                    count = daily_counts[date]
                    avg_quality = np.mean(daily_quality[date]) if daily_quality[date] else 0
                    self.analysis_text.append(f"  {date}: {count} 试验, 平均质量 {avg_quality:.3f}")

                # 趋势分析
                if len(sorted_dates) >= 3:
                    recent_counts = [daily_counts[date] for date in sorted_dates[-3:]]
                    if recent_counts[-1] > recent_counts[0]:
                        self.analysis_text.append("\n📈 数据采集量呈上升趋势")
                    elif recent_counts[-1] < recent_counts[0]:
                        self.analysis_text.append("\n📉 数据采集量呈下降趋势")
                    else:
                        self.analysis_text.append("\n➡️ 数据采集量保持稳定")
            else:
                self.analysis_text.append("最近30天无数据")

        except Exception as e:
            self.analysis_text.append(f"时间趋势分析失败: {e}")

    def _analyze_patient_comparison(self):
        """分析患者对比"""
        try:
            self.analysis_text.append("患者对比分析结果:")
            self.analysis_text.append("-" * 30)

            # 获取所有患者的统计
            if not self.db_manager:
                self.analysis_text.append("数据库未连接")
                return

            patients = self.db_manager.execute_query("SELECT bianhao, name FROM bingren ORDER BY bianhao")

            if patients:
                self.analysis_text.append("患者数据对比:")
                self.analysis_text.append("患者ID | 试验数 | 平均质量 | 最新训练")
                self.analysis_text.append("-" * 50)

                for patient in patients[:10]:  # 限制显示前10个患者
                    patient_id = patient['bianhao']
                    patient_name = patient.get('name', f'患者{patient_id}')

                    # 获取患者统计
                    patient_stats = self.data_loader.get_data_statistics([patient_id])

                    trial_count = patient_stats.get('total_trials', 0)
                    avg_quality = patient_stats.get('avg_quality') or 0

                    # 获取最新训练时间
                    recent_trials = self.data_loader.search_trials({
                        'patient_ids': [patient_id],
                        'limit': 1
                    })

                    last_training = "无记录"
                    if recent_trials:
                        last_training = recent_trials[0].get('start_time', '')[:10]

                    self.analysis_text.append(
                        f"{patient_id:6} | {trial_count:6} | {avg_quality:8.3f} | {last_training}"
                    )
            else:
                self.analysis_text.append("无患者数据")

        except Exception as e:
            self.analysis_text.append(f"患者对比分析失败: {e}")

    def browse_output_dir(self):
        """浏览输出目录"""
        try:
            output_dir = QFileDialog.getExistingDirectory(self, "选择输出目录")
            if output_dir:
                self.output_dir_label.setText(output_dir)
        except Exception as e:
            self.logger.error(f"浏览输出目录失败: {e}")

    def start_export(self):
        """开始导出"""
        try:
            # 检查配置
            if self.output_dir_label.text() == "未选择":
                QMessageBox.warning(self, "警告", "请选择输出目录")
                return

            patient_data = self.export_patient_combo.currentData()
            if not patient_data:
                QMessageBox.warning(self, "警告", "请选择要导出的患者")
                return

            # 配置导出参数
            export_config = {
                'patient_ids': [patient_data],
                'output_dir': self.output_dir_label.text(),
                'format': self.export_format_combo.currentText().lower(),
                'include_metadata': self.include_metadata_check.isChecked(),
                'compress': self.compress_data_check.isChecked()
            }

            # 启动导出工作线程
            self.export_worker = DataExportWorker(self.data_loader, export_config)
            self.export_worker.progress_updated.connect(self._on_export_progress)
            self.export_worker.export_completed.connect(self._on_export_completed)
            self.export_worker.start()

            self.export_progress.setValue(0)
            self.export_status_label.setText("正在导出...")

        except Exception as e:
            self.logger.error(f"开始导出失败: {e}")
            QMessageBox.critical(self, "错误", f"开始导出失败: {e}")

    def cancel_export(self):
        """取消导出"""
        try:
            if self.export_worker and self.export_worker.isRunning():
                self.export_worker.terminate()
                self.export_worker.wait()

                self.export_progress.setValue(0)
                self.export_status_label.setText("导出已取消")

        except Exception as e:
            self.logger.error(f"取消导出失败: {e}")

    def _on_export_progress(self, progress: int, message: str):
        """导出进度更新"""
        self.export_progress.setValue(progress)
        self.export_status_label.setText(message)

    def _on_export_completed(self, success: bool, message: str):
        """导出完成"""
        if success:
            QMessageBox.information(self, "导出完成", message)
        else:
            QMessageBox.critical(self, "导出失败", message)

        self.export_progress.setValue(0)
        self.export_status_label.setText("就绪")

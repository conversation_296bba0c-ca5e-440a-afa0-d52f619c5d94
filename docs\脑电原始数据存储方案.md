# 带标签脑电原始数据存储方案

## 📊 当前系统分析

### 现有数据处理流程
您的系统目前具备以下数据处理能力：

1. **脑电数据采集**：
   - ADS1299设备，8通道，125Hz采样率
   - 实时数据包解析（100字节/包，4组×8通道×3字节）
   - 通道：PZ, P3, P4, C3, CZ, C4, F3, F4

2. **现有存储结构**：
   - **Edata表**：存储处理后的特征数据（频域特征）
   - **zhiliao表**：存储治疗记录和统计信息
   - **models目录**：存储训练好的模型文件

3. **数据处理现状**：
   - 只保存特征提取后的数据（theta、alpha、beta、gamma频段功率）
   - **缺失原始时域数据存储**
   - **缺失标签化数据存储**

## 🎯 带标签脑电原始数据存储方案

### 方案设计原则

1. **医疗级标准**：符合医疗器械软件数据完整性要求
2. **高效存储**：优化存储空间和访问速度
3. **标准格式**：使用国际标准格式，便于数据交换
4. **可扩展性**：支持未来功能扩展和数据分析需求
5. **数据安全**：确保患者数据隐私和完整性

### 推荐存储格式

#### 1. HDF5格式（主推荐）
**优势**：
- 高效压缩，节省存储空间
- 支持大数据集和快速随机访问
- 跨平台兼容性好
- 支持元数据存储
- Python生态系统支持完善

**文件结构**：
```
patient_123_session_001.h5
├── /raw_data
│   ├── /eeg_data          # 原始脑电数据 (8, N)
│   ├── /timestamps        # 时间戳数组
│   └── /sampling_rate     # 采样率
├── /labels
│   ├── /trial_labels      # 试验标签 (0=休息, 1=运动想象)
│   ├── /trial_onsets      # 试验开始时间点
│   └── /trial_durations   # 试验持续时间
├── /metadata
│   ├── /patient_info      # 患者信息
│   ├── /session_info      # 会话信息
│   ├── /device_info       # 设备信息
│   └── /channel_info      # 通道信息
└── /processed_data
    ├── /features          # 提取的特征
    └── /quality_metrics   # 数据质量指标
```

#### 2. EDF+格式（备选方案）
**优势**：
- 医疗行业标准格式
- 广泛的软件支持
- 内置标注功能

**适用场景**：
- 需要与其他医疗设备数据交换
- 长期数据归档

### 数据库表结构扩展

#### 新增表：eeg_raw_data
```sql
CREATE TABLE eeg_raw_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    patient_id INTEGER NOT NULL,                    -- 患者ID
    session_id INTEGER NOT NULL,                    -- 会话ID
    trial_id INTEGER NOT NULL,                      -- 试验ID
    file_path TEXT NOT NULL,                        -- 数据文件路径
    file_format TEXT DEFAULT 'hdf5',                -- 文件格式
    start_time TIMESTAMP NOT NULL,                  -- 开始时间
    duration_seconds REAL NOT NULL,                 -- 持续时间
    sampling_rate INTEGER DEFAULT 125,              -- 采样率
    channels INTEGER DEFAULT 8,                     -- 通道数
    label INTEGER NOT NULL,                         -- 标签 (0=休息, 1=运动想象)
    data_quality REAL,                              -- 数据质量评分
    file_size_bytes INTEGER,                        -- 文件大小
    checksum TEXT,                                  -- 文件校验和
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (patient_id) REFERENCES bingren(bianhao)
);
```

#### 扩展表：eeg_sessions
```sql
CREATE TABLE eeg_sessions (
    session_id INTEGER PRIMARY KEY AUTOINCREMENT,
    patient_id INTEGER NOT NULL,
    treatment_id INTEGER,                           -- 关联治疗记录
    session_type TEXT DEFAULT 'training',          -- 会话类型
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    total_trials INTEGER DEFAULT 0,
    successful_trials INTEGER DEFAULT 0,
    session_notes TEXT,
    data_directory TEXT,                            -- 数据存储目录
    FOREIGN KEY (patient_id) REFERENCES bingren(bianhao),
    FOREIGN KEY (treatment_id) REFERENCES zhiliao(zhiliaobh)
);
```

## 📁 文件存储结构

### 目录组织方案
```
data/
├── raw_eeg_data/                    # 原始脑电数据根目录
│   ├── patients/                    # 按患者组织
│   │   ├── patient_001/             # 患者001
│   │   │   ├── sessions/            # 会话数据
│   │   │   │   ├── 2024-12-19/      # 按日期组织
│   │   │   │   │   ├── session_001.h5
│   │   │   │   │   ├── session_002.h5
│   │   │   │   │   └── metadata.json
│   │   │   │   └── 2024-12-20/
│   │   │   └── models/              # 患者专用模型
│   │   └── patient_002/
│   ├── templates/                   # 数据模板
│   └── exports/                     # 导出数据
├── processed_data/                  # 处理后数据
└── backup/                          # 备份数据
```

### 文件命名规范
```
格式：{patient_id}_{session_date}_{session_time}_{trial_type}.h5
示例：P001_20241219_143022_training.h5
```

## 🔧 实现方案

### 1. 数据采集模块增强
需要修改现有的脑电数据处理流程，在特征提取的同时保存原始数据：

```python
class EEGRawDataManager:
    """脑电原始数据管理器"""
    
    def __init__(self, data_root: str):
        self.data_root = Path(data_root)
        self.current_session = None
        self.current_file = None
        
    def start_session(self, patient_id: int, session_type: str = 'training'):
        """开始新的数据采集会话"""
        
    def save_trial_data(self, eeg_data: np.ndarray, label: int, 
                       trial_metadata: dict):
        """保存单次试验数据"""
        
    def end_session(self):
        """结束当前会话"""
```

### 2. 存储格式实现
使用HDF5格式存储，支持压缩和快速访问：

```python
import h5py
import numpy as np
from datetime import datetime

def save_eeg_trial_hdf5(file_path: str, eeg_data: np.ndarray, 
                       label: int, metadata: dict):
    """保存单次试验数据到HDF5文件"""
    with h5py.File(file_path, 'a') as f:
        # 创建试验组
        trial_group = f.create_group(f'trial_{len(f.keys()):03d}')
        
        # 保存原始数据（压缩）
        trial_group.create_dataset('eeg_data', data=eeg_data, 
                                 compression='gzip', compression_opts=9)
        
        # 保存标签和元数据
        trial_group.attrs['label'] = label
        trial_group.attrs['timestamp'] = datetime.now().isoformat()
        
        for key, value in metadata.items():
            trial_group.attrs[key] = value
```

### 3. 数据查询和访问接口
提供便捷的数据访问接口：

```python
class EEGDataLoader:
    """脑电数据加载器"""
    
    def load_patient_data(self, patient_id: int, 
                         date_range: tuple = None) -> List[dict]:
        """加载患者的脑电数据"""
        
    def load_session_data(self, session_id: int) -> dict:
        """加载特定会话的数据"""
        
    def get_training_dataset(self, patient_ids: List[int], 
                           balance_classes: bool = True) -> tuple:
        """获取训练数据集"""
```

## 📈 存储空间估算

### 数据量计算
- **采样率**：125 Hz
- **通道数**：8
- **数据类型**：int32 (4字节)
- **单次试验时长**：4秒

**单次试验数据量**：
```
125 Hz × 8通道 × 4秒 × 4字节 = 16,000字节 ≈ 16KB
```

**单次治疗会话**（假设60次试验）：
```
16KB × 60 = 960KB ≈ 1MB
```

**HDF5压缩后**（压缩比约3:1）：
```
1MB ÷ 3 ≈ 340KB
```

### 存储需求预估
- **每患者每月**：约10MB（30次治疗）
- **100患者一年**：约12GB
- **建议预留空间**：50GB（包含备份和扩展）

## 🛠️ 实施步骤

### 第一阶段：基础设施搭建
1. 创建数据库表结构
2. 建立文件存储目录
3. 实现基础的数据保存功能

### 第二阶段：集成到现有系统
1. 修改治疗工作流程，添加数据保存
2. 更新用户界面，显示存储状态
3. 实现数据查询和导出功能

### 第三阶段：高级功能
1. 数据质量评估和过滤
2. 自动数据备份和归档
3. 数据分析和可视化工具

## 🔒 数据安全和隐私

### 安全措施
1. **数据加密**：敏感数据文件加密存储
2. **访问控制**：基于用户权限的数据访问
3. **审计日志**：记录所有数据访问操作
4. **备份策略**：定期自动备份到安全位置

### 隐私保护
1. **数据匿名化**：可选的患者信息匿名化
2. **数据脱敏**：导出数据时移除敏感信息
3. **合规性**：符合医疗数据保护法规

## 📋 配置建议

### 系统配置更新
在 `utils/app_config.py` 中添加：

```python
RAW_DATA_CONFIG = {
    'enabled': True,                          # 启用原始数据存储
    'storage_format': 'hdf5',                 # 存储格式
    'compression': True,                      # 启用压缩
    'compression_level': 6,                   # 压缩级别 (1-9)
    'max_file_size_mb': 100,                  # 单文件最大大小
    'auto_backup': True,                      # 自动备份
    'backup_interval_hours': 24,              # 备份间隔
    'data_retention_days': 365,               # 数据保留天数
    'quality_threshold': 0.7,                 # 数据质量阈值
}
```

这个方案为您的系统提供了完整的带标签脑电原始数据存储能力，既保证了数据的完整性和可用性，又考虑了存储效率和系统性能。您希望我开始实施这个方案的哪个部分？

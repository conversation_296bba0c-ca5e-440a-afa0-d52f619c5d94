#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
患者管理界面模块
Patient Management UI Module

作者: AI Assistant
版本: 1.0.0
"""

import logging
from typing import Optional, Dict, Any, List

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout,
                               QTableWidget, QTableWidgetItem, QPushButton,
                               QLineEdit, QComboBox, QTextEdit,
                               QGroupBox, QMessageBox, QHeaderView,
                               QFormLayout, QSplitter, QDialog, QDialogButtonBox)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QRegularExpressionValidator
from PySide6.QtCore import QRegularExpression

from core.database_manager import DatabaseManager
from core.auth_manager import AuthManager, Permission
from core.http_client import PatientDataUploader
from core.logger_system import get_logger_system


class PatientManagementWidget(QWidget):
    """患者管理界面组件"""

    # 信号定义
    patient_selected = Signal(dict)  # 患者选择信号
    switch_to_treatment = Signal(dict)  # 切换到治疗页面信号
    patient_updated = Signal(dict)   # 患者更新信号

    def __init__(self, parent=None):
        super().__init__(parent)

        # 初始化属性
        self.db_manager: Optional[DatabaseManager] = None
        self.auth_manager: Optional[AuthManager] = None
        self.logger = logging.getLogger(__name__)
        self.logger_system = get_logger_system()
        self.current_patient = None

        # 初始化数据上传器
        self.data_uploader = PatientDataUploader()



        # UI组件
        self.patient_table = None
        self.treatment_table = None
        self.patient_form = None
        self.search_edit = None
        self.form_widgets = {}
        self.buttons = {}

        # 初始化界面
        self.init_ui()
        self.setup_connections()

        # 设置初始状态为不可编辑
        self.set_form_editable(False)

        self.logger.info("患者管理界面初始化完成")

    def init_ui(self):
        """初始化用户界面"""
        try:
            # 主布局
            main_layout = QHBoxLayout(self)
            main_layout.setContentsMargins(10, 10, 10, 10)

            # 创建分割器
            splitter = QSplitter(Qt.Horizontal)
            main_layout.addWidget(splitter)

            # 左侧：患者列表和搜索
            left_widget = self.create_patient_list_widget()
            splitter.addWidget(left_widget)

            # 右侧：患者信息和治疗记录
            right_widget = self.create_patient_detail_widget()
            splitter.addWidget(right_widget)

            # 设置分割器比例
            splitter.setStretchFactor(0, 1)
            splitter.setStretchFactor(1, 1)
            splitter.setSizes([600, 600])

        except Exception as e:
            self.logger.error(f"患者管理界面初始化失败: {e}")
            raise

    def create_patient_list_widget(self) -> QWidget:
        """创建患者列表部件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 搜索区域
        search_group = QGroupBox("患者搜索")
        search_layout = QHBoxLayout(search_group)

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("输入患者姓名或编号搜索...")
        search_layout.addWidget(self.search_edit)

        search_button = QPushButton("搜索")
        search_button.clicked.connect(self.search_patients)
        search_layout.addWidget(search_button)

        clear_button = QPushButton("清空")
        clear_button.clicked.connect(self.clear_search)
        search_layout.addWidget(clear_button)

        layout.addWidget(search_group)

        # 患者列表
        list_group = QGroupBox("患者列表")
        list_layout = QVBoxLayout(list_group)

        # 创建患者表格
        self.patient_table = QTableWidget()
        self.setup_patient_table()
        list_layout.addWidget(self.patient_table)

        layout.addWidget(list_group)

        return widget

    def create_patient_detail_widget(self) -> QWidget:
        """创建患者详情部件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 患者信息表单
        form_group = QGroupBox("患者信息")
        self.patient_form = self.create_patient_form()
        form_group.setLayout(self.patient_form)
        layout.addWidget(form_group)

        # 操作按钮
        button_layout = QHBoxLayout()

        self.buttons['add'] = QPushButton("添加")
        self.buttons['edit'] = QPushButton("编辑")
        self.buttons['save'] = QPushButton("保存")
        self.buttons['cancel'] = QPushButton("取消")
        self.buttons['delete'] = QPushButton("删除")

        # 设置按钮初始状态
        self.buttons['add'].setEnabled(False)  # 初始禁用，等待权限检查
        self.buttons['edit'].setEnabled(False)  # 初始禁用，等待权限检查
        self.buttons['delete'].setEnabled(False)  # 初始禁用，等待权限检查
        self.buttons['save'].setEnabled(False)  # 保存按钮初始禁用
        self.buttons['cancel'].setEnabled(False)  # 取消按钮初始禁用

        for button in self.buttons.values():
            button_layout.addWidget(button)

        button_layout.addStretch()
        layout.addLayout(button_layout)

        # 治疗记录
        treatment_group = QGroupBox("治疗记录")
        treatment_layout = QVBoxLayout(treatment_group)

        self.treatment_table = QTableWidget()
        self.setup_treatment_table()
        treatment_layout.addWidget(self.treatment_table)

        layout.addWidget(treatment_group)

        return widget

    def create_patient_form(self) -> QFormLayout:
        """创建患者信息表单"""
        form_layout = QFormLayout()

        # 患者编号
        self.form_widgets['bianhao'] = QLineEdit()
        self.form_widgets['bianhao'].setPlaceholderText("请输入患者编号")
        # 设置输入验证器，允许0开头的数字，最多15位
        bianhao_validator = QRegularExpressionValidator(QRegularExpression(r'^\d{0,15}$'))
        self.form_widgets['bianhao'].setValidator(bianhao_validator)
        form_layout.addRow("患者编号:", self.form_widgets['bianhao'])

        # 姓名
        self.form_widgets['name'] = QLineEdit()
        form_layout.addRow("姓名:", self.form_widgets['name'])

        # 年龄
        self.form_widgets['age'] = QLineEdit()
        self.form_widgets['age'].setPlaceholderText("请输入年龄")
        # 设置输入验证器，只允许输入1-150的数字
        age_validator = QRegularExpressionValidator(QRegularExpression(r'^(1[0-4][0-9]|150|[1-9][0-9]?|[1-9])$'))
        self.form_widgets['age'].setValidator(age_validator)
        form_layout.addRow("年龄:", self.form_widgets['age'])

        # 性别
        self.form_widgets['xingbie'] = QComboBox()
        self.form_widgets['xingbie'].addItems(["男", "女"])
        form_layout.addRow("性别:", self.form_widgets['xingbie'])

        # 身份证号
        self.form_widgets['cardid'] = QLineEdit()
        self.form_widgets['cardid'].setPlaceholderText("请输入数字形式的身份证号")
        # 设置输入验证器，只允许输入数字，最多18位
        cardid_validator = QRegularExpressionValidator(QRegularExpression(r'^\d{0,18}$'))
        self.form_widgets['cardid'].setValidator(cardid_validator)
        form_layout.addRow("身份证号:", self.form_widgets['cardid'])

        # 诊断
        self.form_widgets['zhenduan'] = QTextEdit()
        self.form_widgets['zhenduan'].setMaximumHeight(80)
        form_layout.addRow("诊断:", self.form_widgets['zhenduan'])

        # 既往史
        self.form_widgets['bingshi'] = QTextEdit()
        self.form_widgets['bingshi'].setMaximumHeight(80)
        form_layout.addRow("既往史:", self.form_widgets['bingshi'])

        # 患侧
        self.form_widgets['brhc'] = QComboBox()
        self.form_widgets['brhc'].addItems(["左侧", "右侧", "双侧"])
        form_layout.addRow("患侧:", self.form_widgets['brhc'])

        # 主治医师
        zhuzhi_layout = QHBoxLayout()
        zhuzhi_layout.setContentsMargins(0, 0, 0, 0)  # 移除边距确保对齐
        zhuzhi_layout.setSpacing(5)  # 设置控件间距

        self.form_widgets['zhuzhi'] = QComboBox()
        zhuzhi_layout.addWidget(self.form_widgets['zhuzhi'])

        # 医生管理按钮
        add_doctor_button = QPushButton("添加")
        add_doctor_button.setMaximumWidth(50)
        add_doctor_button.clicked.connect(self.add_doctor_dialog)
        zhuzhi_layout.addWidget(add_doctor_button)

        edit_doctor_button = QPushButton("编辑")
        edit_doctor_button.setMaximumWidth(50)
        edit_doctor_button.clicked.connect(self.edit_doctor_dialog)
        zhuzhi_layout.addWidget(edit_doctor_button)

        delete_doctor_button = QPushButton("删除")
        delete_doctor_button.setMaximumWidth(50)
        delete_doctor_button.clicked.connect(self.delete_doctor_dialog)
        zhuzhi_layout.addWidget(delete_doctor_button)

        zhuzhi_widget = QWidget()
        zhuzhi_widget.setLayout(zhuzhi_layout)
        form_layout.addRow("主治医师:", zhuzhi_widget)

        # 操作员（只读，显示当前登录用户）
        self.form_widgets['czy'] = QLineEdit()
        self.form_widgets['czy'].setReadOnly(True)
        self.form_widgets['czy'].setStyleSheet("background-color: #f0f0f0; color: #666666;")
        form_layout.addRow("操作员:", self.form_widgets['czy'])

        # 科室
        self.form_widgets['keshi'] = QLineEdit()
        self.form_widgets['keshi'].setReadOnly(True)
        self.form_widgets['keshi'].setStyleSheet("background-color: #f0f0f0; color: #666666;")
        form_layout.addRow("科室:", self.form_widgets['keshi'])

        # 设备号
        self.form_widgets['shebeiid'] = QLineEdit()
        self.form_widgets['shebeiid'].setReadOnly(True)
        self.form_widgets['shebeiid'].setStyleSheet("background-color: #f0f0f0; color: #666666;")
        form_layout.addRow("设备号:", self.form_widgets['shebeiid'])

        # 设置Tab键顺序和回车键处理
        self.setup_form_navigation()

        return form_layout

    def setup_form_navigation(self):
        """设置表单导航（Tab键顺序和回车键处理）"""
        try:
            # 定义Tab键顺序
            tab_order = [
                'bianhao',    # 患者编号
                'name',       # 姓名
                'age',        # 年龄
                'xingbie',    # 性别
                'cardid',     # 身份证号
                'zhenduan',   # 诊断
                'bingshi',    # 既往史
                'brhc',       # 患侧
                'zhuzhi',     # 主治医师
                'czy',        # 操作员
            ]

            # 设置Tab键顺序
            for i in range(len(tab_order) - 1):
                current_widget = self.form_widgets[tab_order[i]]
                next_widget = self.form_widgets[tab_order[i + 1]]
                self.setTabOrder(current_widget, next_widget)

            # 为可编辑字段添加回车键处理
            self.setup_enter_key_navigation(tab_order)

        except Exception as e:
            self.logger.error(f"设置表单导航失败: {e}")

    def setup_enter_key_navigation(self, tab_order):
        """设置回车键导航"""
        try:
            for i, field_name in enumerate(tab_order):
                widget = self.form_widgets[field_name]

                # 为不同类型的控件设置回车键处理
                if isinstance(widget, QLineEdit):
                    # 为QLineEdit添加回车键处理
                    widget.returnPressed.connect(
                        lambda idx=i: self.focus_next_field(tab_order, idx)
                    )

                elif isinstance(widget, QComboBox):
                    # 为QComboBox添加回车键处理
                    widget.keyPressEvent = self.create_combobox_key_handler(widget, tab_order, i)
                elif isinstance(widget, QTextEdit):
                    # 为QTextEdit添加Ctrl+Enter处理（因为Enter用于换行）
                    widget.keyPressEvent = self.create_textedit_key_handler(widget, tab_order, i)

        except Exception as e:
            self.logger.error(f"设置回车键导航失败: {e}")

    def create_textedit_key_handler(self, original_widget, tab_order, index):
        """为QTextEdit创建键盘事件处理器"""
        original_key_press = original_widget.keyPressEvent

        def key_press_event(event):
            # Ctrl+Enter 跳转到下一个字段
            if event.key() == Qt.Key_Return and event.modifiers() == Qt.ControlModifier:
                self.focus_next_field(tab_order, index)
            else:
                # 调用原始的键盘事件处理
                original_key_press(event)

        return key_press_event

    def create_combobox_key_handler(self, original_widget, tab_order, index):
        """为QComboBox创建键盘事件处理器"""
        original_key_press = original_widget.keyPressEvent

        def key_press_event(event):
            # Enter或Return键跳转到下一个字段
            if event.key() in (Qt.Key_Return, Qt.Key_Enter):
                # 如果下拉列表是打开的，先关闭它
                if original_widget.view().isVisible():
                    original_widget.hidePopup()
                # 跳转到下一个字段
                self.focus_next_field(tab_order, index)
            else:
                # 调用原始的键盘事件处理
                original_key_press(event)

        return key_press_event

    def focus_next_field(self, tab_order, current_index):
        """聚焦到下一个字段"""
        try:
            if current_index < len(tab_order) - 1:
                next_field = tab_order[current_index + 1]
                next_widget = self.form_widgets[next_field]
                next_widget.setFocus()

                # 如果是QLineEdit，选中所有文本
                if isinstance(next_widget, QLineEdit):
                    next_widget.selectAll()
                elif isinstance(next_widget, QComboBox):
                    # 对于ComboBox，打开下拉列表
                    next_widget.showPopup()

        except Exception as e:
            self.logger.error(f"聚焦下一个字段失败: {e}")

    def setup_patient_table(self):
        """设置患者表格"""
        try:
            # 设置列
            headers = ["编号", "姓名", "年龄", "性别", "身份证号", "诊断", "录入时间", "患侧"]
            self.patient_table.setColumnCount(len(headers))
            self.patient_table.setHorizontalHeaderLabels(headers)

            # 设置表格属性
            self.patient_table.setSelectionBehavior(QTableWidget.SelectRows)
            self.patient_table.setSelectionMode(QTableWidget.SingleSelection)
            self.patient_table.setAlternatingRowColors(True)

            # 禁用表格编辑功能，防止双击进入编辑状态
            self.patient_table.setEditTriggers(QTableWidget.NoEditTriggers)

            # 设置列宽
            header = self.patient_table.horizontalHeader()
            header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # 编号
            header.setSectionResizeMode(1, QHeaderView.Fixed)  # 姓名 - 固定宽度
            header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # 年龄
            header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # 性别
            header.setSectionResizeMode(4, QHeaderView.Stretch)  # 身份证号 - 自适应剩余空间
            header.setSectionResizeMode(5, QHeaderView.Stretch)  # 诊断 - 自适应剩余空间
            header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # 录入时间
            header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # 患侧

            # 设置姓名列的固定宽度（适合3个中文字符）
            header.resizeSection(1, 80)

        except Exception as e:
            self.logger.error(f"设置患者表格失败: {e}")

    def setup_treatment_table(self):
        """设置治疗记录表格"""
        try:
            # 设置列
            headers = ["治疗编号", "日期", "时间", "治疗效果", "要求次数", "实际次数", "治疗时长", "操作员"]
            self.treatment_table.setColumnCount(len(headers))
            self.treatment_table.setHorizontalHeaderLabels(headers)

            # 设置表格属性
            self.treatment_table.setSelectionBehavior(QTableWidget.SelectRows)
            self.treatment_table.setSelectionMode(QTableWidget.SingleSelection)
            self.treatment_table.setAlternatingRowColors(True)

            # 设置列宽
            header = self.treatment_table.horizontalHeader()
            header.setStretchLastSection(True)

        except Exception as e:
            self.logger.error(f"设置治疗记录表格失败: {e}")

    def setup_connections(self):
        """设置信号连接"""
        try:
            # 搜索框回车搜索
            self.search_edit.returnPressed.connect(self.search_patients)

            # 患者表格选择
            self.patient_table.itemSelectionChanged.connect(self.on_patient_selected)

            # 患者表格双击事件
            self.patient_table.itemDoubleClicked.connect(self.on_patient_double_clicked)

            # 按钮连接
            self.buttons['add'].clicked.connect(self.add_patient)
            self.buttons['edit'].clicked.connect(self.edit_patient)
            self.buttons['save'].clicked.connect(self.save_patient)
            self.buttons['cancel'].clicked.connect(self.cancel_edit)
            self.buttons['delete'].clicked.connect(self.delete_patient)

        except Exception as e:
            self.logger.error(f"设置信号连接失败: {e}")

    def set_database_manager(self, db_manager: DatabaseManager):
        """设置数据库管理器"""
        self.db_manager = db_manager

        # 加载初始数据
        self.load_patients()
        self.load_doctors()
        self.load_hospital_info()
        self.update_current_operator()

        self.logger.info("患者管理界面数据库管理器设置完成")

    def set_auth_manager(self, auth_manager: AuthManager):
        """设置权限管理器"""
        self.auth_manager = auth_manager
        self.update_ui_permissions()
        self.logger.info("患者管理界面权限管理器设置完成")

    def update_ui_permissions(self):
        """更新UI权限状态"""
        try:
            if not self.auth_manager:
                # 如果没有权限管理器，禁用所有操作按钮但不弹出警告
                self.buttons['add'].setEnabled(False)
                self.buttons['edit'].setEnabled(False)
                self.buttons['delete'].setEnabled(False)
                return

            # 检查是否已登录
            if not self.auth_manager.is_logged_in():
                # 未登录时禁用所有操作按钮但不弹出警告
                self.buttons['add'].setEnabled(False)
                self.buttons['edit'].setEnabled(False)
                self.buttons['delete'].setEnabled(False)
                self.setEnabled(True)  # 保持界面可用，只是按钮禁用
                return

            # 检查各种权限
            has_patient_view = self.auth_manager.has_permission(Permission.PATIENT_VIEW)
            has_patient_add = self.auth_manager.has_permission(Permission.PATIENT_ADD)
            has_patient_edit = self.auth_manager.has_permission(Permission.PATIENT_EDIT)
            has_patient_delete = self.auth_manager.has_permission(Permission.PATIENT_DELETE)

            # 调试日志（仅在需要时启用）
            current_user = self.auth_manager.get_current_user()
            if current_user:
                self.logger.debug(f"患者管理权限检查 - 用户: {current_user['name']}, 角色: {current_user['role']}")
                self.logger.debug(f"权限状态 - 查看: {has_patient_view}, 添加: {has_patient_add}, 编辑: {has_patient_edit}, 删除: {has_patient_delete}")

            # 更新按钮状态
            self.buttons['add'].setEnabled(bool(has_patient_add))
            self.buttons['edit'].setEnabled(bool(has_patient_edit))
            self.buttons['delete'].setEnabled(bool(has_patient_delete))

            # 如果没有查看权限，禁用整个界面（但只在已登录时才弹出警告）
            if not has_patient_view:
                self.setEnabled(False)
                # 只在已登录时弹出权限不足警告
                QMessageBox.warning(self, "权限不足", "您没有患者查看权限，无法使用此功能")
                return
            else:
                self.setEnabled(True)

        except Exception as e:
            self.logger.error(f"更新UI权限状态失败: {e}")
            # 出错时禁用所有操作按钮
            self.buttons['add'].setEnabled(False)
            self.buttons['edit'].setEnabled(False)
            self.buttons['delete'].setEnabled(False)

    def load_patients(self):
        """加载患者列表"""
        if not self.db_manager:
            return

        try:
            patients = self.db_manager.get_patients()
            self.populate_patient_table(patients)

        except Exception as e:
            self.logger.error(f"加载患者列表失败: {e}")
            QMessageBox.warning(self, "错误", f"加载患者列表失败: {e}")

    def populate_patient_table(self, patients: List[Dict[str, Any]]):
        """填充患者表格"""
        try:
            self.patient_table.setRowCount(len(patients))

            for row, patient in enumerate(patients):
                self.patient_table.setItem(row, 0, QTableWidgetItem(str(patient.get('bianhao', ''))))
                self.patient_table.setItem(row, 1, QTableWidgetItem(patient.get('name', '')))
                self.patient_table.setItem(row, 2, QTableWidgetItem(str(patient.get('age', ''))))
                self.patient_table.setItem(row, 3, QTableWidgetItem(patient.get('xingbie', '')))
                self.patient_table.setItem(row, 4, QTableWidgetItem(patient.get('cardid', '')))
                self.patient_table.setItem(row, 5, QTableWidgetItem(patient.get('zhenduan', '')))
                self.patient_table.setItem(row, 6, QTableWidgetItem(patient.get('lrshijian', '')))
                self.patient_table.setItem(row, 7, QTableWidgetItem(patient.get('brhc', '')))

                # 存储完整的患者数据
                self.patient_table.item(row, 0).setData(Qt.UserRole, patient)

        except Exception as e:
            self.logger.error(f"填充患者表格失败: {e}")

    def load_doctors(self):
        """加载医生列表"""
        if not self.db_manager:
            return

        try:
            doctors = self.db_manager.get_doctors()
            self.form_widgets['zhuzhi'].clear()
            for doctor in doctors:
                self.form_widgets['zhuzhi'].addItem(doctor.get('name', ''))

        except Exception as e:
            self.logger.error(f"加载医生列表失败: {e}")

    def update_current_operator(self):
        """更新当前操作员显示"""
        try:
            if hasattr(self, 'auth_manager') and self.auth_manager:
                current_user = self.auth_manager.get_current_user()
                if current_user:
                    self.form_widgets['czy'].setText(current_user.get('name', ''))
                else:
                    self.form_widgets['czy'].setText('')
            else:
                self.form_widgets['czy'].setText('')
        except Exception as e:
            self.logger.error(f"更新当前操作员失败: {e}")
            self.form_widgets['czy'].setText('')

    def load_hospital_info(self):
        """加载医院信息"""
        if not self.db_manager:
            return

        try:
            hospital_info = self.db_manager.get_hospital_info()
            self.form_widgets['keshi'].setText(hospital_info.get('keshi', ''))
            self.form_widgets['shebeiid'].setText(hospital_info.get('shebeiid', ''))

        except Exception as e:
            self.logger.error(f"加载医院信息失败: {e}")

    def search_patients(self):
        """搜索患者"""
        if not self.db_manager:
            return

        try:
            search_term = self.search_edit.text().strip()
            if search_term:
                patients = self.db_manager.get_patients(search_term)
            else:
                patients = self.db_manager.get_patients()

            self.populate_patient_table(patients)

        except Exception as e:
            self.logger.error(f"搜索患者失败: {e}")
            QMessageBox.warning(self, "错误", f"搜索患者失败: {e}")

    def clear_search(self):
        """清空搜索"""
        self.search_edit.clear()
        self.load_patients()

    def on_patient_selected(self):
        """患者选择处理"""
        try:
            current_row = self.patient_table.currentRow()
            if current_row >= 0:
                item = self.patient_table.item(current_row, 0)
                if item:
                    patient_data = item.data(Qt.UserRole)
                    if patient_data:
                        self.current_patient = patient_data
                        self.populate_patient_form(patient_data)
                        self.load_treatment_records(patient_data.get('bianhao'))
                        self.patient_selected.emit(patient_data)

        except Exception as e:
            self.logger.error(f"患者选择处理失败: {e}")

    def on_patient_double_clicked(self, item):
        """患者表格双击事件处理"""
        try:
            if item:
                row = item.row()
                patient_item = self.patient_table.item(row, 0)
                if patient_item:
                    patient_data = patient_item.data(Qt.UserRole)
                    if patient_data:
                        # 发射切换到治疗页面的信号，传递患者信息
                        self.switch_to_treatment.emit(patient_data)
                        self.logger.info(f"双击患者 {patient_data.get('name', '')}，切换到治疗页面")

        except Exception as e:
            self.logger.error(f"患者双击处理失败: {e}")

    def populate_patient_form(self, patient_data: Dict[str, Any]):
        """填充患者表单"""
        try:
            self.form_widgets['bianhao'].setText(str(patient_data.get('bianhao', '')))
            self.form_widgets['name'].setText(patient_data.get('name', ''))
            self.form_widgets['age'].setText(str(patient_data.get('age', '')))

            # 性别
            xingbie = patient_data.get('xingbie', '')
            index = self.form_widgets['xingbie'].findText(xingbie)
            if index >= 0:
                self.form_widgets['xingbie'].setCurrentIndex(index)

            self.form_widgets['cardid'].setText(patient_data.get('cardid', ''))
            self.form_widgets['zhenduan'].setPlainText(patient_data.get('zhenduan', ''))
            self.form_widgets['bingshi'].setPlainText(patient_data.get('bingshi', ''))

            # 患侧
            brhc = patient_data.get('brhc', '')
            index = self.form_widgets['brhc'].findText(brhc)
            if index >= 0:
                self.form_widgets['brhc'].setCurrentIndex(index)

            # 主治医师
            zhuzhi = patient_data.get('zhuzhi', '')
            index = self.form_widgets['zhuzhi'].findText(zhuzhi)
            if index >= 0:
                self.form_widgets['zhuzhi'].setCurrentIndex(index)

            # 操作员（只读显示）
            czy = patient_data.get('czy', '')
            self.form_widgets['czy'].setText(czy)

        except Exception as e:
            self.logger.error(f"填充患者表单失败: {e}")

    def get_updated_patient_data(self, patient_bianhao: int) -> Optional[Dict[str, Any]]:
        """从数据库获取更新后的患者数据"""
        try:
            if not self.db_manager:
                return None

            patients = self.db_manager.execute_query(
                "SELECT * FROM bingren WHERE bianhao = ?",
                (patient_bianhao,)
            )

            if patients and len(patients) > 0:
                return patients[0]
            return None

        except Exception as e:
            self.logger.error(f"获取更新后的患者数据失败: {e}")
            return None

    def select_patient_in_table(self, patient_bianhao: int):
        """在患者表格中选中指定编号的患者"""
        try:
            for row in range(self.patient_table.rowCount()):
                item = self.patient_table.item(row, 0)  # 编号列
                if item and item.text() == str(patient_bianhao):
                    self.patient_table.selectRow(row)
                    self.patient_table.setCurrentItem(item)
                    break

        except Exception as e:
            self.logger.error(f"选中患者表格行失败: {e}")

    def load_treatment_records(self, patient_id: int):
        """加载治疗记录"""
        if not self.db_manager:
            return

        try:
            records = self.db_manager.get_treatment_records(patient_id)
            self.populate_treatment_table(records)

        except Exception as e:
            self.logger.error(f"加载治疗记录失败: {e}")

    def populate_treatment_table(self, records: List[Dict[str, Any]]):
        """填充治疗记录表格"""
        try:
            from PySide6.QtCore import Qt

            # 按treat_number倒序排列记录
            sorted_records = sorted(records, key=lambda x: x.get('treat_number', 0), reverse=True)

            self.treatment_table.setRowCount(len(sorted_records))

            for row, record in enumerate(sorted_records):
                # 治疗编号：使用treat_number字段
                treat_number = record.get('treat_number', record.get('zhiliaobh', ''))
                item = QTableWidgetItem(str(treat_number))
                item.setTextAlignment(Qt.AlignCenter)
                self.treatment_table.setItem(row, 0, item)

                # 日期
                item = QTableWidgetItem(record.get('rq', ''))
                item.setTextAlignment(Qt.AlignCenter)
                self.treatment_table.setItem(row, 1, item)

                # 时间
                item = QTableWidgetItem(record.get('shijian', ''))
                item.setTextAlignment(Qt.AlignCenter)
                self.treatment_table.setItem(row, 2, item)

                # 治疗效果：使用zlms字段（优/良/中/差）
                treatment_effect = record.get('zlms', '')
                item = QTableWidgetItem(treatment_effect)
                item.setTextAlignment(Qt.AlignCenter)
                self.treatment_table.setItem(row, 3, item)

                # 要求次数
                item = QTableWidgetItem(str(record.get('yaoqiucs', '')))
                item.setTextAlignment(Qt.AlignCenter)
                self.treatment_table.setItem(row, 4, item)

                # 实际次数
                item = QTableWidgetItem(str(record.get('shijics', '')))
                item.setTextAlignment(Qt.AlignCenter)
                self.treatment_table.setItem(row, 5, item)

                # 治疗时长：显示为整数格式
                duration = record.get('zlsj', 0)
                if isinstance(duration, (int, float)):
                    duration_str = str(int(duration))  # 转换为整数格式
                else:
                    duration_str = str(duration)
                item = QTableWidgetItem(duration_str)
                item.setTextAlignment(Qt.AlignCenter)
                self.treatment_table.setItem(row, 6, item)

                # 操作员
                item = QTableWidgetItem(record.get('czy', ''))
                item.setTextAlignment(Qt.AlignCenter)
                self.treatment_table.setItem(row, 7, item)

        except Exception as e:
            self.logger.error(f"填充治疗记录表格失败: {e}")

    def add_patient(self):
        """添加患者"""
        # 权限检查
        if not self.auth_manager or not self.auth_manager.has_permission(Permission.PATIENT_ADD):
            QMessageBox.warning(self, "权限不足", "您没有添加患者的权限！")
            return

        self.clear_form()
        self.update_current_operator()  # 设置当前登录用户为操作员
        self.set_form_editable(True)
        self.buttons['save'].setEnabled(True)
        self.buttons['cancel'].setEnabled(True)
        self.buttons['add'].setEnabled(False)
        self.buttons['edit'].setEnabled(False)
        self.buttons['delete'].setEnabled(False)

    def edit_patient(self):
        """编辑患者"""
        # 权限检查
        if not self.auth_manager or not self.auth_manager.has_permission(Permission.PATIENT_EDIT):
            QMessageBox.warning(self, "权限不足", "您没有编辑患者的权限！")
            return

        if not self.current_patient:
            QMessageBox.warning(self, "警告", "请先选择要编辑的患者！")
            return

        self.set_form_editable(True)
        self.buttons['save'].setEnabled(True)
        self.buttons['cancel'].setEnabled(True)
        self.buttons['add'].setEnabled(False)
        self.buttons['edit'].setEnabled(False)
        self.buttons['delete'].setEnabled(False)

    def save_patient(self):
        """保存患者"""
        if not self.db_manager:
            return

        try:
            # 权限检查
            if self.current_patient:
                # 编辑操作需要编辑权限
                if not self.auth_manager or not self.auth_manager.has_permission(Permission.PATIENT_EDIT):
                    QMessageBox.warning(self, "权限不足", "您没有编辑患者的权限！")
                    return
            else:
                # 添加操作需要添加权限
                if not self.auth_manager or not self.auth_manager.has_permission(Permission.PATIENT_ADD):
                    QMessageBox.warning(self, "权限不足", "您没有添加患者的权限！")
                    return

            # 验证表单
            if not self.validate_form():
                return

            # 收集表单数据
            patient_data = self.collect_form_data()

            # 保存到数据库
            if self.current_patient:
                # 更新现有患者 - 需要上传到平台
                upload_status = "pending"

                # 尝试同步更新到平台
                try:
                    # 获取医院信息
                    hospital_info = self.db_manager.get_hospital_info()
                    if hospital_info:
                        self.logger.info(f"开始更新患者数据到平台: {patient_data.get('name', '')}")

                        # 执行同步更新
                        upload_result = self.data_uploader.update_patient_data(patient_data, hospital_info)

                        if upload_result.success:
                            upload_status = "success"
                            self.logger.info(f"患者数据更新成功: {patient_data.get('name', '')}")
                        else:
                            upload_status = "failed"
                            # 根据错误类型记录不同的日志
                            if "重复或已存在" in upload_result.message:
                                self.logger.info(f"患者数据已存在于平台: {patient_data.get('name', '')} - {upload_result.message}")
                            else:
                                self.logger.warning(f"患者数据更新失败: {upload_result.message}")
                    else:
                        upload_status = "failed"
                        self.logger.warning("无法获取医院信息，跳过数据更新")

                except Exception as e:
                    upload_status = "failed"
                    self.logger.error(f"更新患者数据时发生异常: {e}")

                # 设置上传状态
                patient_data['status'] = upload_status

                # 更新本地数据库
                success = self.db_manager.update_patient(
                    self.current_patient['bianhao'],
                    patient_data
                )
                operation = "更新患者信息"
            else:
                # 添加新患者

                # 设置默认状态为待上传
                upload_status = "pending"

                # 如果是添加新患者，尝试同步上传到平台
                try:
                    # 获取医院信息
                    hospital_info = self.db_manager.get_hospital_info()
                    if hospital_info:
                        self.logger.info(f"开始上传患者数据到平台: {patient_data.get('name', '')}")

                        # 执行同步上传
                        upload_result = self.data_uploader.upload_patient_data(patient_data, hospital_info)

                        if upload_result.success:
                            upload_status = "success"
                            self.logger.info(f"患者数据上传成功: {patient_data.get('name', '')}")
                        else:
                            upload_status = "failed"
                            # 根据错误类型记录不同的日志
                            if "重复或已存在" in upload_result.message:
                                self.logger.info(f"患者数据已存在于平台: {patient_data.get('name', '')} - {upload_result.message}")
                            else:
                                self.logger.warning(f"患者数据上传失败: {upload_result.message}")
                    else:
                        upload_status = "failed"
                        self.logger.warning("无法获取医院信息，跳过数据上传")

                except Exception as e:
                    upload_status = "failed"
                    self.logger.error(f"上传患者数据时发生异常: {e}")

                # 设置上传状态
                patient_data['status'] = upload_status

                # 保存到本地数据库
                success = self.db_manager.add_patient(patient_data)
                operation = "添加患者"

            if success:
                # 简化成功消息，只提示保存成功
                QMessageBox.information(self, "成功", f"{operation}成功！")

                # 重新加载患者列表
                self.load_patients()

                # 如果是编辑操作，立即刷新当前患者信息显示
                if self.current_patient:
                    # 从数据库重新获取更新后的患者信息
                    updated_patient = self.get_updated_patient_data(patient_data['bianhao'])
                    if updated_patient:
                        self.current_patient = updated_patient
                        self.populate_patient_form(updated_patient)
                        # 重新选中患者表格中的对应行
                        self.select_patient_in_table(patient_data['bianhao'])

                self.cancel_edit()

                # 记录操作日志
                self.logger_system.log_operation(
                    "current_user",  # 这里应该是当前用户
                    operation,
                    f"患者: {patient_data.get('name', '')}"
                )
            else:
                self.logger.error(f"{operation}失败")
                QMessageBox.warning(self, "失败", f"{operation}失败！请检查数据完整性。")

        except Exception as e:
            self.logger.error(f"保存患者失败: {e}")

            # 提供更友好的错误信息
            error_message = str(e)
            if "FOREIGN KEY constraint failed" in error_message:
                QMessageBox.warning(
                    self,
                    "数据完整性错误",
                    "保存失败：医院信息不匹配。\n\n"
                    "可能的原因：\n"
                    "1. 医院编号已被修改，请联系管理员\n"
                    "2. 数据库配置异常\n\n"
                    "建议：请刷新页面或重新登录系统。"
                )
            else:
                QMessageBox.warning(self, "错误", f"保存患者失败: {error_message}")

    def cancel_edit(self):
        """取消编辑"""
        self.set_form_editable(False)
        self.buttons['save'].setEnabled(False)
        self.buttons['cancel'].setEnabled(False)
        self.buttons['add'].setEnabled(True)
        self.buttons['edit'].setEnabled(True)
        self.buttons['delete'].setEnabled(True)

        if self.current_patient:
            self.populate_patient_form(self.current_patient)
        else:
            self.clear_form()

    def delete_patient(self):
        """删除患者"""
        # 权限检查
        if not self.auth_manager or not self.auth_manager.has_permission(Permission.PATIENT_DELETE):
            QMessageBox.warning(self, "权限不足", "您没有删除患者的权限！")
            return

        if not self.current_patient:
            QMessageBox.warning(self, "警告", "请先选择要删除的患者！")
            return

        reply = QMessageBox.question(
            self,
            "确认删除",
            f"确定要删除患者 {self.current_patient.get('name', '')} 吗？\n此操作不可恢复！",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 这里应该实现删除逻辑
            QMessageBox.information(self, "提示", "删除功能待实现")

    def validate_form(self) -> bool:
        """验证表单"""
        # 检查必填字段
        if not self.form_widgets['name'].text().strip():
            QMessageBox.warning(self, "验证失败", "患者姓名不能为空！")
            return False

        # 验证年龄
        age_text = self.form_widgets['age'].text().strip()
        if not age_text:
            QMessageBox.warning(self, "验证失败", "请输入年龄！")
            return False

        try:
            age = int(age_text)
            if age <= 0 or age > 150:
                QMessageBox.warning(self, "验证失败", "请输入有效的年龄（1-150）！")
                return False
        except ValueError:
            QMessageBox.warning(self, "验证失败", "年龄必须是数字！")
            return False

        # 验证编号
        bianhao_text = self.form_widgets['bianhao'].text().strip()
        if not bianhao_text:
            QMessageBox.warning(self, "验证失败", "请输入患者编号！")
            return False

        # 检查是否为纯数字
        if not bianhao_text.isdigit():
            QMessageBox.warning(self, "验证失败", "患者编号必须是数字！")
            return False

        # 检查长度
        if len(bianhao_text) > 15:
            QMessageBox.warning(self, "验证失败", "患者编号长度不能超过15位！")
            return False

        bianhao = int(bianhao_text)

        # 验证身份证号（如果填写了的话）
        cardid_text = self.form_widgets['cardid'].text().strip()
        if cardid_text:  # 身份证号不是必填项，但如果填写了就需要验证
            # 检查是否为纯数字
            if not cardid_text.isdigit():
                QMessageBox.warning(self, "验证失败", "身份证号必须为数字形式！")
                return False

            # 检查长度是否合理（一般身份证号为15位或18位，但允许不完整）
            if len(cardid_text) > 18:
                QMessageBox.warning(self, "验证失败", "身份证号长度不能超过18位！")
                return False

        # 检查编号重复（仅在添加时）
        if not self.current_patient:
            if self.db_manager:
                existing = self.db_manager.execute_query(
                    "SELECT COUNT(*) as count FROM bingren WHERE bianhao = ?",
                    (bianhao,)
                )
                if existing and existing[0]['count'] > 0:
                    QMessageBox.warning(self, "验证失败", "患者编号已存在，请使用其他编号！")
                    return False

        return True

    def get_current_hospital_id(self) -> int:
        """获取当前医院ID"""
        if not self.db_manager:
            self.logger.warning("数据库管理器未初始化，使用默认医院ID")
            return 1

        try:
            hospital_info = self.db_manager.get_hospital_info()
            if hospital_info and 'id' in hospital_info:
                hospital_id = hospital_info['id']
                self.logger.debug(f"获取到当前医院ID: {hospital_id}")
                return hospital_id
            else:
                self.logger.warning("未找到医院信息，使用默认医院ID")
                return 1
        except Exception as e:
            self.logger.error(f"获取医院ID失败: {e}，使用默认医院ID")
            return 1

    def collect_form_data(self) -> Dict[str, Any]:
        """收集表单数据"""
        # 动态获取当前医院ID
        yiyuanid = self.get_current_hospital_id()

        # 处理编号和年龄的转换
        try:
            bianhao = int(self.form_widgets['bianhao'].text().strip()) if self.form_widgets['bianhao'].text().strip() else 0
        except ValueError:
            bianhao = 0

        try:
            age = int(self.form_widgets['age'].text().strip()) if self.form_widgets['age'].text().strip() else 0
        except ValueError:
            age = 0

        return {
            'bianhao': bianhao,
            'name': self.form_widgets['name'].text().strip(),
            'age': age,
            'xingbie': self.form_widgets['xingbie'].currentText(),
            'cardid': self.form_widgets['cardid'].text().strip(),
            'zhenduan': self.form_widgets['zhenduan'].toPlainText().strip(),
            'bingshi': self.form_widgets['bingshi'].toPlainText().strip(),
            'brhc': self.form_widgets['brhc'].currentText(),
            'zhuzhi': self.form_widgets['zhuzhi'].currentText(),
            'czy': self.form_widgets['czy'].text(),
            'keshi': self.form_widgets['keshi'].text(),
            'shebeiid': self.form_widgets['shebeiid'].text(),
            'yiyuanid': yiyuanid,
        }

    def clear_form(self):
        """清空表单"""
        self.form_widgets['bianhao'].clear()  # 清空编号框
        self.form_widgets['name'].clear()
        self.form_widgets['age'].clear()  # 清空年龄框
        self.form_widgets['xingbie'].setCurrentIndex(0)
        self.form_widgets['cardid'].clear()
        self.form_widgets['zhenduan'].clear()
        self.form_widgets['bingshi'].clear()
        self.form_widgets['brhc'].setCurrentIndex(0)
        self.form_widgets['zhuzhi'].setCurrentIndex(0)
        self.form_widgets['czy'].clear()  # 操作员字段现在是QLineEdit

        self.current_patient = None

    def set_form_editable(self, editable: bool):
        """设置表单可编辑状态"""
        for widget_name, widget in self.form_widgets.items():
            if widget_name in ['keshi', 'shebeiid', 'czy']:  # 这些字段始终只读
                # 确保只读字段始终保持灰色样式
                widget.setEnabled(False)
                widget.setStyleSheet("background-color: #f0f0f0; color: #666666;")
            elif widget_name == 'bianhao':
                # 患者编号：添加时可编辑，编辑时不可编辑
                if self.current_patient:  # 编辑模式
                    widget.setEnabled(False)
                    widget.setStyleSheet("background-color: #f0f0f0; color: #666666;")
                else:  # 添加模式
                    widget.setEnabled(editable)
                    if editable:
                        widget.setStyleSheet("")  # 恢复正常样式
                    else:
                        widget.setStyleSheet("background-color: #f0f0f0; color: #666666;")
            else:
                # 其他字段正常处理
                widget.setEnabled(editable)
                if editable:
                    widget.setStyleSheet("")  # 恢复正常样式
                else:
                    widget.setStyleSheet("background-color: #f0f0f0; color: #666666;")

    def add_doctor_dialog(self):
        """显示添加医生对话框"""
        if not self.db_manager:
            QMessageBox.warning(self, "错误", "数据库连接未初始化")
            return

        try:
            dialog = DoctorDialog(self, mode='add')
            if dialog.exec() == QDialog.Accepted:
                doctor_data = dialog.get_doctor_data()

                # 检查是否存在已删除的同名医生
                existing_deleted = self.db_manager.execute_query(
                    "SELECT * FROM doctor WHERE name = ? AND is_active = 0",
                    (doctor_data['name'],)
                )

                # 添加到数据库
                success = self.db_manager.add_doctor(doctor_data)
                if success:
                    if existing_deleted:
                        # 重新激活了已删除的医生
                        QMessageBox.information(
                            self,
                            "成功",
                            f"医生 '{doctor_data['name']}' 重新激活成功！\n\n"
                            f"检测到该医生之前已被删除，现已重新激活并更新信息。"
                        )
                        operation = "重新激活医生"
                    else:
                        # 添加新医生
                        QMessageBox.information(self, "成功", f"医生 '{doctor_data['name']}' 添加成功！")
                        operation = "添加医生"

                    # 记录操作日志
                    self.logger_system.log_operation(
                        "current_user",  # 这里应该是当前用户
                        operation,
                        f"医生: {doctor_data['name']}, 职称: {doctor_data.get('title', '')}, "
                        f"科室: {doctor_data.get('department', '')}, 电话: {doctor_data.get('phone', '')}"
                    )

                    # 重新加载医生列表
                    self.load_doctors()
                    # 自动选择新添加的医生
                    doctor_name = doctor_data['name']
                    index = self.form_widgets['zhuzhi'].findText(doctor_name)
                    if index >= 0:
                        self.form_widgets['zhuzhi'].setCurrentIndex(index)
                else:
                    QMessageBox.warning(self, "失败", "添加医生失败，该姓名已被其他活跃医生使用")

                    # 记录失败日志
                    self.logger_system.log_operation(
                        "current_user",
                        "添加医生失败",
                        f"尝试添加医生: {doctor_data['name']}, 原因: 姓名已被活跃医生使用"
                    )

        except Exception as e:
            self.logger.error(f"添加医生失败: {e}")
            QMessageBox.critical(self, "错误", f"添加医生失败: {e}")

    def edit_doctor_dialog(self):
        """显示编辑医生对话框"""
        if not self.db_manager:
            QMessageBox.warning(self, "错误", "数据库连接未初始化")
            return

        # 获取当前选中的医生
        current_doctor_name = self.form_widgets['zhuzhi'].currentText()
        if not current_doctor_name:
            QMessageBox.warning(self, "提示", "请先选择要编辑的医生")
            return

        try:
            # 获取医生详细信息
            doctors = self.db_manager.get_doctors()
            current_doctor = None
            for doctor in doctors:
                if doctor['name'] == current_doctor_name:
                    current_doctor = doctor
                    break

            if not current_doctor:
                QMessageBox.warning(self, "错误", "未找到选中的医生信息")
                return

            dialog = DoctorDialog(self, mode='edit', doctor_data=current_doctor)
            if dialog.exec() == QDialog.Accepted:
                doctor_data = dialog.get_doctor_data()

                # 更新数据库
                success = self.db_manager.update_doctor(current_doctor['id'], doctor_data)
                if success:
                    QMessageBox.information(self, "成功", f"医生 '{doctor_data['name']}' 信息更新成功！")

                    # 记录操作日志
                    self.logger_system.log_operation(
                        "current_user",
                        "编辑医生",
                        f"医生: {doctor_data['name']}, 职称: {doctor_data.get('title', '')}, "
                        f"科室: {doctor_data.get('department', '')}, 电话: {doctor_data.get('phone', '')}, "
                        f"原姓名: {current_doctor.get('name', '')}"
                    )

                    # 重新加载医生列表
                    self.load_doctors()
                    # 尝试选择更新后的医生
                    doctor_name = doctor_data['name']
                    index = self.form_widgets['zhuzhi'].findText(doctor_name)
                    if index >= 0:
                        self.form_widgets['zhuzhi'].setCurrentIndex(index)
                else:
                    QMessageBox.warning(self, "失败", "更新医生信息失败，可能是姓名重复")

                    # 记录失败日志
                    self.logger_system.log_operation(
                        "current_user",
                        "编辑医生失败",
                        f"尝试编辑医生: {current_doctor.get('name', '')} -> {doctor_data['name']}, "
                        f"原因: 可能是姓名重复"
                    )

        except Exception as e:
            self.logger.error(f"编辑医生失败: {e}")
            QMessageBox.critical(self, "错误", f"编辑医生失败: {e}")

    def delete_doctor_dialog(self):
        """显示删除医生确认对话框"""
        if not self.db_manager:
            QMessageBox.warning(self, "错误", "数据库连接未初始化")
            return

        # 获取当前选中的医生
        current_doctor_name = self.form_widgets['zhuzhi'].currentText()
        if not current_doctor_name:
            QMessageBox.warning(self, "提示", "请先选择要删除的医生")
            return

        try:
            # 确认删除
            reply = QMessageBox.question(
                self,
                "确认删除",
                f"确定要删除医生 '{current_doctor_name}' 吗？\n\n注意：删除后该医生将不再出现在选择列表中。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 获取医生ID
                doctors = self.db_manager.get_doctors()
                doctor_id = None
                for doctor in doctors:
                    if doctor['name'] == current_doctor_name:
                        doctor_id = doctor['id']
                        break

                if doctor_id:
                    # 获取医生详细信息用于日志
                    doctor_info = None
                    for doctor in doctors:
                        if doctor['id'] == doctor_id:
                            doctor_info = doctor
                            break

                    # 执行软删除
                    success = self.db_manager.delete_doctor(doctor_id)
                    if success:
                        QMessageBox.information(self, "成功", f"医生 '{current_doctor_name}' 删除成功！")

                        # 记录操作日志
                        self.logger_system.log_operation(
                            "current_user",
                            "删除医生",
                            f"医生: {current_doctor_name}, "
                            f"职称: {doctor_info.get('title', '') if doctor_info else ''}, "
                            f"科室: {doctor_info.get('department', '') if doctor_info else ''}, "
                            f"ID: {doctor_id}"
                        )

                        # 重新加载医生列表
                        self.load_doctors()
                        # 清空选择
                        self.form_widgets['zhuzhi'].setCurrentIndex(-1)
                    else:
                        QMessageBox.warning(self, "失败", "删除医生失败")

                        # 记录失败日志
                        self.logger_system.log_operation(
                            "current_user",
                            "删除医生失败",
                            f"尝试删除医生: {current_doctor_name}, ID: {doctor_id}, 原因: 数据库操作失败"
                        )
                else:
                    QMessageBox.warning(self, "错误", "未找到要删除的医生")

                    # 记录错误日志
                    self.logger_system.log_operation(
                        "current_user",
                        "删除医生失败",
                        f"尝试删除医生: {current_doctor_name}, 原因: 未找到医生ID"
                    )

        except Exception as e:
            self.logger.error(f"删除医生失败: {e}")
            QMessageBox.critical(self, "错误", f"删除医生失败: {e}")





    def cleanup(self):
        """清理资源"""
        try:
            self.logger.info("患者管理界面资源清理完成")
        except Exception as e:
            self.logger.error(f"患者管理界面资源清理失败: {e}")


class DoctorDialog(QDialog):
    """医生管理对话框（支持添加和编辑）"""

    def __init__(self, parent=None, mode='add', doctor_data=None):
        super().__init__(parent)
        self.mode = mode  # 'add' 或 'edit'
        self.doctor_data = doctor_data or {}

        if mode == 'add':
            self.setWindowTitle("添加医生")
        else:
            self.setWindowTitle("编辑医生")

        self.setModal(True)
        self.setFixedSize(400, 300)

        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)

        # 表单
        form_layout = QFormLayout()

        # 姓名（必填）
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("请输入医生姓名（必填）")
        form_layout.addRow("姓名*:", self.name_edit)

        # 职称
        self.title_edit = QLineEdit()
        self.title_edit.setPlaceholderText("如：主任医师、副主任医师等")
        form_layout.addRow("职称:", self.title_edit)

        # 科室
        self.department_edit = QLineEdit()
        self.department_edit.setText("康复科")  # 默认值
        form_layout.addRow("科室:", self.department_edit)

        # 电话
        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText("请输入联系电话")
        form_layout.addRow("电话:", self.phone_edit)

        # 邮箱
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("请输入邮箱地址")
        form_layout.addRow("邮箱:", self.email_edit)

        layout.addLayout(form_layout)

        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        button_box.accepted.connect(self.accept_dialog)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        # 如果是编辑模式，填充现有数据
        if self.mode == 'edit' and self.doctor_data:
            self.populate_form()

        # 设置焦点
        self.name_edit.setFocus()

    def populate_form(self):
        """填充表单数据（编辑模式）"""
        try:
            self.name_edit.setText(self.doctor_data.get('name', ''))
            self.title_edit.setText(self.doctor_data.get('title', ''))
            self.department_edit.setText(self.doctor_data.get('department', '康复科'))
            self.phone_edit.setText(self.doctor_data.get('phone', ''))
            self.email_edit.setText(self.doctor_data.get('email', ''))
        except Exception as e:
            print(f"填充表单数据失败: {e}")

    def accept_dialog(self):
        """确认对话框"""
        # 验证必填字段
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "验证失败", "医生姓名不能为空！")
            self.name_edit.setFocus()
            return

        # 在编辑模式下，如果姓名没有改变，则允许保存
        if self.mode == 'edit':
            original_name = self.doctor_data.get('name', '')
            current_name = self.name_edit.text().strip()
            if original_name == current_name:
                # 姓名未改变，直接保存
                self.accept()
                return

        self.accept()

    def get_doctor_data(self) -> Dict[str, str]:
        """获取医生数据"""
        return {
            'name': self.name_edit.text().strip(),
            'title': self.title_edit.text().strip(),
            'department': self.department_edit.text().strip(),
            'phone': self.phone_edit.text().strip(),
            'email': self.email_edit.text().strip()
        }

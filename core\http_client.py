#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTTP客户端模块
HTTP Client Module

用于患者数据上传到平台的网络通信功能

作者: AI Assistant
版本: 1.0.0
"""

import json
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum

try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False
    logging.warning("requests库未安装，网络上传功能将不可用")

from utils.app_config import AppConfig


class UploadStatus(Enum):
    """上传状态枚举"""
    PENDING = "pending"      # 待上传
    UPLOADING = "uploading"  # 上传中
    SUCCESS = "success"      # 上传成功
    FAILED = "failed"        # 上传失败
    TIMEOUT = "timeout"      # 上传超时


@dataclass
class UploadResult:
    """上传结果"""
    success: bool
    status: UploadStatus
    message: str
    response_data: Optional[Dict[str, Any]] = None
    error_code: Optional[str] = None


class PatientDataUploader:
    """患者数据上传器"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.config = AppConfig.NETWORK_CONFIG['http']
        self.base_url = self.config['base_url']
        self.timeout = self.config['timeout']

        # 检查requests库可用性
        if not REQUESTS_AVAILABLE:
            self.logger.error("requests库未安装，无法使用网络上传功能")

    def upload_patient_data(self, patient_data: Dict[str, Any], hospital_info: Dict[str, Any]) -> UploadResult:
        """
        上传患者数据到平台（新增患者）

        Args:
            patient_data: 患者数据
            hospital_info: 医院信息

        Returns:
            UploadResult: 上传结果
        """
        if not REQUESTS_AVAILABLE:
            return UploadResult(
                success=False,
                status=UploadStatus.FAILED,
                message="requests库未安装，无法进行网络上传",
                error_code="LIBRARY_MISSING"
            )

        try:
            # 组织JSON数据，按照QT程序的格式
            json_data = self._prepare_add_json_data(patient_data, hospital_info)

            # 构建上传URL
            upload_url = f"{self.base_url}AppPatientServlet?act=sendPatients&data={json_data}"

            self.logger.info(f"开始上传患者数据: {patient_data.get('name', '')}")
            self.logger.debug(f"上传URL: {upload_url}")

            # 执行上传（不重试，直接上传）
            return self._upload_once(upload_url, "patient")

        except Exception as e:
            self.logger.error(f"上传患者数据时发生错误: {e}")
            return UploadResult(
                success=False,
                status=UploadStatus.FAILED,
                message=f"上传失败: {str(e)}",
                error_code="UPLOAD_ERROR"
            )

    def update_patient_data(self, patient_data: Dict[str, Any], hospital_info: Dict[str, Any]) -> UploadResult:
        """
        更新患者数据到平台（修改患者）

        Args:
            patient_data: 患者数据
            hospital_info: 医院信息

        Returns:
            UploadResult: 上传结果
        """
        if not REQUESTS_AVAILABLE:
            return UploadResult(
                success=False,
                status=UploadStatus.FAILED,
                message="requests库未安装，无法进行网络上传",
                error_code="LIBRARY_MISSING"
            )

        try:
            # 组织JSON数据，按照QT程序的格式（更新用的格式）
            json_data = self._prepare_update_json_data(patient_data, hospital_info)

            # 构建更新URL
            upload_url = f"{self.base_url}AppPatientServlet?act=updatePatients&data={json_data}"

            self.logger.info(f"开始更新患者数据: {patient_data.get('name', '')}")
            self.logger.debug(f"更新URL: {upload_url}")

            # 执行上传（不重试，直接上传）
            return self._upload_once(upload_url, "patient")

        except Exception as e:
            self.logger.error(f"更新患者数据时发生错误: {e}")
            return UploadResult(
                success=False,
                status=UploadStatus.FAILED,
                message=f"更新失败: {str(e)}",
                error_code="UPDATE_ERROR"
            )

    def update_equipment_status(self, hospital_info: Dict[str, Any], status: str) -> UploadResult:
        """
        上传设备状态到平台

        Args:
            hospital_info: 医院信息
            status: 设备状态 ("1"表示开机, "0"表示关机)

        Returns:
            UploadResult: 上传结果
        """
        if not REQUESTS_AVAILABLE:
            return UploadResult(
                success=False,
                status=UploadStatus.FAILED,
                message="requests库未安装，无法进行网络上传",
                error_code="LIBRARY_MISSING"
            )

        try:
            # 组织JSON数据，按照QT程序的格式
            json_data = self._prepare_equipment_status_json(hospital_info, status)

            # 构建上传URL
            upload_url = f"{self.base_url}AppPatientServlet?act=updateEquipment&data={json_data}"

            status_text = "开机" if status == "1" else "关机"
            self.logger.info(f"开始上传设备{status_text}状态")
            self.logger.debug(f"设备状态上传URL: {upload_url}")

            # 执行上传（不重试，直接上传）
            return self._upload_once(upload_url, "equipment")

        except Exception as e:
            self.logger.error(f"上传设备状态时发生错误: {e}")
            return UploadResult(
                success=False,
                status=UploadStatus.FAILED,
                message=f"设备状态上传失败: {str(e)}",
                error_code="EQUIPMENT_STATUS_ERROR"
            )

    def _prepare_add_json_data(self, patient_data: Dict[str, Any], hospital_info: Dict[str, Any]) -> str:
        """
        准备新增患者的JSON数据，按照QT程序的格式

        Args:
            patient_data: 患者数据
            hospital_info: 医院信息

        Returns:
            str: JSON字符串
        """
        # 按照QT程序中的JSON字段名称组织数据
        json_obj = {
            "num": str(patient_data.get('bianhao', '')),
            "name": patient_data.get('name', ''),
            "sex": patient_data.get('xingbie', ''),
            "age": int(patient_data.get('age', 0)),
            "hospitalID": hospital_info.get('id', 1),
            "idCard": patient_data.get('cardid', ''),
            "department": patient_data.get('keshi', ''),
            "equipmentNum": patient_data.get('shebeiid', ''),
            "attdoctor": patient_data.get('zhuzhi', ''),
            "operator": patient_data.get('czy', ''),
            "jiwangshi": patient_data.get('bingshi', ''),
            "zhenduan": patient_data.get('zhenduan', '')
        }

        # 转换为紧凑的JSON字符串
        json_str = json.dumps(json_obj, ensure_ascii=False, separators=(',', ':'))
        self.logger.debug(f"准备的新增JSON数据: {json_str}")

        return json_str

    def _prepare_update_json_data(self, patient_data: Dict[str, Any], hospital_info: Dict[str, Any]) -> str:
        """
        准备更新患者的JSON数据，按照QT程序的格式和顺序

        Args:
            patient_data: 患者数据
            hospital_info: 医院信息

        Returns:
            str: JSON字符串
        """
        # 按照QT程序中updatePatients的JSON字段名称和顺序组织数据
        # 参考原QT代码的顺序：num, name, sex, age, hospitalID, idCard, jiwangshi, zhenduan
        json_obj = {
            "num": str(patient_data.get('bianhao', '')),
            "name": patient_data.get('name', ''),
            "sex": patient_data.get('xingbie', ''),
            "age": int(patient_data.get('age', 0)),
            "hospitalID": hospital_info.get('id', 1),
            "idCard": patient_data.get('cardid', ''),
            "jiwangshi": patient_data.get('bingshi', ''),
            "zhenduan": patient_data.get('zhenduan', '')
        }

        # 转换为紧凑的JSON字符串
        json_str = json.dumps(json_obj, ensure_ascii=False, separators=(',', ':'))
        self.logger.debug(f"准备的更新JSON数据: {json_str}")

        return json_str

    def _prepare_equipment_status_json(self, hospital_info: Dict[str, Any], status: str) -> str:
        """
        准备设备状态的JSON数据，按照QT程序的格式

        Args:
            hospital_info: 医院信息
            status: 设备状态 ("1"表示开机, "0"表示关机)

        Returns:
            str: JSON字符串
        """
        # 按照QT程序中updateEquipment的JSON字段名称和顺序组织数据
        # 参考原QT代码：hospitalID, equipmentNum, status
        json_obj = {
            "hospitalID": hospital_info.get('id', 1),
            "equipmentNum": hospital_info.get('shebeiid', 'NK001'),
            "status": status
        }

        # 转换为紧凑的JSON字符串
        json_str = json.dumps(json_obj, ensure_ascii=False, separators=(',', ':'))
        status_text = "开机" if status == "1" else "关机"
        self.logger.debug(f"准备的设备{status_text}状态JSON数据: {json_str}")

        return json_str

    def _upload_once(self, upload_url: str, upload_type: str = "patient") -> UploadResult:
        """
        单次上传（不重试）

        Args:
            upload_url: 上传URL

        Returns:
            UploadResult: 上传结果
        """
        try:
            # 发送HTTP GET请求（按照QT程序的方式）
            response = requests.get(
                upload_url,
                timeout=self.timeout,
                headers={
                    'User-Agent': 'NK-BCI-System/1.0',
                    'Accept': 'application/json, text/plain, */*'
                }
            )

            # 检查响应状态
            if response.status_code == 200:
                # 尝试解析响应数据
                response_data = None
                try:
                    response_data = response.json()
                    self.logger.debug(f"服务器响应数据: {response_data}")
                except:
                    response_data = {"raw_response": response.text}
                    self.logger.debug(f"服务器响应文本: {response.text}")

                # 检查业务层面的成功状态
                # 根据服务器返回的status字段判断（"0"表示成功）
                if isinstance(response_data, dict):
                    server_status = response_data.get('status', '')
                    if str(server_status) == "0":
                        if upload_type == "equipment":
                            self.logger.info("设备状态上传成功")
                        else:
                            self.logger.info("患者数据上传成功")
                        return UploadResult(
                            success=True,
                            status=UploadStatus.SUCCESS,
                            message="上传成功",
                            response_data=response_data
                        )

                    else:
                        # 根据状态码和上传类型提供详细的错误信息，记录错误编号
                        if str(server_status) == "-2":
                            if upload_type == "equipment":
                                error_msg = f"设备状态上传异常，错误编号: {server_status}"
                            else:
                                error_msg = f"患者数据重复或已存在，错误编号: {server_status}"
                        elif str(server_status) == "-1":
                            error_msg = f"服务器内部错误，错误编号: {server_status}"
                        else:
                            error_msg = f"服务器返回业务错误，错误编号: {server_status}"

                        self.logger.warning(error_msg)
                        return UploadResult(
                            success=False,
                            status=UploadStatus.FAILED,
                            message=error_msg,
                            response_data=response_data,
                            error_code="BUSINESS_ERROR"
                        )
                else:
                    # 如果无法解析JSON或格式不正确，仍然认为成功（兼容性考虑）
                    if upload_type == "equipment":
                        self.logger.info("设备状态上传成功（响应格式未知）")
                    else:
                        self.logger.info("患者数据上传成功（响应格式未知）")
                    return UploadResult(
                        success=True,
                        status=UploadStatus.SUCCESS,
                        message="上传成功",
                        response_data=response_data
                    )
            else:
                error_msg = f"服务器返回错误状态码: {response.status_code}"
                self.logger.warning(error_msg)
                return UploadResult(
                    success=False,
                    status=UploadStatus.FAILED,
                    message=error_msg,
                    error_code="HTTP_ERROR"
                )

        except requests.exceptions.Timeout:
            error_msg = "上传超时"
            self.logger.warning(error_msg)
            return UploadResult(
                success=False,
                status=UploadStatus.TIMEOUT,
                message=error_msg,
                error_code="TIMEOUT"
            )

        except requests.exceptions.ConnectionError:
            error_msg = "网络连接失败"
            self.logger.warning(error_msg)
            return UploadResult(
                success=False,
                status=UploadStatus.FAILED,
                message=error_msg,
                error_code="CONNECTION_ERROR"
            )

        except Exception as e:
            error_msg = f"上传请求失败: {str(e)}"
            self.logger.warning(error_msg)
            return UploadResult(
                success=False,
                status=UploadStatus.FAILED,
                message=error_msg,
                error_code="REQUEST_ERROR"
            )

    def test_connection(self) -> bool:
        """
        测试网络连接

        Returns:
            bool: 连接是否正常
        """
        if not REQUESTS_AVAILABLE:
            return False

        try:
            # 简单的连接测试
            response = requests.get(
                self.base_url,
                timeout=5,
                headers={'User-Agent': 'NK-BCI-System/1.0'}
            )
            return response.status_code in [200, 404, 405]  # 服务器响应即可

        except Exception as e:
            self.logger.debug(f"网络连接测试失败: {e}")
            return False

    def upload_treatment_data(self, treatment_data: Dict[str, Any]) -> UploadResult:
        """
        上传治疗数据到平台（参考原QT项目sendTreat接口）

        Args:
            treatment_data: 治疗数据

        Returns:
            UploadResult: 上传结果
        """
        if not REQUESTS_AVAILABLE:
            return UploadResult(
                success=False,
                status=UploadStatus.FAILED,
                message="requests库未安装，无法进行网络上传",
                error_code="LIBRARY_MISSING"
            )

        try:
            # 组织JSON数据，按照原QT项目的格式
            json_data = self._prepare_treatment_json_data(treatment_data)

            # 构建上传URL（参考原QT项目：sendTreat接口）
            upload_url = f"{self.base_url}AppPatientServlet?act=sendTreat&data={json_data}"

            self.logger.info(f"开始上传治疗数据: 患者{treatment_data.get('patientNum', '')}")
            self.logger.debug(f"治疗数据上传URL: {upload_url}")

            # 执行上传（不重试，直接上传）
            return self._upload_once(upload_url, "treatment")

        except Exception as e:
            error_msg = f"治疗数据上传失败: {str(e)}"
            self.logger.error(error_msg)
            return UploadResult(
                success=False,
                status=UploadStatus.FAILED,
                message=error_msg,
                error_code="UPLOAD_ERROR"
            )

    def _prepare_treatment_json_data(self, treatment_data: Dict[str, Any]) -> str:
        """
        准备治疗数据的JSON格式（参考原QT项目sendTreat格式）

        Args:
            treatment_data: 治疗数据字典

        Returns:
            str: JSON格式的数据字符串
        """
        try:
            # 按照原QT项目的字段顺序和格式组织数据
            json_obj = {
                "actualTimes": treatment_data.get("actualTimes", 0),
                "commentsOfTreatment": treatment_data.get("commentsOfTreatment", ""),
                "timesOfImagination": treatment_data.get("timesOfImagination", 0),
                "treatScore": treatment_data.get("treatScore", 0),
                "treatTime": treatment_data.get("treatTime", 0),
                "usageTime": treatment_data.get("usageTime", ""),
                "patientNum": treatment_data.get("patientNum", ""),
                "treatNum": treatment_data.get("treatNum", ""),
                "hospitalID": treatment_data.get("hospitalID", 1),
                "department": treatment_data.get("department", ""),
                "equipmentNum": treatment_data.get("equipmentNum", ""),
                "attdoctor": treatment_data.get("attdoctor", ""),
                "operator": treatment_data.get("operator", ""),
                "idCard": treatment_data.get("idCard", "")
            }

            # 转换为紧凑的JSON字符串
            json_str = json.dumps(json_obj, ensure_ascii=False, separators=(',', ':'))
            self.logger.debug(f"准备的治疗数据JSON: {json_str}")

            return json_str

        except Exception as e:
            self.logger.error(f"治疗数据JSON格式化失败: {e}")
            raise

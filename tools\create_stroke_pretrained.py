#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中风患者专用预训练模型创建工具
Stroke Patient Specific Pretrained Model Creator

专门为您的系统特点创建优化的预训练模型：
- 8通道脑电帽
- ADS1299芯片
- 低信噪比环境
- 中风患者群体

作者: AI Assistant
版本: 1.0.0
"""

import sys
import os
from pathlib import Path
import numpy as np
import time

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

try:
    import tensorflow as tf
    from tensorflow import keras
    from scipy.signal import butter, filtfilt
    TF_AVAILABLE = True
except ImportError as e:
    print(f"❌ 依赖库缺失: {e}")
    sys.exit(1)

from core.eegnet_model import create_eegnet_model


class StrokeOptimizedDataGenerator:
    """中风患者优化数据生成器"""
    
    def __init__(self):
        self.sampling_rate = 125  # 您系统的采样率
        self.n_channels = 8       # 您的通道数
        self.duration = 4.0       # 4秒试验
        self.n_samples = int(self.sampling_rate * self.duration)
    
    def generate_stroke_optimized_data(self, n_subjects=20, trials_per_subject=100):
        """生成针对中风患者优化的训练数据"""
        print(f"🧠 生成中风患者优化数据...")
        print(f"  - 受试者数: {n_subjects}")
        print(f"  - 每人试验数: {trials_per_subject}")
        print(f"  - 总样本数: {n_subjects * trials_per_subject}")
        
        total_samples = n_subjects * trials_per_subject
        X = np.zeros((total_samples, self.n_channels, self.n_samples))
        y = np.zeros(total_samples, dtype=int)
        
        sample_idx = 0
        
        for subject in range(n_subjects):
            print(f"  生成受试者 {subject+1}/{n_subjects}...")
            
            # 为每个受试者生成个体化参数（模拟个体差异）
            subject_params = self._generate_subject_params()
            
            for trial in range(trials_per_subject):
                # 标签（0: 休息, 1: 运动想象）
                label = trial % 2
                y[sample_idx] = label
                
                # 生成单个试验数据
                trial_data = self._generate_single_trial(label, subject_params)
                X[sample_idx] = trial_data
                
                sample_idx += 1
        
        print(f"✅ 数据生成完成:")
        print(f"  - 数据形状: {X.shape}")
        print(f"  - 标签分布: {np.bincount(y)}")
        
        return X, y
    
    def _generate_subject_params(self):
        """生成受试者个体化参数"""
        return {
            # 基础噪声水平（模拟ADS1299的噪声特性）
            'noise_level': 8 + np.random.randn() * 2,  # 较高噪声
            
            # α波参数
            'alpha_freq': 9 + np.random.randn() * 1.5,
            'alpha_amp': 12 + np.random.randn() * 3,
            
            # μ波参数（运动想象相关）
            'mu_freq': 10 + np.random.randn() * 1,
            'mu_baseline': 10 + np.random.randn() * 2,
            'mu_erd_strength': 0.3 + np.random.randn() * 0.1,  # ERD强度
            
            # β波参数
            'beta_freq': 18 + np.random.randn() * 3,
            'beta_amp': 6 + np.random.randn() * 2,
            
            # 中风相关参数
            'asymmetry_factor': 0.8 + np.random.randn() * 0.2,  # 左右不对称
            'signal_weakness': 0.7 + np.random.randn() * 0.2,   # 信号减弱
        }
    
    def _generate_single_trial(self, label, params):
        """生成单个试验数据"""
        t = np.linspace(0, self.duration, self.n_samples)
        trial_data = np.zeros((self.n_channels, self.n_samples))
        
        # 通道映射（模拟8通道脑电帽布局）
        channel_positions = {
            0: 'Fp1', 1: 'Fp2', 2: 'C3', 3: 'Cz', 
            4: 'C4', 5: 'P3', 6: 'Pz', 7: 'P4'
        }
        
        for ch in range(self.n_channels):
            # 基础噪声（模拟ADS1299噪声特性）
            noise = np.random.randn(self.n_samples) * params['noise_level']
            
            # 50Hz工频干扰（模拟实际环境）
            powerline = 2 * np.sin(2 * np.pi * 50 * t + np.random.rand() * 2 * np.pi)
            
            # α波成分
            alpha = params['alpha_amp'] * np.sin(
                2 * np.pi * params['alpha_freq'] * t + np.random.rand() * 2 * np.pi
            )
            
            # μ波成分（运动想象相关）
            mu_baseline = params['mu_baseline']
            if label == 1:  # 运动想象
                # 在1-3秒期间应用ERD
                erd_mask = (t >= 1.0) & (t <= 3.0)
                mu_amplitude = np.ones_like(t) * mu_baseline
                
                # 根据通道位置应用不同的ERD
                if ch in [2, 5]:  # C3, P3 (左侧)
                    mu_amplitude[erd_mask] *= params['mu_erd_strength']
                elif ch in [4, 7]:  # C4, P4 (右侧)
                    mu_amplitude[erd_mask] *= (1 - params['mu_erd_strength'] * 0.5)
            else:
                mu_amplitude = mu_baseline
            
            mu_signal = mu_amplitude * np.sin(
                2 * np.pi * params['mu_freq'] * t + np.random.rand() * 2 * np.pi
            )
            
            # β波成分
            beta = params['beta_amp'] * np.sin(
                2 * np.pi * params['beta_freq'] * t + np.random.rand() * 2 * np.pi
            )
            
            # 中风相关的信号特征
            if ch < 4:  # 左侧通道
                asymmetry_factor = params['asymmetry_factor']
            else:  # 右侧通道
                asymmetry_factor = 1.0
            
            signal_strength = params['signal_weakness'] * asymmetry_factor
            
            # 合成最终信号
            signal = (noise + powerline + alpha + mu_signal + beta) * signal_strength
            
            # 应用带通滤波（1-40 Hz）
            nyquist = self.sampling_rate / 2
            low = 1.0 / nyquist
            high = 40.0 / nyquist
            b, a = butter(4, [low, high], btype='band')
            signal = filtfilt(b, a, signal)
            
            # 添加随机基线漂移（模拟实际记录条件）
            baseline_drift = 5 * np.sin(2 * np.pi * 0.1 * t + np.random.rand() * 2 * np.pi)
            signal += baseline_drift
            
            trial_data[ch] = signal
        
        return trial_data


class StrokeEEGNetTrainer:
    """中风患者专用EEGNet训练器"""
    
    def __init__(self):
        self.model = None
    
    def create_stroke_optimized_model(self):
        """创建针对中风患者优化的EEGNet模型"""
        print("🏗️ 创建中风患者优化EEGNet模型...")
        
        # 针对低信噪比和中风患者特点的参数调整
        self.model = create_eegnet_model(
            n_channels=8,
            n_samples=500,  # 4秒 @ 125Hz
            n_classes=2,
            dropout_rate=0.3,  # 增加dropout防止过拟合
            # 可以在这里添加更多优化参数
        )
        
        return self.model
    
    def train_stroke_model(self, X_train, y_train, X_val, y_val):
        """训练中风患者专用模型"""
        print("🎯 开始训练中风患者专用模型...")
        
        # 数据预处理
        X_train = X_train.reshape(X_train.shape[0], X_train.shape[1], X_train.shape[2], 1)
        X_val = X_val.reshape(X_val.shape[0], X_val.shape[1], X_val.shape[2], 1)
        
        # 编译模型（使用适合中风患者的参数）
        self.model.compile(
            optimizer=keras.optimizers.Adam(learning_rate=0.0005),  # 较小学习率
            loss='sparse_categorical_crossentropy',
            metrics=['accuracy']
        )
        
        # 回调函数
        callbacks = [
            keras.callbacks.EarlyStopping(
                monitor='val_accuracy',
                patience=15,  # 更大的耐心值
                restore_best_weights=True
            ),
            keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=8,
                min_lr=1e-6
            ),
            keras.callbacks.ModelCheckpoint(
                'temp_best_model.keras',
                monitor='val_accuracy',
                save_best_only=True
            )
        ]
        
        # 训练模型
        history = self.model.fit(
            X_train, y_train,
            validation_data=(X_val, y_val),
            epochs=100,
            batch_size=16,  # 较小批次适合小数据集
            callbacks=callbacks,
            verbose=1
        )
        
        # 加载最佳模型
        self.model = keras.models.load_model('temp_best_model.keras')
        
        return history
    
    def save_model(self, output_path: Path):
        """保存模型"""
        if self.model is None:
            raise ValueError("模型尚未训练")
        
        self.model.save(str(output_path))
        
        # 保存模型信息
        model_info = {
            'model_type': 'stroke_optimized_eegnet',
            'target_population': 'stroke_patients',
            'device_type': 'ads1299_8ch',
            'sampling_rate': 125,
            'channels': 8,
            'optimizations': [
                'low_snr_adapted',
                'asymmetry_aware',
                'noise_robust'
            ],
            'created_time': time.time(),
            'version': '1.0.0'
        }
        
        import json
        info_path = output_path.with_suffix('.json')
        with open(info_path, 'w', encoding='utf-8') as f:
            json.dump(model_info, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 模型已保存: {output_path}")
        print(f"✅ 信息已保存: {info_path}")


def main():
    """主函数"""
    print("🧠 中风患者专用预训练模型创建工具")
    print("=" * 60)
    
    # 创建输出目录
    output_dir = Path("pretrained_models")
    output_dir.mkdir(exist_ok=True)
    
    try:
        # 生成训练数据
        data_generator = StrokeOptimizedDataGenerator()
        X, y = data_generator.generate_stroke_optimized_data(
            n_subjects=30,      # 30个模拟受试者
            trials_per_subject=80  # 每人80个试验
        )
        
        # 划分训练和验证集
        from sklearn.model_selection import train_test_split
        X_train, X_val, y_train, y_val = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        print(f"\n📊 数据划分:")
        print(f"  - 训练集: {X_train.shape[0]} 样本")
        print(f"  - 验证集: {X_val.shape[0]} 样本")
        
        # 创建和训练模型
        trainer = StrokeEEGNetTrainer()
        model = trainer.create_stroke_optimized_model()
        
        print(f"\n📋 模型结构:")
        model.summary()
        
        # 训练模型
        history = trainer.train_stroke_model(X_train, y_train, X_val, y_val)
        
        # 获取最终性能
        final_acc = max(history.history['accuracy'])
        final_val_acc = max(history.history['val_accuracy'])
        
        print(f"\n📈 训练结果:")
        print(f"  - 最佳训练准确率: {final_acc:.3f}")
        print(f"  - 最佳验证准确率: {final_val_acc:.3f}")
        
        # 保存模型
        output_path = output_dir / "eegnet_stroke_optimized.keras"
        trainer.save_model(output_path)
        
        # 清理临时文件
        temp_file = Path("temp_best_model.keras")
        if temp_file.exists():
            temp_file.unlink()
        
        print(f"\n🎉 中风患者专用预训练模型创建完成!")
        print(f"模型文件: {output_path}")
        print(f"现在可以在您的系统中使用此预训练模型进行迁移学习")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ 创建失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    import sys
    sys.exit(main())

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单实例检查模块
Single Instance Check Module

作者: AI Assistant
版本: 1.0.0
"""

import os
import sys
import tempfile
import atexit
from pathlib import Path

# 条件导入fcntl（仅在Unix/Linux系统上可用）
try:
    import fcntl
    HAS_FCNTL = True
except ImportError:
    HAS_FCNTL = False


class SingleInstance:
    """单实例检查类，确保程序只能运行一个实例"""

    def __init__(self, app_name="NK_BCI_System"):
        self.app_name = app_name
        self.lock_file = None
        self.lock_file_path = None
        self._is_locked = False

        # 根据操作系统选择锁文件位置
        if sys.platform.startswith('win'):
            # Windows系统使用临时目录
            self.lock_file_path = Path(tempfile.gettempdir()) / f"{app_name}.lock"
        else:
            # Unix/Linux系统使用/tmp目录
            self.lock_file_path = Path("/tmp") / f"{app_name}.lock"

    def is_single(self):
        """
        检查是否为单实例

        Returns:
            bool: True表示是单实例，False表示已有实例运行
        """
        try:
            if sys.platform.startswith('win'):
                # Windows系统使用简化的文件检查
                return self._check_windows_simple()
            else:
                # Unix/Linux系统使用fcntl锁
                self.lock_file = open(self.lock_file_path, 'w')
                return self._check_unix_lock()

        except Exception as e:
            print(f"单实例检查异常: {e}")
            return True  # 如果检查失败，允许启动

    def _check_windows_simple(self):
        """Windows系统的简化检查"""
        try:
            # 检查锁文件是否已存在
            if self.lock_file_path.exists():
                # 尝试读取现有的PID
                try:
                    with open(self.lock_file_path, 'r') as f:
                        existing_pid = int(f.read().strip())

                    # 检查该进程是否还在运行
                    if self.is_process_running(existing_pid):
                        # 进程还在运行，不允许启动新实例
                        return False
                    else:
                        # 进程已经不存在，删除旧的锁文件
                        self.lock_file_path.unlink()
                except (ValueError, FileNotFoundError, PermissionError):
                    # 锁文件损坏或无法访问，尝试删除
                    try:
                        self.lock_file_path.unlink()
                    except (FileNotFoundError, PermissionError):
                        pass

            # 创建新的锁文件
            with open(self.lock_file_path, 'w') as f:
                f.write(str(os.getpid()))

            self._is_locked = True

            # 注册退出时的清理函数
            atexit.register(self.cleanup)

            return True

        except Exception as e:
            print(f"Windows简化检查异常: {e}")
            return True  # 如果检查失败，允许启动

    def _check_windows_lock(self):
        """Windows系统的锁检查"""
        try:
            # 检查锁文件是否已存在且被其他进程使用
            if self.lock_file_path.exists():
                # 尝试读取现有的PID
                try:
                    with open(self.lock_file_path, 'r') as f:
                        existing_pid = int(f.read().strip())

                    # 检查该进程是否还在运行
                    if self.is_process_running(existing_pid):
                        # 进程还在运行，不允许启动新实例
                        if self.lock_file:
                            self.lock_file.close()
                        return False
                    else:
                        # 进程已经不存在，删除旧的锁文件
                        self.lock_file_path.unlink()
                except (ValueError, FileNotFoundError):
                    # 锁文件损坏或不存在，删除它
                    try:
                        self.lock_file_path.unlink()
                    except FileNotFoundError:
                        pass

            # 写入当前进程ID
            self.lock_file.write(str(os.getpid()))
            self.lock_file.flush()
            self._is_locked = True

            # 注册退出时的清理函数
            atexit.register(self.cleanup)

            return True

        except Exception as e:
            print(f"Windows锁检查异常: {e}")
            if self.lock_file:
                self.lock_file.close()
            return False

    def _check_unix_lock(self):
        """Unix/Linux系统的锁检查"""
        if not HAS_FCNTL:
            # 如果没有fcntl，回退到Windows方式
            return self._check_windows_lock()

        try:
            # 使用fcntl进行文件锁定
            fcntl.flock(self.lock_file.fileno(), fcntl.LOCK_EX | fcntl.LOCK_NB)

            # 写入当前进程ID
            self.lock_file.write(str(os.getpid()))
            self.lock_file.flush()
            self._is_locked = True

            # 注册退出时的清理函数
            atexit.register(self.cleanup)

            return True

        except (IOError, OSError):
            if self.lock_file:
                self.lock_file.close()
            return False

    def cleanup(self):
        """清理锁文件"""
        try:
            if self._is_locked and self.lock_file:
                if not sys.platform.startswith('win') and HAS_FCNTL:
                    # Unix/Linux系统需要显式解锁
                    fcntl.flock(self.lock_file.fileno(), fcntl.LOCK_UN)

                self.lock_file.close()
                self._is_locked = False

            # 删除锁文件
            if self.lock_file_path and self.lock_file_path.exists():
                try:
                    self.lock_file_path.unlink()
                except Exception:
                    pass  # 忽略删除失败的情况

        except Exception as e:
            print(f"清理锁文件异常: {e}")

    def get_running_pid(self):
        """
        获取正在运行的实例的进程ID

        Returns:
            int: 进程ID，如果无法获取则返回None
        """
        try:
            if self.lock_file_path.exists():
                with open(self.lock_file_path, 'r') as f:
                    pid_str = f.read().strip()
                    if pid_str.isdigit():
                        return int(pid_str)
        except Exception:
            pass
        return None

    def is_process_running(self, pid):
        """
        检查指定PID的进程是否正在运行

        Args:
            pid (int): 进程ID

        Returns:
            bool: True表示进程正在运行
        """
        try:
            if sys.platform.startswith('win'):
                # Windows系统 - 尝试多种方法
                try:
                    import psutil
                    return psutil.pid_exists(pid)
                except ImportError:
                    # 如果没有psutil，使用Windows API
                    try:
                        import ctypes
                        kernel32 = ctypes.windll.kernel32
                        handle = kernel32.OpenProcess(0x1000, False, pid)  # PROCESS_QUERY_LIMITED_INFORMATION
                        if handle:
                            kernel32.CloseHandle(handle)
                            return True
                        return False
                    except Exception:
                        # 最后的回退方法 - 使用tasklist
                        import subprocess
                        try:
                            result = subprocess.run(['tasklist', '/FI', f'PID eq {pid}'],
                                                  capture_output=True, text=True, timeout=5)
                            return str(pid) in result.stdout
                        except Exception:
                            return False
            else:
                # Unix/Linux系统
                os.kill(pid, 0)
                return True
        except (OSError, ImportError):
            return False

    def force_cleanup(self):
        """
        强制清理锁文件（用于异常退出后的清理）

        Returns:
            bool: True表示清理成功
        """
        try:
            if self.lock_file_path and self.lock_file_path.exists():
                # 检查锁文件中的PID是否还在运行
                running_pid = self.get_running_pid()
                if running_pid and not self.is_process_running(running_pid):
                    # 进程已经不存在，可以安全删除锁文件
                    self.lock_file_path.unlink()
                    return True
                elif not running_pid:
                    # 无法读取PID，删除锁文件
                    self.lock_file_path.unlink()
                    return True
            return False
        except Exception as e:
            print(f"强制清理锁文件失败: {e}")
            return False


# 使用示例和测试函数
def test_single_instance():
    """测试单实例功能"""
    print("测试单实例功能...")

    # 创建第一个实例
    instance1 = SingleInstance("test_app")
    if instance1.is_single():
        print("第一个实例启动成功")

        # 尝试创建第二个实例
        instance2 = SingleInstance("test_app")
        if not instance2.is_single():
            print("第二个实例被正确阻止")
        else:
            print("错误：第二个实例不应该被允许启动")

        # 清理第一个实例
        instance1.cleanup()
        print("第一个实例已清理")

        # 现在第二个实例应该可以启动
        instance3 = SingleInstance("test_app")
        if instance3.is_single():
            print("清理后新实例可以正常启动")
            instance3.cleanup()
        else:
            print("错误：清理后新实例应该可以启动")
    else:
        print("第一个实例启动失败")


if __name__ == "__main__":
    test_single_instance()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
治疗数据集成模块
Treatment Data Integration

将原始数据存储功能集成到现有的治疗工作流程中
提供无缝的数据采集和存储体验

作者: AI Assistant
版本: 1.0.0
"""

import logging
import numpy as np
from datetime import datetime
from typing import Optional, Dict, Any, List
from dataclasses import dataclass

from core.eeg_raw_data_manager import EEGRawDataManager, TrialType
from core.eeg_data_loader import EEGDataLoader
from core.eeg_device import EEGDataPacket
from utils.app_config import AppConfig


@dataclass
class TreatmentSession:
    """治疗会话数据"""
    session_id: int
    patient_id: int
    treatment_id: Optional[int]
    start_time: datetime
    current_trial: int
    total_trials: int
    successful_trials: int
    current_state: str
    data_buffer: List[np.ndarray]
    labels_buffer: List[int]
    is_recording: bool


class TreatmentDataIntegration:
    """治疗数据集成管理器"""
    
    def __init__(self, db_manager=None):
        """初始化集成管理器"""
        self.logger = logging.getLogger(__name__)
        self.db_manager = db_manager
        
        # 初始化数据管理器
        self.raw_data_manager = EEGRawDataManager(db_manager)
        self.data_loader = EEGDataLoader(db_manager)
        
        # 配置信息
        self.config = AppConfig.get_config('raw_data')
        
        # 当前治疗会话
        self.current_session: Optional[TreatmentSession] = None
        
        # 数据缓冲区
        self.eeg_buffer = []
        self.buffer_size = 500  # 4秒数据 (125Hz * 4)
        self.sampling_rate = 125
        
        self.logger.info("治疗数据集成管理器初始化完成")
    
    def start_treatment_session(self, patient_id: int, treatment_id: Optional[int] = None) -> bool:
        """开始治疗会话"""
        try:
            if not self.config['enabled']:
                self.logger.info("原始数据存储已禁用，跳过数据采集")
                return True
            
            # 开始原始数据会话
            session_id = self.raw_data_manager.start_session(
                patient_id=patient_id,
                treatment_id=treatment_id,
                session_type='treatment'
            )
            
            if session_id <= 0:
                self.logger.error("启动原始数据会话失败")
                return False
            
            # 创建治疗会话
            self.current_session = TreatmentSession(
                session_id=session_id,
                patient_id=patient_id,
                treatment_id=treatment_id,
                start_time=datetime.now(),
                current_trial=0,
                total_trials=0,
                successful_trials=0,
                current_state='idle',
                data_buffer=[],
                labels_buffer=[],
                is_recording=False
            )
            
            # 清空缓冲区
            self.eeg_buffer.clear()
            
            self.logger.info(f"治疗会话开始: 患者{patient_id}, 会话{session_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"开始治疗会话失败: {e}")
            return False
    
    def start_trial_recording(self, trial_type: str) -> bool:
        """开始试验数据记录"""
        try:
            if not self.current_session or not self.config['enabled']:
                return True
            
            # 转换试验类型
            if trial_type.lower() in ['motor_imagery', 'mi', '运动想象']:
                label = TrialType.MOTOR_IMAGERY.value
                self.current_session.current_state = 'motor_imagery'
            elif trial_type.lower() in ['rest', 'quiet', '休息', '平静']:
                label = TrialType.REST.value
                self.current_session.current_state = 'rest'
            else:
                self.logger.warning(f"未知的试验类型: {trial_type}")
                return False
            
            # 清空当前试验的数据缓冲区
            self.eeg_buffer.clear()
            self.current_session.is_recording = True
            
            self.logger.debug(f"开始记录试验: 类型{trial_type}, 标签{label}")
            return True
            
        except Exception as e:
            self.logger.error(f"开始试验记录失败: {e}")
            return False
    
    def process_eeg_data(self, data_packet: EEGDataPacket):
        """处理脑电数据包"""
        try:
            if not self.current_session or not self.current_session.is_recording:
                return
            
            if not self.config['enabled']:
                return
            
            # 将数据包转换为numpy数组
            for group_data in data_packet.channel_data:
                # group_data是8个通道的数据
                if len(group_data) == 8:
                    # 转换为列向量并添加到缓冲区
                    sample = np.array(group_data, dtype=np.float32).reshape(-1, 1)
                    self.eeg_buffer.append(sample)
            
            # 检查缓冲区大小，如果达到试验长度则自动结束记录
            if len(self.eeg_buffer) >= self.buffer_size:
                self.end_trial_recording()
            
        except Exception as e:
            self.logger.error(f"处理脑电数据失败: {e}")
    
    def end_trial_recording(self, force_save: bool = False) -> bool:
        """结束试验数据记录"""
        try:
            if not self.current_session or not self.current_session.is_recording:
                return True
            
            if not self.config['enabled']:
                self.current_session.is_recording = False
                return True
            
            # 检查数据长度
            if len(self.eeg_buffer) < 100 and not force_save:  # 至少0.8秒数据
                self.logger.warning(f"试验数据太短: {len(self.eeg_buffer)}个样本")
                self.current_session.is_recording = False
                return False
            
            # 组织数据
            if self.eeg_buffer:
                # 将缓冲区数据组合成矩阵 (8通道 × 时间点)
                eeg_data = np.hstack(self.eeg_buffer)
                
                # 确定标签
                if self.current_session.current_state == 'motor_imagery':
                    label = TrialType.MOTOR_IMAGERY.value
                elif self.current_session.current_state == 'rest':
                    label = TrialType.REST.value
                else:
                    label = TrialType.REST.value  # 默认为休息状态
                
                # 创建试验元数据
                trial_metadata = {
                    'trial_type': self.current_session.current_state,
                    'treatment_id': self.current_session.treatment_id,
                    'notes': f'治疗试验 {self.current_session.current_trial + 1}'
                }
                
                # 保存数据
                success = self.raw_data_manager.save_trial_data(
                    eeg_data=eeg_data,
                    label=label,
                    trial_metadata=trial_metadata
                )
                
                if success:
                    self.current_session.current_trial += 1
                    self.current_session.total_trials += 1
                    
                    # 添加到会话缓冲区（用于实时分析）
                    self.current_session.data_buffer.append(eeg_data)
                    self.current_session.labels_buffer.append(label)
                    
                    self.logger.debug(f"试验数据保存成功: 试验{self.current_session.current_trial}, "
                                    f"数据形状{eeg_data.shape}, 标签{label}")
                else:
                    self.logger.error("试验数据保存失败")
            
            # 重置状态
            self.current_session.is_recording = False
            self.current_session.current_state = 'idle'
            self.eeg_buffer.clear()
            
            return True
            
        except Exception as e:
            self.logger.error(f"结束试验记录失败: {e}")
            return False
    
    def end_treatment_session(self) -> bool:
        """结束治疗会话"""
        try:
            if not self.current_session:
                return True
            
            # 如果正在记录，先结束当前试验
            if self.current_session.is_recording:
                self.end_trial_recording(force_save=True)
            
            # 结束原始数据会话
            if self.config['enabled']:
                success = self.raw_data_manager.end_session()
                if not success:
                    self.logger.error("结束原始数据会话失败")
            
            self.logger.info(f"治疗会话结束: 会话{self.current_session.session_id}, "
                           f"总试验{self.current_session.total_trials}")
            
            # 清理会话数据
            self.current_session = None
            self.eeg_buffer.clear()
            
            return True
            
        except Exception as e:
            self.logger.error(f"结束治疗会话失败: {e}")
            return False
    
    def get_session_data_for_training(self) -> Optional[tuple]:
        """获取当前会话的数据用于实时训练"""
        try:
            if not self.current_session or not self.current_session.data_buffer:
                return None
            
            # 返回当前会话的数据和标签
            return (
                self.current_session.data_buffer.copy(),
                self.current_session.labels_buffer.copy()
            )
            
        except Exception as e:
            self.logger.error(f"获取会话训练数据失败: {e}")
            return None
    
    def get_patient_historical_data(self, patient_id: int, 
                                  max_sessions: int = 10) -> Optional[tuple]:
        """获取患者的历史数据用于模型训练"""
        try:
            # 使用数据加载器获取患者数据
            patient_data = self.data_loader.load_patient_data(
                patient_id=patient_id,
                min_quality=self.config['quality_threshold']
            )
            
            if not patient_data:
                return None
            
            # 限制会话数量
            if len(patient_data) > max_sessions * 20:  # 假设每会话20个试验
                patient_data = patient_data[:max_sessions * 20]
            
            # 加载原始数据
            all_data = []
            all_labels = []
            
            for record in patient_data:
                trial_data = self.data_loader._load_trial_raw_data(
                    record['file_path'], 
                    record['trial_id']
                )
                
                if trial_data:
                    all_data.append(trial_data[0])
                    all_labels.append(record['label'])
            
            if all_data:
                self.logger.info(f"加载患者{patient_id}历史数据: {len(all_data)}个试验")
                return all_data, all_labels
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取患者历史数据失败: {e}")
            return None
    
    def get_session_statistics(self) -> Dict[str, Any]:
        """获取当前会话统计信息"""
        if not self.current_session:
            return {}
        
        return {
            'session_id': self.current_session.session_id,
            'patient_id': self.current_session.patient_id,
            'start_time': self.current_session.start_time.isoformat(),
            'current_trial': self.current_session.current_trial,
            'total_trials': self.current_session.total_trials,
            'successful_trials': self.current_session.successful_trials,
            'current_state': self.current_session.current_state,
            'is_recording': self.current_session.is_recording,
            'buffer_size': len(self.eeg_buffer),
            'data_enabled': self.config['enabled']
        }
    
    def validate_data_integrity(self, session_id: int) -> Dict[str, Any]:
        """验证数据完整性"""
        try:
            # 获取会话数据
            session_data = self.data_loader.load_session_data(session_id)
            
            if not session_data:
                return {'valid': False, 'error': '会话不存在'}
            
            trials = session_data['trials']
            validation_results = {
                'valid': True,
                'total_trials': len(trials),
                'valid_trials': 0,
                'invalid_trials': 0,
                'missing_files': 0,
                'corrupted_files': 0,
                'quality_issues': 0,
                'details': []
            }
            
            for trial in trials:
                trial_result = {
                    'trial_id': trial['trial_id'],
                    'file_path': trial['file_path'],
                    'issues': []
                }
                
                # 检查文件是否存在
                from pathlib import Path
                if not Path(trial['file_path']).exists():
                    trial_result['issues'].append('文件不存在')
                    validation_results['missing_files'] += 1
                    validation_results['invalid_trials'] += 1
                    continue
                
                # 尝试加载数据
                trial_data = self.data_loader._load_trial_raw_data(
                    trial['file_path'], trial['trial_id']
                )
                
                if not trial_data:
                    trial_result['issues'].append('数据加载失败')
                    validation_results['corrupted_files'] += 1
                    validation_results['invalid_trials'] += 1
                    continue
                
                # 检查数据质量
                if trial['data_quality'] < self.config['quality_threshold']:
                    trial_result['issues'].append(f"质量低于阈值: {trial['data_quality']}")
                    validation_results['quality_issues'] += 1
                
                if not trial_result['issues']:
                    validation_results['valid_trials'] += 1
                else:
                    validation_results['invalid_trials'] += 1
                    validation_results['details'].append(trial_result)
            
            # 判断整体有效性
            if validation_results['invalid_trials'] > validation_results['valid_trials'] * 0.2:
                validation_results['valid'] = False
                validation_results['error'] = '无效试验比例过高'
            
            return validation_results
            
        except Exception as e:
            self.logger.error(f"数据完整性验证失败: {e}")
            return {'valid': False, 'error': str(e)}

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主题管理器
Theme Manager

作者: AI Assistant
版本: 2.0.0
"""

import logging
from typing import Dict, Any, Optional, Callable
from PySide6.QtCore import QObject, Signal
from PySide6.QtWidgets import QApplication

from .medical_theme import MedicalTheme
from .tech_theme import TechTheme
from ..styles import load_stylesheet


class ThemeManager(QObject):
    """主题管理器类"""
    
    # 信号定义
    theme_changed = Signal(str)  # 主题切换信号
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(__name__)
        
        # 当前主题
        self.current_theme = "medical"  # 默认医疗主题
        
        # 主题配置
        self.themes = {
            "medical": MedicalTheme,
            "tech": TechTheme,
        }
        
        # 主题切换回调函数列表
        self.theme_callbacks: list[Callable[[str], None]] = []
        
        self.logger.info("主题管理器初始化完成")
    
    def get_current_theme(self) -> str:
        """获取当前主题名称"""
        return self.current_theme
    
    def get_available_themes(self) -> list[str]:
        """获取可用主题列表"""
        return list(self.themes.keys())
    
    def get_theme_config(self, theme_name: Optional[str] = None) -> Dict[str, Any]:
        """获取主题配置"""
        if theme_name is None:
            theme_name = self.current_theme
        
        if theme_name not in self.themes:
            self.logger.warning(f"主题 {theme_name} 不存在，使用默认主题")
            theme_name = "medical"
        
        return self.themes[theme_name].get_theme_config()
    
    def get_color(self, color_name: str, theme_name: Optional[str] = None) -> str:
        """获取指定颜色值"""
        if theme_name is None:
            theme_name = self.current_theme
        
        if theme_name not in self.themes:
            theme_name = "medical"
        
        return self.themes[theme_name].get_color(color_name)
    
    def switch_theme(self, theme_name: str) -> bool:
        """切换主题"""
        try:
            if theme_name not in self.themes:
                self.logger.error(f"主题 {theme_name} 不存在")
                return False
            
            if theme_name == self.current_theme:
                self.logger.info(f"主题 {theme_name} 已经是当前主题")
                return True
            
            old_theme = self.current_theme
            self.current_theme = theme_name
            
            # 应用新主题样式
            self.apply_theme_styles()
            
            # 执行主题切换回调
            for callback in self.theme_callbacks:
                try:
                    callback(theme_name)
                except Exception as e:
                    self.logger.error(f"执行主题切换回调失败: {e}")
            
            # 发送主题切换信号
            self.theme_changed.emit(theme_name)
            
            self.logger.info(f"主题已从 {old_theme} 切换到 {theme_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"切换主题失败: {e}")
            return False
    
    def apply_theme_styles(self):
        """应用当前主题样式"""
        try:
            # 加载样式表
            stylesheet = load_stylesheet(self.current_theme)
            
            if stylesheet:
                # 应用到整个应用程序
                app = QApplication.instance()
                if app:
                    app.setStyleSheet(stylesheet)
                    self.logger.info(f"已应用 {self.current_theme} 主题样式")
                else:
                    self.logger.warning("无法获取应用程序实例，样式应用失败")
            else:
                self.logger.warning(f"无法加载 {self.current_theme} 主题样式文件")
                
        except Exception as e:
            self.logger.error(f"应用主题样式失败: {e}")
    
    def register_theme_callback(self, callback: Callable[[str], None]):
        """注册主题切换回调函数"""
        if callback not in self.theme_callbacks:
            self.theme_callbacks.append(callback)
            self.logger.debug("主题切换回调函数已注册")
    
    def unregister_theme_callback(self, callback: Callable[[str], None]):
        """注销主题切换回调函数"""
        if callback in self.theme_callbacks:
            self.theme_callbacks.remove(callback)
            self.logger.debug("主题切换回调函数已注销")
    
    def toggle_theme(self):
        """切换主题（在医疗和科技主题间切换）"""
        if self.current_theme == "medical":
            self.switch_theme("tech")
        else:
            self.switch_theme("medical")
    
    def get_theme_display_name(self, theme_name: Optional[str] = None) -> str:
        """获取主题显示名称"""
        if theme_name is None:
            theme_name = self.current_theme
        
        config = self.get_theme_config(theme_name)
        return config.get('display_name', theme_name)

# 训练阶段数据存储集成指南

## 🎯 问题解决方案

### 问题分析
1. **数据没有保存**：原始数据存储功能没有集成到现有的训练和治疗流程中
2. **标签不准确**：治疗阶段的标签基于推测，不够准确
3. **数据质量**：训练阶段的数据质量更高，标签更明确

### 解决方案
**将原始数据存储功能集成到训练阶段**，因为：
- ✅ **明确标签**：训练时用户明确知道何时进行运动想象
- ✅ **高质量数据**：用户更专注，数据质量更好
- ✅ **标准化流程**：训练有固定的时间窗口和明确指令

## 🚀 快速集成方法

### 方法1：使用增强补丁（推荐）

在您的 `ui/treatment_ui.py` 文件中添加以下代码：

```python
# 在文件顶部导入
from training_integration_patch import apply_training_enhancement

# 在 TreatmentUI 类的 __init__ 方法末尾添加
def __init__(self, parent=None):
    # ... 现有初始化代码 ...
    
    # 应用训练数据存储增强
    try:
        self.training_enhancement = apply_training_enhancement(self)
        self.logger.info("训练数据存储增强已启用")
    except Exception as e:
        self.logger.error(f"启用训练数据存储增强失败: {e}")
```

### 方法2：手动集成

#### 步骤1：导入必要模块

```python
from core.training_data_integration import TrainingDataIntegration
```

#### 步骤2：初始化数据集成管理器

```python
# 在 TreatmentUI.__init__ 中添加
self.training_data_integration = TrainingDataIntegration(self.db_manager)
```

#### 步骤3：修改训练开始方法

```python
def start_training(self):
    """开始训练"""
    try:
        # ... 现有代码 ...
        
        # 如果训练开始成功，启动数据记录
        if self.is_training:
            patient_id = self._get_current_patient_id()
            if patient_id:
                success = self.training_data_integration.start_training_session(patient_id)
                if success:
                    self.add_training_log("📊 原始数据记录已启动")
        
        # ... 现有代码 ...
    except Exception as e:
        self.logger.error(f"开始训练失败: {e}")
```

#### 步骤4：修改训练停止方法

```python
def stop_training(self):
    """停止训练"""
    try:
        # 结束数据记录会话
        if hasattr(self, 'training_data_integration'):
            success = self.training_data_integration.end_training_session()
            if success:
                self.add_training_log("📊 原始数据记录已停止")
        
        # ... 现有代码 ...
    except Exception as e:
        self.logger.error(f"停止训练失败: {e}")
```

#### 步骤5：修改脑电数据接收方法

```python
def on_eeg_data_received(self, data_packet: EEGDataPacket):
    """处理接收到的脑电数据"""
    try:
        # ... 现有代码 ...
        
        # 如果正在训练，将数据传递给数据集成管理器
        if self.is_training and hasattr(self, 'training_data_integration'):
            self.training_data_integration.process_eeg_data(data_packet)
        
        # ... 现有代码 ...
    except Exception as e:
        self.logger.error(f"处理脑电数据失败: {e}")
```

#### 步骤6：添加训练器状态回调

```python
# 在训练器初始化后添加
if self.mi_trainer:
    self.mi_trainer.state_changed_callback = self._on_training_state_changed

def _on_training_state_changed(self, old_state, new_state, round_number=0, trial_number=0):
    """训练状态变化回调"""
    try:
        from core.motor_imagery_trainer import TrainingState
        
        # 如果进入运动想象或休息状态，开始记录
        if new_state in [TrainingState.MOTOR_IMAGERY, TrainingState.QUIET]:
            if hasattr(self, 'training_data_integration'):
                self.training_data_integration.start_trial_recording(
                    training_state=new_state,
                    round_number=round_number,
                    trial_number=trial_number
                )
        
        # 如果离开运动想象或休息状态，结束记录
        elif old_state in [TrainingState.MOTOR_IMAGERY, TrainingState.QUIET]:
            if hasattr(self, 'training_data_integration'):
                self.training_data_integration.end_trial_recording()
                
    except Exception as e:
        self.logger.error(f"处理训练状态变化失败: {e}")
```

## 🔧 配置检查

### 1. 确认配置已启用

检查 `data/user_config.json` 或 `utils/app_config.py`：

```json
{
  "raw_data": {
    "enabled": true,
    "storage_format": "hdf5",
    "compression": true,
    "quality_threshold": 0.7
  }
}
```

### 2. 检查依赖

确保已安装 HDF5 支持：

```bash
pip install h5py
```

### 3. 检查数据目录

数据将保存在：
```
data/raw_eeg_data/patients/patient_XXX/sessions/YYYY-MM-DD/
```

## 📊 验证数据存储

### 1. 运行测试

```bash
python training_integration_patch.py
```

### 2. 检查数据目录

```python
from core.training_data_integration import TrainingDataIntegration
from core.database_manager import DatabaseManager

db_manager = DatabaseManager()
db_manager.initialize()

integration = TrainingDataIntegration(db_manager)
status = integration.check_data_directory()
print(status)
```

### 3. 查看数据库记录

```python
from core.eeg_data_loader import EEGDataLoader

data_loader = EEGDataLoader(db_manager)
stats = data_loader.get_data_statistics()
print(f"总试验数: {stats['total_trials']}")
print(f"总患者数: {stats['total_patients']}")
```

## 📈 数据使用

### 1. 获取患者训练数据

```python
# 获取特定患者的训练数据
patient_data = data_loader.load_patient_data(
    patient_id=123,
    session_types=['training'],  # 只获取训练数据
    min_quality=0.7
)

print(f"患者123的训练数据: {len(patient_data)}个试验")
```

### 2. 获取训练数据集

```python
# 获取多个患者的训练数据集
dataset = data_loader.get_training_dataset(
    patient_ids=[123, 124, 125],
    balance_classes=True,
    min_quality=0.7
)

train_data = dataset.train_data
train_labels = dataset.train_labels
print(f"训练集: {len(train_data)}个样本")
```

### 3. 加载原始数据

```python
# 加载特定试验的原始数据
trial_data = data_loader.load_trial_by_id(trial_db_id=1)
if trial_data:
    eeg_data, metadata = trial_data
    print(f"数据形状: {eeg_data.shape}")
    print(f"标签: {metadata['label']}")
```

## 🔍 故障排除

### 1. 数据没有保存

**检查项目**：
- [ ] 配置是否启用 (`raw_data.enabled = true`)
- [ ] HDF5库是否安装 (`pip install h5py`)
- [ ] 患者ID是否正确
- [ ] 数据库连接是否正常
- [ ] 磁盘空间是否充足

**调试方法**：
```python
# 检查配置
from utils.app_config import AppConfig
config = AppConfig.get_config('raw_data')
print(f"数据存储启用: {config['enabled']}")

# 检查日志
import logging
logging.basicConfig(level=logging.DEBUG)
```

### 2. 外键约束错误

**原因**：患者记录不存在

**解决方法**：
```python
# 确保患者记录存在
patient_data = {
    'bianhao': 123,
    'name': '测试患者',
    'age': 45,
    'xingbie': '男',
    # ... 其他必要字段
}
db_manager.add_patient(patient_data)
```

### 3. 数据质量过低

**原因**：数据质量低于阈值

**解决方法**：
```python
# 降低质量阈值
config['quality_threshold'] = 0.5

# 或者检查数据质量
stats = data_loader.get_data_statistics()
print(f"平均质量: {stats['avg_quality']}")
```

## 📋 最佳实践

### 1. 数据管理

- **定期备份**：设置自动备份机制
- **质量监控**：监控数据质量趋势
- **存储优化**：定期清理过期数据

### 2. 性能优化

- **批量处理**：避免频繁的小文件写入
- **压缩设置**：根据需要调整压缩级别
- **缓存管理**：合理设置缓存大小

### 3. 数据安全

- **访问控制**：限制数据访问权限
- **数据加密**：敏感数据加密存储
- **审计日志**：记录数据访问操作

## 🎉 总结

通过将原始数据存储功能集成到训练阶段，您将获得：

1. **高质量数据**：明确标签的训练数据
2. **完整记录**：每次训练的完整数据记录
3. **便于分析**：支持后续的数据分析和模型训练
4. **标准化格式**：HDF5格式，便于数据交换

建议使用**方法1（增强补丁）**进行快速集成，这样可以最小化对现有代码的修改。

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统设置界面模块
System Settings UI Module

作者: AI Assistant
版本: 1.0.0
"""

import logging
from typing import Optional
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                               QGroupBox, QLabel, QPushButton, QLineEdit,
                               QSpinBox, QDoubleSpinBox, QComboBox, QCheckBox,
                               QTabWidget, QTextEdit, QMessageBox, QInputDialog)
from PySide6.QtCore import Qt

from core.database_manager import DatabaseManager
from utils.app_config import AppConfig


class SettingsWidget(QWidget):
    """系统设置界面组件"""

    def __init__(self, parent=None):
        super().__init__(parent)

        # 初始化属性
        self.db_manager: Optional[DatabaseManager] = None
        self.logger = logging.getLogger(__name__)

        # 初始化界面
        self.init_ui()

        self.logger.info("系统设置界面初始化完成")

    def init_ui(self):
        """初始化用户界面"""
        # 主布局
        main_layout = QVBoxLayout(self)

        # 创建标签页
        tab_widget = QTabWidget()

        # 系统配置标签页
        system_tab = self.create_system_config_tab()
        tab_widget.addTab(system_tab, "系统配置")

        # 设备配置标签页
        device_tab = self.create_device_config_tab()
        tab_widget.addTab(device_tab, "设备配置")

        # 数据库管理标签页
        database_tab = self.create_database_management_tab()
        tab_widget.addTab(database_tab, "数据库管理")

        main_layout.addWidget(tab_widget)

        # 底部按钮
        button_layout = QHBoxLayout()

        self.save_settings_button = QPushButton("保存设置")
        self.save_settings_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        self.save_settings_button.clicked.connect(self.save_settings)
        button_layout.addWidget(self.save_settings_button)

        self.reset_settings_button = QPushButton("重置设置")
        self.reset_settings_button.clicked.connect(self.reset_settings)
        button_layout.addWidget(self.reset_settings_button)

        button_layout.addStretch()

        main_layout.addLayout(button_layout)

    def create_system_config_tab(self) -> QWidget:
        """创建系统配置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 基本设置组
        basic_group = QGroupBox("基本设置")
        basic_layout = QGridLayout(basic_group)

        basic_layout.addWidget(QLabel("系统名称:"), 0, 0)
        self.system_name_edit = QLineEdit(AppConfig.APP_NAME)
        basic_layout.addWidget(self.system_name_edit, 0, 1)

        basic_layout.addWidget(QLabel("医院编号:"), 1, 0)
        hospital_id_layout = QHBoxLayout()
        self.hospital_id_edit = QLineEdit()
        self.hospital_id_edit.setReadOnly(True)  # 默认只读
        self.hospital_id_edit.setStyleSheet("background-color: #f0f0f0;")  # 灰色背景表示只读
        hospital_id_layout.addWidget(self.hospital_id_edit)

        # 厂家配置按钮
        self.factory_config_button = QPushButton("厂家配置")
        self.factory_config_button.setMaximumWidth(80)
        self.factory_config_button.clicked.connect(self.open_factory_config)
        hospital_id_layout.addWidget(self.factory_config_button)

        basic_layout.addLayout(hospital_id_layout, 1, 1)

        basic_layout.addWidget(QLabel("医院名称:"), 2, 0)
        self.hospital_name_edit = QLineEdit()
        basic_layout.addWidget(self.hospital_name_edit, 2, 1)

        basic_layout.addWidget(QLabel("科室名称:"), 3, 0)
        self.department_edit = QLineEdit()
        basic_layout.addWidget(self.department_edit, 3, 1)

        basic_layout.addWidget(QLabel("设备编号:"), 4, 0)
        self.device_id_edit = QLineEdit()
        basic_layout.addWidget(self.device_id_edit, 4, 1)

        layout.addWidget(basic_group)

        # 界面设置组
        ui_group = QGroupBox("界面设置")
        ui_layout = QGridLayout(ui_group)

        ui_layout.addWidget(QLabel("主题:"), 0, 0)
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["浅色主题", "深色主题"])
        ui_layout.addWidget(self.theme_combo, 0, 1)

        ui_layout.addWidget(QLabel("字体大小:"), 1, 0)
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 20)
        self.font_size_spin.setValue(AppConfig.UI_CONFIG.get('font_size', 10))
        ui_layout.addWidget(self.font_size_spin, 1, 1)

        ui_layout.addWidget(QLabel("语言:"), 2, 0)
        self.language_combo = QComboBox()
        self.language_combo.addItems(["中文", "English"])
        ui_layout.addWidget(self.language_combo, 2, 1)

        layout.addWidget(ui_group)

        # 日志设置组
        log_group = QGroupBox("日志设置")
        log_layout = QGridLayout(log_group)

        log_layout.addWidget(QLabel("日志级别:"), 0, 0)
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"])
        self.log_level_combo.setCurrentText(AppConfig.LOG_CONFIG.get('level', 'INFO'))
        log_layout.addWidget(self.log_level_combo, 0, 1)

        log_layout.addWidget(QLabel("日志文件大小(MB):"), 1, 0)
        self.log_file_size_spin = QSpinBox()
        self.log_file_size_spin.setRange(1, 100)
        self.log_file_size_spin.setValue(AppConfig.LOG_CONFIG.get('max_file_size', 10485760) // 1048576)
        log_layout.addWidget(self.log_file_size_spin, 1, 1)

        self.console_log_checkbox = QCheckBox("控制台输出")
        self.console_log_checkbox.setChecked(AppConfig.LOG_CONFIG.get('console_output', True))
        log_layout.addWidget(self.console_log_checkbox, 2, 0, 1, 2)

        layout.addWidget(log_group)

        layout.addStretch()

        return widget

    def create_device_config_tab(self) -> QWidget:
        """创建设备配置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 脑电设备组
        eeg_group = QGroupBox("脑电设备配置")
        eeg_layout = QGridLayout(eeg_group)

        eeg_layout.addWidget(QLabel("串口:"), 0, 0)
        self.serial_port_combo = QComboBox()
        self.serial_port_combo.addItems(["COM1", "COM2", "COM3", "COM4", "COM5", "COM6", "COM7", "COM8"])
        self.serial_port_combo.setCurrentText(AppConfig.EEG_CONFIG.get('serial_port', 'COM8'))
        eeg_layout.addWidget(self.serial_port_combo, 0, 1)

        eeg_layout.addWidget(QLabel("波特率:"), 1, 0)
        self.baud_rate_combo = QComboBox()
        self.baud_rate_combo.addItems(["9600", "19200", "38400", "57600", "115200"])
        self.baud_rate_combo.setCurrentText(str(AppConfig.EEG_CONFIG.get('baud_rate', 115200)))
        eeg_layout.addWidget(self.baud_rate_combo, 1, 1)

        eeg_layout.addWidget(QLabel("采样率(Hz):"), 2, 0)
        self.sample_rate_spin = QDoubleSpinBox()
        self.sample_rate_spin.setRange(50.0, 1000.0)
        self.sample_rate_spin.setValue(AppConfig.EEG_CONFIG.get('sample_rate', 125.0))
        eeg_layout.addWidget(self.sample_rate_spin, 2, 1)

        eeg_layout.addWidget(QLabel("通道数:"), 3, 0)
        self.channels_spin = QSpinBox()
        self.channels_spin.setRange(1, 32)
        self.channels_spin.setValue(AppConfig.EEG_CONFIG.get('channels', 8))
        eeg_layout.addWidget(self.channels_spin, 3, 1)

        layout.addWidget(eeg_group)

        # 信号处理组
        signal_group = QGroupBox("信号处理配置")
        signal_layout = QGridLayout(signal_group)

        filter_config = AppConfig.SIGNAL_PROCESSING_CONFIG.get('filter_config', {})

        signal_layout.addWidget(QLabel("高通滤波(Hz):"), 0, 0)
        self.highpass_spin = QDoubleSpinBox()
        self.highpass_spin.setRange(0.1, 10.0)
        self.highpass_spin.setValue(filter_config.get('highpass_freq', 0.5))
        signal_layout.addWidget(self.highpass_spin, 0, 1)

        signal_layout.addWidget(QLabel("低通滤波(Hz):"), 1, 0)
        self.lowpass_spin = QDoubleSpinBox()
        self.lowpass_spin.setRange(10.0, 100.0)
        self.lowpass_spin.setValue(filter_config.get('lowpass_freq', 50.0))
        signal_layout.addWidget(self.lowpass_spin, 1, 1)

        signal_layout.addWidget(QLabel("陷波滤波(Hz):"), 2, 0)
        self.notch_spin = QDoubleSpinBox()
        self.notch_spin.setRange(45.0, 55.0)
        self.notch_spin.setValue(filter_config.get('notch_freq', 50.0))
        signal_layout.addWidget(self.notch_spin, 2, 1)

        signal_layout.addWidget(QLabel("滤波器阶数:"), 3, 0)
        self.filter_order_spin = QSpinBox()
        self.filter_order_spin.setRange(2, 10)
        self.filter_order_spin.setValue(filter_config.get('filter_order', 4))
        signal_layout.addWidget(self.filter_order_spin, 3, 1)

        layout.addWidget(signal_group)

        # 电刺激设备组
        stimulation_group = QGroupBox("电刺激设备配置")
        stimulation_layout = QGridLayout(stimulation_group)

        stimulation_config = AppConfig.STIMULATION_CONFIG

        stimulation_layout.addWidget(QLabel("端口号:"), 0, 0)
        self.port_num_combo = QComboBox()
        self._populate_stimulation_ports()
        # 设置当前值
        current_port = stimulation_config.get('port_num', 1)
        self._set_current_port(current_port)
        stimulation_layout.addWidget(self.port_num_combo, 0, 1)

        stimulation_layout.addWidget(QLabel("最大电流(mA):"), 1, 0)
        self.max_current_spin = QSpinBox()
        self.max_current_spin.setRange(1, 100)
        self.max_current_spin.setValue(int(stimulation_config.get('max_current', 50)))
        stimulation_layout.addWidget(self.max_current_spin, 1, 1)

        stimulation_layout.addWidget(QLabel("最小电流(mA):"), 2, 0)
        self.min_current_spin = QSpinBox()
        self.min_current_spin.setRange(1, 10)
        self.min_current_spin.setValue(int(stimulation_config.get('min_current', 1)))
        stimulation_layout.addWidget(self.min_current_spin, 2, 1)

        stimulation_layout.addWidget(QLabel("电流步长(mA):"), 3, 0)
        self.current_step_spin = QSpinBox()
        self.current_step_spin.setRange(1, 1)  # 固定为1
        self.current_step_spin.setValue(1)
        self.current_step_spin.setEnabled(False)  # 禁用编辑，因为固定为1
        stimulation_layout.addWidget(self.current_step_spin, 3, 1)

        stimulation_layout.addWidget(QLabel("默认频率(Hz):"), 4, 0)
        self.default_frequency_spin = QSpinBox()
        self.default_frequency_spin.setRange(2, 160)
        self.default_frequency_spin.setValue(int(stimulation_config.get('default_frequency', 20)))
        stimulation_layout.addWidget(self.default_frequency_spin, 4, 1)

        stimulation_layout.addWidget(QLabel("默认脉宽(μs):"), 5, 0)
        self.default_pulse_width_spin = QSpinBox()
        self.default_pulse_width_spin.setRange(10, 500)
        self.default_pulse_width_spin.setValue(int(stimulation_config.get('default_pulse_width', 200)))
        stimulation_layout.addWidget(self.default_pulse_width_spin, 5, 1)

        stimulation_layout.addWidget(QLabel("默认休息时间(s):"), 6, 0)
        self.default_relax_time_spin = QSpinBox()
        self.default_relax_time_spin.setRange(0, 16)
        self.default_relax_time_spin.setValue(int(stimulation_config.get('default_relax_time', 5)))
        stimulation_layout.addWidget(self.default_relax_time_spin, 6, 1)

        stimulation_layout.addWidget(QLabel("默认工作时间(s):"), 7, 0)
        self.default_work_time_spin = QSpinBox()
        self.default_work_time_spin.setRange(0, 30)
        self.default_work_time_spin.setValue(int(stimulation_config.get('default_work_time', 10)))
        stimulation_layout.addWidget(self.default_work_time_spin, 7, 1)

        stimulation_layout.addWidget(QLabel("默认上升时间(s):"), 8, 0)
        self.default_climb_time_spin = QSpinBox()
        self.default_climb_time_spin.setRange(0, 5)
        self.default_climb_time_spin.setValue(int(stimulation_config.get('default_climb_time', 2)))
        stimulation_layout.addWidget(self.default_climb_time_spin, 8, 1)

        stimulation_layout.addWidget(QLabel("默认下降时间(s):"), 9, 0)
        self.default_fall_time_spin = QSpinBox()
        self.default_fall_time_spin.setRange(0, 5)
        self.default_fall_time_spin.setValue(int(stimulation_config.get('default_fall_time', 2)))
        stimulation_layout.addWidget(self.default_fall_time_spin, 9, 1)

        stimulation_layout.addWidget(QLabel("默认波形类型:"), 10, 0)
        self.default_wave_type_combo = QComboBox()
        self.default_wave_type_combo.addItems(["双相波", "单相波"])
        self.default_wave_type_combo.setCurrentIndex(stimulation_config.get('default_wave_type', 0))
        stimulation_layout.addWidget(self.default_wave_type_combo, 10, 1)

        stimulation_layout.addWidget(QLabel("连接超时(s):"), 11, 0)
        self.connection_timeout_spin = QSpinBox()
        self.connection_timeout_spin.setRange(1, 30)
        self.connection_timeout_spin.setValue(int(stimulation_config.get('connection_timeout', 5)))
        stimulation_layout.addWidget(self.connection_timeout_spin, 11, 1)

        layout.addWidget(stimulation_group)

        layout.addStretch()

        return widget



    def create_database_management_tab(self) -> QWidget:
        """创建数据库管理标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 数据库信息组
        info_group = QGroupBox("数据库信息")
        info_layout = QGridLayout(info_group)

        info_layout.addWidget(QLabel("数据库类型:"), 0, 0)
        self.db_type_label = QLabel("SQLite")
        info_layout.addWidget(self.db_type_label, 0, 1)

        info_layout.addWidget(QLabel("数据库路径:"), 1, 0)
        self.db_path_label = QLabel(str(AppConfig.DATABASE_CONFIG.get('path', '')))
        info_layout.addWidget(self.db_path_label, 1, 1)

        info_layout.addWidget(QLabel("数据库大小:"), 2, 0)
        self.db_size_label = QLabel("计算中...")
        info_layout.addWidget(self.db_size_label, 2, 1)

        layout.addWidget(info_group)

        # 数据库操作组
        operation_group = QGroupBox("数据库操作")
        operation_layout = QVBoxLayout(operation_group)

        button_layout = QHBoxLayout()

        self.backup_db_button = QPushButton("备份数据库")
        button_layout.addWidget(self.backup_db_button)

        self.restore_db_button = QPushButton("恢复数据库")
        button_layout.addWidget(self.restore_db_button)

        self.optimize_db_button = QPushButton("优化数据库")
        button_layout.addWidget(self.optimize_db_button)

        button_layout.addStretch()

        operation_layout.addLayout(button_layout)

        # 备份设置
        backup_layout = QGridLayout()

        backup_layout.addWidget(QLabel("自动备份:"), 0, 0)
        self.auto_backup_checkbox = QCheckBox("启用")
        self.auto_backup_checkbox.setChecked(AppConfig.DATABASE_CONFIG.get('auto_backup', True))
        backup_layout.addWidget(self.auto_backup_checkbox, 0, 1)

        backup_layout.addWidget(QLabel("备份间隔(小时):"), 1, 0)
        self.backup_interval_spin = QSpinBox()
        self.backup_interval_spin.setRange(1, 168)
        self.backup_interval_spin.setValue(AppConfig.DATABASE_CONFIG.get('backup_interval', 86400) // 3600)
        backup_layout.addWidget(self.backup_interval_spin, 1, 1)

        backup_layout.addWidget(QLabel("治疗时长(分钟):"), 2, 0)
        self.treatment_duration_spin = QSpinBox()
        self.treatment_duration_spin.setRange(10, 90)
        self.treatment_duration_spin.setValue(AppConfig.DATABASE_CONFIG.get('treatment_duration', 20))
        backup_layout.addWidget(self.treatment_duration_spin, 2, 1)

        backup_layout.addWidget(QLabel("治疗数据最小时长(分钟):"), 3, 0)
        self.min_treatment_duration_spin = QSpinBox()
        self.min_treatment_duration_spin.setRange(1, 60)
        self.min_treatment_duration_spin.setValue(AppConfig.DATABASE_CONFIG.get('min_treatment_duration', 5))
        backup_layout.addWidget(self.min_treatment_duration_spin, 3, 1)

        operation_layout.addLayout(backup_layout)

        layout.addWidget(operation_group)

        # 数据库状态
        status_group = QGroupBox("数据库状态")
        status_layout = QVBoxLayout(status_group)

        self.db_status_text = QTextEdit()
        self.db_status_text.setMaximumHeight(150)
        self.db_status_text.setReadOnly(True)
        self.db_status_text.setPlainText("数据库状态信息将在这里显示...")
        status_layout.addWidget(self.db_status_text)

        layout.addWidget(status_group)

        layout.addStretch()

        return widget

    def set_database_manager(self, db_manager: DatabaseManager):
        """设置数据库管理器"""
        self.db_manager = db_manager
        self.load_settings()
        self.logger.info("系统设置界面数据库管理器设置完成")

    def load_settings(self):
        """加载设置"""
        if not self.db_manager:
            return

        try:
            # 加载医院信息
            hospital_info = self.db_manager.get_hospital_info()
            if hospital_info:
                self.hospital_id_edit.setText(str(hospital_info.get('id', '')))
                self.hospital_name_edit.setText(hospital_info.get('hname', ''))
                self.department_edit.setText(hospital_info.get('keshi', ''))
                self.device_id_edit.setText(hospital_info.get('shebeiid', ''))

            # 更新数据库大小信息
            self.update_database_info()

        except Exception as e:
            self.logger.error(f"加载设置失败: {e}")

    def update_database_info(self):
        """更新数据库信息"""
        try:
            db_path = AppConfig.DATABASE_CONFIG.get('path')
            if db_path and db_path.exists():
                size_bytes = db_path.stat().st_size
                size_mb = size_bytes / (1024 * 1024)
                self.db_size_label.setText(f"{size_mb:.2f} MB")
            else:
                self.db_size_label.setText("未知")
        except Exception as e:
            self.logger.error(f"更新数据库信息失败: {e}")
            self.db_size_label.setText("错误")

    def _populate_stimulation_ports(self):
        """填充电刺激设备可用串口"""
        try:
            # 清空现有项目
            self.port_num_combo.clear()

            # 获取系统可用串口
            available_ports = []
            try:
                import serial.tools.list_ports
                ports = serial.tools.list_ports.comports()
                for port in ports:
                    if port.device.startswith('COM'):
                        available_ports.append(port.device)
                self.logger.debug(f"检测到系统串口: {available_ports}")
            except ImportError:
                self.logger.warning("pyserial未安装，无法检测系统串口")
            except Exception as e:
                self.logger.warning(f"检测系统串口失败: {e}")

            # 添加常用端口号（COM1-COM20）
            common_ports = [f"COM{i}" for i in range(1, 21)]

            # 合并并去重，优先显示系统检测到的端口
            all_ports = []

            # 先添加系统检测到的端口
            for port in available_ports:
                if port not in all_ports:
                    all_ports.append(port)

            # 再添加常用端口（如果不在系统检测列表中）
            for port in common_ports:
                if port not in all_ports:
                    all_ports.append(port)

            # 添加到下拉框（不显示可用标识）
            for port in all_ports:
                self.port_num_combo.addItem(port, port)

            self.logger.info(f"已填充电刺激设备端口列表，共{len(all_ports)}个端口")

        except Exception as e:
            self.logger.error(f"填充电刺激设备端口列表失败: {e}")
            # 如果出错，至少添加一些基本端口
            for i in range(1, 11):
                self.port_num_combo.addItem(f"COM{i}", f"COM{i}")

    def _set_current_port(self, port_num: int):
        """设置当前选中的端口号"""
        try:
            target_port = f"COM{port_num}"

            # 方法1: 先尝试通过 itemData 查找
            for i in range(self.port_num_combo.count()):
                item_data = self.port_num_combo.itemData(i)
                if item_data == target_port:
                    self.port_num_combo.setCurrentIndex(i)
                    self.logger.debug(f"通过itemData设置端口: {target_port} (索引: {i})")
                    return

            # 方法2: 通过文本精确匹配
            exact_index = self.port_num_combo.findText(target_port)
            if exact_index >= 0:
                self.port_num_combo.setCurrentIndex(exact_index)
                self.logger.debug(f"通过精确匹配设置端口: {target_port} (索引: {exact_index})")
                return



            # 如果都找不到，记录警告并设置为第一个选项
            self.logger.warning(f"未找到端口 {target_port}，设置为第一个选项")
            if self.port_num_combo.count() > 0:
                self.port_num_combo.setCurrentIndex(0)

        except Exception as e:
            self.logger.error(f"设置当前端口失败: {e}")
            # 设置为第一个选项作为备选
            if self.port_num_combo.count() > 0:
                self.port_num_combo.setCurrentIndex(0)

    def save_settings(self):
        """保存设置"""
        try:
            self.logger.info("开始保存设置")

            # 获取原始配置值进行比较（在修改之前）
            # 电刺激设备配置原始值
            original_stimulation_port = AppConfig.STIMULATION_CONFIG.get('port_num', 1)
            original_max_current = AppConfig.STIMULATION_CONFIG.get('max_current', 50)
            original_min_current = AppConfig.STIMULATION_CONFIG.get('min_current', 1)
            original_current_step = AppConfig.STIMULATION_CONFIG.get('current_step', 1)
            original_frequency = AppConfig.STIMULATION_CONFIG.get('default_frequency', 20)
            original_pulse_width = AppConfig.STIMULATION_CONFIG.get('default_pulse_width', 200)
            original_relax_time = AppConfig.STIMULATION_CONFIG.get('default_relax_time', 5)
            original_work_time = AppConfig.STIMULATION_CONFIG.get('default_work_time', 10)
            original_climb_time = AppConfig.STIMULATION_CONFIG.get('default_climb_time', 2)
            original_fall_time = AppConfig.STIMULATION_CONFIG.get('default_fall_time', 2)
            original_wave_type = AppConfig.STIMULATION_CONFIG.get('default_wave_type', 0)
            original_timeout = AppConfig.STIMULATION_CONFIG.get('connection_timeout', 5)

            # 脑电设备配置原始值
            original_eeg_port = AppConfig.EEG_CONFIG.get('serial_port', 'COM8')
            original_eeg_baud = AppConfig.EEG_CONFIG.get('baud_rate', 115200)
            original_eeg_sample_rate = AppConfig.EEG_CONFIG.get('sample_rate', 125.0)
            original_eeg_channels = AppConfig.EEG_CONFIG.get('channels', 8)

            # 信号处理配置原始值
            original_highpass = AppConfig.SIGNAL_PROCESSING_CONFIG['filter_config'].get('highpass_freq', 0.5)
            original_lowpass = AppConfig.SIGNAL_PROCESSING_CONFIG['filter_config'].get('lowpass_freq', 50.0)
            original_notch = AppConfig.SIGNAL_PROCESSING_CONFIG['filter_config'].get('notch_freq', 50.0)
            original_filter_order = AppConfig.SIGNAL_PROCESSING_CONFIG['filter_config'].get('filter_order', 4)

            # UI配置原始值
            original_font_size = AppConfig.UI_CONFIG.get('font_size', 10)

            # 日志配置原始值
            original_log_level = AppConfig.LOG_CONFIG.get('level', 'INFO')
            original_log_file_size = AppConfig.LOG_CONFIG.get('max_file_size', 10485760) // 1048576  # 转换为MB
            original_console_output = AppConfig.LOG_CONFIG.get('console_output', True)

            # 数据库配置原始值（如果有的话）
            original_auto_backup = AppConfig.DATABASE_CONFIG.get('auto_backup', True)
            original_backup_interval = AppConfig.DATABASE_CONFIG.get('backup_interval', 86400) // 3600  # 转换为小时
            original_treatment_duration = AppConfig.DATABASE_CONFIG.get('treatment_duration', 20)
            original_min_treatment_duration = AppConfig.DATABASE_CONFIG.get('min_treatment_duration', 5)

            # 获取原始医院信息
            original_hospital_info = {}
            if self.db_manager:
                original_hospital_info = self.db_manager.get_hospital_info() or {}

            # 获取当前界面值
            # 电刺激设备配置
            port_text = self.port_num_combo.currentData() or self.port_num_combo.currentText()
            if port_text and port_text.startswith('COM'):
                try:
                    if ' (' in port_text:
                        port_text = port_text.split(' (')[0]
                    port_num = int(port_text[3:])
                except (ValueError, IndexError):
                    port_num = 1
            else:
                port_num = 1

            current_max_current = self.max_current_spin.value()
            current_min_current = self.min_current_spin.value()
            current_current_step = self.current_step_spin.value()
            current_frequency = self.default_frequency_spin.value()
            current_pulse_width = self.default_pulse_width_spin.value()
            current_relax_time = self.default_relax_time_spin.value()
            current_work_time = self.default_work_time_spin.value()
            current_climb_time = self.default_climb_time_spin.value()
            current_fall_time = self.default_fall_time_spin.value()
            current_wave_type = self.default_wave_type_combo.currentIndex()
            current_timeout = self.connection_timeout_spin.value()

            # 脑电设备配置
            current_eeg_port = self.serial_port_combo.currentText()
            current_eeg_baud = int(self.baud_rate_combo.currentText())
            current_eeg_sample_rate = self.sample_rate_spin.value()
            current_eeg_channels = self.channels_spin.value()

            # 信号处理配置
            current_highpass = self.highpass_spin.value()
            current_lowpass = self.lowpass_spin.value()
            current_notch = self.notch_spin.value()
            current_filter_order = self.filter_order_spin.value()

            # UI配置
            current_font_size = self.font_size_spin.value()

            # 日志配置
            current_log_level = self.log_level_combo.currentText()
            current_log_file_size = self.log_file_size_spin.value()
            current_console_output = self.console_log_checkbox.isChecked()

            # 数据库配置
            current_auto_backup = self.auto_backup_checkbox.isChecked()
            current_backup_interval = self.backup_interval_spin.value()
            current_treatment_duration = self.treatment_duration_spin.value()
            current_min_treatment_duration = self.min_treatment_duration_spin.value()

            # 医院信息
            current_hospital_info = {
                'hname': self.hospital_name_edit.text().strip(),
                'keshi': self.department_edit.text().strip(),
                'shebeiid': self.device_id_edit.text().strip()
            }

            # 检测变化并记录
            changed_items = []
            saved_components = []

            # 检查医院信息变化
            hospital_changed = False
            hospital_changes = []
            if self.db_manager:
                if current_hospital_info['hname'] != original_hospital_info.get('hname', ''):
                    hospital_changes.append(f"医院名称: {original_hospital_info.get('hname', '')} → {current_hospital_info['hname']}")
                    hospital_changed = True
                if current_hospital_info['keshi'] != original_hospital_info.get('keshi', ''):
                    hospital_changes.append(f"科室名称: {original_hospital_info.get('keshi', '')} → {current_hospital_info['keshi']}")
                    hospital_changed = True
                if current_hospital_info['shebeiid'] != original_hospital_info.get('shebeiid', ''):
                    hospital_changes.append(f"设备编号: {original_hospital_info.get('shebeiid', '')} → {current_hospital_info['shebeiid']}")
                    hospital_changed = True

                if hospital_changed:
                    if self.db_manager.update_hospital_info(current_hospital_info):
                        self.logger.info(f"医院信息更新成功: {', '.join(hospital_changes)}")
                        saved_components.append("医院信息")
                        changed_items.extend(hospital_changes)
                    else:
                        self.logger.error("医院信息保存失败")
                        QMessageBox.critical(
                            self,
                            "保存失败",
                            "医院信息保存失败，请检查数据库连接。"
                        )
                        return

            # 检查电刺激设备配置变化
            stimulation_changes = []
            if port_num != original_stimulation_port:
                stimulation_changes.append(f"端口号: COM{original_stimulation_port} → COM{port_num}")
            if current_max_current != original_max_current:
                stimulation_changes.append(f"最大电流: {original_max_current}mA → {current_max_current}mA")
            if current_min_current != original_min_current:
                stimulation_changes.append(f"最小电流: {original_min_current}mA → {current_min_current}mA")
            if current_current_step != original_current_step:
                stimulation_changes.append(f"电流步长: {original_current_step}mA → {current_current_step}mA")
            if current_frequency != original_frequency:
                stimulation_changes.append(f"默认频率: {original_frequency}Hz → {current_frequency}Hz")
            if current_pulse_width != original_pulse_width:
                stimulation_changes.append(f"默认脉宽: {original_pulse_width}μs → {current_pulse_width}μs")
            if current_relax_time != original_relax_time:
                stimulation_changes.append(f"默认休息时间: {original_relax_time}s → {current_relax_time}s")
            if current_work_time != original_work_time:
                stimulation_changes.append(f"默认工作时间: {original_work_time}s → {current_work_time}s")
            if current_climb_time != original_climb_time:
                stimulation_changes.append(f"默认上升时间: {original_climb_time}s → {current_climb_time}s")
            if current_fall_time != original_fall_time:
                stimulation_changes.append(f"默认下降时间: {original_fall_time}s → {current_fall_time}s")
            if current_wave_type != original_wave_type:
                wave_types = ["双相波", "单相波"]
                stimulation_changes.append(f"默认波形类型: {wave_types[original_wave_type]} → {wave_types[current_wave_type]}")
            if current_timeout != original_timeout:
                stimulation_changes.append(f"连接超时: {original_timeout}s → {current_timeout}s")

            # 更新电刺激配置
            AppConfig.STIMULATION_CONFIG['port_num'] = port_num
            AppConfig.STIMULATION_CONFIG['max_current'] = current_max_current
            AppConfig.STIMULATION_CONFIG['min_current'] = current_min_current
            AppConfig.STIMULATION_CONFIG['current_step'] = current_current_step
            AppConfig.STIMULATION_CONFIG['default_frequency'] = current_frequency
            AppConfig.STIMULATION_CONFIG['default_pulse_width'] = current_pulse_width
            AppConfig.STIMULATION_CONFIG['default_relax_time'] = current_relax_time
            AppConfig.STIMULATION_CONFIG['default_work_time'] = current_work_time
            AppConfig.STIMULATION_CONFIG['default_climb_time'] = current_climb_time
            AppConfig.STIMULATION_CONFIG['default_fall_time'] = current_fall_time
            AppConfig.STIMULATION_CONFIG['default_wave_type'] = current_wave_type
            AppConfig.STIMULATION_CONFIG['connection_timeout'] = current_timeout

            if stimulation_changes:
                self.logger.info(f"电刺激设备配置更新成功: {', '.join(stimulation_changes)}")
                saved_components.append("电刺激设备配置")
                changed_items.extend(stimulation_changes)

            # 检查脑电设备配置变化
            eeg_changes = []
            if current_eeg_port != original_eeg_port:
                eeg_changes.append(f"端口: {original_eeg_port} → {current_eeg_port}")
            if current_eeg_baud != original_eeg_baud:
                eeg_changes.append(f"波特率: {original_eeg_baud} → {current_eeg_baud}")
            if current_eeg_sample_rate != original_eeg_sample_rate:
                eeg_changes.append(f"采样率: {original_eeg_sample_rate} → {current_eeg_sample_rate}")
            if current_eeg_channels != original_eeg_channels:
                eeg_changes.append(f"通道数: {original_eeg_channels} → {current_eeg_channels}")

            # 更新脑电配置
            AppConfig.EEG_CONFIG['serial_port'] = current_eeg_port
            AppConfig.EEG_CONFIG['baud_rate'] = current_eeg_baud
            AppConfig.EEG_CONFIG['sample_rate'] = current_eeg_sample_rate
            AppConfig.EEG_CONFIG['channels'] = current_eeg_channels

            if eeg_changes:
                self.logger.info(f"脑电设备配置更新成功: {', '.join(eeg_changes)}")
                saved_components.append("脑电设备配置")
                changed_items.extend(eeg_changes)

            # 检查信号处理配置变化
            signal_changes = []
            if current_highpass != original_highpass:
                signal_changes.append(f"高通滤波: {original_highpass}Hz → {current_highpass}Hz")
            if current_lowpass != original_lowpass:
                signal_changes.append(f"低通滤波: {original_lowpass}Hz → {current_lowpass}Hz")
            if current_notch != original_notch:
                signal_changes.append(f"陷波滤波: {original_notch}Hz → {current_notch}Hz")
            if current_filter_order != original_filter_order:
                signal_changes.append(f"滤波器阶数: {original_filter_order} → {current_filter_order}")

            # 更新信号处理配置
            AppConfig.SIGNAL_PROCESSING_CONFIG['filter_config']['highpass_freq'] = current_highpass
            AppConfig.SIGNAL_PROCESSING_CONFIG['filter_config']['lowpass_freq'] = current_lowpass
            AppConfig.SIGNAL_PROCESSING_CONFIG['filter_config']['notch_freq'] = current_notch
            AppConfig.SIGNAL_PROCESSING_CONFIG['filter_config']['filter_order'] = current_filter_order

            if signal_changes:
                self.logger.info(f"信号处理配置更新成功: {', '.join(signal_changes)}")
                saved_components.append("信号处理配置")
                changed_items.extend(signal_changes)

            # 检查UI配置变化
            ui_changes = []
            if current_font_size != original_font_size:
                ui_changes.append(f"字体大小: {original_font_size} → {current_font_size}")

            # 更新UI配置
            AppConfig.UI_CONFIG['font_size'] = current_font_size

            if ui_changes:
                self.logger.info(f"UI配置更新成功: {', '.join(ui_changes)}")
                saved_components.append("UI配置")
                changed_items.extend(ui_changes)

            # 检查日志配置变化
            log_changes = []
            if current_log_level != original_log_level:
                log_changes.append(f"日志级别: {original_log_level} → {current_log_level}")
            if current_log_file_size != original_log_file_size:
                log_changes.append(f"日志文件大小: {original_log_file_size}MB → {current_log_file_size}MB")
            if current_console_output != original_console_output:
                log_changes.append(f"控制台输出: {'开启' if original_console_output else '关闭'} → {'开启' if current_console_output else '关闭'}")

            # 更新日志配置
            AppConfig.LOG_CONFIG['level'] = current_log_level
            AppConfig.LOG_CONFIG['max_file_size'] = current_log_file_size * 1048576  # 转换为字节
            AppConfig.LOG_CONFIG['console_output'] = current_console_output

            if log_changes:
                self.logger.info(f"日志配置更新成功: {', '.join(log_changes)}")
                saved_components.append("日志配置")
                changed_items.extend(log_changes)

            # 检查数据库配置变化
            database_changes = []
            if current_auto_backup != original_auto_backup:
                database_changes.append(f"自动备份: {'开启' if original_auto_backup else '关闭'} → {'开启' if current_auto_backup else '关闭'}")
            if current_backup_interval != original_backup_interval:
                database_changes.append(f"备份间隔: {original_backup_interval}小时 → {current_backup_interval}小时")
            if current_treatment_duration != original_treatment_duration:
                database_changes.append(f"治疗时长: {original_treatment_duration}分钟 → {current_treatment_duration}分钟")
            if current_min_treatment_duration != original_min_treatment_duration:
                database_changes.append(f"治疗数据最小时长: {original_min_treatment_duration}分钟 → {current_min_treatment_duration}分钟")

            # 更新数据库配置
            AppConfig.DATABASE_CONFIG['auto_backup'] = current_auto_backup
            AppConfig.DATABASE_CONFIG['backup_interval'] = current_backup_interval * 3600  # 转换为秒
            AppConfig.DATABASE_CONFIG['treatment_duration'] = current_treatment_duration
            AppConfig.DATABASE_CONFIG['min_treatment_duration'] = current_min_treatment_duration

            if database_changes:
                self.logger.info(f"数据库配置更新成功: {', '.join(database_changes)}")
                saved_components.append("数据库配置")
                changed_items.extend(database_changes)

            # 保存配置到文件
            if AppConfig.save_user_config():
                self.logger.info("用户配置已保存到文件")
            else:
                self.logger.warning("保存用户配置到文件失败")

            # 显示保存结果
            if changed_items:
                # 有变化时显示具体变化内容
                message = "设置保存成功！\n\n已更新的配置项：\n\n" + "\n".join(f"• {item}" for item in changed_items) + "\n\n配置将立即生效。"
                QMessageBox.information(
                    self,
                    "保存成功",
                    message
                )
                self.logger.info(f"设置保存完成，已更新组件: {', '.join(saved_components)}")
            else:
                # 无变化时明确提示
                message = "未检测到任何配置变化。\n\n当前设置已是最新状态，无需保存。"
                QMessageBox.information(
                    self,
                    "无变化",
                    message
                )
                self.logger.info("设置保存完成，无配置变化")

        except Exception as e:
            self.logger.error(f"保存设置失败: {e}")
            QMessageBox.critical(
                self,
                "保存失败",
                f"保存设置时发生错误：\n{e}"
            )

    def reset_settings(self):
        """重置设置"""
        try:
            reply = QMessageBox.question(
                self,
                "重置设置",
                "确定要重置所有设置到默认值吗？\n此操作不可撤销。",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                # 重置电刺激设备配置
                # 重置端口号下拉框到COM1
                self._set_current_port(1)
                self.max_current_spin.setValue(50)
                self.min_current_spin.setValue(1)
                self.current_step_spin.setValue(1)
                self.default_frequency_spin.setValue(20)
                self.default_pulse_width_spin.setValue(200)
                self.default_relax_time_spin.setValue(5)
                self.default_work_time_spin.setValue(10)
                self.default_climb_time_spin.setValue(2)
                self.default_fall_time_spin.setValue(2)
                self.default_wave_type_combo.setCurrentIndex(0)
                self.connection_timeout_spin.setValue(5)

                # 重置脑电设备配置
                self.serial_port_combo.setCurrentText('COM8')
                self.baud_rate_combo.setCurrentText('115200')
                self.sample_rate_spin.setValue(125.0)
                self.channels_spin.setValue(8)

                # 重置信号处理配置
                self.highpass_spin.setValue(0.5)
                self.lowpass_spin.setValue(50.0)
                self.notch_spin.setValue(50.0)
                self.filter_order_spin.setValue(4)

                # 重置UI配置
                self.font_size_spin.setValue(10)

                # 重置日志配置
                self.log_level_combo.setCurrentText('INFO')
                self.log_file_size_spin.setValue(10)
                self.console_log_checkbox.setChecked(True)

                # 重置数据库配置
                self.auto_backup_checkbox.setChecked(True)
                self.backup_interval_spin.setValue(24)
                self.treatment_duration_spin.setValue(20)
                self.min_treatment_duration_spin.setValue(5)

                QMessageBox.information(
                    self,
                    "重置完成",
                    "设置已重置到默认值。\n请点击\"保存设置\"按钮保存更改。"
                )

                self.logger.info("设置已重置到默认值")

        except Exception as e:
            self.logger.error(f"重置设置失败: {e}")
            QMessageBox.critical(
                self,
                "重置失败",
                f"重置设置时发生错误：\n{e}"
            )





    def open_factory_config(self):
        """打开厂家配置对话框"""
        try:
            # 验证厂家权限
            password, ok = QInputDialog.getText(
                self,
                "厂家配置验证",
                "请输入厂家配置密码:",
                QLineEdit.EchoMode.Password
            )

            if not ok:
                return

            # 厂家密码验证（可以从配置文件读取或硬编码）
            factory_password = AppConfig.get_factory_password()  # 需要在AppConfig中添加
            if password != factory_password:
                QMessageBox.warning(
                    self,
                    "验证失败",
                    "厂家配置密码错误！"
                )
                return

            # 显示厂家配置对话框
            self.show_factory_config_dialog()

        except Exception as e:
            self.logger.error(f"打开厂家配置失败: {e}")
            QMessageBox.critical(
                self,
                "错误",
                f"打开厂家配置时发生错误：\n{e}"
            )

    def show_factory_config_dialog(self):
        """显示厂家配置对话框"""
        try:
            from PySide6.QtWidgets import QDialog, QVBoxLayout, QFormLayout, QDialogButtonBox

            dialog = QDialog(self)
            dialog.setWindowTitle("厂家配置 - 医院编号设置")
            dialog.setModal(True)
            dialog.resize(400, 200)

            layout = QVBoxLayout(dialog)

            # 说明文本
            info_label = QLabel(
                "⚠️ 警告：修改医院编号会影响所有相关数据！\n"
                "请确保在系统初始化时或经过充分备份后进行此操作。"
            )
            info_label.setStyleSheet("color: red; font-weight: bold; padding: 10px;")
            layout.addWidget(info_label)

            # 表单布局
            form_layout = QFormLayout()

            # 当前医院编号
            current_info = self.db_manager.get_hospital_info() if self.db_manager else {}
            current_id = current_info.get('id', 1)

            current_id_label = QLabel(str(current_id))
            current_id_label.setStyleSheet("font-weight: bold;")
            form_layout.addRow("当前医院编号:", current_id_label)

            # 新医院编号输入
            new_id_edit = QLineEdit()
            new_id_edit.setPlaceholderText("请输入新的医院编号（1-999）")
            new_id_edit.setText(str(current_id))
            form_layout.addRow("新医院编号:", new_id_edit)

            layout.addLayout(form_layout)

            # 按钮
            button_box = QDialogButtonBox(
                QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
            )
            button_box.accepted.connect(dialog.accept)
            button_box.rejected.connect(dialog.reject)
            layout.addWidget(button_box)

            # 显示对话框
            if dialog.exec() == QDialog.DialogCode.Accepted:
                try:
                    new_id = int(new_id_edit.text())
                    if new_id < 1 or new_id > 999:
                        raise ValueError("医院编号必须在1-999之间")

                    if new_id != current_id:
                        # 执行医院编号更新
                        self.update_hospital_id_safely(current_id, new_id)
                    else:
                        QMessageBox.information(self, "提示", "医院编号未发生变化。")

                except ValueError as e:
                    QMessageBox.warning(self, "输入错误", f"医院编号格式错误：{e}")
                except Exception as e:
                    QMessageBox.critical(self, "更新失败", f"更新医院编号时发生错误：{e}")

        except Exception as e:
            self.logger.error(f"显示厂家配置对话框失败: {e}")
            QMessageBox.critical(
                self,
                "错误",
                f"显示厂家配置对话框时发生错误：\n{e}"
            )

    def update_hospital_id_safely(self, old_id: int, new_id: int):
        """安全地更新医院编号"""
        try:
            if not self.db_manager:
                raise Exception("数据库管理器未初始化")

            # 确认操作
            reply = QMessageBox.question(
                self,
                "确认更新",
                f"确定要将医院编号从 {old_id} 更改为 {new_id} 吗？\n\n"
                "此操作将：\n"
                "1. 更新医院信息表\n"
                "2. 更新所有相关患者记录\n"
                "3. 可能需要较长时间\n\n"
                "建议在操作前备份数据库！",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply != QMessageBox.StandardButton.Yes:
                return

            # 执行更新
            success = self.db_manager.update_hospital_id_safely(old_id, new_id)

            if success:
                # 更新UI显示
                self.hospital_id_edit.setText(str(new_id))
                QMessageBox.information(
                    self,
                    "更新成功",
                    f"医院编号已成功更新为：{new_id}\n\n"
                    "相关数据已同步更新。"
                )
                self.logger.info(f"医院编号已从 {old_id} 更新为 {new_id}")
            else:
                QMessageBox.critical(
                    self,
                    "更新失败",
                    "医院编号更新失败，请检查日志获取详细信息。"
                )

        except Exception as e:
            self.logger.error(f"安全更新医院编号失败: {e}")
            QMessageBox.critical(
                self,
                "更新失败",
                f"更新医院编号时发生错误：\n{e}"
            )



    def cleanup(self):
        """清理资源"""
        try:
            self.logger.info("系统设置界面资源清理完成")
        except Exception as e:
            self.logger.error(f"系统设置界面资源清理失败: {e}")

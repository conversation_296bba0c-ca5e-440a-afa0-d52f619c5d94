#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理模块
Database Management Module

作者: AI Assistant
版本: 1.0.0
"""

import sqlite3
import logging
import threading
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any
from contextlib import contextmanager

from utils.app_config import AppConfig


class DatabaseManager:
    """数据库管理类"""

    def __init__(self):
        self.db_config = AppConfig.get_config('database')
        self.db_path = Path(self.db_config['path'])
        self.backup_path = Path(self.db_config['backup_path'])
        self._connection_pool = {}
        self._lock = threading.RLock()
        self.logger = logging.getLogger(__name__)

    def initialize(self) -> bool:
        """初始化数据库"""
        try:
            # 确保数据库目录存在
            self.db_path.parent.mkdir(parents=True, exist_ok=True)
            self.backup_path.mkdir(parents=True, exist_ok=True)

            # 创建数据库表
            if not self._create_tables():
                return False

            # 执行数据库迁移
            if not self._migrate_database():
                return False

            # 初始化基础数据
            if not self._initialize_base_data():
                return False

            # 设置自动备份
            if self.db_config.get('auto_backup', True):
                self._setup_auto_backup()

            self.logger.info("数据库初始化成功")
            return True

        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            return False

    @contextmanager
    def get_connection(self):
        """获取数据库连接（上下文管理器）"""
        thread_id = threading.get_ident()

        with self._lock:
            if thread_id not in self._connection_pool:
                conn = sqlite3.connect(
                    str(self.db_path),
                    check_same_thread=False,
                    timeout=30.0
                )
                conn.row_factory = sqlite3.Row  # 使结果可以按列名访问
                conn.execute("PRAGMA foreign_keys = ON")  # 启用外键约束
                self._connection_pool[thread_id] = conn

            connection = self._connection_pool[thread_id]

        try:
            yield connection
        except Exception as e:
            connection.rollback()
            self.logger.error(f"数据库操作异常: {e}")
            raise
        finally:
            connection.commit()

    def _create_tables(self) -> bool:
        """创建数据库表"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 医院信息表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS yiyuan (
                        id INTEGER PRIMARY KEY,
                        hname TEXT NOT NULL,
                        keshi TEXT NOT NULL,
                        shebeiid TEXT NOT NULL,
                        created_at TIMESTAMP DEFAULT (datetime('now', 'localtime')),
                        updated_at TIMESTAMP DEFAULT (datetime('now', 'localtime'))
                    )
                """)

                # 操作员表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS operator (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name TEXT NOT NULL UNIQUE,
                        password TEXT NOT NULL,
                        role TEXT DEFAULT 'operator',
                        permissions TEXT,
                        last_login TIMESTAMP,
                        is_active BOOLEAN DEFAULT 1,
                        created_at TIMESTAMP DEFAULT (datetime('now', 'localtime')),
                        updated_at TIMESTAMP DEFAULT (datetime('now', 'localtime'))
                    )
                """)

                # 医生表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS doctor (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name TEXT NOT NULL UNIQUE,
                        title TEXT,
                        department TEXT,
                        phone TEXT,
                        email TEXT,
                        is_active BOOLEAN DEFAULT 1,
                        created_at TIMESTAMP DEFAULT (datetime('now', 'localtime')),
                        updated_at TIMESTAMP DEFAULT (datetime('now', 'localtime'))
                    )
                """)

                # 患者信息表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS bingren (
                        bianhao INTEGER PRIMARY KEY,
                        name TEXT NOT NULL,
                        age INTEGER NOT NULL,
                        xingbie TEXT NOT NULL,
                        cardid TEXT,
                        zhenduan TEXT,
                        bingshi TEXT,
                        shebeiid TEXT,
                        yiyuanid INTEGER,
                        keshi TEXT,
                        zhuzhi TEXT,
                        czy TEXT,
                        lrshijian TIMESTAMP DEFAULT (datetime('now', 'localtime')),
                        status TEXT DEFAULT 'normal',
                        brhc TEXT,
                        updated_at TIMESTAMP DEFAULT (datetime('now', 'localtime')),
                        FOREIGN KEY (yiyuanid) REFERENCES yiyuan (id)
                    )
                """)

                # 治疗记录表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS zhiliao (
                        zhiliaobh INTEGER PRIMARY KEY AUTOINCREMENT,
                        bianh INTEGER NOT NULL,
                        rq DATE NOT NULL,
                        shijian TIME NOT NULL,
                        defen REAL,
                        yaoqiucs INTEGER,
                        shijics INTEGER,
                        zlsj REAL,
                        czy TEXT,
                        zhuzhi TEXT,
                        zlms TEXT,
                        start_time TIMESTAMP,
                        end_time TIMESTAMP,
                        notes TEXT,
                        upload_status TEXT DEFAULT '0',
                        FOREIGN KEY (bianh) REFERENCES bingren (bianhao)
                    )
                """)

                # 脑电数据表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS Edata (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        ebianhao INTEGER NOT NULL,
                        ename TEXT,
                        eshijian TIMESTAMP DEFAULT (datetime('now', 'localtime')),
                        channel_data TEXT,
                        theta REAL,
                        alpha REAL,
                        low_beta REAL,
                        high_beta REAL,
                        gamma REAL,
                        state INTEGER
                    )
                """)

                # 系统日志表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS system_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp TIMESTAMP DEFAULT (datetime('now', 'localtime')),
                        level TEXT NOT NULL,
                        logger_name TEXT,
                        message TEXT NOT NULL,
                        user_id TEXT,
                        session_id TEXT,
                        ip_address TEXT
                    )
                """)

                # 操作审计表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS audit_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp TIMESTAMP DEFAULT (datetime('now', 'localtime')),
                        user_id TEXT NOT NULL,
                        action TEXT NOT NULL,
                        resource TEXT,
                        resource_id TEXT,
                        old_values TEXT,
                        new_values TEXT,
                        result TEXT,
                        ip_address TEXT,
                        user_agent TEXT
                    )
                """)

                # 设备状态表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS device_status (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        device_name TEXT NOT NULL,
                        device_type TEXT NOT NULL,
                        status TEXT NOT NULL,
                        last_update TIMESTAMP DEFAULT (datetime('now', 'localtime')),
                        details TEXT,
                        error_message TEXT
                    )
                """)

                # 检查并添加缺失的字段
                self._add_missing_columns(cursor)

                # 创建索引
                self._create_indexes(cursor)

                self.logger.info("数据库表创建成功")
                return True

        except Exception as e:
            self.logger.error(f"创建数据库表失败: {e}")
            return False

    def _add_missing_columns(self, cursor):
        """检查并添加缺失的字段"""
        try:
            # 检查zhiliao表是否有upload_status字段
            cursor.execute("PRAGMA table_info(zhiliao)")
            columns = cursor.fetchall()
            column_names = [col[1] for col in columns]

            if 'upload_status' not in column_names:
                self.logger.info("添加zhiliao表的upload_status字段")
                cursor.execute("ALTER TABLE zhiliao ADD COLUMN upload_status TEXT DEFAULT '0'")

            if 'treat_number' not in column_names:
                self.logger.info("添加zhiliao表的treat_number字段")
                cursor.execute("ALTER TABLE zhiliao ADD COLUMN treat_number INTEGER DEFAULT 1")

        except Exception as e:
            self.logger.warning(f"添加缺失字段失败: {e}")

    def _create_indexes(self, cursor):
        """创建数据库索引"""
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_bingren_name ON bingren(name)",
            "CREATE INDEX IF NOT EXISTS idx_bingren_cardid ON bingren(cardid)",
            "CREATE INDEX IF NOT EXISTS idx_zhiliao_bianh ON zhiliao(bianh)",
            "CREATE INDEX IF NOT EXISTS idx_zhiliao_rq ON zhiliao(rq)",
            "CREATE INDEX IF NOT EXISTS idx_edata_ebianhao ON Edata(ebianhao)",
            "CREATE INDEX IF NOT EXISTS idx_edata_eshijian ON Edata(eshijian)",
            "CREATE INDEX IF NOT EXISTS idx_system_logs_timestamp ON system_logs(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp ON audit_logs(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id)",
        ]

        for index_sql in indexes:
            try:
                cursor.execute(index_sql)
            except Exception as e:
                self.logger.warning(f"创建索引失败: {index_sql}, 错误: {e}")

    def _initialize_base_data(self) -> bool:
        """初始化基础数据"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 检查是否已有医院信息
                cursor.execute("SELECT COUNT(*) FROM yiyuan")
                if cursor.fetchone()[0] == 0:
                    # 插入默认医院信息
                    cursor.execute("""
                        INSERT INTO yiyuan (id, hname, keshi, shebeiid)
                        VALUES (1, '默认医院', '康复科', 'NK001')
                    """)

                # 检查是否已有默认管理员
                cursor.execute("SELECT COUNT(*) FROM operator WHERE role = 'admin'")
                if cursor.fetchone()[0] == 0:
                    # 插入默认管理员账户（密码：admin123）
                    import hashlib
                    salt = "NK_BCI_SYSTEM_2024"
                    # 计算正确的密码哈希值
                    password_hash = hashlib.sha256(("admin123" + salt).encode()).hexdigest()
                    self.logger.info("创建默认管理员账户")
                    cursor.execute("""
                        INSERT INTO operator (name, password, role, permissions)
                        VALUES ('admin', ?, 'admin', 'all')
                    """, (password_hash,))

                # 检查是否已有默认医生
                cursor.execute("SELECT COUNT(*) FROM doctor")
                if cursor.fetchone()[0] == 0:
                    # 插入默认医生数据
                    default_doctors = [
                        ('张医生', '主任医师', '康复科', '13800138001', '<EMAIL>'),
                        ('李医生', '副主任医师', '康复科', '13800138002', '<EMAIL>'),
                        ('王医生', '主治医师', '康复科', '13800138003', '<EMAIL>'),
                        ('刘医生', '主治医师', '康复科', '13800138004', '<EMAIL>'),
                        ('陈医生', '住院医师', '康复科', '13800138005', '<EMAIL>')
                    ]

                    for doctor in default_doctors:
                        cursor.execute("""
                            INSERT INTO doctor (name, title, department, phone, email)
                            VALUES (?, ?, ?, ?, ?)
                        """, doctor)

                    self.logger.info(f"创建了 {len(default_doctors)} 个默认医生账户")

                self.logger.info("基础数据初始化成功")
                return True

        except Exception as e:
            self.logger.error(f"基础数据初始化失败: {e}")
            return False

    def _migrate_database(self) -> bool:
        """执行数据库迁移"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 检查是否需要迁移时间戳字段
                cursor.execute("PRAGMA table_info(bingren)")
                columns = cursor.fetchall()

                # 检查lrshijian字段的默认值
                lrshijian_default = None
                for col in columns:
                    if col[1] == 'lrshijian':  # 列名
                        lrshijian_default = col[4]  # 默认值
                        break

                # 如果默认值还是CURRENT_TIMESTAMP，需要迁移
                if lrshijian_default == 'CURRENT_TIMESTAMP':
                    self.logger.info("检测到需要迁移时间戳字段，开始迁移...")

                    # 备份当前数据库
                    backup_name = f"before_timestamp_migration_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
                    if not self.backup_database(backup_name):
                        self.logger.warning("备份失败，但继续迁移")

                    # 执行时间戳迁移
                    if self._migrate_timestamp_fields(cursor):
                        self.logger.info("时间戳字段迁移成功")
                    else:
                        self.logger.error("时间戳字段迁移失败")
                        return False

                return True

        except Exception as e:
            self.logger.error(f"数据库迁移失败: {e}")
            return False

    def _migrate_timestamp_fields(self, cursor) -> bool:
        """迁移时间戳字段"""
        try:
            # 禁用外键约束
            cursor.execute("PRAGMA foreign_keys = OFF")

            # 重建患者表
            self.logger.info("重建患者表以更新时间戳字段...")

            # 1. 创建新的患者表
            cursor.execute("""
                CREATE TABLE bingren_new (
                    bianhao INTEGER PRIMARY KEY,
                    name TEXT NOT NULL,
                    age INTEGER NOT NULL,
                    xingbie TEXT NOT NULL,
                    cardid TEXT,
                    zhenduan TEXT,
                    bingshi TEXT,
                    shebeiid TEXT,
                    yiyuanid INTEGER,
                    keshi TEXT,
                    zhuzhi TEXT,
                    czy TEXT,
                    lrshijian TIMESTAMP DEFAULT (datetime('now', 'localtime')),
                    status TEXT DEFAULT 'normal',
                    brhc TEXT,
                    updated_at TIMESTAMP DEFAULT (datetime('now', 'localtime')),
                    FOREIGN KEY (yiyuanid) REFERENCES yiyuan (id)
                )
            """)

            # 2. 复制数据到新表
            cursor.execute("""
                INSERT INTO bingren_new
                SELECT bianhao, name, age, xingbie, cardid, zhenduan, bingshi,
                       shebeiid, yiyuanid, keshi, zhuzhi, czy, lrshijian,
                       status, brhc, updated_at
                FROM bingren
            """)

            # 3. 删除旧表
            cursor.execute("DROP TABLE bingren")

            # 4. 重命名新表
            cursor.execute("ALTER TABLE bingren_new RENAME TO bingren")

            # 5. 重新创建索引
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_bingren_name ON bingren(name)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_bingren_cardid ON bingren(cardid)")

            # 重新启用外键约束
            cursor.execute("PRAGMA foreign_keys = ON")

            self.logger.info("患者表时间戳字段迁移完成")
            return True

        except Exception as e:
            self.logger.error(f"迁移时间戳字段失败: {e}")
            # 重新启用外键约束
            cursor.execute("PRAGMA foreign_keys = ON")
            return False

    def _setup_auto_backup(self):
        """设置自动备份"""
        # 这里可以设置定时备份任务
        # 实际实现中可能需要使用定时器或调度器
        pass

    def execute_query(self, sql: str, params: tuple = None) -> List[Dict[str, Any]]:
        """执行查询SQL"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                if params:
                    cursor.execute(sql, params)
                else:
                    cursor.execute(sql)

                # 将结果转换为字典列表
                columns = [description[0] for description in cursor.description]
                results = []
                for row in cursor.fetchall():
                    results.append(dict(zip(columns, row)))

                return results

        except Exception as e:
            self.logger.error(f"执行查询失败: {sql}, 错误: {e}")
            return []

    def execute_non_query(self, sql: str, params: tuple = None) -> bool:
        """执行非查询SQL（INSERT, UPDATE, DELETE）"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                if params:
                    cursor.execute(sql, params)
                else:
                    cursor.execute(sql)

                return cursor.rowcount > 0

        except Exception as e:
            self.logger.error(f"执行非查询失败: {sql}, 错误: {e}")
            return False

    def get_row_count(self, sql: str, params: tuple = None) -> int:
        """获取查询结果行数"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                if params:
                    cursor.execute(sql, params)
                else:
                    cursor.execute(sql)

                return len(cursor.fetchall())

        except Exception as e:
            self.logger.error(f"获取行数失败: {sql}, 错误: {e}")
            return 0

    def get_column_count(self, sql: str, params: tuple = None) -> int:
        """获取查询结果列数"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                if params:
                    cursor.execute(sql, params)
                else:
                    cursor.execute(sql)

                return len(cursor.description) if cursor.description else 0

        except Exception as e:
            self.logger.error(f"获取列数失败: {sql}, 错误: {e}")
            return 0

    def backup_database(self, backup_name: str = None) -> bool:
        """备份数据库"""
        try:
            if backup_name is None:
                backup_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"

            backup_file = self.backup_path / backup_name
            shutil.copy2(self.db_path, backup_file)

            self.logger.info(f"数据库备份成功: {backup_file}")
            return True

        except Exception as e:
            self.logger.error(f"数据库备份失败: {e}")
            return False

    def restore_database(self, backup_file: str) -> bool:
        """恢复数据库"""
        try:
            backup_path = self.backup_path / backup_file
            if not backup_path.exists():
                self.logger.error(f"备份文件不存在: {backup_path}")
                return False

            # 关闭所有连接
            self.close_all_connections()

            # 备份当前数据库
            current_backup = f"before_restore_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
            shutil.copy2(self.db_path, self.backup_path / current_backup)

            # 恢复数据库
            shutil.copy2(backup_path, self.db_path)

            self.logger.info(f"数据库恢复成功: {backup_file}")
            return True

        except Exception as e:
            self.logger.error(f"数据库恢复失败: {e}")
            return False

    def close_all_connections(self):
        """关闭所有数据库连接"""
        with self._lock:
            for conn in self._connection_pool.values():
                try:
                    conn.commit()  # 确保提交所有事务
                    conn.close()
                except Exception:
                    pass
            self._connection_pool.clear()

    def close(self):
        """关闭数据库管理器"""
        self.close_all_connections()
        self.logger.info("数据库管理器已关闭")

    # 业务相关的数据库操作方法
    def get_hospital_info(self) -> Dict[str, Any]:
        """获取医院信息"""
        results = self.execute_query("SELECT * FROM yiyuan LIMIT 1")
        return results[0] if results else {}

    def update_hospital_info(self, hospital_info: Dict[str, Any]) -> bool:
        """更新医院信息（不允许修改医院编号）"""
        try:
            # 检查是否已有医院信息
            existing = self.get_hospital_info()

            if existing:
                # 只更新医院名称、科室名称、设备编号，不更新医院编号
                sql = """
                    UPDATE yiyuan
                    SET hname = ?, keshi = ?, shebeiid = ?, updated_at = datetime('now', 'localtime')
                    WHERE id = ?
                """
                params = (
                    hospital_info.get('hname', ''),
                    hospital_info.get('keshi', ''),
                    hospital_info.get('shebeiid', ''),
                    existing.get('id', 1)
                )
            else:
                # 插入新记录（使用默认医院编号1）
                sql = """
                    INSERT INTO yiyuan (id, hname, keshi, shebeiid)
                    VALUES (1, ?, ?, ?)
                """
                params = (
                    hospital_info.get('hname', ''),
                    hospital_info.get('keshi', ''),
                    hospital_info.get('shebeiid', '')
                )

            success = self.execute_non_query(sql, params)
            if success:
                self.logger.info(f"医院信息更新成功")
            else:
                self.logger.error("医院信息更新失败")

            return success

        except Exception as e:
            self.logger.error(f"更新医院信息时发生错误: {e}")
            return False

    def update_hospital_id_safely(self, old_id: int, new_id: int) -> bool:
        """安全地更新医院编号（处理外键约束）"""
        try:
            self.logger.info(f"开始安全更新医院编号：{old_id} -> {new_id}")

            # 开始事务
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 1. 首先检查新ID是否已存在
                cursor.execute("SELECT COUNT(*) FROM yiyuan WHERE id = ?", (new_id,))
                if cursor.fetchone()[0] > 0:
                    self.logger.error(f"医院编号 {new_id} 已存在")
                    return False

                # 2. 临时禁用外键约束
                cursor.execute("PRAGMA foreign_keys = OFF")
                self.logger.debug("已禁用外键约束")

                try:
                    # 3. 更新患者表中的医院编号引用（先更新引用表）
                    cursor.execute("""
                        UPDATE bingren
                        SET yiyuanid = ?
                        WHERE yiyuanid = ?
                    """, (new_id, old_id))

                    updated_patients = cursor.rowcount
                    self.logger.info(f"已更新 {updated_patients} 个患者记录的医院编号")

                    # 4. 更新医院信息表
                    cursor.execute("""
                        UPDATE yiyuan
                        SET id = ?, updated_at = datetime('now', 'localtime')
                        WHERE id = ?
                    """, (new_id, old_id))

                    if cursor.rowcount == 0:
                        self.logger.error(f"未找到医院编号 {old_id}")
                        return False

                    # 5. 更新其他可能引用医院ID的表
                    # 这里可以根据实际数据库结构添加更多表的更新

                    # 6. 重新启用外键约束并检查完整性
                    cursor.execute("PRAGMA foreign_keys = ON")
                    cursor.execute("PRAGMA foreign_key_check")

                    # 检查是否有外键约束违反
                    fk_violations = cursor.fetchall()
                    if fk_violations:
                        self.logger.error(f"外键约束检查失败: {fk_violations}")
                        return False

                    # 提交事务
                    conn.commit()

                    self.logger.info(f"医院编号更新成功：{old_id} -> {new_id}")
                    return True

                except Exception as e:
                    # 恢复外键约束
                    cursor.execute("PRAGMA foreign_keys = ON")
                    raise e

        except Exception as e:
            self.logger.error(f"安全更新医院编号时发生错误: {e}")
            return False

    def get_operators(self) -> List[Dict[str, Any]]:
        """获取所有操作员"""
        return self.execute_query("SELECT * FROM operator WHERE is_active = 1")

    def get_doctors(self) -> List[Dict[str, Any]]:
        """获取所有医生"""
        return self.execute_query("SELECT * FROM doctor WHERE is_active = 1 ORDER BY name")

    def add_doctor(self, doctor_data: Dict[str, Any]) -> bool:
        """添加医生（智能处理重复姓名）"""
        try:
            doctor_name = doctor_data.get('name', '').strip()
            if not doctor_name:
                self.logger.error("医生姓名不能为空")
                return False

            # 检查是否存在同名医生（包括已删除的）
            existing_doctor = self.execute_query(
                "SELECT * FROM doctor WHERE name = ?",
                (doctor_name,)
            )

            if existing_doctor:
                doctor = existing_doctor[0]
                if doctor['is_active'] == 1:
                    # 活跃医生重名，返回失败
                    self.logger.warning(f"医生姓名 '{doctor_name}' 已存在且处于活跃状态")
                    return False
                else:
                    # 已删除的医生，重新激活并更新信息
                    self.logger.info(f"重新激活已删除的医生: {doctor_name}")
                    return self._reactivate_doctor(doctor['id'], doctor_data)
            else:
                # 新医生，直接添加
                return self._insert_new_doctor(doctor_data)

        except Exception as e:
            self.logger.error(f"添加医生失败: {e}")
            return False

    def _insert_new_doctor(self, doctor_data: Dict[str, Any]) -> bool:
        """插入新医生记录"""
        sql = """
            INSERT INTO doctor (name, title, department, phone, email)
            VALUES (?, ?, ?, ?, ?)
        """
        params = (
            doctor_data.get('name'),
            doctor_data.get('title', ''),
            doctor_data.get('department', ''),
            doctor_data.get('phone', ''),
            doctor_data.get('email', '')
        )
        return self.execute_non_query(sql, params)

    def _reactivate_doctor(self, doctor_id: int, doctor_data: Dict[str, Any]) -> bool:
        """重新激活已删除的医生并更新信息"""
        sql = """
            UPDATE doctor
            SET title=?, department=?, phone=?, email=?,
                is_active=1, updated_at=datetime('now', 'localtime')
            WHERE id=?
        """
        params = (
            doctor_data.get('title', ''),
            doctor_data.get('department', ''),
            doctor_data.get('phone', ''),
            doctor_data.get('email', ''),
            doctor_id
        )
        return self.execute_non_query(sql, params)

    def update_doctor(self, doctor_id: int, doctor_data: Dict[str, Any]) -> bool:
        """更新医生信息"""
        sql = """
            UPDATE doctor SET name=?, title=?, department=?, phone=?, email=?, updated_at=datetime('now', 'localtime')
            WHERE id=?
        """
        params = (
            doctor_data.get('name'),
            doctor_data.get('title', ''),
            doctor_data.get('department', ''),
            doctor_data.get('phone', ''),
            doctor_data.get('email', ''),
            doctor_id
        )
        return self.execute_non_query(sql, params)

    def delete_doctor(self, doctor_id: int) -> bool:
        """删除医生（软删除）"""
        sql = "UPDATE doctor SET is_active = 0, updated_at = datetime('now', 'localtime') WHERE id = ?"
        return self.execute_non_query(sql, (doctor_id,))

    def add_patient(self, patient_data: Dict[str, Any]) -> bool:
        """添加患者"""
        sql = """
            INSERT INTO bingren (bianhao, name, age, xingbie, cardid, zhenduan,
                               bingshi, shebeiid, yiyuanid, keshi, zhuzhi, czy, brhc, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        params = (
            patient_data.get('bianhao'),
            patient_data.get('name'),
            patient_data.get('age'),
            patient_data.get('xingbie'),
            patient_data.get('cardid'),
            patient_data.get('zhenduan'),
            patient_data.get('bingshi'),
            patient_data.get('shebeiid'),
            patient_data.get('yiyuanid'),
            patient_data.get('keshi'),
            patient_data.get('zhuzhi'),
            patient_data.get('czy'),
            patient_data.get('brhc'),
            patient_data.get('status', 'pending')  # 默认为待上传状态
        )
        return self.execute_non_query(sql, params)

    def update_patient(self, bianhao: int, patient_data: Dict[str, Any]) -> bool:
        """更新患者信息"""
        sql = """
            UPDATE bingren SET name=?, age=?, xingbie=?, cardid=?,
                              zhenduan=?, bingshi=?, brhc=?, zhuzhi=?, czy=?, updated_at=datetime('now', 'localtime')
            WHERE bianhao=?
        """
        params = (
            patient_data.get('name'),
            patient_data.get('age'),
            patient_data.get('xingbie'),
            patient_data.get('cardid'),
            patient_data.get('zhenduan'),
            patient_data.get('bingshi'),
            patient_data.get('brhc'),
            patient_data.get('zhuzhi'),
            patient_data.get('czy'),
            bianhao
        )
        return self.execute_non_query(sql, params)

    def get_patients(self, search_term: str = None) -> List[Dict[str, Any]]:
        """获取患者列表"""
        if search_term:
            sql = """
                SELECT * FROM bingren
                WHERE name LIKE ? OR CAST(bianhao AS TEXT) LIKE ?
                ORDER BY lrshijian DESC, bianhao DESC
            """
            params = (f'%{search_term}%', f'%{search_term}%')
        else:
            sql = "SELECT * FROM bingren ORDER BY lrshijian DESC, bianhao DESC"
            params = None

        return self.execute_query(sql, params)

    def get_treatment_records(self, patient_id: int) -> List[Dict[str, Any]]:
        """获取患者治疗记录"""
        sql = "SELECT * FROM zhiliao WHERE bianh = ? ORDER BY zhiliaobh DESC"
        return self.execute_query(sql, (patient_id,))

    def add_treatment_record(self, treatment_data: Dict[str, Any]) -> bool:
        """添加治疗记录"""
        # 检查是否有必要的字段，如果没有则添加
        try:
            # 检查表结构
            columns = self.execute_query("PRAGMA table_info(zhiliao)")
            column_names = [col['name'] for col in columns] if columns else []

            if 'upload_status' not in column_names:
                # 添加upload_status字段
                self.execute_non_query("ALTER TABLE zhiliao ADD COLUMN upload_status TEXT DEFAULT '0'")
                self.logger.info("已为zhiliao表添加upload_status字段")

            if 'treat_number' not in column_names:
                # 添加treat_number字段
                self.execute_non_query("ALTER TABLE zhiliao ADD COLUMN treat_number INTEGER DEFAULT 1")
                self.logger.info("已为zhiliao表添加treat_number字段")
        except Exception as e:
            self.logger.warning(f"检查/添加字段失败: {e}")

        sql = """
            INSERT INTO zhiliao (bianh, rq, shijian, defen, yaoqiucs, shijics,
                               zlsj, czy, zhuzhi, zlms, start_time, end_time, notes, upload_status, treat_number)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        params = (
            treatment_data.get('bianh'),
            treatment_data.get('rq'),
            treatment_data.get('shijian'),
            treatment_data.get('defen'),
            treatment_data.get('yaoqiucs'),
            treatment_data.get('shijics'),
            treatment_data.get('zlsj'),
            treatment_data.get('czy'),
            treatment_data.get('zhuzhi'),
            treatment_data.get('zlms'),
            treatment_data.get('start_time'),
            treatment_data.get('end_time'),
            treatment_data.get('notes'),
            treatment_data.get('upload_status', '0'),  # 默认未上传
            treatment_data.get('treat_number', 1)  # 默认治疗编号1
        )
        return self.execute_non_query(sql, params)

    def add_eeg_data(self, eeg_data: Dict[str, Any]) -> bool:
        """添加脑电数据"""
        sql = """
            INSERT INTO Edata (ebianhao, ename, channel_data, theta, alpha,
                             low_beta, high_beta, gamma, state)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        params = (
            eeg_data.get('ebianhao'),
            eeg_data.get('ename'),
            eeg_data.get('channel_data'),
            eeg_data.get('theta'),
            eeg_data.get('alpha'),
            eeg_data.get('low_beta'),
            eeg_data.get('high_beta'),
            eeg_data.get('gamma'),
            eeg_data.get('state')
        )
        return self.execute_non_query(sql, params)

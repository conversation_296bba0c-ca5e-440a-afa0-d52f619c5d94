# NK脑机接口系统 - API接口文档

## 概述

本文档描述了NK脑机接口系统的所有API接口，包括内部模块接口、设备接口、网络通信接口等。

## 内部API接口

### 1. 数据库管理器接口 (DatabaseManager)

#### 1.1 患者管理接口

##### 添加患者
```python
def add_patient(self, patient_data: Dict[str, Any]) -> bool:
    """
    添加新患者
    
    参数:
        patient_data: 患者信息字典
        {
            'bianhao': str,      # 患者编号 (必需)
            'xingming': str,     # 姓名 (必需)
            'nianling': int,     # 年龄
            'xingbie': str,      # 性别 ('男'/'女')
            'shenfenzheng': str, # 身份证号
            'zhenduan': str,     # 诊断
            'zhuzhi_yishi': str, # 主治医师
            'beizhu': str        # 备注
        }
    
    返回:
        bool: 添加成功返回True，失败返回False
    """
```

##### 查询患者
```python
def get_patients(self, filters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
    """
    查询患者列表
    
    参数:
        filters: 查询条件字典 (可选)
        {
            'bianhao': str,      # 患者编号
            'xingming': str,     # 姓名 (支持模糊查询)
            'xingbie': str,      # 性别
            'zhenduan': str,     # 诊断
            'is_active': bool    # 是否活跃
        }
    
    返回:
        List[Dict]: 患者信息列表
    """
```

##### 更新患者
```python
def update_patient(self, patient_id: int, patient_data: Dict[str, Any]) -> bool:
    """
    更新患者信息
    
    参数:
        patient_id: 患者ID
        patient_data: 更新的患者信息
    
    返回:
        bool: 更新成功返回True，失败返回False
    """
```

##### 删除患者
```python
def delete_patient(self, patient_id: int) -> bool:
    """
    删除患者 (软删除)
    
    参数:
        patient_id: 患者ID
    
    返回:
        bool: 删除成功返回True，失败返回False
    """
```

#### 1.2 治疗记录接口

##### 添加治疗记录
```python
def add_treatment_record(self, treatment_data: Dict[str, Any]) -> bool:
    """
    添加治疗记录
    
    参数:
        treatment_data: 治疗记录字典
        {
            'bianh': str,           # 患者编号 (必需)
            'treat_number': int,    # 治疗次数
            'riqi': str,           # 治疗日期 (YYYY-MM-DD)
            'shijian': str,        # 治疗时间 (HH:MM:SS)
            'defen': str,          # 得分 (实际/总数)
            'chufa_cishu': int,    # 触发次数
            'zlsj': int,           # 治疗时长(分钟)
            'zlms': str,           # 治疗描述
            'czy': str             # 操作员
        }
    
    返回:
        bool: 添加成功返回True，失败返回False
    """
```

##### 查询治疗记录
```python
def get_treatment_records(self, patient_id: str = None, 
                         start_date: str = None, 
                         end_date: str = None) -> List[Dict[str, Any]]:
    """
    查询治疗记录
    
    参数:
        patient_id: 患者编号 (可选)
        start_date: 开始日期 (可选)
        end_date: 结束日期 (可选)
    
    返回:
        List[Dict]: 治疗记录列表
    """
```

#### 1.3 脑电数据接口

##### 添加脑电数据
```python
def add_eeg_data(self, eeg_data: Dict[str, Any]) -> bool:
    """
    添加脑电数据
    
    参数:
        eeg_data: 脑电数据字典
        {
            'bianh': str,        # 患者编号
            'delta_power': float, # δ波功率
            'theta_power': float, # θ波功率
            'alpha_power': float, # α波功率
            'beta_power': float,  # β波功率
            'gamma_power': float, # γ波功率
            'mu_power': float,    # μ波功率
            'zhuangtai': int     # 状态 (0:休息, 1:运动想象)
        }
    
    返回:
        bool: 添加成功返回True，失败返回False
    """
```

### 2. 脑电设备接口 (EEGDevice)

#### 2.1 设备连接
```python
def connect(self) -> bool:
    """
    连接脑电设备
    
    返回:
        bool: 连接成功返回True，失败返回False
    """

def disconnect(self) -> bool:
    """
    断开脑电设备连接
    
    返回:
        bool: 断开成功返回True，失败返回False
    """

def is_connected(self) -> bool:
    """
    检查设备连接状态
    
    返回:
        bool: 已连接返回True，未连接返回False
    """
```

#### 2.2 数据采集
```python
def start_acquisition(self) -> bool:
    """
    开始数据采集
    
    返回:
        bool: 启动成功返回True，失败返回False
    """

def stop_acquisition(self) -> bool:
    """
    停止数据采集
    
    返回:
        bool: 停止成功返回True，失败返回False
    """

def get_latest_data(self) -> np.ndarray:
    """
    获取最新的脑电数据
    
    返回:
        np.ndarray: 形状为 (channels, samples) 的数据数组
    """
```

#### 2.3 设备状态
```python
def get_impedance(self) -> List[float]:
    """
    获取电极阻抗
    
    返回:
        List[float]: 8个通道的阻抗值 (kΩ)
    """

def get_device_info(self) -> Dict[str, Any]:
    """
    获取设备信息
    
    返回:
        Dict: 设备信息字典
        {
            'device_type': str,    # 设备类型
            'serial_number': str,  # 序列号
            'firmware_version': str, # 固件版本
            'sample_rate': float,  # 采样率
            'channels': int        # 通道数
        }
    """
```

### 3. 电刺激设备接口 (StimulationDevice)

#### 3.1 设备连接
```python
def connect(self, port_num: int = 1) -> bool:
    """
    连接电刺激设备
    
    参数:
        port_num: 端口号
    
    返回:
        bool: 连接成功返回True，失败返回False
    """

def disconnect(self) -> bool:
    """
    断开电刺激设备连接
    
    返回:
        bool: 断开成功返回True，失败返回False
    """

def is_connected(self) -> bool:
    """
    检查设备连接状态
    
    返回:
        bool: 已连接返回True，未连接返回False
    """
```

#### 3.2 参数设置
```python
def set_parameters(self, channel: str, params: Dict[str, Any]) -> bool:
    """
    设置刺激参数
    
    参数:
        channel: 通道 ('A' 或 'B')
        params: 参数字典
        {
            'frequency': int,     # 频率 (2-160 Hz)
            'pulse_width': int,   # 脉宽 (10-500 μs)
            'current': int,       # 电流 (1-100 mA)
            'relax_time': int,    # 休息时间 (0-16 s)
            'climb_time': int,    # 上升时间 (0-5 s)
            'work_time': int,     # 工作时间 (0-30 s)
            'fall_time': int,     # 下降时间 (0-5 s)
            'wave_type': int      # 波形类型 (0:双相, 1:单相)
        }
    
    返回:
        bool: 设置成功返回True，失败返回False
    """
```

#### 3.3 刺激控制
```python
def start_stimulation(self, channels: List[str]) -> bool:
    """
    开始电刺激
    
    参数:
        channels: 要启动的通道列表 ['A'] 或 ['B'] 或 ['A', 'B']
    
    返回:
        bool: 启动成功返回True，失败返回False
    """

def stop_stimulation(self) -> bool:
    """
    停止电刺激
    
    返回:
        bool: 停止成功返回True，失败返回False
    """

def get_stimulation_status(self) -> int:
    """
    获取刺激状态
    
    返回:
        int: 状态码 (0:停止, 1:刺激中)
    """
```

### 4. 信号处理接口 (SignalProcessor)

#### 4.1 预处理
```python
def preprocess(self, data: np.ndarray) -> np.ndarray:
    """
    信号预处理
    
    参数:
        data: 原始脑电数据，形状 (channels, samples)
    
    返回:
        np.ndarray: 预处理后的数据
    """

def apply_filters(self, data: np.ndarray) -> np.ndarray:
    """
    应用滤波器
    
    参数:
        data: 输入数据
    
    返回:
        np.ndarray: 滤波后的数据
    """
```

#### 4.2 特征提取
```python
def extract_features(self, data: np.ndarray) -> np.ndarray:
    """
    提取特征
    
    参数:
        data: 预处理后的脑电数据
    
    返回:
        np.ndarray: 特征向量
    """

def extract_psd_features(self, data: np.ndarray) -> np.ndarray:
    """
    提取功率谱密度特征
    
    参数:
        data: 脑电数据
    
    返回:
        np.ndarray: PSD特征向量
    """

def extract_csp_features(self, data: np.ndarray) -> np.ndarray:
    """
    提取CSP特征
    
    参数:
        data: 脑电数据
    
    返回:
        np.ndarray: CSP特征向量
    """
```

### 5. 机器学习模型接口 (MLModel, EEGNetModel)

#### 5.1 模型训练
```python
def train(self, X: np.ndarray, y: np.ndarray) -> bool:
    """
    训练模型
    
    参数:
        X: 训练数据，形状 (samples, features)
        y: 标签，形状 (samples,)
    
    返回:
        bool: 训练成功返回True，失败返回False
    """

def validate(self, X: np.ndarray, y: np.ndarray) -> Dict[str, float]:
    """
    验证模型
    
    参数:
        X: 验证数据
        y: 验证标签
    
    返回:
        Dict: 验证结果
        {
            'accuracy': float,    # 准确率
            'precision': float,   # 精确率
            'recall': float,      # 召回率
            'f1_score': float     # F1分数
        }
    """
```

#### 5.2 模型预测
```python
def predict(self, X: np.ndarray) -> np.ndarray:
    """
    预测分类结果
    
    参数:
        X: 输入数据
    
    返回:
        np.ndarray: 预测标签
    """

def predict_proba(self, X: np.ndarray) -> np.ndarray:
    """
    预测分类概率
    
    参数:
        X: 输入数据
    
    返回:
        np.ndarray: 预测概率，形状 (samples, classes)
    """
```

#### 5.3 模型管理
```python
def save_model(self, filepath: str) -> bool:
    """
    保存模型
    
    参数:
        filepath: 保存路径
    
    返回:
        bool: 保存成功返回True，失败返回False
    """

def load_model(self, filepath: str) -> bool:
    """
    加载模型
    
    参数:
        filepath: 模型文件路径
    
    返回:
        bool: 加载成功返回True，失败返回False
    """
```

## 外部API接口

### 1. HTTP API接口

#### 1.1 患者数据上传
```http
POST /shdekf/Api/uploadPatient
Content-Type: application/json

{
    "patientId": "string",           # 患者编号
    "name": "string",                # 姓名
    "age": 25,                       # 年龄
    "gender": "男",                  # 性别
    "idCard": "string",              # 身份证号
    "diagnosis": "string",           # 诊断
    "doctor": "string",              # 主治医师
    "hospitalID": 1,                 # 医院ID
    "department": "string",          # 科室
    "equipmentNum": "string",        # 设备编号
    "operator": "string",            # 操作员
    "createTime": "2024-12-19 10:30:00"  # 创建时间
}
```

**响应:**
```json
{
    "code": 200,
    "message": "success",
    "data": null
}
```

#### 1.2 治疗数据上传
```http
POST /shdekf/Api/uploadTreatment
Content-Type: application/json

{
    "patientId": "string",           # 患者编号
    "treatNum": 1,                   # 治疗次数
    "treatDate": "2024-12-19",       # 治疗日期
    "treatTime": 10,                 # 治疗时长(分钟)
    "score": "49/61",                # 得分
    "triggerCount": 49,              # 触发次数
    "commentsOfTreatment": "良",     # 治疗评价
    "hospitalID": 1,                 # 医院ID
    "department": "string",          # 科室
    "equipmentNum": "string",        # 设备编号
    "operator": "string",            # 操作员
    "createTime": "2024-12-19 10:30:00"  # 创建时间
}
```

**响应:**
```json
{
    "code": 200,
    "message": "success",
    "data": null
}
```

#### 1.3 设备状态上传
```http
POST /shdekf/Api/updateEquipment
Content-Type: application/json

{
    "hospitalID": 1,                 # 医院ID
    "equipmentNum": "string",        # 设备编号
    "status": 1                      # 状态 (1:在线, 0:离线)
}
```

**响应:**
```json
{
    "code": 200,
    "message": "success",
    "data": null
}
```

### 2. UDP通信接口

#### 2.1 VR系统通信
```python
# 发送地址: 127.0.0.1:3004
# 本地绑定: 127.0.0.1:3005

# 治疗指令
commands = {
    'treat': '开始治疗准备',      # 初始化治疗
    'start': '开始电刺激',        # 启动电刺激
    'stop': '停止电刺激',         # 停止电刺激
    'stopall': '结束治疗'         # 结束治疗
}

# 发送格式
message = command_key  # 直接发送命令键
```

## 错误码定义

### HTTP API错误码
- **200**: 成功
- **400**: 请求参数错误
- **401**: 未授权
- **403**: 权限不足
- **404**: 资源不存在
- **500**: 服务器内部错误
- **-1**: 网络连接失败
- **-2**: 数据格式错误

### 设备接口错误码
- **0**: 成功
- **-1**: 设备未连接
- **-2**: 参数错误
- **-3**: 设备忙碌
- **-4**: 硬件故障
- **-5**: 超时错误

### 数据库错误码
- **0**: 成功
- **-1**: 连接失败
- **-2**: SQL语法错误
- **-3**: 数据约束违反
- **-4**: 事务回滚
- **-5**: 权限不足

## 接口使用示例

### 1. 完整治疗流程示例
```python
# 1. 连接设备
eeg_device = EEGDevice()
stim_device = StimulationDevice()

if not eeg_device.connect():
    print("脑电设备连接失败")
    return

if not stim_device.connect():
    print("电刺激设备连接失败")
    return

# 2. 开始数据采集
eeg_device.start_acquisition()

# 3. 设置电刺激参数
stim_params = {
    'frequency': 20,
    'pulse_width': 200,
    'current': 15,
    'work_time': 5
}
stim_device.set_parameters('A', stim_params)

# 4. 运动想象检测和电刺激
while treatment_active:
    # 获取脑电数据
    data = eeg_device.get_latest_data()
    
    # 信号处理和分类
    processed_data = signal_processor.preprocess(data)
    features = signal_processor.extract_features(processed_data)
    prediction = ml_model.predict(features)
    
    # 如果检测到运动想象，启动电刺激
    if prediction == 1:  # 运动想象状态
        stim_device.start_stimulation(['A'])
        time.sleep(5)  # 刺激5秒
        stim_device.stop_stimulation()

# 5. 清理资源
eeg_device.stop_acquisition()
eeg_device.disconnect()
stim_device.disconnect()
```

### 2. 数据上传示例
```python
# 上传患者数据
patient_data = {
    "patientId": "P001",
    "name": "张三",
    "age": 45,
    "gender": "男",
    "diagnosis": "脑卒中",
    "doctor": "李医生"
}

http_client = HTTPClient()
result = http_client.upload_patient(patient_data)
if result['code'] == 200:
    print("患者数据上传成功")
else:
    print(f"上传失败: {result['message']}")
```

这个API文档提供了系统所有接口的详细说明，便于开发者理解和使用系统的各项功能。

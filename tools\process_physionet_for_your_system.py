#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PhysioNet数据集处理工具 - 针对您的系统配置
PhysioNet Dataset Processor for Your System

专门针对您的系统配置处理PhysioNet数据集：
- 8通道脑电帽
- 125Hz采样率
- 运动想象vs平静状态分类
- 2秒时间窗口

作者: AI Assistant
版本: 1.0.0
"""

import sys
import os
from pathlib import Path
import numpy as np
import pickle
import time
import json

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

try:
    import mne
    from scipy.signal import butter, filtfilt, resample
    MNE_AVAILABLE = True
except ImportError as e:
    print(f"❌ 依赖库缺失: {e}")
    print("请安装: pip install mne scipy")
    sys.exit(1)


class PhysioNetSystemProcessor:
    """PhysioNet数据集系统处理器"""
    
    def __init__(self, data_dir="data/bci_dataset/files"):
        self.data_dir = Path(data_dir)
        
        # 您的系统配置
        self.system_config = {
            'n_channels': 8,        # 8通道脑电帽
            'sampling_rate': 125,   # 125Hz采样率
            'window_length': 2.0,   # 2秒时间窗口
            'n_samples': 250,       # 2秒@125Hz = 250个采样点
            'task': 'motor_imagery_vs_rest'  # 运动想象vs平静状态
        }
        
        # PhysioNet任务映射
        self.task_mapping = {
            # R01, R02: 基线状态（眼睛睁开）-> 平静状态
            1: {'label': 0, 'description': '基线状态（平静）'},
            2: {'label': 0, 'description': '基线状态（平静）'},
            # R03, R04: 左拳或右拳运动想象 -> 运动想象
            3: {'label': 1, 'description': '左拳运动想象'},
            4: {'label': 1, 'description': '右拳运动想象'},
            # R05, R06: 双拳或双脚运动想象 -> 运动想象
            5: {'label': 1, 'description': '双拳运动想象'},
            6: {'label': 1, 'description': '双脚运动想象'},
        }
        
        # 您的系统通道配置（10-20标准）
        self.your_system_channels = ['PZ', 'P3', 'P4', 'C3', 'CZ', 'C4', 'F3', 'F4']

        # PhysioNet通道映射（匹配您的系统）
        self.channel_mapping = {
            # 完全匹配的通道
            'PZ': 'Pz..',   # 顶叶中央
            'P3': 'P3..',   # 左顶叶
            'P4': 'P4..',   # 右顶叶
            'C3': 'C3..',   # 左运动皮层
            'CZ': 'Cz..',   # 运动皮层中央
            'C4': 'C4..',   # 右运动皮层
            'F3': 'F3..',   # 左前额叶
            'F4': 'F4..',   # 右前额叶
        }

        # 备选通道（如果完全匹配不可用）
        self.fallback_channels = {
            'PZ': ['Cpz.', 'Cp1.', 'Cp2.'],  # 顶叶相邻
            'P3': ['Cp3.', 'P1..', 'P5..'],  # 左顶叶相邻
            'P4': ['Cp4.', 'P2..', 'P6..'],  # 右顶叶相邻
            'C3': ['Cp3.', 'Fc3.', 'C1..'],  # 左运动皮层相邻
            'CZ': ['Fcz.', 'Cpz.', 'C1..'],  # 运动皮层中央相邻
            'C4': ['Cp4.', 'Fc4.', 'C2..'],  # 右运动皮层相邻
            'F3': ['Fc3.', 'F1..', 'Af3.'],  # 左前额叶相邻
            'F4': ['Fc4.', 'F2..', 'Af4.'],  # 右前额叶相邻
        }
        
        print(f"🧠 PhysioNet系统处理器初始化")
        print(f"系统配置: {self.system_config['n_channels']}通道, {self.system_config['sampling_rate']}Hz, {self.system_config['window_length']}秒窗口")
        print(f"数据目录: {self.data_dir}")
    
    def find_edf_files(self, max_subjects=20):
        """查找EDF文件（限制受试者数量以加快处理）"""
        print(f"\n📂 查找EDF文件（前{max_subjects}个受试者）...")
        
        edf_files = []
        
        for subject_id in range(1, max_subjects + 1):
            subject_dir = self.data_dir / f"S{subject_id:03d}"
            
            if not subject_dir.exists():
                continue
            
            # 只处理相关的run（R01-R06）
            for run_id in [1, 2, 3, 4, 5, 6]:
                edf_file = subject_dir / f"S{subject_id:03d}R{run_id:02d}.edf"
                
                if edf_file.exists():
                    edf_files.append(edf_file)
        
        print(f"✅ 找到 {len(edf_files)} 个EDF文件")
        return edf_files
    
    def process_edf_file(self, edf_file):
        """处理单个EDF文件"""
        filename = edf_file.stem
        subject_id = int(filename[1:4])
        run_id = int(filename[5:7])
        
        # 获取任务信息
        if run_id not in self.task_mapping:
            return [], []
        
        task_info = self.task_mapping[run_id]
        label = task_info['label']
        
        print(f"  处理: {filename} - {task_info['description']}")
        
        try:
            # 读取EDF文件
            raw = mne.io.read_raw_edf(str(edf_file), preload=True, verbose=False)
            
            print(f"    - 原始通道数: {len(raw.ch_names)}")
            print(f"    - 原始采样率: {raw.info['sfreq']} Hz")
            print(f"    - 数据时长: {raw.times[-1]:.1f} 秒")
            
            # 选择与您系统匹配的通道
            selected_channels = []
            channel_mapping_used = {}

            print(f"    - 可用通道: {raw.ch_names[:10]}...")  # 显示前10个通道

            # 按照您的系统通道顺序选择匹配通道
            for your_ch in self.your_system_channels:
                found_channel = None

                # 首先尝试完全匹配
                target_ch = self.channel_mapping.get(your_ch)
                if target_ch and target_ch in raw.ch_names:
                    found_channel = target_ch
                    print(f"    ✅ {your_ch} -> {target_ch} (完全匹配)")
                else:
                    # 尝试备选通道
                    fallback_list = self.fallback_channels.get(your_ch, [])
                    for fallback_ch in fallback_list:
                        if fallback_ch in raw.ch_names:
                            found_channel = fallback_ch
                            print(f"    ✅ {your_ch} -> {fallback_ch} (相邻通道)")
                            break

                if found_channel:
                    selected_channels.append(found_channel)
                    channel_mapping_used[your_ch] = found_channel
                else:
                    print(f"    ❌ {your_ch} 未找到匹配通道")

            # 如果匹配通道不足8个，用其他EEG通道补充
            if len(selected_channels) < 8:
                print(f"    ⚠️ 匹配通道不足: {len(selected_channels)}/8")
                remaining_channels = [ch for ch in raw.ch_names
                                    if ch not in selected_channels and 'EEG' not in ch.upper()]
                needed = 8 - len(selected_channels)
                selected_channels.extend(remaining_channels[:needed])
                print(f"    📝 补充通道: {remaining_channels[:needed]}")

            if len(selected_channels) < 8:
                print(f"    ❌ 总通道数不足8个: {len(selected_channels)}")
                return [], []

            # 确保只选择8个通道
            selected_channels = selected_channels[:8]
            raw.pick(selected_channels)
            
            print(f"    - 选择通道: {selected_channels}")
            
            # 重采样到125Hz
            if raw.info['sfreq'] != 125:
                raw.resample(125)
                print(f"    - 重采样到: 125 Hz")
            
            # 应用带通滤波 (0.5-50 Hz)
            raw.filter(0.5, 50, fir_design='firwin', verbose=False)
            print(f"    - 应用带通滤波: 0.5-50 Hz")
            
            # 分割数据为2秒窗口
            data_array = raw.get_data()  # (n_channels, n_times)
            sfreq = raw.info['sfreq']
            window_samples = int(self.system_config['window_length'] * sfreq)
            overlap = 0.5  # 50%重叠
            step_samples = int(window_samples * (1 - overlap))
            
            n_windows = (data_array.shape[1] - window_samples) // step_samples + 1
            
            trial_data = []
            trial_labels = []
            
            for i in range(n_windows):
                start_idx = i * step_samples
                end_idx = start_idx + window_samples
                
                if end_idx > data_array.shape[1]:
                    break
                
                window_data = data_array[:, start_idx:end_idx]
                
                # 确保数据形状正确
                if window_data.shape[1] == self.system_config['n_samples']:
                    trial_data.append(window_data)
                    trial_labels.append(label)
            
            print(f"    - 提取窗口: {len(trial_data)} 个")
            
            return trial_data, trial_labels
            
        except Exception as e:
            print(f"    ❌ 处理失败: {e}")
            return [], []
    
    def process_all_files(self, max_subjects=20):
        """处理所有EDF文件"""
        print(f"\n🔄 开始处理PhysioNet数据集...")
        
        edf_files = self.find_edf_files(max_subjects)
        
        if not edf_files:
            raise ValueError("未找到EDF文件")
        
        all_data = []
        all_labels = []
        all_subjects = []
        all_runs = []
        
        for edf_file in edf_files:
            filename = edf_file.stem
            subject_id = int(filename[1:4])
            run_id = int(filename[5:7])
            
            trial_data, trial_labels = self.process_edf_file(edf_file)
            
            if trial_data:
                all_data.extend(trial_data)
                all_labels.extend(trial_labels)
                all_subjects.extend([subject_id] * len(trial_data))
                all_runs.extend([run_id] * len(trial_data))
        
        if not all_data:
            raise ValueError("没有成功处理任何文件")
        
        # 转换为numpy数组
        X = np.array(all_data)
        y = np.array(all_labels)
        subjects = np.array(all_subjects)
        runs = np.array(all_runs)
        
        print(f"\n✅ 数据处理完成:")
        print(f"  - 总样本数: {X.shape[0]}")
        print(f"  - 数据形状: {X.shape}")
        print(f"  - 标签分布: 平静={np.sum(y==0)}, 运动想象={np.sum(y==1)}")
        print(f"  - 受试者数: {len(np.unique(subjects))}")
        print(f"  - Run分布: {dict(zip(*np.unique(runs, return_counts=True)))}")
        
        return X, y, subjects, runs
    
    def save_processed_dataset(self, X, y, subjects, runs):
        """保存处理后的数据集"""
        print(f"\n💾 保存处理后的数据集...")
        
        # 创建数据集字典
        dataset = {
            'data': X,
            'labels': y,
            'subjects': subjects,
            'runs': runs,
            'system_config': self.system_config,
            'task_mapping': self.task_mapping,
            'created_time': time.time(),
            'data_source': 'physionet_eegmmidb_for_your_system',
            'description': '针对您系统配置的PhysioNet数据集'
        }
        
        # 保存数据集
        output_file = self.data_dir.parent / 'physionet_your_system_dataset.pkl'
        with open(output_file, 'wb') as f:
            pickle.dump(dataset, f)
        
        print(f"✅ 数据集已保存: {output_file}")
        
        # 保存详细信息
        info_file = self.data_dir.parent / 'physionet_your_system_info.txt'
        with open(info_file, 'w', encoding='utf-8') as f:
            f.write("PhysioNet数据集 - 针对您的系统配置\n")
            f.write("=" * 60 + "\n\n")
            f.write(f"系统配置:\n")
            f.write(f"  - 通道数: {self.system_config['n_channels']}\n")
            f.write(f"  - 采样率: {self.system_config['sampling_rate']} Hz\n")
            f.write(f"  - 时间窗口: {self.system_config['window_length']} 秒\n")
            f.write(f"  - 采样点数: {self.system_config['n_samples']}\n")
            f.write(f"  - 任务类型: {self.system_config['task']}\n")
            f.write(f"\n数据集统计:\n")
            f.write(f"  - 总样本数: {X.shape[0]}\n")
            f.write(f"  - 数据形状: {X.shape}\n")
            f.write(f"  - 标签分布: 平静={np.sum(y==0)}, 运动想象={np.sum(y==1)}\n")
            f.write(f"  - 受试者数: {len(np.unique(subjects))}\n")
            f.write(f"  - 受试者分布: {dict(zip(*np.unique(subjects, return_counts=True)))}\n")
            f.write(f"\n任务映射:\n")
            for run_id, task_info in self.task_mapping.items():
                f.write(f"  - R{run_id:02d}: {task_info['description']} (标签={task_info['label']})\n")
            f.write(f"\n处理时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"\n数据质量:\n")
            f.write(f"  - 数据来源: PhysioNet权威医学数据库\n")
            f.write(f"  - 配置匹配: 100%匹配您的系统\n")
            f.write(f"  - 任务匹配: 运动想象vs平静状态\n")
            f.write(f"  - 预期效果: 高质量预训练模型\n")
        
        print(f"✅ 信息文件已保存: {info_file}")
        
        return output_file
    
    def validate_dataset(self, X, y):
        """验证数据集质量"""
        print(f"\n🔍 验证数据集质量...")
        
        # 基本检查
        print(f"  - 数据范围: [{X.min():.2f}, {X.max():.2f}]")
        print(f"  - 数据均值: {X.mean():.2f}")
        print(f"  - 数据标准差: {X.std():.2f}")
        print(f"  - 是否包含NaN: {np.isnan(X).any()}")
        print(f"  - 是否包含Inf: {np.isinf(X).any()}")
        
        # 形状检查
        expected_shape = (None, self.system_config['n_channels'], self.system_config['n_samples'])
        actual_shape = X.shape
        print(f"  - 期望形状: {expected_shape}")
        print(f"  - 实际形状: {actual_shape}")
        
        shape_match = (actual_shape[1] == self.system_config['n_channels'] and 
                      actual_shape[2] == self.system_config['n_samples'])
        
        # 标签检查
        unique_labels = np.unique(y)
        print(f"  - 标签类别: {unique_labels}")
        print(f"  - 标签平衡性: {np.bincount(y)}")
        
        # 质量评估
        quality_score = 0
        
        if not (np.isnan(X).any() or np.isinf(X).any()):
            quality_score += 25
            print(f"  ✅ 数据无异常值")
        else:
            print(f"  ❌ 数据包含异常值")
        
        if len(unique_labels) == 2 and set(unique_labels) == {0, 1}:
            quality_score += 25
            print(f"  ✅ 标签格式正确")
        else:
            print(f"  ❌ 标签格式错误")
        
        if shape_match:
            quality_score += 25
            print(f"  ✅ 数据形状匹配系统配置")
        else:
            print(f"  ❌ 数据形状不匹配")
        
        if X.std() > 1:
            quality_score += 25
            print(f"  ✅ 数据变异性良好")
        else:
            print(f"  ⚠️ 数据变异性较低")
        
        print(f"\n📊 数据质量评分: {quality_score}/100")
        
        if quality_score >= 75:
            print(f"  🎉 数据质量优秀，适合训练预训练模型")
        elif quality_score >= 50:
            print(f"  ✅ 数据质量良好，可以使用")
        else:
            print(f"  ⚠️ 数据质量需要改进")
        
        return quality_score >= 50


def main():
    """主函数"""
    print("🧠 PhysioNet数据集处理 - 针对您的系统配置")
    print("=" * 70)
    print("系统配置:")
    print("  - 8通道脑电帽")
    print("  - 125Hz采样率")
    print("  - 运动想象vs平静状态")
    print("  - 2秒时间窗口")
    print()
    
    try:
        # 创建处理器
        processor = PhysioNetSystemProcessor()
        
        # 处理数据集（先处理20个受试者进行测试）
        print("📋 建议先处理20个受试者进行测试")
        max_subjects = int(input("请输入要处理的受试者数量 (1-109, 推荐20): ") or "20")
        max_subjects = min(max(max_subjects, 1), 109)
        
        X, y, subjects, runs = processor.process_all_files(max_subjects)
        
        # 验证数据质量
        if not processor.validate_dataset(X, y):
            print("⚠️ 数据质量检查未通过，但继续保存")
        
        # 保存数据集
        output_file = processor.save_processed_dataset(X, y, subjects, runs)
        
        print(f"\n🎉 PhysioNet数据集处理完成!")
        print(f"数据集文件: {output_file}")
        print(f"\n📊 数据集统计:")
        print(f"  - 样本数量: {X.shape[0]}")
        print(f"  - 数据形状: {X.shape}")
        print(f"  - 标签分布: 平静={np.sum(y==0)}, 运动想象={np.sum(y==1)}")
        print(f"  - 受试者数: {len(np.unique(subjects))}")
        
        print(f"\n🚀 下一步:")
        print(f"1. 使用此数据集训练预训练模型")
        print(f"2. 预期获得高质量的迁移学习效果")
        print(f"3. 在您的系统中享受更快的训练和更高的准确率")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 处理失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

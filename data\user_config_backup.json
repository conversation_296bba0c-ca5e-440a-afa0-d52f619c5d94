{"stimulation": {"dll_path": "D:\\NK_QT\\QT6\\NK\\NK\\Python_NK_System\\libs\\RecoveryDLL.dll", "max_current": 100.0, "min_current": 0.1, "current_step": 1.0, "default_frequency": 50.0, "default_pulse_width": 100.0, "default_relax_time": 1.0, "default_climb_time": 1.0, "default_work_time": 10.0, "default_fall_time": 1.0, "default_wave_type": 1, "max_channels": 2, "port_num": 7, "connection_timeout": 5.0, "current_steps": [0.1, 0.2, 0.5, 1.0, 2.0, 5.0]}, "eeg": {"serial_port": "COM8", "baud_rate": 115200, "sample_rate": 125.0, "channels": 8, "packet_size": 100, "timeout": 5.0, "channel_names": ["PZ", "P3", "P4", "C3", "CZ", "C4", "F3", "F4"], "packet_header": "5aa5", "packet_footer": "0d0a", "data_groups_per_packet": 4, "bytes_per_group": 24, "start_command": "START", "stop_command": "STOP", "connection_timeout": 3.0, "max_connection_attempts": 3}, "signal_processing": {"filter_config": {"highpass_freq": 0.5, "lowpass_freq": 50.0, "notch_freq": 50.0, "filter_order": 4}, "feature_extraction": {"window_size": 2.0, "overlap": 0.5, "frequency_bands": {"delta": [0.5, 4.0], "theta": [4.0, 8.0], "alpha": [8.0, 13.0], "mu": [8.0, 12.0], "beta": [13.0, 30.0], "gamma": [30.0, 50.0]}}, "deep_learning": {"model_type": "eegnet", "training_samples": 100, "validation_split": 0.2, "epochs": 50, "batch_size": 32, "learning_rate": 0.001, "temperature": 1.0, "dropout_rate": 0.25}}, "ui": {"theme": "dark", "language": "zh_CN", "font_family": "Microsoft YaHei", "font_size": 10, "window_size": [1400, 900], "auto_save_interval": 300}, "log": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file_path": "D:\\NK_QT\\QT6\\NK\\NK\\Python_NK_System\\logs\\nk_system.log", "max_file_size": 10485760, "backup_count": 5, "console_output": true}, "treatment": {"training_duration": {"rest_state": 5.0, "motor_imagery": 5.0, "feedback_delay": 1.0}, "session_config": {"max_sessions_per_day": 5, "min_rest_between_sessions": 300, "max_session_duration": 1800}, "feedback": {"visual_feedback": true, "audio_feedback": true, "haptic_feedback": true, "real_time_threshold": 0.1}}}
/* 基础样式文件 - Base Styles */
/* 通用样式，适用于所有主题 */

/* 全局字体设置 */
* {
    font-family: "Segoe UI", -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 滚动条样式 */
QScrollBar:vertical {
    border: none;
    background: transparent;
    width: 12px;
    margin: 0;
}

QScrollBar::handle:vertical {
    background: rgba(128, 128, 128, 0.3);
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background: rgba(128, 128, 128, 0.5);
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    border: none;
    background: none;
    height: 0;
}

QScrollBar:horizontal {
    border: none;
    background: transparent;
    height: 12px;
    margin: 0;
}

QScrollBar::handle:horizontal {
    background: rgba(128, 128, 128, 0.3);
    border-radius: 6px;
    min-width: 20px;
}

QScrollBar::handle:horizontal:hover {
    background: rgba(128, 128, 128, 0.5);
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:horizontal {
    border: none;
    background: none;
    width: 0;
}

/* 工具提示样式 */
QToolTip {
    border: 1px solid #cccccc;
    border-radius: 6px;
    padding: 8px;
    font-size: 12px;
    opacity: 230;
}

/* 分割器样式 */
QSplitter::handle {
    background: transparent;
}

QSplitter::handle:horizontal {
    width: 2px;
}

QSplitter::handle:vertical {
    height: 2px;
}

/* 通用动画类 */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

.slide-in-left {
    animation: slideInLeft 0.3s ease-in-out;
}

.slide-in-right {
    animation: slideInRight 0.3s ease-in-out;
}

/* 通用布局类 */
.flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

.flex-column {
    display: flex;
    flex-direction: column;
}

.flex-row {
    display: flex;
    flex-direction: row;
}

/* 间距类 */
.margin-xs { margin: 4px; }
.margin-sm { margin: 8px; }
.margin-base { margin: 16px; }
.margin-lg { margin: 24px; }
.margin-xl { margin: 32px; }

.padding-xs { padding: 4px; }
.padding-sm { padding: 8px; }
.padding-base { padding: 16px; }
.padding-lg { padding: 24px; }
.padding-xl { padding: 32px; }

/* 圆角类 */
.rounded-sm { border-radius: 6px; }
.rounded { border-radius: 8px; }
.rounded-lg { border-radius: 12px; }
.rounded-xl { border-radius: 16px; }
.rounded-full { border-radius: 9999px; }

/* 字体大小类 */
.text-xs { font-size: 12px; }
.text-sm { font-size: 14px; }
.text-base { font-size: 16px; }
.text-lg { font-size: 18px; }
.text-xl { font-size: 20px; }
.text-xxl { font-size: 24px; }
.text-xxxl { font-size: 32px; }

/* 字体粗细类 */
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

/* 文本对齐类 */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

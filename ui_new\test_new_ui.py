#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新UI框架测试启动文件
Test Launcher for New UI Framework

作者: AI Assistant
版本: 2.0.0
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QIcon

# 导入新UI组件
from ui_new.main_window_new import ModernMainWindow

# 导入现有业务逻辑（直接迁移）
from core.database_manager import DatabaseManager
from core.auth_manager import AuthManager
from core.logger_system import LoggerSystem
from core.performance_optimizer import PerformanceOptimizer
from utils.app_config import AppConfig


class NewUITestApp:
    """新UI框架测试应用"""
    
    def __init__(self):
        self.app = None
        self.main_window = None
        self.db_manager = None
        self.auth_manager = None
        self.logger_system = None
        self.performance_optimizer = None
        
        # 设置日志
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
    def setup_logging(self):
        """设置日志系统"""
        try:
            # 配置基础日志
            logging.basicConfig(
                level=logging.INFO,
                format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                handlers=[
                    logging.StreamHandler(),
                    logging.FileHandler('logs/new_ui_test.log', encoding='utf-8')
                ]
            )
            
            # 确保日志目录存在
            log_dir = Path('logs')
            log_dir.mkdir(exist_ok=True)
            
        except Exception as e:
            print(f"设置日志系统失败: {e}")
    
    def init_business_components(self):
        """初始化业务组件（直接迁移）"""
        try:
            self.logger.info("初始化业务组件...")
            
            # 初始化数据库管理器
            self.db_manager = DatabaseManager()
            # 检查数据库连接（使用现有方法）
            try:
                # 测试数据库连接
                self.db_manager.get_hospital_info()
                self.logger.info("数据库连接正常")
            except Exception as e:
                self.logger.warning(f"数据库连接测试失败: {e}")
                # 继续运行，允许在没有数据库的情况下测试UI
            
            # 初始化权限管理器
            self.auth_manager = AuthManager(self.db_manager)
            
            # 初始化日志系统
            self.logger_system = LoggerSystem()
            
            # 初始化性能优化器
            self.performance_optimizer = PerformanceOptimizer()
            
            self.logger.info("业务组件初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"初始化业务组件失败: {e}")
            return False
    
    def create_application(self):
        """创建应用程序"""
        try:
            # 创建QApplication
            self.app = QApplication(sys.argv)
            self.app.setApplicationName(AppConfig.APP_NAME)
            self.app.setApplicationVersion(AppConfig.VERSION)
            self.app.setOrganizationName("NK Medical")
            
            # 设置应用图标
            try:
                icon_path = AppConfig.PROJECT_ROOT / "resources" / "images" / "app_icon.png"
                if icon_path.exists():
                    self.app.setWindowIcon(QIcon(str(icon_path)))
            except Exception as e:
                self.logger.warning(f"设置应用图标失败: {e}")
            
            # 设置高DPI支持（移除已弃用的设置）
            # Qt6中这些设置已默认启用
            
            self.logger.info("应用程序创建完成")
            return True
            
        except Exception as e:
            self.logger.error(f"创建应用程序失败: {e}")
            return False
    
    def create_main_window(self):
        """创建主窗口"""
        try:
            # 创建现代化主窗口
            self.main_window = ModernMainWindow()
            
            # 设置业务组件（直接迁移，无适配器）
            if self.db_manager:
                self.main_window.set_database_manager(self.db_manager)
            
            if self.auth_manager:
                self.main_window.set_auth_manager(self.auth_manager)
            
            if self.performance_optimizer:
                self.main_window.set_performance_optimizer(self.performance_optimizer)
            
            # 连接窗口关闭信号
            self.main_window.window_closing.connect(self.on_window_closing)
            
            self.logger.info("现代化主窗口创建完成")
            return True
            
        except Exception as e:
            self.logger.error(f"创建主窗口失败: {e}")
            return False
    
    def show_main_window(self):
        """显示主窗口"""
        try:
            if self.main_window:
                self.main_window.show()
                self.main_window.raise_()
                self.main_window.activateWindow()
                
                self.logger.info("主窗口已显示")
                return True
            else:
                self.logger.error("主窗口未创建")
                return False
                
        except Exception as e:
            self.logger.error(f"显示主窗口失败: {e}")
            return False
    
    def on_window_closing(self):
        """窗口关闭处理"""
        try:
            self.logger.info("正在关闭应用程序...")
            
            # 清理业务组件
            if self.performance_optimizer:
                self.performance_optimizer.stop()
            
            if self.db_manager:
                self.db_manager.close()
            
            # 退出应用
            if self.app:
                self.app.quit()
                
        except Exception as e:
            self.logger.error(f"关闭应用程序失败: {e}")
    
    def run(self):
        """运行应用程序"""
        try:
            self.logger.info("启动新UI框架测试应用...")
            
            # 创建应用程序
            if not self.create_application():
                return 1
            
            # 初始化业务组件
            if not self.init_business_components():
                QMessageBox.critical(None, "错误", "业务组件初始化失败，请检查日志")
                return 1
            
            # 创建主窗口
            if not self.create_main_window():
                QMessageBox.critical(None, "错误", "主窗口创建失败，请检查日志")
                return 1
            
            # 显示主窗口
            if not self.show_main_window():
                QMessageBox.critical(None, "错误", "主窗口显示失败，请检查日志")
                return 1
            
            self.logger.info("新UI框架测试应用启动成功")
            
            # 运行应用程序事件循环
            return self.app.exec()
            
        except Exception as e:
            self.logger.error(f"运行应用程序失败: {e}")
            if self.app:
                QMessageBox.critical(None, "严重错误", f"应用程序运行失败:\n{e}")
            return 1


def main():
    """主函数"""
    try:
        # 创建并运行测试应用
        test_app = NewUITestApp()
        exit_code = test_app.run()
        
        print(f"应用程序退出，退出码: {exit_code}")
        return exit_code
        
    except Exception as e:
        print(f"应用程序启动失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())

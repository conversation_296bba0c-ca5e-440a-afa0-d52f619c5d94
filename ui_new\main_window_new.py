#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代化主窗口
Modern Main Window

作者: AI Assistant
版本: 2.0.0
"""

import logging
from typing import Optional, Dict, Any
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QHBoxLayout, QVBoxLayout, QStackedWidget,
    QSplitter, QMessageBox, QApplication, QLabel, QPushButton
)
from PySide6.QtCore import Qt, Signal, QTimer, QPropertyAnimation, QEasingCurve
from PySide6.QtGui import QIcon

# 导入新UI组件
from .components.sidebar import ModernSidebar
from .components.top_bar import ModernTopBar
from .themes.theme_manager import ThemeManager

# 导入现有核心业务逻辑（直接迁移）
from core.database_manager import DatabaseManager
from core.auth_manager import AuthManager, Permission
from core.logger_system import get_logger_system
from core.performance_optimizer import PerformanceOptimizer
from utils.app_config import AppConfig


class ModernMainWindow(QMainWindow):
    """现代化主窗口类 - 单页应用架构"""
    
    # 信号定义
    window_closing = Signal()
    user_logged_in = Signal(str)
    user_logged_out = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 初始化属性
        self.db_manager: Optional[DatabaseManager] = None
        self.auth_manager: Optional[AuthManager] = None
        self.performance_optimizer: Optional[PerformanceOptimizer] = None
        self.logger = logging.getLogger(__name__)
        self.logger_system = get_logger_system()
        
        # 用户状态
        self.current_user = None
        self.current_user_role = None
        self.is_user_logged_in = False
        
        # UI组件
        self.central_widget = None
        self.main_splitter = None
        self.sidebar = None
        self.top_bar = None
        self.content_area = None
        self.stacked_widget = None
        
        # 主题管理器
        self.theme_manager = ThemeManager()
        
        # 页面缓存（延迟加载）
        self.page_cache = {}
        self.page_classes = {}
        
        # 动画
        self.page_transition_animation = None
        
        # 定时器
        self.status_update_timer = QTimer()
        self.auto_save_timer = QTimer()
        
        # 初始化界面
        self.init_ui()
        self.setup_connections()
        self.setup_animations()
        self.setup_timers()
        
        # 应用默认主题
        self.theme_manager.apply_theme_styles()
        
        # 初始状态：隐藏敏感内容
        self.hide_sensitive_content()
        
        self.logger.info("现代化主窗口初始化完成")
    
    def init_ui(self):
        """初始化用户界面"""
        try:
            # 设置窗口属性
            self.setObjectName("ModernMainWindow")
            self.setWindowTitle(f"{AppConfig.APP_NAME} v{AppConfig.VERSION} - 现代化版本")
            self.setMinimumSize(1400, 900)
            
            # 设置窗口图标
            self.set_window_icon()
            
            # 创建中央部件
            self.create_central_widget()
            
            # 不创建传统菜单栏和状态栏（现代化单页应用）
            
            self.logger.info("现代化主窗口UI初始化完成")
            
        except Exception as e:
            self.logger.error(f"现代化主窗口UI初始化失败: {e}")
            raise
    
    def set_window_icon(self):
        """设置窗口图标"""
        try:
            icon_path = AppConfig.PROJECT_ROOT / "resources" / "images" / "app_icon.png"
            if icon_path.exists():
                self.setWindowIcon(QIcon(str(icon_path)))
        except Exception as e:
            self.logger.warning(f"设置窗口图标失败: {e}")
    
    def create_central_widget(self):
        """创建中央部件 - 现代化单页应用布局"""
        try:
            self.central_widget = QWidget()
            self.setCentralWidget(self.central_widget)
            
            # 主布局：水平布局（侧边栏 + 主内容区）
            main_layout = QHBoxLayout(self.central_widget)
            main_layout.setContentsMargins(0, 0, 0, 0)
            main_layout.setSpacing(0)
            
            # 创建分割器
            self.main_splitter = QSplitter(Qt.Horizontal)
            main_layout.addWidget(self.main_splitter)
            
            # 创建侧边栏
            self.create_sidebar()
            self.main_splitter.addWidget(self.sidebar)
            
            # 创建主内容区
            self.create_main_content()
            self.main_splitter.addWidget(self.main_content_widget)
            
            # 设置分割器比例
            self.main_splitter.setStretchFactor(0, 0)  # 侧边栏固定
            self.main_splitter.setStretchFactor(1, 1)  # 主内容区自适应
            self.main_splitter.setSizes([280, 1120])
            
            # 禁用分割器手柄（固定布局）
            self.main_splitter.handle(1).setEnabled(False)
            
        except Exception as e:
            self.logger.error(f"创建中央部件失败: {e}")
            raise
    
    def create_sidebar(self):
        """创建侧边栏"""
        try:
            self.sidebar = ModernSidebar()
            
            # 连接侧边栏信号
            self.sidebar.page_changed.connect(self.on_page_changed)
            self.sidebar.collapse_toggled.connect(self.on_sidebar_collapsed)
            
        except Exception as e:
            self.logger.error(f"创建侧边栏失败: {e}")
            raise
    
    def create_main_content(self):
        """创建主内容区"""
        try:
            self.main_content_widget = QWidget()
            
            # 主内容区布局：垂直布局（顶部栏 + 内容区域）
            content_layout = QVBoxLayout(self.main_content_widget)
            content_layout.setContentsMargins(0, 0, 0, 0)
            content_layout.setSpacing(0)
            
            # 创建顶部栏
            self.create_top_bar()
            content_layout.addWidget(self.top_bar)
            
            # 创建内容区域
            self.create_content_area()
            content_layout.addWidget(self.content_area)
            
        except Exception as e:
            self.logger.error(f"创建主内容区失败: {e}")
            raise
    
    def create_top_bar(self):
        """创建顶部栏"""
        try:
            self.top_bar = ModernTopBar()
            
            # 连接顶部栏信号
            self.top_bar.menu_toggle_clicked.connect(self.toggle_sidebar)
            self.top_bar.theme_changed.connect(self.on_theme_changed)
            
        except Exception as e:
            self.logger.error(f"创建顶部栏失败: {e}")
            raise
    
    def create_content_area(self):
        """创建内容区域"""
        try:
            self.content_area = QWidget()
            self.content_area.setObjectName("content_area")
            
            # 内容区域布局
            content_layout = QVBoxLayout(self.content_area)
            content_layout.setContentsMargins(0, 0, 0, 0)
            content_layout.setSpacing(0)
            
            # 创建页面堆栈
            self.stacked_widget = QStackedWidget()
            content_layout.addWidget(self.stacked_widget)
            
            # 初始化页面系统
            self.init_page_system()
            
        except Exception as e:
            self.logger.error(f"创建内容区域失败: {e}")
            raise
    
    def init_page_system(self):
        """初始化页面系统"""
        try:
            # 直接导入页面类（直接迁移模式）
            from .pages.dashboard_page import DashboardPage
            # 其他页面将在后续步骤中添加

            self.page_classes = {
                'dashboard': DashboardPage,      # 实时监测仪表板
                'patients': None,       # 患者管理（待迁移）
                'treatment': None,      # 治疗系统（待迁移）
                'data_management': None, # 数据管理（待迁移）
                'reports': None,        # 报告分析（待迁移）
                'users': None,          # 用户管理（待迁移）
                'settings': None,       # 系统设置（待迁移）
            }

            # 页面实例缓存
            self.page_instances = {}

            # 创建登录提示页面
            self.create_login_prompt_page()

            # 设置默认页面
            self.stacked_widget.setCurrentWidget(self.login_prompt_page)

        except Exception as e:
            self.logger.error(f"初始化页面系统失败: {e}")
            raise
    
    def create_login_prompt_page(self):
        """创建登录提示页面"""
        try:
            self.login_prompt_page = QWidget()
            self.login_prompt_page.setObjectName("login_prompt_page")
            
            layout = QVBoxLayout(self.login_prompt_page)
            layout.setAlignment(Qt.AlignCenter)
            layout.setSpacing(24)
            
            # 系统Logo
            logo_label = QLabel("NK")
            logo_label.setObjectName("logo")
            logo_label.setAlignment(Qt.AlignCenter)
            logo_label.setFixedSize(128, 128)
            layout.addWidget(logo_label)
            
            # 系统标题
            title_label = QLabel(f"{AppConfig.APP_NAME}")
            title_label.setProperty("class", "text-xxxl font-bold text-center")
            title_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(title_label)
            
            # 版本信息
            version_label = QLabel(f"版本 {AppConfig.VERSION} - 现代化版本")
            version_label.setProperty("class", "text-lg text-center")
            version_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(version_label)
            
            # 隐私保护提示
            privacy_label = QLabel("🔒 为保护患者隐私，请先登录系统")
            privacy_label.setProperty("class", "text-base text-center")
            privacy_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(privacy_label)
            
            # 合规说明
            compliance_label = QLabel("本系统符合医疗器械软件相关规定\n未经授权不得访问患者信息")
            compliance_label.setProperty("class", "text-sm text-center")
            compliance_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(compliance_label)
            
            # 登录按钮
            login_button = QPushButton("点击登录")
            login_button.setProperty("class", "btn-primary")
            login_button.setFixedSize(200, 50)
            login_button.clicked.connect(self.show_login_dialog)
            
            button_layout = QHBoxLayout()
            button_layout.addStretch()
            button_layout.addWidget(login_button)
            button_layout.addStretch()
            layout.addLayout(button_layout)
            
            # 添加到堆栈
            self.stacked_widget.addWidget(self.login_prompt_page)
            
        except Exception as e:
            self.logger.error(f"创建登录提示页面失败: {e}")
    
    def setup_connections(self):
        """设置信号连接"""
        try:
            # 用户登录/登出信号
            self.user_logged_in.connect(self.on_user_logged_in)
            self.user_logged_out.connect(self.on_user_logged_out)
            
            # 主题管理器信号
            self.theme_manager.theme_changed.connect(self.on_theme_applied)
            
        except Exception as e:
            self.logger.error(f"设置信号连接失败: {e}")
    
    def setup_animations(self):
        """设置动画效果"""
        try:
            # 页面切换动画
            self.page_transition_animation = QPropertyAnimation(self.stacked_widget, b"currentIndex")
            self.page_transition_animation.setDuration(300)
            self.page_transition_animation.setEasingCurve(QEasingCurve.OutCubic)
            
        except Exception as e:
            self.logger.error(f"设置动画效果失败: {e}")
    
    def setup_timers(self):
        """设置定时器"""
        try:
            # 状态更新定时器
            self.status_update_timer.timeout.connect(self.update_status)
            self.status_update_timer.start(1000)  # 每秒更新
            
            # 自动保存定时器
            auto_save_interval = AppConfig.UI_CONFIG.get('auto_save_interval', 300) * 1000
            self.auto_save_timer.timeout.connect(self.auto_save)
            self.auto_save_timer.start(auto_save_interval)
            
        except Exception as e:
            self.logger.error(f"设置定时器失败: {e}")
    
    def on_page_changed(self, page_id: str):
        """处理页面切换"""
        try:
            if page_id == "logout":
                self.handle_logout()
                return
            
            # 检查权限
            if not self.check_page_permission(page_id):
                QMessageBox.warning(self, "权限不足", f"您没有访问{page_id}页面的权限")
                return
            
            # 切换页面
            self.switch_to_page(page_id)
            
            # 更新页面标题
            self.update_page_title(page_id)
            
        except Exception as e:
            self.logger.error(f"处理页面切换失败: {e}")
    
    def on_sidebar_collapsed(self, is_collapsed: bool):
        """处理侧边栏折叠"""
        try:
            # 更新分割器尺寸
            if is_collapsed:
                self.main_splitter.setSizes([80, 1320])
            else:
                self.main_splitter.setSizes([280, 1120])
                
        except Exception as e:
            self.logger.error(f"处理侧边栏折叠失败: {e}")
    
    def toggle_sidebar(self):
        """切换侧边栏折叠状态"""
        self.sidebar.toggle_collapse()
    
    def on_theme_changed(self, theme_name: str):
        """处理主题切换"""
        try:
            self.theme_manager.switch_theme(theme_name)
        except Exception as e:
            self.logger.error(f"处理主题切换失败: {e}")
    
    def on_theme_applied(self, theme_name: str):
        """主题应用完成"""
        self.logger.info(f"主题已切换到: {theme_name}")
    
    def check_page_permission(self, page_id: str) -> bool:
        """检查页面访问权限"""
        if not self.auth_manager or not self.is_user_logged_in:
            return False
        
        # 权限映射
        permission_map = {
            'dashboard': Permission.PATIENT_VIEW,
            'patients': Permission.PATIENT_VIEW,
            'treatment': Permission.TREATMENT_OPERATE,
            'data_management': Permission.DATA_ANALYSIS,
            'reports': Permission.DATA_ANALYSIS,
            'users': Permission.USER_MANAGE,
            'settings': Permission.SYSTEM_CONFIG,
        }
        
        required_permission = permission_map.get(page_id)
        if required_permission:
            return self.auth_manager.has_permission(required_permission)
        
        return True
    
    def switch_to_page(self, page_id: str):
        """切换到指定页面（直接迁移模式）"""
        try:
            # 检查页面类是否存在
            page_class = self.page_classes.get(page_id)
            if not page_class:
                self.logger.warning(f"页面 {page_id} 尚未实现")
                return

            # 检查页面实例是否已创建
            if page_id not in self.page_instances:
                # 创建页面实例
                page_instance = page_class()

                # 直接设置业务逻辑组件（无适配器）
                if self.db_manager:
                    page_instance.set_database_manager(self.db_manager)
                if self.auth_manager:
                    page_instance.set_auth_manager(self.auth_manager)

                # 连接页面信号
                self.connect_page_signals(page_instance, page_id)

                # 添加到堆栈
                self.stacked_widget.addWidget(page_instance)
                self.page_instances[page_id] = page_instance

                self.logger.info(f"页面 {page_id} 创建完成")

            # 切换到页面
            page_instance = self.page_instances[page_id]
            self.stacked_widget.setCurrentWidget(page_instance)

            # 调用页面显示方法
            page_instance.show_page()

            self.logger.info(f"已切换到页面: {page_id}")

        except Exception as e:
            self.logger.error(f"切换页面失败: {e}")
            QMessageBox.critical(self, "错误", f"切换页面失败: {e}")

    def connect_page_signals(self, page_instance, page_id: str):
        """连接页面信号（直接迁移模式）"""
        try:
            # 根据页面类型连接特定信号
            if page_id == "dashboard":
                # 仪表板页面信号连接
                if hasattr(page_instance, 'patient_selection_requested'):
                    page_instance.patient_selection_requested.connect(self.show_patient_selection)
                if hasattr(page_instance, 'training_started'):
                    page_instance.training_started.connect(self.on_training_started)
                if hasattr(page_instance, 'recording_started'):
                    page_instance.recording_started.connect(self.on_recording_started)

            # 通用信号连接
            if hasattr(page_instance, 'status_changed'):
                page_instance.status_changed.connect(self.on_page_status_changed)

        except Exception as e:
            self.logger.error(f"连接页面信号失败: {e}")

    def show_patient_selection(self):
        """显示患者选择（从仪表板触发）"""
        try:
            # 切换到患者管理页面
            self.sidebar.set_active_page("patients")
            self.on_page_changed("patients")
        except Exception as e:
            self.logger.error(f"显示患者选择失败: {e}")

    def on_training_started(self):
        """训练开始处理"""
        try:
            self.top_bar.set_system_status("normal", "训练进行中")
            self.logger.info("康复训练已开始")
        except Exception as e:
            self.logger.error(f"处理训练开始失败: {e}")

    def on_recording_started(self):
        """记录开始处理"""
        try:
            self.top_bar.set_signal_status("normal", "正在记录")
            self.logger.info("脑电记录已开始")
        except Exception as e:
            self.logger.error(f"处理记录开始失败: {e}")

    def on_page_status_changed(self, status_type: str, message: str):
        """页面状态变更处理"""
        try:
            if status_type == "device":
                self.top_bar.set_device_status("normal", message)
            elif status_type == "signal":
                self.top_bar.set_signal_status("normal", message)
            elif status_type == "system":
                self.top_bar.set_system_status("normal", message)
        except Exception as e:
            self.logger.error(f"处理页面状态变更失败: {e}")
    
    def update_page_title(self, page_id: str):
        """更新页面标题"""
        title_map = {
            'dashboard': '实时监测仪表板',
            'patients': '患者管理',
            'treatment': '治疗系统',
            'data_management': '数据管理',
            'reports': '报告分析',
            'users': '用户管理',
            'settings': '系统设置',
        }
        
        title = title_map.get(page_id, '未知页面')
        self.top_bar.set_page_title(title)
    
    def handle_logout(self):
        """处理退出系统"""
        if self.auth_manager:
            self.auth_manager.logout()
        self.user_logged_out.emit()
    
    def show_login_dialog(self):
        """显示登录对话框"""
        if not self.auth_manager:
            QMessageBox.warning(self, "错误", "权限管理器未初始化")
            return
        
        # 导入登录对话框
        from ui.login_dialog import LoginDialog
        login_dialog = LoginDialog(self.auth_manager, self)
        login_dialog.login_successful.connect(self.on_login_successful)
        login_dialog.exec()
    
    def on_login_successful(self, user_info: dict):
        """处理登录成功"""
        try:
            self.current_user = user_info['name']
            self.current_user_role = user_info['role']
            self.is_user_logged_in = True
            
            # 更新侧边栏用户信息
            role_display = getattr(user_info['role'], 'value', str(user_info['role']))
            self.sidebar.update_user_info(user_info['name'], role_display)
            
            # 显示敏感内容
            self.show_sensitive_content()
            
            # 发送登录信号
            self.user_logged_in.emit(user_info['name'])
            
            # 记录日志
            self.logger_system.log_operation(user_info['name'], "用户登录成功")
            
        except Exception as e:
            self.logger.error(f"处理登录成功失败: {e}")
    
    def on_user_logged_in(self, username: str):
        """用户登录处理"""
        self.logger.info(f"用户 {username} 已登录")
    
    def on_user_logged_out(self):
        """用户登出处理"""
        if self.current_user:
            self.logger_system.log_operation(self.current_user, "用户登出")
        
        self.current_user = None
        self.current_user_role = None
        self.is_user_logged_in = False
        
        # 更新侧边栏用户信息
        self.sidebar.update_user_info("未登录", "请先登录")
        
        # 隐藏敏感内容
        self.hide_sensitive_content()
    
    def hide_sensitive_content(self):
        """隐藏敏感内容"""
        try:
            # 切换到登录提示页面
            self.stacked_widget.setCurrentWidget(self.login_prompt_page)
            
            # 重置侧边栏活跃状态
            self.sidebar.set_active_page("")
            
        except Exception as e:
            self.logger.error(f"隐藏敏感内容失败: {e}")
    
    def show_sensitive_content(self):
        """显示敏感内容"""
        try:
            # 切换到默认页面（实时监测）
            self.sidebar.set_active_page("dashboard")
            self.on_page_changed("dashboard")
            
        except Exception as e:
            self.logger.error(f"显示敏感内容失败: {e}")
    
    def update_status(self):
        """更新状态"""
        try:
            # 更新设备状态
            if self.db_manager:
                self.top_bar.set_system_status("normal", "系统正常")
            else:
                self.top_bar.set_system_status("error", "数据库未连接")
                
        except Exception as e:
            self.logger.error(f"更新状态失败: {e}")
    
    def auto_save(self):
        """自动保存"""
        try:
            # 实现自动保存逻辑
            pass
        except Exception as e:
            self.logger.error(f"自动保存失败: {e}")
    
    # 业务逻辑设置方法（直接迁移）
    def set_database_manager(self, db_manager: DatabaseManager):
        """设置数据库管理器"""
        self.db_manager = db_manager
        self.logger.info("数据库管理器设置完成")
    
    def set_auth_manager(self, auth_manager: AuthManager):
        """设置权限管理器"""
        self.auth_manager = auth_manager
        self.logger.info("权限管理器设置完成")
    
    def set_performance_optimizer(self, optimizer: PerformanceOptimizer):
        """设置性能优化器"""
        self.performance_optimizer = optimizer
        if optimizer:
            optimizer.start()
        self.logger.info("性能优化器设置完成")
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        self.window_closing.emit()
        super().closeEvent(event)

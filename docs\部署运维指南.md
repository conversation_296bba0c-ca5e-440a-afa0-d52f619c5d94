# NK脑机接口系统 - 部署运维指南

## 系统要求

### 硬件要求
- **CPU**: Intel i5-8400 或 AMD Ryzen 5 2600 及以上
- **内存**: 8GB RAM (推荐16GB)
- **存储**: 至少5GB可用空间 (推荐SSD)
- **显卡**: 支持DirectX 11的独立显卡 (可选，用于深度学习加速)
- **接口**: 
  - USB 2.0/3.0端口 (电刺激设备)
  - 串口或USB转串口 (脑电设备)
  - 网络接口 (数据上传)

### 软件要求
- **操作系统**: Windows 10 (1903及以上) 或 Windows 11
- **Python**: 3.9.0 - 3.11.x (推荐3.11.5)
- **Visual C++ Redistributable**: 2019或更新版本
- **Microsoft .NET Framework**: 4.7.2或更新版本

### 网络要求
- **带宽**: 至少1Mbps上行带宽 (数据上传)
- **延迟**: 小于100ms (VR系统通信)
- **端口**: 
  - HTTP: 80, 443 (数据上传)
  - UDP: 3004, 3005 (VR通信)

## 安装部署

### 1. 环境准备

#### 1.1 Python环境安装
```bash
# 下载Python 3.11.5
# 从 https://www.python.org/downloads/ 下载官方安装包

# 安装时注意勾选：
# ✓ Add Python to PATH
# ✓ Install for all users
# ✓ pip (package installer)

# 验证安装
python --version
pip --version
```

#### 1.2 系统依赖安装
```bash
# 安装Visual C++ Redistributable
# 下载地址: https://aka.ms/vs/17/release/vc_redist.x64.exe

# 安装.NET Framework
# 下载地址: https://dotnet.microsoft.com/download/dotnet-framework
```

### 2. 项目部署

#### 2.1 获取项目文件
```bash
# 方式1: 从版本控制系统获取
git clone <repository_url>
cd Python_NK_System

# 方式2: 解压发布包
unzip NK_System_v1.0.0.zip
cd Python_NK_System
```

#### 2.2 创建虚拟环境 (推荐)
```bash
# 创建虚拟环境
python -m venv nk_env

# 激活虚拟环境
# Windows
nk_env\Scripts\activate

# 验证环境
where python
where pip
```

#### 2.3 安装依赖包
```bash
# 自动安装所有依赖
python install_dependencies.py

# 或手动安装核心依赖
pip install PySide6>=6.4.0
pip install numpy>=1.21.0
pip install matplotlib>=3.5.0
pip install pyserial>=3.5

# 安装可选依赖
pip install scipy>=1.7.0
pip install scikit-learn>=1.0.0
pip install tensorflow>=2.18.1
```

#### 2.4 验证安装
```bash
# 检查依赖
python check_dependencies.py

# 运行系统测试
python test_system.py

# 快速功能验证
python quick_start.py
```

### 3. 配置设置

#### 3.1 基础配置
```bash
# 编辑配置文件
notepad data\user_config.json

# 主要配置项：
{
  "database": {
    "path": "D:\\NK_System\\data\\nk_system.db",
    "min_treatment_duration": 1
  },
  "eeg": {
    "serial_port": "COM8",
    "baud_rate": 115200
  },
  "stimulation": {
    "port_num": 1,
    "dll_path": "D:\\NK_System\\libs\\RecoveryDLL.dll"
  }
}
```

#### 3.2 设备配置
```bash
# 1. 脑电设备配置
# - 确认设备连接的串口号 (设备管理器查看)
# - 修改 user_config.json 中的 serial_port 配置

# 2. 电刺激设备配置
# - 确认DLL文件路径正确
# - 检查设备驱动是否安装
# - 测试设备连接
```

#### 3.3 网络配置
```bash
# 编辑网络配置
# HTTP服务器地址 (数据上传)
"http": {
    "base_url": "http://*************:8082/shdekf/Api/",
    "timeout": 30
}

# UDP通信配置 (VR系统)
"udp": {
    "vr_host": "127.0.0.1",
    "vr_port": 3004,
    "local_port": 3005
}
```

### 4. 首次启动

#### 4.1 数据库初始化
```bash
# 系统首次启动会自动创建数据库
python main.py

# 如果需要手动初始化
python -c "from core.database_manager import DatabaseManager; db = DatabaseManager(); db.initialize()"
```

#### 4.2 创建管理员账户
```bash
# 默认管理员账户
用户名: admin
密码: 123456

# 首次登录后请立即修改密码
```

#### 4.3 系统验证
```bash
# 1. 启动系统
python main.py

# 2. 检查各模块状态
# - 数据库连接状态
# - 设备连接状态
# - 网络连接状态
# - 日志系统状态

# 3. 功能测试
# - 用户登录
# - 患者管理
# - 设备连接
# - 数据记录
```

## 生产环境配置

### 1. 性能优化

#### 1.1 系统优化
```bash
# Windows系统优化
# 1. 关闭不必要的后台程序
# 2. 设置高性能电源计划
# 3. 关闭Windows更新自动重启
# 4. 设置虚拟内存大小 (推荐16GB)

# 进程优先级设置
# 在任务管理器中将python.exe设置为"高"优先级
```

#### 1.2 数据库优化
```sql
-- SQLite优化设置
PRAGMA journal_mode = WAL;
PRAGMA synchronous = NORMAL;
PRAGMA cache_size = 10000;
PRAGMA temp_store = MEMORY;
```

#### 1.3 内存优化
```python
# 在 user_config.json 中调整缓冲区大小
"signal_processing": {
    "buffer_size": 1000,        # 减少内存使用
    "batch_size": 32           # 批处理大小
}
```

### 2. 安全配置

#### 2.1 用户权限
```bash
# 1. 修改默认管理员密码
# 2. 创建具体角色用户
# 3. 设置密码策略
# 4. 定期更新密码

# 权限配置示例
{
    "password_policy": {
        "min_length": 8,
        "require_numbers": true,
        "max_age_days": 90
    }
}
```

#### 2.2 数据安全
```bash
# 1. 设置数据库文件权限
icacls data\nk_system.db /grant:r Users:R

# 2. 配置自动备份
"database": {
    "auto_backup": true,
    "backup_interval": 86400,  # 24小时
    "backup_path": "D:\\NK_Backup"
}

# 3. 加密敏感配置
# 使用环境变量存储敏感信息
set NK_DB_PASSWORD=your_password
```

#### 2.3 网络安全
```bash
# 1. 配置防火墙规则
# 允许程序访问网络
netsh advfirewall firewall add rule name="NK System" dir=out action=allow program="python.exe"

# 2. 使用HTTPS (如果服务器支持)
"http": {
    "base_url": "https://secure-server.com/api/",
    "verify_ssl": true
}
```

### 3. 监控配置

#### 3.1 日志监控
```bash
# 配置日志级别
"log": {
    "level": "INFO",           # 生产环境使用INFO
    "max_file_size": 10485760, # 10MB
    "backup_count": 10         # 保留10个备份
}

# 日志轮转配置
# 系统会自动轮转日志文件
```

#### 3.2 性能监控
```python
# 启用性能监控
"monitoring": {
    "enable_performance_monitor": true,
    "memory_threshold": 1024,      # MB
    "cpu_threshold": 80,           # %
    "disk_threshold": 90           # %
}
```

#### 3.3 设备监控
```bash
# 设备状态监控
# 系统会自动监控设备连接状态
# 异常时会记录到日志并发送告警
```

## 运维管理

### 1. 日常维护

#### 1.1 日志管理
```bash
# 查看系统日志
type logs\nk_system.log

# 查看错误日志
type logs\error.log | findstr "ERROR"

# 清理旧日志 (保留最近30天)
forfiles /p logs /s /m *.log.* /d -30 /c "cmd /c del @path"
```

#### 1.2 数据库维护
```bash
# 数据库完整性检查
python -c "
from core.database_manager import DatabaseManager
db = DatabaseManager()
with db.get_connection() as conn:
    result = conn.execute('PRAGMA integrity_check').fetchone()
    print(f'数据库状态: {result[0]}')
"

# 数据库优化
python -c "
from core.database_manager import DatabaseManager
db = DatabaseManager()
with db.get_connection() as conn:
    conn.execute('VACUUM')
    print('数据库优化完成')
"

# 手动备份
python -c "
from core.database_manager import DatabaseManager
db = DatabaseManager()
if db.backup_database():
    print('备份成功')
else:
    print('备份失败')
"
```

#### 1.3 系统清理
```bash
# 清理临时文件
del /q /s __pycache__\*
del /q /s *.pyc

# 清理测试数据
del /q test_data\*

# 磁盘空间检查
dir /s
```

### 2. 故障处理

#### 2.1 常见故障诊断
```bash
# 1. 系统无法启动
# 检查Python环境
python --version

# 检查依赖包
python check_dependencies.py

# 查看启动日志
type logs\nk_system.log | findstr "ERROR\|CRITICAL"

# 2. 设备连接失败
# 检查串口状态
mode COM8

# 检查USB设备
devmgmt.msc

# 3. 数据库错误
# 检查数据库文件
dir data\nk_system.db

# 检查磁盘空间
fsutil volume diskfree C:
```

#### 2.2 故障恢复
```bash
# 1. 从备份恢复数据库
copy data\backup\nk_system_backup_*.db data\nk_system.db

# 2. 重置配置文件
copy data\user_config_backup.json data\user_config.json

# 3. 重新安装依赖
pip uninstall -y -r requirements.txt
python install_dependencies.py

# 4. 完全重置 (谨慎使用)
# 删除所有配置和数据，重新初始化
```

### 3. 更新升级

#### 3.1 版本更新流程
```bash
# 1. 备份当前系统
xcopy /E /I Python_NK_System Python_NK_System_Backup

# 2. 备份数据库
copy data\nk_system.db data\nk_system_backup_before_update.db

# 3. 下载新版本
# 获取新版本文件

# 4. 停止系统
# 关闭所有NK系统进程

# 5. 更新文件
# 覆盖程序文件，保留data目录

# 6. 更新依赖
python install_dependencies.py

# 7. 数据库迁移 (如需要)
python migrate_database.py

# 8. 验证更新
python test_system.py

# 9. 启动系统
python main.py
```

#### 3.2 回滚流程
```bash
# 如果更新失败，回滚到之前版本
# 1. 停止系统
# 2. 恢复程序文件
xcopy /E /Y Python_NK_System_Backup\* Python_NK_System\

# 3. 恢复数据库
copy data\nk_system_backup_before_update.db data\nk_system.db

# 4. 启动系统
python main.py
```

### 4. 监控告警

#### 4.1 系统监控脚本
```python
# monitor.py - 系统监控脚本
import psutil
import time
import logging

def monitor_system():
    """系统监控"""
    while True:
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        if cpu_percent > 80:
            logging.warning(f"CPU使用率过高: {cpu_percent}%")
        
        # 内存使用率
        memory = psutil.virtual_memory()
        if memory.percent > 85:
            logging.warning(f"内存使用率过高: {memory.percent}%")
        
        # 磁盘空间
        disk = psutil.disk_usage('C:')
        if disk.percent > 90:
            logging.warning(f"磁盘空间不足: {disk.percent}%")
        
        time.sleep(60)  # 每分钟检查一次

if __name__ == "__main__":
    monitor_system()
```

#### 4.2 设备监控脚本
```python
# device_monitor.py - 设备监控脚本
import serial
import time
import logging

def monitor_devices():
    """设备监控"""
    while True:
        try:
            # 检查脑电设备
            ser = serial.Serial('COM8', 115200, timeout=1)
            ser.close()
            logging.info("脑电设备连接正常")
        except:
            logging.error("脑电设备连接失败")
        
        # 检查电刺激设备
        # 根据实际DLL接口实现检查逻辑
        
        time.sleep(300)  # 每5分钟检查一次

if __name__ == "__main__":
    monitor_devices()
```

### 5. 性能调优

#### 5.1 系统级优化
```bash
# 1. 设置系统性能模式
powercfg /setactive 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c

# 2. 调整虚拟内存
# 控制面板 -> 系统 -> 高级系统设置 -> 性能设置 -> 高级 -> 虚拟内存

# 3. 关闭不必要服务
sc config "Windows Search" start= disabled
sc config "Superfetch" start= disabled
```

#### 5.2 应用级优化
```python
# 在 user_config.json 中调整性能参数
{
    "performance": {
        "max_threads": 4,           # 最大线程数
        "buffer_size": 2048,        # 缓冲区大小
        "gc_threshold": 1000,       # 垃圾回收阈值
        "enable_gpu": false         # 是否启用GPU加速
    }
}
```

## 备份策略

### 1. 自动备份
```bash
# 配置自动备份
"database": {
    "auto_backup": true,
    "backup_interval": 86400,    # 24小时
    "backup_retention": 30       # 保留30天
}
```

### 2. 手动备份
```bash
# 创建完整备份脚本 backup.bat
@echo off
set BACKUP_DIR=D:\NK_Backup\%date:~0,4%%date:~5,2%%date:~8,2%
mkdir %BACKUP_DIR%

# 备份数据库
copy data\nk_system.db %BACKUP_DIR%\

# 备份配置文件
copy data\user_config.json %BACKUP_DIR%\

# 备份日志
xcopy /E logs %BACKUP_DIR%\logs\

# 备份模型文件
xcopy /E data\models %BACKUP_DIR%\models\

echo 备份完成: %BACKUP_DIR%
```

### 3. 灾难恢复
```bash
# 灾难恢复流程
# 1. 重新安装系统和Python环境
# 2. 部署NK系统程序
# 3. 从备份恢复数据
# 4. 验证系统功能
# 5. 恢复正常运行
```

这个部署运维指南提供了从安装部署到日常维护的完整流程，确保系统能够稳定可靠地运行在生产环境中。

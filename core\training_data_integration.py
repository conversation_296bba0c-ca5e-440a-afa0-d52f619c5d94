#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练数据集成模块
Training Data Integration

将原始数据存储功能集成到训练阶段
提供明确标签的高质量数据采集

作者: AI Assistant
版本: 1.0.0
"""

import logging
import numpy as np
from datetime import datetime
from typing import Optional, Dict, Any, List
from dataclasses import dataclass

from core.eeg_raw_data_manager import EEGRawDataManager, TrialType
from core.eeg_data_loader import EEGDataLoader
from core.eeg_device import EEGDataPacket
from core.motor_imagery_trainer import TrainingState
from utils.app_config import AppConfig


@dataclass
class TrainingSession:
    """训练会话数据"""
    session_id: int
    patient_id: int
    start_time: datetime
    current_trial: int
    total_trials: int
    successful_trials: int
    current_state: str
    current_round: int
    total_rounds: int
    data_buffer: List[np.ndarray]
    labels_buffer: List[int]
    is_recording: bool


class TrainingDataIntegration:
    """训练数据集成管理器"""
    
    def __init__(self, db_manager=None):
        """初始化集成管理器"""
        self.logger = logging.getLogger(__name__)
        self.db_manager = db_manager
        
        # 初始化数据管理器
        self.raw_data_manager = EEGRawDataManager(db_manager)
        self.data_loader = EEGDataLoader(db_manager)
        
        # 配置信息
        self.config = AppConfig.get_config('raw_data')
        
        # 当前训练会话
        self.current_session: Optional[TrainingSession] = None
        
        # 数据缓冲区
        self.eeg_buffer = []
        self.buffer_size = 500  # 4秒数据 (125Hz * 4)
        self.sampling_rate = 125
        
        # 训练状态映射
        self.state_mapping = {
            TrainingState.MOTOR_IMAGERY: TrialType.MOTOR_IMAGERY,
            TrainingState.QUIET: TrialType.REST,
            'motor_imagery': TrialType.MOTOR_IMAGERY,
            'rest': TrialType.REST,
            'quiet': TrialType.REST
        }
        
        self.logger.info("训练数据集成管理器初始化完成")
    
    def start_training_session(self, patient_id: int) -> bool:
        """开始训练会话"""
        try:
            if not self.config['enabled']:
                self.logger.info("原始数据存储已禁用，跳过数据采集")
                return True
            
            # 开始原始数据会话
            session_id = self.raw_data_manager.start_session(
                patient_id=patient_id,
                treatment_id=None,
                session_type='training'
            )
            
            if session_id <= 0:
                self.logger.error("启动原始数据会话失败")
                return False
            
            # 创建训练会话
            self.current_session = TrainingSession(
                session_id=session_id,
                patient_id=patient_id,
                start_time=datetime.now(),
                current_trial=0,
                total_trials=0,
                successful_trials=0,
                current_state='idle',
                current_round=0,
                total_rounds=0,
                data_buffer=[],
                labels_buffer=[],
                is_recording=False
            )
            
            # 清空缓冲区
            self.eeg_buffer.clear()
            
            self.logger.info(f"训练会话开始: 患者{patient_id}, 会话{session_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"开始训练会话失败: {e}")
            return False
    
    def start_trial_recording(self, training_state: TrainingState, round_number: int = 0, trial_number: int = 0) -> bool:
        """开始试验数据记录"""
        try:
            if not self.current_session or not self.config['enabled']:
                return True
            
            # 转换训练状态到标签
            if training_state in self.state_mapping:
                trial_type = self.state_mapping[training_state]
                state_name = 'motor_imagery' if trial_type == TrialType.MOTOR_IMAGERY else 'rest'
            else:
                self.logger.warning(f"未知的训练状态: {training_state}")
                return False
            
            # 更新会话状态
            self.current_session.current_state = state_name
            self.current_session.current_round = round_number
            
            # 清空当前试验的数据缓冲区
            self.eeg_buffer.clear()
            self.current_session.is_recording = True
            
            self.logger.debug(f"开始记录训练试验: 轮次{round_number}, 试验{trial_number}, 状态{state_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"开始试验记录失败: {e}")
            return False
    
    def process_eeg_data(self, data_packet: EEGDataPacket):
        """处理脑电数据包"""
        try:
            if not self.current_session or not self.current_session.is_recording:
                return
            
            if not self.config['enabled']:
                return
            
            # 将数据包转换为numpy数组
            for group_data in data_packet.channel_data:
                # group_data是8个通道的数据
                if len(group_data) == 8:
                    # 转换为列向量并添加到缓冲区
                    sample = np.array(group_data, dtype=np.float32).reshape(-1, 1)
                    self.eeg_buffer.append(sample)
            
            # 检查缓冲区大小，如果达到试验长度则自动结束记录
            if len(self.eeg_buffer) >= self.buffer_size:
                self.end_trial_recording()
            
        except Exception as e:
            self.logger.error(f"处理脑电数据失败: {e}")
    
    def end_trial_recording(self, force_save: bool = False) -> bool:
        """结束试验数据记录"""
        try:
            if not self.current_session or not self.current_session.is_recording:
                return True
            
            if not self.config['enabled']:
                self.current_session.is_recording = False
                return True
            
            # 检查数据长度
            if len(self.eeg_buffer) < 100 and not force_save:  # 至少0.8秒数据
                self.logger.warning(f"训练试验数据太短: {len(self.eeg_buffer)}个样本")
                self.current_session.is_recording = False
                return False
            
            # 组织数据
            if self.eeg_buffer:
                # 将缓冲区数据组合成矩阵 (8通道 × 时间点)
                eeg_data = np.hstack(self.eeg_buffer)
                
                # 确定标签
                if self.current_session.current_state == 'motor_imagery':
                    label = TrialType.MOTOR_IMAGERY.value
                elif self.current_session.current_state == 'rest':
                    label = TrialType.REST.value
                else:
                    label = TrialType.REST.value  # 默认为休息状态
                
                # 创建试验元数据
                trial_metadata = {
                    'trial_type': self.current_session.current_state,
                    'round_number': self.current_session.current_round,
                    'trial_number': self.current_session.current_trial,
                    'session_type': 'training',
                    'notes': f'训练轮次{self.current_session.current_round} 试验{self.current_session.current_trial + 1}'
                }
                
                # 保存数据
                success = self.raw_data_manager.save_trial_data(
                    eeg_data=eeg_data,
                    label=label,
                    trial_metadata=trial_metadata
                )
                
                if success:
                    self.current_session.current_trial += 1
                    self.current_session.total_trials += 1
                    
                    # 添加到会话缓冲区（用于实时分析）
                    self.current_session.data_buffer.append(eeg_data)
                    self.current_session.labels_buffer.append(label)
                    
                    # 如果数据质量好，增加成功计数
                    self.current_session.successful_trials += 1
                    
                    self.logger.debug(f"训练试验数据保存成功: 轮次{self.current_session.current_round}, "
                                    f"试验{self.current_session.current_trial}, "
                                    f"数据形状{eeg_data.shape}, 标签{label}")
                else:
                    self.logger.error("训练试验数据保存失败")
            
            # 重置状态
            self.current_session.is_recording = False
            self.current_session.current_state = 'idle'
            self.eeg_buffer.clear()
            
            return True
            
        except Exception as e:
            self.logger.error(f"结束试验记录失败: {e}")
            return False
    
    def end_training_session(self) -> bool:
        """结束训练会话"""
        try:
            if not self.current_session:
                return True
            
            # 如果正在记录，先结束当前试验
            if self.current_session.is_recording:
                self.end_trial_recording(force_save=True)
            
            # 结束原始数据会话
            if self.config['enabled']:
                success = self.raw_data_manager.end_session()
                if not success:
                    self.logger.error("结束原始数据会话失败")
            
            self.logger.info(f"训练会话结束: 会话{self.current_session.session_id}, "
                           f"总试验{self.current_session.total_trials}, "
                           f"成功{self.current_session.successful_trials}")
            
            # 清理会话数据
            self.current_session = None
            self.eeg_buffer.clear()
            
            return True
            
        except Exception as e:
            self.logger.error(f"结束训练会话失败: {e}")
            return False
    
    def get_session_data_for_training(self) -> Optional[tuple]:
        """获取当前会话的数据用于实时训练"""
        try:
            if not self.current_session or not self.current_session.data_buffer:
                return None
            
            # 返回当前会话的数据和标签
            return (
                self.current_session.data_buffer.copy(),
                self.current_session.labels_buffer.copy()
            )
            
        except Exception as e:
            self.logger.error(f"获取会话训练数据失败: {e}")
            return None
    
    def get_patient_training_data(self, patient_id: int, 
                                max_sessions: int = 10) -> Optional[tuple]:
        """获取患者的训练数据用于模型训练"""
        try:
            # 使用数据加载器获取患者训练数据
            patient_data = self.data_loader.load_patient_data(
                patient_id=patient_id,
                session_types=['training'],  # 只获取训练数据
                min_quality=self.config['quality_threshold']
            )
            
            if not patient_data:
                return None
            
            # 限制会话数量
            if len(patient_data) > max_sessions * 20:  # 假设每会话20个试验
                patient_data = patient_data[:max_sessions * 20]
            
            # 加载原始数据
            all_data = []
            all_labels = []
            
            for record in patient_data:
                trial_data = self.data_loader._load_trial_raw_data(
                    record['file_path'], 
                    record['trial_id']
                )
                
                if trial_data:
                    all_data.append(trial_data[0])
                    all_labels.append(record['label'])
            
            if all_data:
                self.logger.info(f"加载患者{patient_id}训练数据: {len(all_data)}个试验")
                return all_data, all_labels
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取患者训练数据失败: {e}")
            return None
    
    def get_session_statistics(self) -> Dict[str, Any]:
        """获取当前会话统计信息"""
        if not self.current_session:
            return {}
        
        return {
            'session_id': self.current_session.session_id,
            'patient_id': self.current_session.patient_id,
            'start_time': self.current_session.start_time.isoformat(),
            'current_trial': self.current_session.current_trial,
            'total_trials': self.current_session.total_trials,
            'successful_trials': self.current_session.successful_trials,
            'current_round': self.current_session.current_round,
            'current_state': self.current_session.current_state,
            'is_recording': self.current_session.is_recording,
            'buffer_size': len(self.eeg_buffer),
            'data_enabled': self.config['enabled']
        }
    
    def check_data_directory(self) -> Dict[str, Any]:
        """检查数据目录状态"""
        try:
            from pathlib import Path
            
            data_root = Path(AppConfig.get_config('paths')['raw_eeg_data'])
            
            result = {
                'data_root_exists': data_root.exists(),
                'data_root_path': str(data_root),
                'patient_dirs': [],
                'total_files': 0,
                'total_size_mb': 0
            }
            
            if data_root.exists():
                patients_dir = data_root / 'patients'
                if patients_dir.exists():
                    for patient_dir in patients_dir.iterdir():
                        if patient_dir.is_dir():
                            sessions_dir = patient_dir / 'sessions'
                            if sessions_dir.exists():
                                file_count = 0
                                total_size = 0
                                for file_path in sessions_dir.rglob('*.h5'):
                                    file_count += 1
                                    total_size += file_path.stat().st_size
                                
                                result['patient_dirs'].append({
                                    'patient_dir': patient_dir.name,
                                    'file_count': file_count,
                                    'size_mb': total_size / 1024 / 1024
                                })
                                
                                result['total_files'] += file_count
                                result['total_size_mb'] += total_size / 1024 / 1024
            
            return result
            
        except Exception as e:
            self.logger.error(f"检查数据目录失败: {e}")
            return {'error': str(e)}

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
权限管理模块
Authentication and Authorization Manager

作者: AI Assistant
版本: 1.0.0
"""

import hashlib
import logging
import time
from datetime import datetime, timedelta
from typing import Optional, Dict, List, Any
from enum import Enum

from core.database_manager import DatabaseManager
from utils.app_config import AppConfig


class UserRole(Enum):
    """用户角色枚举"""
    ADMIN = "admin"
    DOCTOR = "doctor"
    TECHNICIAN = "technician"
    OPERATOR = "operator"


class Permission(Enum):
    """权限枚举"""
    # 患者管理权限
    PATIENT_VIEW = "patient_view"
    PATIENT_ADD = "patient_add"
    PATIENT_EDIT = "patient_edit"
    PATIENT_DELETE = "patient_delete"

    # 治疗操作权限
    TREATMENT_OPERATE = "treatment_operate"
    TREATMENT_VIEW = "treatment_view"
    TREATMENT_RECORD = "treatment_record"

    # 设备控制权限
    DEVICE_CONTROL = "device_control"
    DEVICE_CONFIG = "device_config"

    # 数据分析权限
    DATA_ANALYSIS = "data_analysis"
    REPORT_GENERATE = "report_generate"
    DATA_EXPORT = "data_export"

    # 系统管理权限
    SYSTEM_CONFIG = "system_config"
    USER_MANAGE = "user_manage"
    LOG_VIEW = "log_view"

    # 特殊权限
    ALL = "all"


class AuthManager:
    """权限管理器"""

    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)
        self.current_user = None
        self.login_time = None
        self.session_timeout = 3600  # 1小时超时

        # 角色权限映射
        self.role_permissions = {
            UserRole.ADMIN: [Permission.ALL],
            UserRole.DOCTOR: [
                Permission.PATIENT_VIEW, Permission.PATIENT_ADD, Permission.PATIENT_EDIT,
                Permission.TREATMENT_OPERATE, Permission.TREATMENT_VIEW, Permission.TREATMENT_RECORD,
                Permission.DATA_ANALYSIS, Permission.REPORT_GENERATE, Permission.DATA_EXPORT
            ],
            UserRole.TECHNICIAN: [
                Permission.PATIENT_VIEW, Permission.TREATMENT_OPERATE, Permission.TREATMENT_RECORD,
                Permission.DEVICE_CONTROL, Permission.DEVICE_CONFIG, Permission.DATA_ANALYSIS
            ],
            UserRole.OPERATOR: [
                Permission.PATIENT_VIEW, Permission.TREATMENT_OPERATE, Permission.TREATMENT_VIEW
            ]
        }

        # 初始化权限系统
        self.init_auth_system()

    def init_auth_system(self):
        """初始化权限系统"""
        try:
            # 检查是否首次安装（数据库中没有用户）
            users = self.get_all_users_without_permission_check()

            if len(users) == 0:
                self.logger.info("检测到首次安装，创建默认管理员账户")
                self.create_default_admin()
            else:
                self.logger.info(f"权限系统初始化完成，当前有 {len(users)} 个用户")

        except Exception as e:
            self.logger.error(f"初始化权限系统失败: {e}")

    def get_all_users_without_permission_check(self) -> List[Dict[str, Any]]:
        """获取所有用户列表（不检查权限，用于系统初始化）"""
        try:
            users = self.db_manager.execute_query(
                "SELECT id, name, role, last_login, is_active FROM operator ORDER BY name"
            )
            return users or []
        except Exception as e:
            self.logger.error(f"获取用户列表异常: {e}")
            return []

    def create_default_admin(self):
        """创建默认管理员账户"""
        try:
            # 默认管理员信息
            default_username = "admin"
            default_password = "admin123"
            default_role = UserRole.ADMIN

            # 检查是否已存在admin用户
            existing = self.db_manager.execute_query(
                "SELECT COUNT(*) as count FROM operator WHERE name = ?",
                (default_username,)
            )

            if existing and existing[0]['count'] > 0:
                self.logger.info("默认管理员账户已存在")
                return True

            # 创建默认管理员
            hashed_password = self.hash_password(default_password)
            self.logger.info("创建默认管理员账户")
            permissions = ','.join([p.value for p in self.role_permissions[default_role]])

            success = self.db_manager.execute_non_query(
                """INSERT INTO operator (name, password, role, permissions, is_active)
                   VALUES (?, ?, ?, ?, 1)""",
                (default_username, hashed_password, default_role.value, permissions)
            )

            if success:
                self.logger.info(f"默认管理员账户创建成功: {default_username}")
                self.logger.info("默认密码: admin123 (请登录后立即修改)")
            else:
                self.logger.error("默认管理员账户创建失败")

            return success

        except Exception as e:
            self.logger.error(f"创建默认管理员账户异常: {e}")
            return False

    def hash_password(self, password: str) -> str:
        """密码哈希"""
        # 与DatabaseManager._initialize_base_data中使用的哈希算法保持一致
        salt = "NK_BCI_SYSTEM_2024"
        # 安全考虑：不记录明文密码和哈希值到日志
        self.logger.debug("正在计算密码哈希")
        hashed = hashlib.sha256((password + salt).encode()).hexdigest()
        self.logger.debug("密码哈希计算完成")
        return hashed

    def verify_password(self, password: str, hashed: str) -> bool:
        """验证密码"""
        # 计算输入密码的哈希值
        password_hash = self.hash_password(password)
        # 安全考虑：不记录明文密码和哈希值到日志
        self.logger.debug("正在验证密码")
        # 比较哈希值
        is_valid = password_hash == hashed
        self.logger.debug(f"密码验证完成，结果: {'成功' if is_valid else '失败'}")
        return is_valid

    def login(self, username: str, password: str) -> bool:
        """用户登录"""
        try:
            # 查询用户信息
            user_data = self.db_manager.execute_query(
                "SELECT * FROM operator WHERE name = ? AND is_active = 1",
                (username,)
            )

            if not user_data:
                self.logger.warning(f"登录失败：用户不存在 - {username}")
                return False

            user = user_data[0]

            # 验证密码
            if not self.verify_password(password, user['password']):
                self.logger.warning(f"登录失败：密码错误 - {username}")
                return False

            # 登录成功
            self.current_user = {
                'id': user['id'],
                'name': user['name'],
                'role': UserRole(user['role']),
                'permissions': user['permissions']
            }
            self.login_time = time.time()

            # 更新最后登录时间
            self.db_manager.execute_non_query(
                "UPDATE operator SET last_login = CURRENT_TIMESTAMP WHERE id = ?",
                (user['id'],)
            )

            self.logger.info(f"用户登录成功 - {username}")
            return True

        except Exception as e:
            self.logger.error(f"登录异常: {e}")
            return False

    def logout(self):
        """用户登出"""
        if self.current_user:
            self.logger.info(f"用户登出 - {self.current_user['name']}")
            self.current_user = None
            self.login_time = None

    def is_logged_in(self) -> bool:
        """检查是否已登录"""
        if not self.current_user or not self.login_time:
            return False

        # 检查会话是否超时
        if time.time() - self.login_time > self.session_timeout:
            self.logger.info(f"会话超时，自动登出 - {self.current_user['name']}")
            self.logout()
            return False

        return True

    def has_permission(self, permission: Permission) -> bool:
        """检查当前用户是否有指定权限"""
        if not self.is_logged_in():
            return False

        user_role = self.current_user['role']

        # 管理员拥有所有权限
        role_perms = self.role_permissions.get(user_role, [])

        if Permission.ALL in role_perms:
            return True

        # 检查角色权限
        return permission in role_perms

    def require_permission(self, permission: Permission) -> bool:
        """要求指定权限（装饰器使用）"""
        if not self.has_permission(permission):
            self.logger.warning(
                f"权限不足：用户 {self.current_user['name'] if self.current_user else 'None'} "
                f"尝试访问需要 {permission.value} 权限的功能"
            )
            return False
        return True

    def get_current_user(self) -> Optional[Dict[str, Any]]:
        """获取当前用户信息"""
        if self.is_logged_in():
            return self.current_user.copy()
        return None

    def get_user_permissions(self) -> List[Permission]:
        """获取当前用户权限列表"""
        if not self.is_logged_in():
            return []

        user_role = self.current_user['role']
        return self.role_permissions.get(user_role, [])

    def create_user(self, username: str, password: str, role: UserRole) -> bool:
        """创建新用户（需要管理员权限）"""
        if not self.has_permission(Permission.USER_MANAGE):
            return False

        try:
            # 检查用户名是否已存在
            existing = self.db_manager.execute_query(
                "SELECT COUNT(*) as count FROM operator WHERE name = ?",
                (username,)
            )

            if existing[0]['count'] > 0:
                self.logger.warning(f"创建用户失败：用户名已存在 - {username}")
                return False

            # 创建用户
            hashed_password = self.hash_password(password)
            permissions = ','.join([p.value for p in self.role_permissions[role]])

            success = self.db_manager.execute_non_query(
                """INSERT INTO operator (name, password, role, permissions, is_active)
                   VALUES (?, ?, ?, ?, 1)""",
                (username, hashed_password, role.value, permissions)
            )

            if success:
                self.logger.info(f"用户创建成功 - {username} ({role.value})")

            return success

        except Exception as e:
            self.logger.error(f"创建用户异常: {e}")
            return False

    def change_password(self, old_password: str, new_password: str) -> bool:
        """修改当前用户密码"""
        if not self.is_logged_in():
            return False

        try:
            # 验证旧密码
            user_data = self.db_manager.execute_query(
                "SELECT password FROM operator WHERE id = ?",
                (self.current_user['id'],)
            )

            if not user_data or not self.verify_password(old_password, user_data[0]['password']):
                self.logger.warning(f"修改密码失败：旧密码错误 - {self.current_user['name']}")
                return False

            # 更新密码
            new_hashed = self.hash_password(new_password)
            success = self.db_manager.execute_non_query(
                "UPDATE operator SET password = ? WHERE id = ?",
                (new_hashed, self.current_user['id'])
            )

            if success:
                self.logger.info(f"密码修改成功 - {self.current_user['name']}")

            return success

        except Exception as e:
            self.logger.error(f"修改密码异常: {e}")
            return False

    def reset_user_password(self, user_id: int, new_password: str) -> bool:
        """重置用户密码（管理员权限）"""
        if not self.has_permission(Permission.USER_MANAGE):
            return False

        try:
            # 更新密码
            new_hashed = self.hash_password(new_password)
            success = self.db_manager.execute_non_query(
                "UPDATE operator SET password = ? WHERE id = ?",
                (new_hashed, user_id)
            )

            if success:
                self.logger.info(f"管理员重置用户密码成功 - 用户ID: {user_id}")

            return success

        except Exception as e:
            self.logger.error(f"重置用户密码异常: {e}")
            return False

    def update_user(self, user_id: int, username: str, role: UserRole, is_active: bool) -> bool:
        """更新用户信息（管理员权限）"""
        if not self.has_permission(Permission.USER_MANAGE):
            return False

        try:
            # 检查用户名是否已存在（排除当前用户）
            existing_user = self.db_manager.execute_query(
                "SELECT id FROM operator WHERE name = ? AND id != ?",
                (username, user_id)
            )

            if existing_user:
                self.logger.warning(f"更新用户失败：用户名已存在 - {username}")
                return False

            # 更新用户信息
            success = self.db_manager.execute_non_query(
                "UPDATE operator SET name = ?, role = ?, is_active = ? WHERE id = ?",
                (username, role.value, 1 if is_active else 0, user_id)
            )

            if success:
                self.logger.info(f"用户信息更新成功 - ID: {user_id}, 用户名: {username}")

            return success

        except Exception as e:
            self.logger.error(f"更新用户信息异常: {e}")
            return False

    def change_admin_password(self, username: str, new_password: str) -> bool:
        """修改指定用户密码（仅限管理员，用于首次登录修改默认密码）"""
        # 对于首次登录的admin用户，允许修改自己的密码
        if (self.is_logged_in() and
            self.current_user['name'] == username and
            username == 'admin'):
            # admin用户可以修改自己的密码
            pass
        elif not self.has_permission(Permission.USER_MANAGE):
            return False

        try:
            # 更新密码
            new_hashed = self.hash_password(new_password)
            success = self.db_manager.execute_non_query(
                "UPDATE operator SET password = ? WHERE name = ?",
                (new_hashed, username)
            )

            if success:
                self.logger.info(f"用户密码修改成功 - {username}")

            return success

        except Exception as e:
            self.logger.error(f"修改用户密码异常: {e}")
            return False

    def change_own_password(self, old_password: str, new_password: str) -> bool:
        """修改当前用户自己的密码"""
        if not self.is_logged_in():
            return False

        try:
            # 验证旧密码
            user_data = self.db_manager.execute_query(
                "SELECT password FROM operator WHERE id = ?",
                (self.current_user['id'],)
            )

            if not user_data or not self.verify_password(old_password, user_data[0]['password']):
                self.logger.warning(f"修改密码失败：旧密码错误 - {self.current_user['name']}")
                return False

            # 更新密码
            new_hashed = self.hash_password(new_password)
            success = self.db_manager.execute_non_query(
                "UPDATE operator SET password = ? WHERE id = ?",
                (new_hashed, self.current_user['id'])
            )

            if success:
                self.logger.info(f"用户自己修改密码成功 - {self.current_user['name']}")

            return success

        except Exception as e:
            self.logger.error(f"修改自己密码异常: {e}")
            return False

    def get_all_users(self) -> List[Dict[str, Any]]:
        """获取所有用户列表（需要管理员权限）"""
        if not self.has_permission(Permission.USER_MANAGE):
            return []

        try:
            users = self.db_manager.execute_query(
                "SELECT id, name, role, last_login, is_active FROM operator ORDER BY name"
            )
            return users
        except Exception as e:
            self.logger.error(f"获取用户列表异常: {e}")
            return []

    def deactivate_user(self, user_id: int) -> bool:
        """停用用户（需要管理员权限）"""
        if not self.has_permission(Permission.USER_MANAGE):
            self.logger.warning("停用用户失败：权限不足")
            return False

        try:
            # 安全检查：不能停用当前登录用户
            if self.current_user and self.current_user['id'] == user_id:
                self.logger.warning(f"停用用户失败：不能停用当前登录用户 - ID: {user_id}")
                return False

            # 安全检查：不能停用最后一个管理员
            if self.is_last_admin(user_id):
                self.logger.warning(f"停用用户失败：不能停用最后一个管理员 - ID: {user_id}")
                return False

            success = self.db_manager.execute_non_query(
                "UPDATE operator SET is_active = 0 WHERE id = ?",
                (user_id,)
            )

            if success:
                self.logger.info(f"用户已停用 - ID: {user_id}")

            return success

        except Exception as e:
            self.logger.error(f"停用用户异常: {e}")
            return False

    def is_last_admin(self, user_id: int) -> bool:
        """检查是否是最后一个管理员"""
        try:
            # 获取所有活跃的管理员用户数量
            active_admins = self.db_manager.execute_query(
                "SELECT COUNT(*) as count FROM operator WHERE role = 'admin' AND is_active = 1"
            )

            if not active_admins:
                return True

            admin_count = active_admins[0]['count']

            # 检查要操作的用户是否是管理员
            user_info = self.db_manager.execute_query(
                "SELECT role FROM operator WHERE id = ? AND is_active = 1",
                (user_id,)
            )

            if user_info and user_info[0]['role'] == 'admin':
                # 如果只有一个管理员且要操作的就是管理员，则不允许
                return admin_count <= 1

            return False

        except Exception as e:
            self.logger.error(f"检查最后管理员失败: {e}")
            return True  # 出错时保守处理，不允许操作

    def activate_user(self, user_id: int) -> bool:
        """激活用户（需要管理员权限）"""
        if not self.has_permission(Permission.USER_MANAGE):
            self.logger.warning("激活用户失败：权限不足")
            return False

        try:
            success = self.db_manager.execute_non_query(
                "UPDATE operator SET is_active = 1 WHERE id = ?",
                (user_id,)
            )

            if success:
                self.logger.info(f"用户已激活 - ID: {user_id}")

            return success

        except Exception as e:
            self.logger.error(f"激活用户异常: {e}")
            return False

    def ensure_admin_exists(self) -> bool:
        """确保系统中至少有一个活跃的管理员账户"""
        try:
            # 检查是否有活跃的管理员
            active_admins = self.db_manager.execute_query(
                "SELECT COUNT(*) as count FROM operator WHERE role = 'admin' AND is_active = 1"
            )

            if not active_admins or active_admins[0]['count'] == 0:
                self.logger.warning("系统中没有活跃的管理员账户，正在创建默认管理员")
                return self.create_default_admin()

            return True

        except Exception as e:
            self.logger.error(f"检查管理员账户失败: {e}")
            return False

    def extend_session(self):
        """延长会话时间"""
        if self.is_logged_in():
            self.login_time = time.time()

    def get_session_remaining_time(self) -> int:
        """获取会话剩余时间（秒）"""
        if not self.is_logged_in():
            return 0

        elapsed = time.time() - self.login_time
        remaining = max(0, self.session_timeout - elapsed)
        return int(remaining)


def permission_required(permission: Permission):
    """权限装饰器"""
    def decorator(func):
        def wrapper(self, *args, **kwargs):
            if hasattr(self, 'auth_manager') and self.auth_manager:
                if not self.auth_manager.require_permission(permission):
                    raise PermissionError(f"需要 {permission.value} 权限")
            return func(self, *args, **kwargs)
        return wrapper
    return decorator

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF导出器模块
PDF Exporter Module

作者: AI Assistant
版本: 1.0.0
"""

import logging
import os
import base64
from datetime import datetime
from typing import Dict, List, Any, Optional
from io import BytesIO

try:
    from reportlab.lib.pagesizes import A4, letter
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch, cm
    from reportlab.lib import colors
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
    from reportlab.platypus.flowables import PageBreak
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

from utils.app_config import AppConfig


class PDFExporter:
    """PDF导出器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.setup_fonts()
    
    def setup_fonts(self):
        """设置中文字体"""
        if not REPORTLAB_AVAILABLE:
            self.logger.warning("ReportLab未安装，PDF导出功能不可用")
            return
        
        try:
            # 尝试注册中文字体
            font_paths = [
                "C:/Windows/Fonts/simhei.ttf",  # 黑体
                "C:/Windows/Fonts/simsun.ttc",  # 宋体
                "/System/Library/Fonts/PingFang.ttc",  # macOS
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"  # Linux
            ]
            
            for font_path in font_paths:
                if os.path.exists(font_path):
                    if font_path.endswith('simhei.ttf'):
                        pdfmetrics.registerFont(TTFont('SimHei', font_path))
                        self.chinese_font = 'SimHei'
                        break
                    elif font_path.endswith('simsun.ttc'):
                        pdfmetrics.registerFont(TTFont('SimSun', font_path))
                        self.chinese_font = 'SimSun'
                        break
            else:
                self.chinese_font = 'Helvetica'  # 默认字体
                self.logger.warning("未找到中文字体，使用默认字体")
                
        except Exception as e:
            self.logger.error(f"字体设置失败: {e}")
            self.chinese_font = 'Helvetica'
    
    def export_personal_report(self, patient_info: Dict[str, Any], 
                             report_content: str, 
                             charts: List[str],
                             output_path: str) -> bool:
        """导出个人报告为PDF"""
        if not REPORTLAB_AVAILABLE:
            self.logger.error("ReportLab未安装，无法导出PDF")
            return False
        
        try:
            # 创建PDF文档
            doc = SimpleDocTemplate(output_path, pagesize=A4,
                                  rightMargin=2*cm, leftMargin=2*cm,
                                  topMargin=2*cm, bottomMargin=2*cm)
            
            # 获取样式
            styles = getSampleStyleSheet()
            
            # 创建中文样式
            title_style = ParagraphStyle(
                'ChineseTitle',
                parent=styles['Title'],
                fontName=self.chinese_font,
                fontSize=18,
                spaceAfter=20,
                alignment=1  # 居中
            )
            
            heading_style = ParagraphStyle(
                'ChineseHeading',
                parent=styles['Heading2'],
                fontName=self.chinese_font,
                fontSize=14,
                spaceAfter=12,
                spaceBefore=12
            )
            
            normal_style = ParagraphStyle(
                'ChineseNormal',
                parent=styles['Normal'],
                fontName=self.chinese_font,
                fontSize=10,
                spaceAfter=6
            )
            
            # 构建PDF内容
            story = []
            
            # 标题
            story.append(Paragraph("脑机接口康复训练报告", title_style))
            story.append(Spacer(1, 20))
            
            # 患者信息表格
            patient_data = [
                ['患者编号', str(patient_info.get('bianhao', ''))],
                ['姓名', patient_info.get('name', '')],
                ['年龄', f"{patient_info.get('age', '')}岁"],
                ['性别', patient_info.get('xingbie', '')],
                ['诊断', patient_info.get('zhenduan', '')],
                ['主治医师', patient_info.get('zhuzhi', '')],
            ]
            
            patient_table = Table(patient_data, colWidths=[3*cm, 6*cm])
            patient_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
                ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, -1), self.chinese_font),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ]))
            
            story.append(Paragraph("患者基本信息", heading_style))
            story.append(patient_table)
            story.append(Spacer(1, 20))
            
            # 报告内容
            story.append(Paragraph("详细报告", heading_style))
            
            # 将报告内容按行分割并添加
            for line in report_content.split('\n'):
                if line.strip():
                    if line.startswith('=') or line.startswith('-'):
                        continue  # 跳过分隔线
                    elif line.startswith('📋') or line.startswith('📈') or line.startswith('🎯') or line.startswith('🧠') or line.startswith('💡'):
                        story.append(Paragraph(line, heading_style))
                    else:
                        story.append(Paragraph(line, normal_style))
                else:
                    story.append(Spacer(1, 6))
            
            # 添加图表
            if charts:
                story.append(PageBreak())
                story.append(Paragraph("统计图表", heading_style))
                
                for i, chart_base64 in enumerate(charts):
                    if chart_base64 and chart_base64.startswith('data:image/png;base64,'):
                        try:
                            # 解码base64图片
                            image_data = base64.b64decode(chart_base64.split(',')[1])
                            image_buffer = BytesIO(image_data)
                            
                            # 创建图片对象
                            img = Image(image_buffer, width=15*cm, height=10*cm)
                            story.append(img)
                            story.append(Spacer(1, 20))
                            
                        except Exception as e:
                            self.logger.error(f"添加图表{i+1}失败: {e}")
            
            # 添加页脚信息
            story.append(Spacer(1, 30))
            footer_text = f"报告生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}"
            story.append(Paragraph(footer_text, normal_style))
            
            # 生成PDF
            doc.build(story)
            
            self.logger.info(f"PDF报告导出成功: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出PDF报告失败: {e}")
            return False
    
    def export_statistics_report(self, statistics_data: Dict[str, Any], 
                               charts: List[str],
                               output_path: str) -> bool:
        """导出统计报告为PDF"""
        if not REPORTLAB_AVAILABLE:
            self.logger.error("ReportLab未安装，无法导出PDF")
            return False
        
        try:
            # 创建PDF文档
            doc = SimpleDocTemplate(output_path, pagesize=A4,
                                  rightMargin=2*cm, leftMargin=2*cm,
                                  topMargin=2*cm, bottomMargin=2*cm)
            
            # 获取样式
            styles = getSampleStyleSheet()
            
            # 创建中文样式
            title_style = ParagraphStyle(
                'ChineseTitle',
                parent=styles['Title'],
                fontName=self.chinese_font,
                fontSize=18,
                spaceAfter=20,
                alignment=1
            )
            
            heading_style = ParagraphStyle(
                'ChineseHeading',
                parent=styles['Heading2'],
                fontName=self.chinese_font,
                fontSize=14,
                spaceAfter=12,
                spaceBefore=12
            )
            
            normal_style = ParagraphStyle(
                'ChineseNormal',
                parent=styles['Normal'],
                fontName=self.chinese_font,
                fontSize=10,
                spaceAfter=6
            )
            
            # 构建PDF内容
            story = []
            
            # 标题
            story.append(Paragraph("脑机接口系统统计分析报告", title_style))
            story.append(Spacer(1, 20))
            
            # 统计摘要
            story.append(Paragraph("统计摘要", heading_style))
            
            # 这里可以根据statistics_data的内容生成具体的统计信息
            summary_text = f"""
            统计时间范围: {statistics_data.get('start_date', '')} 至 {statistics_data.get('end_date', '')}
            总治疗次数: {statistics_data.get('total_treatments', 0)}次
            总患者数: {statistics_data.get('total_patients', 0)}人
            平均治疗得分: {statistics_data.get('avg_score', 0):.1f}分
            """
            
            for line in summary_text.strip().split('\n'):
                if line.strip():
                    story.append(Paragraph(line.strip(), normal_style))
            
            # 添加图表
            if charts:
                story.append(Spacer(1, 30))
                story.append(Paragraph("统计图表", heading_style))
                
                for i, chart_base64 in enumerate(charts):
                    if chart_base64 and chart_base64.startswith('data:image/png;base64,'):
                        try:
                            # 解码base64图片
                            image_data = base64.b64decode(chart_base64.split(',')[1])
                            image_buffer = BytesIO(image_data)
                            
                            # 创建图片对象
                            img = Image(image_buffer, width=15*cm, height=10*cm)
                            story.append(img)
                            story.append(Spacer(1, 20))
                            
                        except Exception as e:
                            self.logger.error(f"添加统计图表{i+1}失败: {e}")
            
            # 添加页脚信息
            story.append(Spacer(1, 30))
            footer_text = f"报告生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}"
            story.append(Paragraph(footer_text, normal_style))
            
            # 生成PDF
            doc.build(story)
            
            self.logger.info(f"PDF统计报告导出成功: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出PDF统计报告失败: {e}")
            return False
    
    def is_available(self) -> bool:
        """检查PDF导出功能是否可用"""
        return REPORTLAB_AVAILABLE

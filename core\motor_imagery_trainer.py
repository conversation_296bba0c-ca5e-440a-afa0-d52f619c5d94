#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运动想象训练控制器
Motor Imagery Training Controller

作者: AI Assistant
版本: 1.0.0
"""

import numpy as np
import logging
import time
import random
import threading
from typing import List, Dict, Any, Optional, Callable, Tuple
from dataclasses import dataclass
from enum import Enum
from PySide6.QtCore import QObject, Signal, QTimer

from core.signal_processor import EEGSignalProcessor, SignalQuality
from core.eeg_device import EEGDataPacket


class TrainingState(Enum):
    """训练状态枚举"""
    IDLE = "待机"
    PREPARING = "准备中"
    REST = "休息"
    MOTOR_IMAGERY = "运动想象"
    QUIET = "平静状态"
    EVALUATING = "评估中"
    COMPLETED = "完成"


@dataclass
class TrainingConfig:
    """训练配置"""
    trials_per_round: int = 50  # 每轮试验次数
    motor_imagery_duration: float = 4.0  # 运动想象持续时间(秒)
    quiet_duration: float = 4.0  # 平静状态持续时间(秒)
    rest_duration: float = 2.0  # 休息时间(秒)
    preparation_time: float = 3.0  # 准备时间(秒)


@dataclass
class TrainingSession:
    """训练会话数据"""
    session_id: int
    round_number: int
    trial_data: List[Dict[str, Any]]
    labels: List[int]  # 0: 平静, 1: 运动想象
    timestamps: List[float]
    signal_quality: List[SignalQuality]


class MotorImageryTrainer(QObject):
    """运动想象训练控制器"""

    # 信号定义
    state_changed = Signal(str)  # 状态改变
    progress_updated = Signal(int, int)  # 进度更新 (当前, 总数)
    trial_started = Signal(str, int)  # 试验开始 (状态, 剩余时间)
    trial_completed = Signal(str, dict)  # 试验完成 (状态, 数据)
    round_completed = Signal(int, dict)  # 轮次完成 (轮次, 评估结果)
    training_completed = Signal(dict)  # 训练完成 (总结果)
    voice_prompt = Signal(str)  # 语音提示

    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(__name__)

        # 训练配置
        self.config = TrainingConfig()

        # 信号处理器
        self.signal_processor = EEGSignalProcessor()

        # 训练状态
        self.current_state = TrainingState.IDLE
        self.is_training = False
        self.current_round = 0
        self.current_trial = 0
        self.total_trials = 0

        # 数据收集
        self.current_session: Optional[TrainingSession] = None
        self.collected_data: List[np.ndarray] = []
        self.collected_labels: List[int] = []
        self.data_buffer: List[float] = []
        self.buffer_lock = threading.Lock()

        # 定时器
        self.trial_timer = QTimer()
        self.trial_timer.timeout.connect(self._on_trial_timeout)
        self.trial_timer.setSingleShot(True)

        self.rest_timer = QTimer()
        self.rest_timer.timeout.connect(self._start_next_trial)
        self.rest_timer.setSingleShot(True)

        self.preparation_timer = QTimer()
        self.preparation_timer.timeout.connect(self._start_first_trial)
        self.preparation_timer.setSingleShot(True)

        # 回调函数
        self.data_callback: Optional[Callable] = None

        # 基础模型（用于累进训练）
        self.base_model = None

        self.logger.info("运动想象训练控制器初始化完成")

    def set_config(self, config: TrainingConfig):
        """设置训练配置"""
        self.config = config
        self.total_trials = config.trials_per_round * 2  # 运动想象 + 平静状态
        self.logger.info(f"训练配置已更新: 每轮{config.trials_per_round}次试验")

    def set_data_callback(self, callback: Callable[[EEGDataPacket], None]):
        """设置数据回调函数"""
        self.data_callback = callback

    def set_base_model(self, model):
        """设置基础模型用于累进训练"""
        self.base_model = model
        if model:
            self.logger.info(f"设置基础模型: {model.model_name}")
        else:
            self.logger.info("清除基础模型，将进行新EEGNet模型训练")

    def start_training_round(self, round_number: int = None):
        """开始训练轮次"""
        try:
            if self.is_training:
                self.logger.warning("训练已在进行中")
                return False

            if round_number is None:
                self.current_round += 1
            else:
                self.current_round = round_number

            self.logger.info(f"开始第{self.current_round}轮训练")

            # 初始化训练会话
            self.current_session = TrainingSession(
                session_id=int(time.time()),
                round_number=self.current_round,
                trial_data=[],
                labels=[],
                timestamps=[],
                signal_quality=[]
            )

            # 重置状态
            self.is_training = True
            self.current_trial = 0
            self.collected_data.clear()
            self.collected_labels.clear()

            # 生成随机试验序列
            self._generate_trial_sequence()

            # 开始准备阶段
            self._change_state(TrainingState.PREPARING)
            self.voice_prompt.emit("准备开始训练，请放松")

            # 启动准备定时器
            self.preparation_timer.start(int(self.config.preparation_time * 1000))

            return True

        except Exception as e:
            self.logger.error(f"开始训练失败: {e}")
            return False

    def stop_training(self):
        """停止训练"""
        try:
            if not self.is_training:
                return

            self.logger.info("停止训练")

            self.is_training = False

            # 停止所有定时器
            self.trial_timer.stop()
            self.rest_timer.stop()
            self.preparation_timer.stop()

            self._change_state(TrainingState.IDLE)

            # 发送训练完成信号
            if self.current_session:
                self.training_completed.emit({
                    'round': self.current_round,
                    'completed_trials': len(self.current_session.trial_data),
                    'total_trials': self.total_trials
                })

        except Exception as e:
            self.logger.error(f"停止训练失败: {e}")

    def on_eeg_data_received(self, data_packet: EEGDataPacket):
        """处理接收到的脑电数据"""
        try:
            if not self.is_training:
                return

            # 只在运动想象和平静状态时收集数据
            if self.current_state not in [TrainingState.MOTOR_IMAGERY, TrainingState.QUIET]:
                return

            # 将数据添加到缓冲区
            with self.buffer_lock:
                for group_data in data_packet.channel_data:
                    self.data_buffer.extend(group_data)

            # 调用外部回调
            if self.data_callback:
                self.data_callback(data_packet)

        except Exception as e:
            self.logger.error(f"处理脑电数据失败: {e}")

    def _generate_trial_sequence(self):
        """生成随机试验序列"""
        # 创建平衡的试验序列
        trials = []

        # 添加运动想象试验
        for _ in range(self.config.trials_per_round):
            trials.append((TrainingState.MOTOR_IMAGERY, 1))

        # 添加平静状态试验
        for _ in range(self.config.trials_per_round):
            trials.append((TrainingState.QUIET, 0))

        # 随机打乱
        random.shuffle(trials)

        self.trial_sequence = trials
        self.total_trials = len(trials)

        self.logger.info(f"生成试验序列: {self.total_trials}个试验")

    def _start_first_trial(self):
        """开始第一个试验"""
        self.current_trial = 0
        self._start_next_trial()

    def _start_next_trial(self):
        """开始下一个试验"""
        try:
            if self.current_trial >= len(self.trial_sequence):
                # 所有试验完成
                self._complete_round()
                return

            # 获取当前试验信息
            trial_state, label = self.trial_sequence[self.current_trial]

            # 更新进度
            self.progress_updated.emit(self.current_trial + 1, self.total_trials)

            # 清空数据缓冲区
            with self.buffer_lock:
                self.data_buffer.clear()

            # 改变状态并开始试验
            self._change_state(trial_state)

            # 发送语音提示
            if trial_state == TrainingState.MOTOR_IMAGERY:
                self.voice_prompt.emit("开始想象运动")
                duration = self.config.motor_imagery_duration
            else:
                self.voice_prompt.emit("保持平静")
                duration = self.config.quiet_duration

            # 发送试验开始信号
            self.trial_started.emit(trial_state.value, int(duration))

            # 启动试验定时器
            self.trial_timer.start(int(duration * 1000))

            self.logger.debug(f"开始试验 {self.current_trial + 1}/{self.total_trials}: {trial_state.value}")

        except Exception as e:
            self.logger.error(f"开始试验失败: {e}")

    def _on_trial_timeout(self):
        """试验超时处理"""
        try:
            self.trial_timer.stop()

            # 获取当前试验信息
            trial_state, label = self.trial_sequence[self.current_trial]

            # 收集试验数据
            with self.buffer_lock:
                trial_data = np.array(self.data_buffer.copy())

            # 预处理数据
            if len(trial_data) > 0:
                # 重塑数据为 [channels, samples]
                n_channels = 8
                n_samples = len(trial_data) // n_channels
                if n_samples > 0:
                    reshaped_data = trial_data[:n_samples * n_channels].reshape(n_channels, n_samples)

                    # 信号预处理
                    processed_data, quality = self.signal_processor.preprocess_signal(reshaped_data)

                    # 存储数据
                    self.collected_data.append(processed_data)
                    self.collected_labels.append(label)

                    # 更新会话数据
                    if self.current_session:
                        self.current_session.trial_data.append({
                            'raw_data': trial_data,
                            'processed_data': processed_data,
                            'state': trial_state.value
                        })
                        self.current_session.labels.append(label)
                        self.current_session.timestamps.append(time.time())
                        self.current_session.signal_quality.append(quality)

                    # 发送试验完成信号
                    self.trial_completed.emit(trial_state.value, {
                        'trial': self.current_trial + 1,
                        'quality': quality.overall_quality,
                        'usable': quality.is_usable
                    })

                    self.logger.debug(f"试验 {self.current_trial + 1} 完成，质量: {quality.overall_quality:.3f}")

            # 移动到下一个试验
            self.current_trial += 1

            # 休息阶段
            self._change_state(TrainingState.REST)
            self.voice_prompt.emit("休息")

            # 启动休息定时器
            self.rest_timer.start(int(self.config.rest_duration * 1000))

        except Exception as e:
            self.logger.error(f"试验超时处理失败: {e}")

    def _complete_round(self):
        """完成当前轮次"""
        try:
            self.logger.info(f"第{self.current_round}轮训练完成")

            # 改变状态
            self._change_state(TrainingState.EVALUATING)
            self.voice_prompt.emit("训练轮次完成，正在评估")

            # 评估训练结果
            evaluation_result = self._evaluate_round()

            # 发送轮次完成信号
            self.round_completed.emit(self.current_round, evaluation_result)

            # 重置状态
            self.is_training = False
            self._change_state(TrainingState.COMPLETED)

        except Exception as e:
            self.logger.error(f"完成轮次失败: {e}")

    def _evaluate_round(self) -> Dict[str, Any]:
        """评估训练轮次"""
        try:
            if not self.current_session or len(self.collected_data) == 0:
                return {'error': '没有可用的训练数据'}

            # 计算基本统计信息
            total_trials = len(self.collected_data)
            usable_trials = sum(1 for q in self.current_session.signal_quality if q.is_usable)
            avg_quality = np.mean([q.overall_quality for q in self.current_session.signal_quality])

            # 计算类别分布
            motor_imagery_count = sum(1 for label in self.collected_labels if label == 1)
            quiet_count = sum(1 for label in self.collected_labels if label == 0)

            evaluation = {
                'round': self.current_round,
                'total_trials': total_trials,
                'usable_trials': usable_trials,
                'usability_rate': usable_trials / total_trials if total_trials > 0 else 0,
                'average_quality': avg_quality,
                'motor_imagery_trials': motor_imagery_count,
                'quiet_trials': quiet_count,
                'data_balance': min(motor_imagery_count, quiet_count) / max(motor_imagery_count, quiet_count) if max(motor_imagery_count, quiet_count) > 0 else 0,
                'recommendation': self._get_training_recommendation(usable_trials, avg_quality)
            }

            self.logger.info(f"轮次评估完成: 可用试验 {usable_trials}/{total_trials}, 平均质量 {avg_quality:.3f}")

            return evaluation

        except Exception as e:
            self.logger.error(f"评估轮次失败: {e}")
            return {'error': str(e)}

    def _get_training_recommendation(self, usable_trials: int, avg_quality: float) -> str:
        """获取训练建议"""
        if usable_trials < 20:
            return "数据量不足，建议继续训练"
        elif avg_quality < 0.6:
            return "信号质量较低，建议检查电极连接或环境"
        elif usable_trials < 40:
            return "数据量适中，可以继续训练以提高模型性能"
        else:
            return "数据量充足，可以尝试训练模型"

    def _change_state(self, new_state: TrainingState):
        """改变训练状态"""
        if self.current_state != new_state:
            self.current_state = new_state
            self.state_changed.emit(new_state.value)
            self.logger.debug(f"训练状态改变: {new_state.value}")

    def get_collected_data(self) -> Tuple[List[np.ndarray], List[int]]:
        """获取收集的数据"""
        return self.collected_data.copy(), self.collected_labels.copy()

    def get_current_session(self) -> Optional[TrainingSession]:
        """获取当前训练会话"""
        return self.current_session

/* NK脑机接口系统现代化样式表 */

/* 全局样式 */
* {
    font-family: "Microsoft YaHei", "Segoe UI", Arial, sans-serif;
}

/* 主窗口样式 */
QMainWindow {
    background-color: #f5f5f5;
    color: #333333;
}

/* 菜单栏样式 */
QMenuBar {
    background-color: #2c3e50;
    color: white;
    border: none;
    padding: 4px;
}

QMenuBar::item {
    background-color: transparent;
    padding: 8px 12px;
    border-radius: 4px;
    margin: 2px;
}

QMenuBar::item:selected {
    background-color: #34495e;
}

QMenuBar::item:pressed {
    background-color: #1abc9c;
}

QMenu {
    background-color: white;
    border: 1px solid #bdc3c7;
    border-radius: 6px;
    padding: 4px;
}

QMenu::item {
    padding: 8px 20px;
    border-radius: 4px;
}

QMenu::item:selected {
    background-color: #3498db;
    color: white;
}

/* 状态栏样式 */
QStatusBar {
    background-color: #ecf0f1;
    border-top: 1px solid #bdc3c7;
    color: #2c3e50;
    font-size: 12px;
}

QStatusBar::item {
    border: none;
}

/* 导航区域样式 */
QFrame#navigation_frame {
    background-color: #34495e;
    border-right: 2px solid #2c3e50;
}

/* 导航按钮样式 */
QToolButton {
    background-color: transparent;
    border: none;
    color: white;
    padding: 12px;
    text-align: center;
    border-radius: 8px;
    margin: 4px;
    font-size: 12px;
    font-weight: bold;
}

QToolButton:hover {
    background-color: #3498db;
}

QToolButton:checked {
    background-color: #1abc9c;
}

QToolButton:disabled {
    color: #7f8c8d;
    background-color: transparent;
}

/* 按钮样式 */
QPushButton {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: bold;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #2980b9;
}

QPushButton:pressed {
    background-color: #21618c;
}

QPushButton:disabled {
    background-color: #bdc3c7;
    color: #7f8c8d;
}

/* 主要按钮样式 */
QPushButton.primary {
    background-color: #1abc9c;
}

QPushButton.primary:hover {
    background-color: #16a085;
}

/* 危险按钮样式 */
QPushButton.danger {
    background-color: #e74c3c;
}

QPushButton.danger:hover {
    background-color: #c0392b;
}

/* 成功按钮样式 */
QPushButton.success {
    background-color: #27ae60;
}

QPushButton.success:hover {
    background-color: #229954;
}

/* 输入框样式 */
QLineEdit {
    background-color: white;
    border: 2px solid #bdc3c7;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 13px;
    color: #2c3e50;
}

QLineEdit:focus {
    border-color: #3498db;
    outline: none;
}

QLineEdit:disabled {
    background-color: #ecf0f1;
    color: #7f8c8d;
}

/* 文本区域样式 */
QTextEdit {
    background-color: white;
    border: 2px solid #bdc3c7;
    border-radius: 6px;
    padding: 8px;
    font-size: 13px;
    color: #2c3e50;
}

QTextEdit:focus {
    border-color: #3498db;
}

/* 表格样式 */
QTableWidget {
    background-color: white;
    border: 1px solid #bdc3c7;
    border-radius: 6px;
    gridline-color: #ecf0f1;
    selection-background-color: #3498db;
    selection-color: white;
}

QTableWidget::item {
    padding: 8px;
    border-bottom: 1px solid #ecf0f1;
}

QTableWidget::item:selected {
    background-color: #3498db;
    color: white;
}

QHeaderView::section {
    background-color: #34495e;
    color: white;
    padding: 10px;
    border: none;
    font-weight: bold;
}

QHeaderView::section:hover {
    background-color: #2c3e50;
}

/* 列表样式 */
QListWidget {
    background-color: white;
    border: 1px solid #bdc3c7;
    border-radius: 6px;
    padding: 4px;
}

QListWidget::item {
    padding: 8px;
    border-radius: 4px;
    margin: 2px;
}

QListWidget::item:selected {
    background-color: #3498db;
    color: white;
}

QListWidget::item:hover {
    background-color: #ecf0f1;
}

/* 组合框样式 */
QComboBox {
    background-color: white;
    border: 2px solid #bdc3c7;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 13px;
    color: #2c3e50;
    min-width: 120px;
}

QComboBox:focus {
    border-color: #3498db;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox QAbstractItemView {
    background-color: white;
    border: 1px solid #bdc3c7;
    border-radius: 6px;
    selection-background-color: #3498db;
    selection-color: white;
}

/* 复选框样式 */
QCheckBox {
    color: #2c3e50;
    font-size: 13px;
    spacing: 8px;
}

QCheckBox::indicator {
    width: 18px;
    height: 18px;
    border: 2px solid #bdc3c7;
    border-radius: 4px;
    background-color: white;
}

QCheckBox::indicator:checked {
    background-color: #3498db;
    border-color: #3498db;
}

QCheckBox::indicator:hover {
    border-color: #3498db;
}

/* 分组框样式 */
QGroupBox {
    font-weight: bold;
    border: 2px solid #bdc3c7;
    border-radius: 8px;
    margin-top: 12px;
    padding-top: 12px;
    color: #2c3e50;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 12px;
    padding: 0 8px 0 8px;
    background-color: #f5f5f5;
    color: #2c3e50;
}

/* 进度条样式 */
QProgressBar {
    border: 1px solid #bdc3c7;
    border-radius: 6px;
    text-align: center;
    font-weight: bold;
    color: #2c3e50;
    background-color: #ecf0f1;
}

QProgressBar::chunk {
    background-color: #3498db;
    border-radius: 5px;
}

/* 滚动条样式 */
QScrollBar:vertical {
    background-color: #ecf0f1;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #bdc3c7;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #95a5a6;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    background-color: #ecf0f1;
    height: 12px;
    border-radius: 6px;
}

QScrollBar::handle:horizontal {
    background-color: #bdc3c7;
    border-radius: 6px;
    min-width: 20px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #95a5a6;
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:horizontal {
    width: 0px;
}

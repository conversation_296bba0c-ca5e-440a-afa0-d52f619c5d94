#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
报告分析界面模块
Report Analysis UI Module

作者: AI Assistant
版本: 1.0.0
"""

import logging
import os
from datetime import datetime, timedelta
from typing import Optional, Dict, List, Any
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                               QGroupBox, QLabel, QPushButton, QComboBox,
                               QTextEdit, QTableWidget, QDateEdit, QSplitter,
                               QFrame, QTabWidget, QTableWidgetItem, QHeaderView,
                               QMessageBox, QFileDialog, QProgressDialog, QCheckBox)
from PySide6.QtCore import Qt, QDate, QThread, Signal
from PySide6.QtGui import QFont, QPixmap

from core.database_manager import DatabaseManager
from core.report_generator import ReportGenerator
from core.chart_generator import ChartGenerator
from core.pdf_exporter import PDFExporter
from utils.app_config import AppConfig


class ReportGenerationThread(QThread):
    """报告生成线程"""
    finished = Signal(str, list)  # 报告内容, 图表列表
    error = Signal(str)

    def __init__(self, generator, chart_generator, patient_id, start_date, end_date, report_type):
        super().__init__()
        self.generator = generator
        self.chart_generator = chart_generator
        self.patient_id = patient_id
        self.start_date = start_date
        self.end_date = end_date
        self.report_type = report_type

    def run(self):
        try:
            # 生成报告
            report_content = self.generator.generate_personal_report(
                self.patient_id, self.start_date, self.end_date, self.report_type
            )

            # 生成图表
            charts = []
            if self.patient_id:
                # 获取治疗数据
                treatment_data = self.generator._get_treatment_records(
                    self.patient_id, self.start_date, self.end_date
                )

                # 生成趋势图
                trend_chart = self.chart_generator.generate_treatment_trend_chart(treatment_data)
                if trend_chart:
                    charts.append(trend_chart)

                # 生成分布图
                dist_chart = self.chart_generator.generate_score_distribution_chart(treatment_data)
                if dist_chart:
                    charts.append(dist_chart)

                # 生成脑电特征图
                eeg_data = self.generator._get_eeg_data(
                    self.patient_id, self.start_date, self.end_date
                )
                if eeg_data:
                    eeg_chart = self.chart_generator.generate_eeg_features_chart(eeg_data)
                    if eeg_chart:
                        charts.append(eeg_chart)

            self.finished.emit(report_content, charts)

        except Exception as e:
            self.error.emit(str(e))


class ReportWidget(QWidget):
    """报告分析界面组件"""

    def __init__(self, parent=None):
        super().__init__(parent)

        # 初始化属性
        self.db_manager: Optional[DatabaseManager] = None
        self.report_generator: Optional[ReportGenerator] = None
        self.chart_generator = ChartGenerator()
        self.pdf_exporter = PDFExporter()
        self.logger = logging.getLogger(__name__)

        # 当前报告数据
        self.current_report_content = ""
        self.current_charts = []
        self.current_patient_info = {}

        # 初始化界面
        self.init_ui()
        self.connect_signals()

        self.logger.info("报告分析界面初始化完成")

    def connect_signals(self):
        """连接信号槽"""
        # 个人报告信号
        self.generate_report_button.clicked.connect(self.generate_report)
        self.preview_report_button.clicked.connect(self.preview_report)
        self.print_report_button.clicked.connect(self.print_report)
        self.export_pdf_button.clicked.connect(self.export_pdf)

        # 统计分析信号
        self.generate_statistics_button.clicked.connect(self.generate_statistics)
        self.preview_export_button.clicked.connect(self.preview_export_data)

        # 高级分析信号
        self.start_analysis_button.clicked.connect(self.start_advanced_analysis)

        # 数据导出信号
        if hasattr(self, 'export_data_button'):
            self.export_data_button.clicked.connect(self.export_data)
        if hasattr(self, 'preview_export_button'):
            self.preview_export_button.clicked.connect(self.preview_export_data)

    def init_ui(self):
        """初始化用户界面"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建现代化标题栏
        self.create_title_bar(main_layout)

        # 创建标签页
        tab_widget = QTabWidget()
        tab_widget.setStyleSheet(self.get_modern_tab_style())

        # 个人报告标签页
        personal_tab = self.create_personal_report_tab()
        tab_widget.addTab(personal_tab, "📊 个人报告")

        # 统计分析标签页
        statistics_tab = self.create_statistics_tab()
        tab_widget.addTab(statistics_tab, "📈 统计分析")

        # 高级分析标签页
        advanced_tab = self.create_advanced_analysis_tab()
        tab_widget.addTab(advanced_tab, "🔬 高级分析")

        # 数据导出标签页
        export_tab = self.create_export_tab()
        tab_widget.addTab(export_tab, "📤 数据导出")

        main_layout.addWidget(tab_widget)

    def create_title_bar(self, layout):
        """创建现代化标题栏"""
        title_frame = QFrame()
        title_frame.setFixedHeight(80)
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2E86AB, stop:1 #A23B72);
                border: none;
                border-radius: 0px;
            }
        """)

        title_layout = QHBoxLayout(title_frame)
        title_layout.setContentsMargins(30, 20, 30, 20)

        # 标题文本
        title_label = QLabel("🏥 脑机接口康复训练系统 - 报告分析中心")
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 24px;
                font-weight: bold;
                background: transparent;
            }
        """)
        title_layout.addWidget(title_label)

        # 状态指示器
        status_label = QLabel("● 系统运行正常")
        status_label.setStyleSheet("""
            QLabel {
                color: #90EE90;
                font-size: 14px;
                background: transparent;
            }
        """)
        title_layout.addWidget(status_label)

        layout.addWidget(title_frame)

    def get_modern_panel_style(self):
        """获取现代化面板样式"""
        return """
            QFrame {
                background: white;
                border: 1px solid #E0E0E0;
                border-radius: 12px;
                box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
            }
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #333333;
                border: 2px solid #E0E0E0;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background: #F8F9FA;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                background: white;
                color: #2E86AB;
            }
            QLabel {
                color: #555555;
                font-size: 12px;
            }
            QComboBox, QDateEdit {
                border: 2px solid #E0E0E0;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 12px;
                background: white;
                min-height: 20px;
            }
            QComboBox:focus, QDateEdit:focus {
                border-color: #4A90E2;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4A90E2, stop:1 #357ABD);
                border: none;
                border-radius: 8px;
                color: white;
                font-size: 13px;
                font-weight: bold;
                padding: 12px 20px;
                min-height: 20px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5BA0F2, stop:1 #4A90E2);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #357ABD, stop:1 #2E6BA8);
            }
        """

    def get_modern_tab_style(self):
        """获取现代化标签页样式"""
        return """
            QTabWidget::pane {
                border: 1px solid #C0C0C0;
                background-color: #FAFAFA;
                border-radius: 8px;
                margin-top: 10px;
            }

            QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #F0F0F0, stop:1 #E0E0E0);
                border: 1px solid #C0C0C0;
                border-bottom: none;
                border-radius: 8px 8px 0px 0px;
                padding: 12px 20px;
                margin-right: 2px;
                font-size: 14px;
                font-weight: 500;
                color: #333333;
                min-width: 120px;
            }

            QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4A90E2, stop:1 #357ABD);
                color: white;
                font-weight: bold;
            }

            QTabBar::tab:hover:!selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #E8F4FD, stop:1 #D0E8FC);
                color: #2E86AB;
            }
        """
    
    def create_personal_report_tab(self) -> QWidget:
        """创建个人报告标签页"""
        widget = QWidget()
        widget.setStyleSheet("QWidget { background-color: #FAFAFA; }")
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # 左侧控制面板
        control_panel = QFrame()
        control_panel.setFixedWidth(350)
        control_panel.setStyleSheet(self.get_modern_panel_style())

        control_layout = QVBoxLayout(control_panel)
        control_layout.setContentsMargins(25, 25, 25, 25)
        control_layout.setSpacing(20)
        
        # 患者选择组
        patient_group = QGroupBox("🔍 患者选择")
        patient_layout = QGridLayout(patient_group)
        patient_layout.setSpacing(15)
        patient_layout.setContentsMargins(20, 25, 20, 20)

        patient_layout.addWidget(QLabel("患者编号:"), 0, 0)
        self.report_patient_combo = QComboBox()
        self.report_patient_combo.setMinimumHeight(35)
        patient_layout.addWidget(self.report_patient_combo, 0, 1)

        patient_layout.addWidget(QLabel("开始日期:"), 1, 0)
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setDate(QDate.currentDate().addDays(-30))
        self.start_date_edit.setMinimumHeight(35)
        self.start_date_edit.setCalendarPopup(True)
        patient_layout.addWidget(self.start_date_edit, 1, 1)

        patient_layout.addWidget(QLabel("结束日期:"), 2, 0)
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setDate(QDate.currentDate())
        self.end_date_edit.setMinimumHeight(35)
        self.end_date_edit.setCalendarPopup(True)
        patient_layout.addWidget(self.end_date_edit, 2, 1)

        control_layout.addWidget(patient_group)

        # 报告类型组
        type_group = QGroupBox("📋 报告类型")
        type_layout = QVBoxLayout(type_group)
        type_layout.setContentsMargins(20, 25, 20, 20)
        type_layout.setSpacing(10)

        self.report_type_combo = QComboBox()
        self.report_type_combo.addItems(["📊 综合报告", "🎯 训练报告", "📈 评定报告", "📉 进度报告"])
        self.report_type_combo.setMinimumHeight(35)
        type_layout.addWidget(self.report_type_combo)

        control_layout.addWidget(type_group)
        
        # 操作按钮组
        button_group = QGroupBox("⚡ 操作中心")
        button_group_layout = QVBoxLayout(button_group)
        button_group_layout.setContentsMargins(20, 25, 20, 20)
        button_group_layout.setSpacing(15)

        self.generate_report_button = QPushButton("🚀 生成报告")
        self.generate_report_button.setMinimumHeight(45)
        self.generate_report_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #45A049);
                border: none;
                border-radius: 8px;
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 12px 20px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5CBF60, stop:1 #4CAF50);
            }
        """)
        button_group_layout.addWidget(self.generate_report_button)

        self.preview_report_button = QPushButton("👁️ 预览报告")
        self.preview_report_button.setMinimumHeight(40)
        button_group_layout.addWidget(self.preview_report_button)

        self.print_report_button = QPushButton("🖨️ 打印报告")
        self.print_report_button.setMinimumHeight(40)
        button_group_layout.addWidget(self.print_report_button)

        self.export_pdf_button = QPushButton("📄 导出PDF")
        self.export_pdf_button.setMinimumHeight(40)
        button_group_layout.addWidget(self.export_pdf_button)

        control_layout.addWidget(button_group)
        control_layout.addStretch()
        
        layout.addWidget(control_panel)
        
        # 右侧预览区域
        preview_panel = QWidget()
        preview_layout = QVBoxLayout(preview_panel)
        preview_layout.setContentsMargins(0, 0, 0, 0)
        preview_layout.setSpacing(20)

        # 报告预览区域
        preview_group = QGroupBox("📄 报告预览")
        preview_group.setStyleSheet(self.get_modern_panel_style())
        preview_group_layout = QVBoxLayout(preview_group)
        preview_group_layout.setContentsMargins(20, 25, 20, 20)

        self.report_preview = QTextEdit()
        self.report_preview.setReadOnly(True)
        self.report_preview.setPlainText("📋 报告预览区域\n\n请选择患者并点击'生成报告'按钮开始分析...")
        self.report_preview.setFont(QFont("Microsoft YaHei", 10))
        self.report_preview.setStyleSheet("""
            QTextEdit {
                border: 1px solid #E0E0E0;
                border-radius: 8px;
                background: #FAFAFA;
                padding: 15px;
                line-height: 1.6;
            }
        """)
        self.report_preview.setMinimumHeight(300)
        preview_group_layout.addWidget(self.report_preview)

        preview_layout.addWidget(preview_group)

        # 图表预览区域
        chart_group = QGroupBox("📊 图表预览")
        chart_group.setStyleSheet(self.get_modern_panel_style())
        chart_layout = QVBoxLayout(chart_group)
        chart_layout.setContentsMargins(20, 25, 20, 20)

        self.chart_preview_label = QLabel("📈 图表预览区域\n\n生成报告后将在此显示数据可视化图表")
        self.chart_preview_label.setAlignment(Qt.AlignCenter)
        self.chart_preview_label.setMinimumHeight(250)
        self.chart_preview_label.setStyleSheet("""
            QLabel {
                border: 2px dashed #D0D0D0;
                border-radius: 8px;
                background: #F8F9FA;
                color: #888888;
                font-size: 14px;
                padding: 20px;
            }
        """)
        chart_layout.addWidget(self.chart_preview_label)

        preview_layout.addWidget(chart_group)

        layout.addWidget(preview_panel)

        return widget

    def create_advanced_analysis_tab(self) -> QWidget:
        """创建高级分析标签页"""
        widget = QWidget()
        widget.setStyleSheet("QWidget { background-color: #FAFAFA; }")
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # 顶部控制区域
        control_frame = QFrame()
        control_frame.setStyleSheet(self.get_modern_panel_style())
        control_layout = QHBoxLayout(control_frame)
        control_layout.setContentsMargins(25, 20, 25, 20)
        control_layout.setSpacing(20)

        # 分析类型选择
        analysis_group = QGroupBox("🔬 分析类型")
        analysis_layout = QHBoxLayout(analysis_group)
        analysis_layout.setContentsMargins(15, 20, 15, 15)

        self.analysis_type_combo = QComboBox()
        self.analysis_type_combo.addItems([
            "🎯 训练报告分析",
            "🧠 智能异常检测",
            "📈 趋势预测分析",
            "🔥 热力图分析",
            "🎪 雷达图分析"
        ])
        self.analysis_type_combo.setMinimumHeight(35)
        analysis_layout.addWidget(self.analysis_type_combo)

        control_layout.addWidget(analysis_group)

        # 患者选择
        patient_group = QGroupBox("👤 患者选择")
        patient_layout = QHBoxLayout(patient_group)
        patient_layout.setContentsMargins(15, 20, 15, 15)

        self.advanced_patient_combo = QComboBox()
        self.advanced_patient_combo.setMinimumHeight(35)
        patient_layout.addWidget(self.advanced_patient_combo)

        control_layout.addWidget(patient_group)

        # 分析按钮
        self.start_analysis_button = QPushButton("🚀 开始分析")
        self.start_analysis_button.setMinimumHeight(45)
        self.start_analysis_button.setMinimumWidth(120)
        self.start_analysis_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FF6B6B, stop:1 #EE5A52);
                border: none;
                border-radius: 8px;
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 12px 20px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FF7B7B, stop:1 #FF6B6B);
            }
        """)
        control_layout.addWidget(self.start_analysis_button)

        layout.addWidget(control_frame)

        # 分析结果显示区域
        result_splitter = QSplitter(Qt.Horizontal)

        # 左侧：分析详情
        detail_group = QGroupBox("📋 分析详情")
        detail_group.setStyleSheet(self.get_modern_panel_style())
        detail_layout = QVBoxLayout(detail_group)
        detail_layout.setContentsMargins(20, 25, 20, 20)

        self.analysis_detail_text = QTextEdit()
        self.analysis_detail_text.setReadOnly(True)
        self.analysis_detail_text.setPlainText("🔍 分析详情将在此显示\n\n请选择分析类型和患者，然后点击'开始分析'")
        self.analysis_detail_text.setFont(QFont("Microsoft YaHei", 10))
        self.analysis_detail_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #E0E0E0;
                border-radius: 8px;
                background: #FAFAFA;
                padding: 15px;
            }
        """)
        detail_layout.addWidget(self.analysis_detail_text)

        result_splitter.addWidget(detail_group)

        # 右侧：高级图表
        chart_group = QGroupBox("📊 高级图表")
        chart_group.setStyleSheet(self.get_modern_panel_style())
        chart_layout = QVBoxLayout(chart_group)
        chart_layout.setContentsMargins(20, 25, 20, 20)

        self.advanced_chart_label = QLabel("📈 高级图表显示区域\n\n将显示热力图、雷达图等高级可视化图表")
        self.advanced_chart_label.setAlignment(Qt.AlignCenter)
        self.advanced_chart_label.setMinimumHeight(400)
        self.advanced_chart_label.setStyleSheet("""
            QLabel {
                border: 2px dashed #D0D0D0;
                border-radius: 8px;
                background: #F8F9FA;
                color: #888888;
                font-size: 14px;
                padding: 20px;
            }
        """)
        chart_layout.addWidget(self.advanced_chart_label)

        result_splitter.addWidget(chart_group)
        result_splitter.setSizes([400, 600])

        layout.addWidget(result_splitter)

        return widget

    def create_statistics_tab(self) -> QWidget:
        """创建统计分析标签页"""
        widget = QWidget()
        widget.setStyleSheet("QWidget { background-color: #FAFAFA; }")
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # 顶部控制区域
        control_frame = QFrame()
        control_frame.setStyleSheet(self.get_modern_panel_style())
        control_layout = QHBoxLayout(control_frame)
        control_layout.setContentsMargins(25, 20, 25, 20)
        control_layout.setSpacing(20)

        # 统计类型选择
        type_group = QGroupBox("📊 统计类型")
        type_layout = QVBoxLayout(type_group)
        type_layout.setContentsMargins(15, 20, 15, 15)

        self.statistics_type_combo = QComboBox()
        self.statistics_type_combo.addItems([
            "📅 按日统计",
            "📆 按周统计",
            "🗓️ 按月统计",
            "👥 按患者统计"
        ])
        self.statistics_type_combo.setMinimumHeight(35)
        type_layout.addWidget(self.statistics_type_combo)

        control_layout.addWidget(type_group)

        # 日期范围选择
        date_group = QGroupBox("📅 日期范围")
        date_layout = QGridLayout(date_group)
        date_layout.setContentsMargins(15, 20, 15, 15)
        date_layout.setSpacing(10)

        date_layout.addWidget(QLabel("开始日期:"), 0, 0)
        self.stats_start_date = QDateEdit()
        self.stats_start_date.setDate(QDate.currentDate().addDays(-30))
        self.stats_start_date.setMinimumHeight(35)
        self.stats_start_date.setCalendarPopup(True)
        date_layout.addWidget(self.stats_start_date, 0, 1)

        date_layout.addWidget(QLabel("结束日期:"), 1, 0)
        self.stats_end_date = QDateEdit()
        self.stats_end_date.setDate(QDate.currentDate())
        self.stats_end_date.setMinimumHeight(35)
        self.stats_end_date.setCalendarPopup(True)
        date_layout.addWidget(self.stats_end_date, 1, 1)

        control_layout.addWidget(date_group)

        # 操作按钮
        button_group = QGroupBox("⚡ 操作")
        button_layout = QVBoxLayout(button_group)
        button_layout.setContentsMargins(15, 20, 15, 15)
        button_layout.setSpacing(10)

        self.generate_statistics_button = QPushButton("📊 生成统计")
        self.generate_statistics_button.setMinimumHeight(45)
        self.generate_statistics_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2196F3, stop:1 #1976D2);
                border: none;
                border-radius: 8px;
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 12px 20px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #42A5F5, stop:1 #2196F3);
            }
        """)
        button_layout.addWidget(self.generate_statistics_button)

        self.preview_export_button = QPushButton("👁️ 预览导出")
        self.preview_export_button.setMinimumHeight(40)
        button_layout.addWidget(self.preview_export_button)

        control_layout.addWidget(button_group)

        layout.addWidget(control_frame)
        
        # 统计结果显示区域
        result_splitter = QSplitter(Qt.Horizontal)

        # 统计表格
        table_group = QGroupBox("📋 统计数据")
        table_group.setStyleSheet(self.get_modern_panel_style())
        table_layout = QVBoxLayout(table_group)
        table_layout.setContentsMargins(20, 25, 20, 20)

        self.statistics_table = QTableWidget()
        self.statistics_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #E0E0E0;
                border-radius: 8px;
                background: white;
                gridline-color: #F0F0F0;
                selection-background-color: #E3F2FD;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #F0F0F0;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #F5F5F5, stop:1 #E8E8E8);
                border: 1px solid #D0D0D0;
                padding: 8px;
                font-weight: bold;
                color: #333333;
            }
        """)
        table_layout.addWidget(self.statistics_table)

        result_splitter.addWidget(table_group)

        # 统计图表
        chart_group = QGroupBox("📊 统计图表")
        chart_group.setStyleSheet(self.get_modern_panel_style())
        chart_layout = QVBoxLayout(chart_group)
        chart_layout.setContentsMargins(20, 25, 20, 20)

        self.statistics_chart_label = QLabel("📈 统计图表显示区域\n\n请选择统计类型并点击'生成统计'")
        self.statistics_chart_label.setAlignment(Qt.AlignCenter)
        self.statistics_chart_label.setMinimumHeight(350)
        self.statistics_chart_label.setStyleSheet("""
            QLabel {
                border: 2px dashed #D0D0D0;
                border-radius: 8px;
                background: #F8F9FA;
                color: #888888;
                font-size: 14px;
                padding: 20px;
            }
        """)
        chart_layout.addWidget(self.statistics_chart_label)

        result_splitter.addWidget(chart_group)
        result_splitter.setSizes([500, 700])

        layout.addWidget(result_splitter)
        
        return widget
    
    def create_export_tab(self) -> QWidget:
        """创建数据导出标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 导出控制
        control_group = QGroupBox("导出控制")
        control_layout = QGridLayout(control_group)
        
        control_layout.addWidget(QLabel("数据类型:"), 0, 0)
        self.export_type_combo = QComboBox()
        self.export_type_combo.addItems(["患者信息", "治疗记录", "脑电数据", "全部数据"])
        control_layout.addWidget(self.export_type_combo, 0, 1)
        
        control_layout.addWidget(QLabel("导出格式:"), 0, 2)
        self.export_format_combo = QComboBox()
        self.export_format_combo.addItems(["Excel (.xlsx)", "CSV (.csv)", "JSON (.json)"])
        control_layout.addWidget(self.export_format_combo, 0, 3)
        
        control_layout.addWidget(QLabel("开始日期:"), 1, 0)
        self.export_start_date = QDateEdit()
        self.export_start_date.setDate(QDate.currentDate().addDays(-30))
        control_layout.addWidget(self.export_start_date, 1, 1)
        
        control_layout.addWidget(QLabel("结束日期:"), 1, 2)
        self.export_end_date = QDateEdit()
        self.export_end_date.setDate(QDate.currentDate())
        control_layout.addWidget(self.export_end_date, 1, 3)
        
        layout.addWidget(control_group)
        
        # 导出选项
        options_group = QGroupBox("📋 导出选项")
        options_group.setStyleSheet(self.get_modern_panel_style())
        options_layout = QVBoxLayout(options_group)
        options_layout.setContentsMargins(20, 25, 20, 20)

        # 导出内容选择
        content_layout = QGridLayout()
        content_layout.setSpacing(10)

        # 包含字段选择
        content_layout.addWidget(QLabel("包含字段:"), 0, 0)
        self.include_fields_layout = QVBoxLayout()

        # 创建复选框
        self.field_checkboxes = {}
        fields = [
            ("basic_info", "基本信息"),
            ("treatment_records", "治疗记录"),
            ("statistics", "统计数据"),
            ("charts", "图表数据")
        ]

        for field_key, field_name in fields:
            checkbox = QCheckBox(field_name)
            checkbox.setChecked(True)
            self.field_checkboxes[field_key] = checkbox
            self.include_fields_layout.addWidget(checkbox)

        content_layout.addLayout(self.include_fields_layout, 0, 1)

        options_layout.addLayout(content_layout)

        layout.addWidget(options_group)
        
        # 导出按钮
        button_layout = QHBoxLayout()
        
        self.preview_export_button = QPushButton("预览数据")
        button_layout.addWidget(self.preview_export_button)
        
        self.export_data_button = QPushButton("导出数据")
        self.export_data_button.setStyleSheet("QPushButton { background-color: #2196F3; color: white; font-weight: bold; }")
        button_layout.addWidget(self.export_data_button)
        
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        
        # 导出预览
        preview_group = QGroupBox("数据预览")
        preview_layout = QVBoxLayout(preview_group)

        self.export_preview_table = QTableWidget()
        preview_layout.addWidget(self.export_preview_table)

        # 状态信息
        self.export_status_label = QLabel("💡 请选择导出选项并点击'预览数据'查看要导出的内容")
        self.export_status_label.setStyleSheet("""
            QLabel {
                color: #7F8C8D;
                font-size: 12px;
                padding: 10px;
                background: #F8F9FA;
                border-radius: 4px;
                border: 1px solid #E9ECEF;
            }
        """)
        preview_layout.addWidget(self.export_status_label)

        layout.addWidget(preview_group)

        return widget
    
    def set_database_manager(self, db_manager: DatabaseManager):
        """设置数据库管理器"""
        self.db_manager = db_manager
        self.report_generator = ReportGenerator(db_manager)
        self.load_patients()
        self.logger.info("报告分析界面数据库管理器设置完成")
    
    def load_patients(self):
        """加载患者列表"""
        if not self.db_manager:
            self.logger.warning("数据库管理器未设置，无法加载患者列表")
            return

        try:
            patients = self.db_manager.get_patients()
            self.logger.info(f"获取到 {len(patients)} 个患者")

            # 清空现有项目
            self.report_patient_combo.clear()
            if hasattr(self, 'advanced_patient_combo'):
                self.advanced_patient_combo.clear()

            # 添加默认选项
            self.report_patient_combo.addItem("请选择患者", None)
            if hasattr(self, 'advanced_patient_combo'):
                self.advanced_patient_combo.addItem("请选择患者", None)

            # 添加患者到下拉框
            for patient in patients:
                display_text = f"{patient.get('bianhao', '')} - {patient.get('name', '')}"
                self.report_patient_combo.addItem(display_text, patient.get('bianhao'))

                # 同时添加到高级分析患者选择
                if hasattr(self, 'advanced_patient_combo'):
                    self.advanced_patient_combo.addItem(display_text, patient.get('bianhao'))

            self.logger.info(f"患者列表加载完成，共 {len(patients)} 个患者")

        except Exception as e:
            self.logger.error(f"加载患者列表失败: {e}")
            # 添加错误提示项
            self.report_patient_combo.clear()
            self.report_patient_combo.addItem("加载患者失败", None)
            if hasattr(self, 'advanced_patient_combo'):
                self.advanced_patient_combo.clear()
                self.advanced_patient_combo.addItem("加载患者失败", None)

    def generate_report(self):
        """生成个人报告"""
        try:
            # 获取选择的患者
            patient_data = self.report_patient_combo.currentData()
            if not patient_data:
                QMessageBox.warning(self, "警告", "请选择患者")
                return

            # 获取日期范围
            start_date = self.start_date_edit.date().toString("yyyy-MM-dd")
            end_date = self.end_date_edit.date().toString("yyyy-MM-dd")

            # 获取报告类型
            report_type = self.report_type_combo.currentText()

            # 获取患者信息
            self.current_patient_info = self._get_patient_info(patient_data)

            # 显示进度对话框
            progress = QProgressDialog("正在生成报告...", "取消", 0, 0, self)
            progress.setWindowModality(Qt.WindowModal)
            progress.show()

            # 创建报告生成线程
            self.report_thread = ReportGenerationThread(
                self.report_generator, self.chart_generator,
                patient_data, start_date, end_date, report_type
            )

            # 连接信号
            self.report_thread.finished.connect(self._on_report_generated)
            self.report_thread.error.connect(self._on_report_error)
            self.report_thread.finished.connect(progress.close)
            self.report_thread.error.connect(progress.close)

            # 启动线程
            self.report_thread.start()

        except Exception as e:
            self.logger.error(f"生成报告失败: {e}")
            QMessageBox.critical(self, "错误", f"生成报告失败: {str(e)}")

    def _on_report_generated(self, report_content: str, charts: List[str]):
        """报告生成完成回调"""
        try:
            self.current_report_content = report_content
            self.current_charts = charts

            # 显示报告内容
            self.report_preview.setPlainText(report_content)

            # 显示第一个图表
            if charts:
                self._display_chart(charts[0])
            else:
                self.chart_preview_label.setText("暂无图表数据")

            self.logger.info("报告生成完成")

        except Exception as e:
            self.logger.error(f"显示报告失败: {e}")

    def _on_report_error(self, error_msg: str):
        """报告生成错误回调"""
        self.logger.error(f"报告生成错误: {error_msg}")
        QMessageBox.critical(self, "错误", f"报告生成失败: {error_msg}")

    def _display_chart(self, chart_base64: str):
        """显示图表"""
        try:
            if chart_base64 and chart_base64.startswith('data:image/png;base64,'):
                # 解码base64图片
                import base64
                image_data = base64.b64decode(chart_base64.split(',')[1])

                # 创建QPixmap
                pixmap = QPixmap()
                pixmap.loadFromData(image_data)

                # 缩放图片以适应标签
                scaled_pixmap = pixmap.scaled(
                    self.chart_preview_label.size(),
                    Qt.KeepAspectRatio,
                    Qt.SmoothTransformation
                )

                self.chart_preview_label.setPixmap(scaled_pixmap)
            else:
                self.chart_preview_label.setText("图表数据格式错误")

        except Exception as e:
            self.logger.error(f"显示图表失败: {e}")
            self.chart_preview_label.setText("图表显示失败")

    def _get_patient_info(self, patient_id: int) -> Dict[str, Any]:
        """获取患者信息"""
        try:
            if self.db_manager:
                results = self.db_manager.execute_query(
                    "SELECT * FROM bingren WHERE bianhao = ?", (patient_id,)
                )
                return results[0] if results else {}
            return {}
        except Exception as e:
            self.logger.error(f"获取患者信息失败: {e}")
            return {}

    def preview_report(self):
        """预览报告"""
        if not self.current_report_content:
            QMessageBox.information(self, "提示", "请先生成报告")
            return

        # 这里可以打开一个新窗口显示完整报告
        QMessageBox.information(self, "预览", "报告预览功能开发中...")

    def print_report(self):
        """打印报告"""
        if not self.current_report_content:
            QMessageBox.information(self, "提示", "请先生成报告")
            return

        # 这里可以调用打印功能
        QMessageBox.information(self, "打印", "打印功能开发中...")

    def export_pdf(self):
        """导出PDF"""
        try:
            if not self.current_report_content:
                QMessageBox.information(self, "提示", "请先生成报告")
                return

            if not self.pdf_exporter.is_available():
                QMessageBox.warning(self, "警告", "PDF导出功能不可用，请安装reportlab库")
                return

            # 选择保存路径
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出PDF报告",
                f"患者报告_{self.current_patient_info.get('name', 'unknown')}_{datetime.now().strftime('%Y%m%d')}.pdf",
                "PDF文件 (*.pdf)"
            )

            if file_path:
                # 导出PDF
                success = self.pdf_exporter.export_personal_report(
                    self.current_patient_info,
                    self.current_report_content,
                    self.current_charts,
                    file_path
                )

                if success:
                    QMessageBox.information(self, "成功", f"PDF报告已导出到: {file_path}")
                else:
                    QMessageBox.critical(self, "错误", "PDF导出失败")

        except Exception as e:
            self.logger.error(f"导出PDF失败: {e}")
            QMessageBox.critical(self, "错误", f"导出PDF失败: {str(e)}")

    def generate_statistics(self):
        """生成统计分析"""
        try:
            # 获取统计类型
            stats_type = self.statistics_type_combo.currentText()

            # 获取日期范围
            start_date = self.stats_start_date.date().toString("yyyy-MM-dd")
            end_date = self.stats_end_date.date().toString("yyyy-MM-dd")

            if "按日统计" in stats_type:
                self._generate_daily_statistics(start_date, end_date)
            elif "按周统计" in stats_type:
                self._generate_weekly_statistics(start_date, end_date)
            elif "按月统计" in stats_type:
                self._generate_monthly_statistics(start_date, end_date)
            elif "按患者统计" in stats_type:
                self._generate_patient_statistics(start_date, end_date)

        except Exception as e:
            self.logger.error(f"生成统计分析失败: {e}")
            QMessageBox.critical(self, "错误", f"生成统计分析失败: {str(e)}")

    def _generate_daily_statistics(self, start_date: str, end_date: str):
        """生成按日统计"""
        try:
            # 获取日期范围内的所有日期
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')

            daily_stats = []
            current_date = start_dt

            while current_date <= end_dt:
                date_str = current_date.strftime('%Y-%m-%d')
                stats = self.report_generator.generate_daily_statistics(date_str)
                if stats:
                    daily_stats.append(stats)
                current_date += timedelta(days=1)

            # 更新统计表格
            self._update_statistics_table(daily_stats, "按日统计")

            # 生成统计图表
            if daily_stats:
                chart = self.chart_generator.generate_daily_statistics_chart(daily_stats)
                if chart:
                    self._display_statistics_chart(chart)

        except Exception as e:
            self.logger.error(f"生成按日统计失败: {e}")
            QMessageBox.critical(self, "错误", f"生成按日统计失败: {str(e)}")

    def _generate_weekly_statistics(self, start_date: str, end_date: str):
        """生成按周统计"""
        try:
            # 生成周统计数据
            weekly_data = self.report_generator.generate_weekly_statistics(start_date, end_date)

            if not weekly_data or not weekly_data.get('weekly_stats'):
                QMessageBox.information(self, "提示", "选择的日期范围内没有数据")
                return

            # 更新统计表格
            self._update_weekly_statistics_table(weekly_data)

            # 生成周统计图表
            chart = self.chart_generator.generate_weekly_statistics_chart(weekly_data)
            if chart:
                self._display_statistics_chart(chart)

            self.logger.info("按周统计生成完成")

        except Exception as e:
            self.logger.error(f"生成按周统计失败: {e}")
            QMessageBox.critical(self, "错误", f"生成按周统计失败: {str(e)}")

    def _generate_monthly_statistics(self, start_date: str, end_date: str):
        """生成按月统计"""
        try:
            # 生成月统计数据
            monthly_data = self.report_generator.generate_monthly_statistics(start_date, end_date)

            if not monthly_data or not monthly_data.get('monthly_stats'):
                QMessageBox.information(self, "提示", "选择的日期范围内没有数据")
                return

            # 更新统计表格
            self._update_monthly_statistics_table(monthly_data)

            # 生成月统计图表
            chart = self.chart_generator.generate_monthly_statistics_chart(monthly_data)
            if chart:
                self._display_statistics_chart(chart)

            self.logger.info("按月统计生成完成")

        except Exception as e:
            self.logger.error(f"生成按月统计失败: {e}")
            QMessageBox.critical(self, "错误", f"生成按月统计失败: {str(e)}")

    def _generate_patient_statistics(self, start_date: str, end_date: str):
        """生成按患者统计"""
        try:
            # 生成患者统计数据
            patient_data = self.report_generator.generate_patient_statistics(start_date, end_date)

            if not patient_data or not patient_data.get('patient_rankings'):
                QMessageBox.information(self, "提示", "选择的日期范围内没有足够的患者数据")
                return

            # 更新统计表格
            self._update_patient_statistics_table(patient_data)

            # 生成患者统计图表
            chart = self.chart_generator.generate_patient_statistics_chart(patient_data)
            if chart:
                self._display_statistics_chart(chart)

            self.logger.info("按患者统计生成完成")

        except Exception as e:
            self.logger.error(f"生成按患者统计失败: {e}")
            QMessageBox.critical(self, "错误", f"生成按患者统计失败: {str(e)}")

    def _update_statistics_table(self, stats_data: List[Dict[str, Any]], stats_type: str):
        """更新统计表格"""
        try:
            if not stats_data:
                self.statistics_table.setRowCount(0)
                return

            if stats_type == "按日统计":
                # 设置表格列
                headers = ["日期", "治疗次数", "患者人数", "平均得分", "优", "良", "中", "差"]
                self.statistics_table.setColumnCount(len(headers))
                self.statistics_table.setHorizontalHeaderLabels(headers)

                # 填充数据
                self.statistics_table.setRowCount(len(stats_data))

                for row, stat in enumerate(stats_data):
                    treatment_stat = stat.get('treatment_stats', {})

                    self.statistics_table.setItem(row, 0, QTableWidgetItem(stat.get('date', '')))
                    self.statistics_table.setItem(row, 1, QTableWidgetItem(str(treatment_stat.get('treatment_count', 0))))
                    self.statistics_table.setItem(row, 2, QTableWidgetItem(str(treatment_stat.get('patient_count', 0))))
                    self.statistics_table.setItem(row, 3, QTableWidgetItem(f"{treatment_stat.get('avg_score', 0):.1f}"))
                    self.statistics_table.setItem(row, 4, QTableWidgetItem(str(treatment_stat.get('excellent_count', 0))))
                    self.statistics_table.setItem(row, 5, QTableWidgetItem(str(treatment_stat.get('good_count', 0))))
                    self.statistics_table.setItem(row, 6, QTableWidgetItem(str(treatment_stat.get('fair_count', 0))))
                    self.statistics_table.setItem(row, 7, QTableWidgetItem(str(treatment_stat.get('poor_count', 0))))

                # 调整列宽
                self.statistics_table.horizontalHeader().setStretchLastSection(True)
                self.statistics_table.resizeColumnsToContents()

        except Exception as e:
            self.logger.error(f"更新统计表格失败: {e}")

    def _update_weekly_statistics_table(self, weekly_data: Dict[str, Any]):
        """更新周统计表格"""
        try:
            weekly_stats = weekly_data.get('weekly_stats', [])
            if not weekly_stats:
                self.statistics_table.setRowCount(0)
                return

            # 设置表格列
            headers = ["周次", "治疗次数", "患者人数", "平均得分", "总时长(分钟)", "优", "良", "中", "差"]
            self.statistics_table.setColumnCount(len(headers))
            self.statistics_table.setHorizontalHeaderLabels(headers)

            # 填充数据
            self.statistics_table.setRowCount(len(weekly_stats))

            for row, stat in enumerate(weekly_stats):
                self.statistics_table.setItem(row, 0, QTableWidgetItem(stat.get('week', '')))
                self.statistics_table.setItem(row, 1, QTableWidgetItem(str(stat.get('treatment_count', 0))))
                self.statistics_table.setItem(row, 2, QTableWidgetItem(str(stat.get('patient_count', 0))))
                self.statistics_table.setItem(row, 3, QTableWidgetItem(f"{stat.get('avg_score', 0):.1f}"))
                self.statistics_table.setItem(row, 4, QTableWidgetItem(f"{stat.get('total_duration', 0):.1f}"))
                self.statistics_table.setItem(row, 5, QTableWidgetItem(str(stat.get('excellent_count', 0))))
                self.statistics_table.setItem(row, 6, QTableWidgetItem(str(stat.get('good_count', 0))))
                self.statistics_table.setItem(row, 7, QTableWidgetItem(str(stat.get('fair_count', 0))))
                self.statistics_table.setItem(row, 8, QTableWidgetItem(str(stat.get('poor_count', 0))))

            # 调整列宽
            self.statistics_table.horizontalHeader().setStretchLastSection(True)
            self.statistics_table.resizeColumnsToContents()

        except Exception as e:
            self.logger.error(f"更新周统计表格失败: {e}")

    def _update_monthly_statistics_table(self, monthly_data: Dict[str, Any]):
        """更新月统计表格"""
        try:
            monthly_stats = monthly_data.get('monthly_stats', [])
            if not monthly_stats:
                self.statistics_table.setRowCount(0)
                return

            # 设置表格列
            headers = ["月份", "治疗次数", "患者人数", "平均得分", "总时长(分钟)", "优", "良", "中", "差"]
            self.statistics_table.setColumnCount(len(headers))
            self.statistics_table.setHorizontalHeaderLabels(headers)

            # 填充数据
            self.statistics_table.setRowCount(len(monthly_stats))

            for row, stat in enumerate(monthly_stats):
                self.statistics_table.setItem(row, 0, QTableWidgetItem(stat.get('month', '')))
                self.statistics_table.setItem(row, 1, QTableWidgetItem(str(stat.get('treatment_count', 0))))
                self.statistics_table.setItem(row, 2, QTableWidgetItem(str(stat.get('patient_count', 0))))
                self.statistics_table.setItem(row, 3, QTableWidgetItem(f"{stat.get('avg_score', 0):.1f}"))
                self.statistics_table.setItem(row, 4, QTableWidgetItem(f"{stat.get('total_duration', 0):.1f}"))
                self.statistics_table.setItem(row, 5, QTableWidgetItem(str(stat.get('excellent_count', 0))))
                self.statistics_table.setItem(row, 6, QTableWidgetItem(str(stat.get('good_count', 0))))
                self.statistics_table.setItem(row, 7, QTableWidgetItem(str(stat.get('fair_count', 0))))
                self.statistics_table.setItem(row, 8, QTableWidgetItem(str(stat.get('poor_count', 0))))

            # 调整列宽
            self.statistics_table.horizontalHeader().setStretchLastSection(True)
            self.statistics_table.resizeColumnsToContents()

        except Exception as e:
            self.logger.error(f"更新月统计表格失败: {e}")

    def _update_patient_statistics_table(self, patient_data: Dict[str, Any]):
        """更新患者统计表格"""
        try:
            patient_rankings = patient_data.get('patient_rankings', [])
            if not patient_rankings:
                self.statistics_table.setRowCount(0)
                return

            # 设置表格列
            headers = ["排名", "患者编号", "姓名", "治疗次数", "平均得分", "最高得分", "总时长(分钟)", "优秀次数", "进步幅度"]
            self.statistics_table.setColumnCount(len(headers))
            self.statistics_table.setHorizontalHeaderLabels(headers)

            # 填充数据
            self.statistics_table.setRowCount(len(patient_rankings))

            for row, patient in enumerate(patient_rankings):
                self.statistics_table.setItem(row, 0, QTableWidgetItem(str(row + 1)))
                self.statistics_table.setItem(row, 1, QTableWidgetItem(str(patient.get('bianh', ''))))
                self.statistics_table.setItem(row, 2, QTableWidgetItem(patient.get('name', '')))
                self.statistics_table.setItem(row, 3, QTableWidgetItem(str(patient.get('treatment_count', 0))))
                self.statistics_table.setItem(row, 4, QTableWidgetItem(f"{patient.get('avg_score', 0):.1f}"))
                self.statistics_table.setItem(row, 5, QTableWidgetItem(f"{patient.get('best_score', 0):.1f}"))
                self.statistics_table.setItem(row, 6, QTableWidgetItem(f"{patient.get('total_duration', 0):.1f}"))
                self.statistics_table.setItem(row, 7, QTableWidgetItem(str(patient.get('excellent_count', 0))))
                self.statistics_table.setItem(row, 8, QTableWidgetItem(f"{patient.get('improvement', 0):.1f}"))

            # 调整列宽
            self.statistics_table.horizontalHeader().setStretchLastSection(True)
            self.statistics_table.resizeColumnsToContents()

        except Exception as e:
            self.logger.error(f"更新患者统计表格失败: {e}")

    def _display_statistics_chart(self, chart_base64: str):
        """显示统计图表"""
        try:
            if chart_base64 and chart_base64.startswith('data:image/png;base64,'):
                # 解码base64图片
                import base64
                image_data = base64.b64decode(chart_base64.split(',')[1])

                # 创建QPixmap
                pixmap = QPixmap()
                pixmap.loadFromData(image_data)

                # 缩放图片以适应标签
                scaled_pixmap = pixmap.scaled(
                    self.statistics_chart_label.size(),
                    Qt.KeepAspectRatio,
                    Qt.SmoothTransformation
                )

                self.statistics_chart_label.setPixmap(scaled_pixmap)
            else:
                self.statistics_chart_label.setText("统计图表数据格式错误")

        except Exception as e:
            self.logger.error(f"显示统计图表失败: {e}")
            self.statistics_chart_label.setText("统计图表显示失败")

    def preview_export_data(self):
        """预览导出数据"""
        try:
            # 获取导出设置
            data_type = self.export_type_combo.currentText()
            start_date = self.export_start_date.date().toString("yyyy-MM-dd")
            end_date = self.export_end_date.date().toString("yyyy-MM-dd")

            # 检查选择的字段
            selected_fields = []
            for field_key, checkbox in self.field_checkboxes.items():
                if checkbox.isChecked():
                    selected_fields.append(field_key)

            if not selected_fields:
                QMessageBox.warning(self, "警告", "请至少选择一个导出字段")
                return

            # 获取预览数据
            if "患者信息" in data_type:
                data = self._get_patient_export_data(selected_fields)
                self.export_status_label.setText(f"💡 患者信息预览 - 共 {len(data)} 条记录")
            elif "治疗记录" in data_type:
                data = self._get_treatment_export_data(start_date, end_date, selected_fields)
                self.export_status_label.setText(f"💡 治疗记录预览 - 共 {len(data)} 条记录")
            elif "脑电数据" in data_type:
                data = []  # 脑电数据较大，暂不预览
                self.export_status_label.setText("💡 脑电数据量较大，请直接导出")
            else:  # 全部数据
                data = self._get_patient_export_data(selected_fields)
                self.export_status_label.setText(f"💡 全部数据预览 - 共 {len(data)} 条记录")

            # 更新预览表格
            self._update_export_preview_table(data)

        except Exception as e:
            self.logger.error(f"预览导出数据失败: {e}")
            QMessageBox.critical(self, "错误", f"预览数据失败: {str(e)}")

    def _update_export_preview_table(self, data: List[Dict[str, Any]]):
        """更新导出预览表格"""
        try:
            if not data:
                self.export_preview_table.setRowCount(0)
                self.export_preview_table.setColumnCount(0)
                return

            # 设置表格大小
            self.export_preview_table.setRowCount(min(len(data), 100))  # 最多预览100行

            # 设置列
            if data:
                headers = list(data[0].keys())
                self.export_preview_table.setColumnCount(len(headers))
                self.export_preview_table.setHorizontalHeaderLabels(headers)

                # 填充数据
                for row, item in enumerate(data[:100]):  # 最多预览100行
                    for col, (key, value) in enumerate(item.items()):
                        self.export_preview_table.setItem(
                            row, col, QTableWidgetItem(str(value) if value is not None else "")
                        )

                # 调整列宽
                self.export_preview_table.resizeColumnsToContents()

                # 如果数据超过100行，显示提示
                if len(data) > 100:
                    self.export_status_label.setText(
                        f"💡 预览前100条记录，实际共 {len(data)} 条记录"
                    )

        except Exception as e:
            self.logger.error(f"更新导出预览表格失败: {e}")

    def _get_treatment_export_data(self, start_date: str, end_date: str,
                                 selected_fields: List[str]) -> List[Dict[str, Any]]:
        """获取治疗记录导出数据"""
        try:
            if not self.db_manager:
                return []

            # 获取日期范围内的治疗记录
            sql = """
                SELECT z.*, b.name as patient_name
                FROM zhiliao z
                LEFT JOIN bingren b ON z.bianh = b.bianhao
                WHERE z.rq BETWEEN ? AND ?
                ORDER BY z.rq DESC, z.shijian DESC
            """

            records = self.db_manager.execute_query(sql, (start_date, end_date))

            # 根据选择的字段过滤数据
            filtered_data = []
            for record in records:
                filtered_record = {}

                if "basic_info" in selected_fields:
                    filtered_record.update({
                        "治疗编号": record.get('zhiliaobh'),
                        "患者编号": record.get('bianh'),
                        "患者姓名": record.get('patient_name'),
                        "治疗日期": record.get('rq'),
                        "治疗时间": record.get('shijian'),
                        "操作员": record.get('czy')
                    })

                if "treatment_records" in selected_fields:
                    filtered_record.update({
                        "治疗时长": record.get('zlsj'),
                        "要求次数": record.get('yaoqiucs'),
                        "实际次数": record.get('shijics'),
                        "成功率": record.get('defen'),
                        "治疗评价": record.get('zlms')
                    })

                if "statistics" in selected_fields:
                    filtered_record.update({
                        "上传状态": record.get('upload_status', 0)
                    })

                filtered_data.append(filtered_record)

            return filtered_data

        except Exception as e:
            self.logger.error(f"获取治疗记录导出数据失败: {e}")
            return []

    def _get_eeg_export_data(self, start_date: str, end_date: str,
                           selected_fields: List[str]) -> List[Dict[str, Any]]:
        """获取脑电数据导出数据"""
        # 脑电数据通常很大，这里返回空列表，实际导出时再处理
        return []

    def _get_all_export_data(self, start_date: str, end_date: str,
                           selected_fields: List[str]) -> List[Dict[str, Any]]:
        """获取全部导出数据"""
        try:
            all_data = []

            # 合并患者信息和治疗记录
            if "basic_info" in selected_fields:
                patient_data = self._get_patient_export_data(selected_fields)
                all_data.extend(patient_data)

            if "treatment_records" in selected_fields:
                treatment_data = self._get_treatment_export_data(start_date, end_date, selected_fields)
                all_data.extend(treatment_data)

            return all_data

        except Exception as e:
            self.logger.error(f"获取全部导出数据失败: {e}")
            return []

    def export_data(self):
        """导出数据"""
        try:
            # 获取导出设置
            data_type = self.export_type_combo.currentText()
            export_format = self.export_format_combo.currentText()
            start_date = self.export_start_date.date().toString("yyyy-MM-dd")
            end_date = self.export_end_date.date().toString("yyyy-MM-dd")

            # 检查选择的字段
            selected_fields = []
            for field_key, checkbox in self.field_checkboxes.items():
                if checkbox.isChecked():
                    selected_fields.append(field_key)

            if not selected_fields:
                QMessageBox.warning(self, "警告", "请至少选择一个导出字段")
                return

            # 选择保存路径
            if "Excel" in export_format:
                file_filter = "Excel文件 (*.xlsx)"
                default_ext = ".xlsx"
            elif "CSV" in export_format:
                file_filter = "CSV文件 (*.csv)"
                default_ext = ".csv"
            else:
                file_filter = "JSON文件 (*.json)"
                default_ext = ".json"

            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出数据",
                f"数据导出_{datetime.now().strftime('%Y%m%d_%H%M%S')}{default_ext}",
                file_filter
            )

            if file_path:
                # 执行导出
                success = self._perform_data_export(
                    data_type, export_format, start_date, end_date,
                    selected_fields, file_path
                )

                if success:
                    QMessageBox.information(self, "成功", f"数据已导出到: {file_path}")
                else:
                    QMessageBox.critical(self, "错误", "数据导出失败")

        except Exception as e:
            self.logger.error(f"导出数据失败: {e}")
            QMessageBox.critical(self, "错误", f"导出数据失败: {str(e)}")

    def _perform_data_export(self, data_type: str, export_format: str,
                           start_date: str, end_date: str,
                           selected_fields: List[str], file_path: str) -> bool:
        """执行数据导出"""
        try:
            # 根据数据类型获取数据
            if "患者信息" in data_type:
                data = self._get_patient_export_data(selected_fields)
            elif "治疗记录" in data_type:
                data = self._get_treatment_export_data(start_date, end_date, selected_fields)
            elif "脑电数据" in data_type:
                data = self._get_eeg_export_data(start_date, end_date, selected_fields)
            else:  # 全部数据
                data = self._get_all_export_data(start_date, end_date, selected_fields)

            if not data:
                QMessageBox.information(self, "提示", "没有找到要导出的数据")
                return False

            # 根据格式导出
            if "Excel" in export_format:
                return self._export_to_excel(data, file_path)
            elif "CSV" in export_format:
                return self._export_to_csv(data, file_path)
            else:  # JSON
                return self._export_to_json(data, file_path)

        except Exception as e:
            self.logger.error(f"执行数据导出失败: {e}")
            return False

    def _get_patient_export_data(self, selected_fields: List[str]) -> List[Dict[str, Any]]:
        """获取患者导出数据"""
        try:
            if not self.db_manager:
                return []

            patients = self.db_manager.get_patients()

            # 根据选择的字段过滤数据
            filtered_data = []
            for patient in patients:
                filtered_patient = {}
                if "basic_info" in selected_fields:
                    filtered_patient.update({
                        "编号": patient.get('bianhao'),
                        "姓名": patient.get('name'),
                        "年龄": patient.get('age'),
                        "性别": patient.get('xingbie'),
                        "身份证": patient.get('cardid'),
                        "诊断": patient.get('zhenduan'),
                        "病史": patient.get('bingshi'),
                        "患侧": patient.get('brhc'),
                        "主治医师": patient.get('zhuzhi'),
                        "录入时间": patient.get('lrshijian')
                    })

                if "statistics" in selected_fields:
                    # 添加统计信息
                    treatment_count = len(self.db_manager.get_treatment_records(patient.get('bianhao', 0)))
                    filtered_patient.update({
                        "治疗次数": treatment_count
                    })

                filtered_data.append(filtered_patient)

            return filtered_data

        except Exception as e:
            self.logger.error(f"获取患者导出数据失败: {e}")
            return []

    def _export_to_excel(self, data: List[Dict[str, Any]], file_path: str) -> bool:
        """导出到Excel"""
        try:
            import pandas as pd

            df = pd.DataFrame(data)
            df.to_excel(file_path, index=False, engine='openpyxl')
            return True

        except ImportError:
            # 如果没有pandas，提示用户使用CSV格式
            reply = QMessageBox.question(
                self, "缺少依赖库",
                "Excel导出需要安装pandas和openpyxl库。\n\n是否改为导出CSV格式？",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 将文件扩展名改为.csv
                csv_path = file_path.rsplit('.', 1)[0] + '.csv'
                return self._export_to_csv(data, csv_path)
            else:
                return False

        except Exception as e:
            self.logger.error(f"导出Excel失败: {e}")
            return False

    def _export_to_csv(self, data: List[Dict[str, Any]], file_path: str) -> bool:
        """导出到CSV"""
        try:
            import csv

            if not data:
                return False

            # 收集所有可能的字段名
            all_fieldnames = set()
            for item in data:
                all_fieldnames.update(item.keys())

            fieldnames = list(all_fieldnames)

            with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames, extrasaction='ignore')
                writer.writeheader()

                # 确保每行数据都有所有字段
                for item in data:
                    row = {}
                    for field in fieldnames:
                        row[field] = item.get(field, '')
                    writer.writerow(row)

            return True

        except Exception as e:
            self.logger.error(f"导出CSV失败: {e}")
            return False

    def _export_to_json(self, data: List[Dict[str, Any]], file_path: str) -> bool:
        """导出到JSON"""
        try:
            import json

            with open(file_path, 'w', encoding='utf-8') as jsonfile:
                json.dump(data, jsonfile, ensure_ascii=False, indent=2)

            return True

        except Exception as e:
            self.logger.error(f"导出JSON失败: {e}")
            return False

    def start_advanced_analysis(self):
        """开始高级分析"""
        try:
            # 获取分析类型
            analysis_type = self.analysis_type_combo.currentText()

            # 获取选择的患者
            patient_data = self.advanced_patient_combo.currentData()
            if not patient_data:
                QMessageBox.warning(self, "警告", "请选择患者")
                return

            # 根据分析类型执行不同的分析
            if "训练报告分析" in analysis_type:
                self._perform_training_analysis(patient_data)
            elif "智能异常检测" in analysis_type:
                self._perform_anomaly_detection(patient_data)
            elif "趋势预测分析" in analysis_type:
                self._perform_trend_prediction(patient_data)
            elif "热力图分析" in analysis_type:
                self._perform_heatmap_analysis(patient_data)
            elif "雷达图分析" in analysis_type:
                self._perform_radar_analysis(patient_data)

        except Exception as e:
            self.logger.error(f"高级分析失败: {e}")
            QMessageBox.critical(self, "错误", f"高级分析失败: {str(e)}")

    def _perform_training_analysis(self, patient_id: int):
        """执行训练报告分析"""
        try:
            # 获取最近的治疗记录
            treatment_records = self.db_manager.execute_query("""
                SELECT * FROM zhiliao
                WHERE bianh = ?
                ORDER BY rq DESC, shijian DESC
                LIMIT 1
            """, (patient_id,))

            if not treatment_records:
                self.analysis_detail_text.setPlainText("❌ 未找到该患者的治疗记录")
                return

            record = treatment_records[0]

            # 生成详细的训练分析报告
            analysis_text = f"""🎯 单次训练详细分析报告

📋 基本信息
患者编号: {patient_id}
治疗日期: {record.get('rq', '')}
治疗时间: {record.get('shijian', '')}
治疗时长: {record.get('zlsj', 0):.1f} 分钟

📊 训练表现
要求次数: {record.get('yaoqiucs', 0)} 次
成功次数: {record.get('shijics', 0)} 次
成功率: {record.get('defen', 0):.1f}%
治疗评价: {record.get('zlms', '')}

📈 表现分析
"""

            # 分析成功率
            success_rate = record.get('defen', 0)
            if success_rate >= 85:
                analysis_text += "✅ 表现优秀：成功率超过85%，患者运动想象能力强\n"
            elif success_rate >= 70:
                analysis_text += "✅ 表现良好：成功率在70-85%之间，有进步空间\n"
            elif success_rate >= 50:
                analysis_text += "⚠️ 表现一般：成功率在50-70%之间，需要加强训练\n"
            else:
                analysis_text += "❌ 表现较差：成功率低于50%，建议调整训练方案\n"

            # 分析治疗时长
            duration = record.get('zlsj', 0)
            if duration >= 30:
                analysis_text += "⏱️ 治疗时长充足，有利于技能巩固\n"
            elif duration >= 20:
                analysis_text += "⏱️ 治疗时长适中，建议适当延长\n"
            else:
                analysis_text += "⏱️ 治疗时长较短，建议增加训练时间\n"

            analysis_text += f"""
💡 改进建议
• 基于当前表现的个性化建议
• 下次训练的重点方向
• 参数调整建议

📅 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

            self.analysis_detail_text.setPlainText(analysis_text)

            # 生成训练分析图表
            self._generate_training_chart(patient_id)

        except Exception as e:
            self.logger.error(f"训练分析失败: {e}")
            self.analysis_detail_text.setPlainText(f"❌ 训练分析失败: {str(e)}")

    def _perform_anomaly_detection(self, patient_id: int):
        """执行智能异常检测"""
        analysis_text = f"""🔍 智能异常检测分析

🎯 检测目标
• 异常治疗数据识别
• 设备故障检测
• 患者状态异常

📊 检测结果
✅ 数据质量正常
✅ 设备运行正常
✅ 患者状态稳定

💡 建议
• 继续保持当前训练方案
• 定期进行异常检测

⚠️ 注意：此功能正在开发中，将在第二阶段完善
"""
        self.analysis_detail_text.setPlainText(analysis_text)
        self.advanced_chart_label.setText("🔍 异常检测图表\n\n功能开发中...")

    def _perform_trend_prediction(self, patient_id: int):
        """执行趋势预测分析"""
        analysis_text = f"""📈 趋势预测分析

🎯 预测目标
• 康复进度预测
• 治疗效果趋势
• 最佳训练时机

📊 预测结果
📈 预计康复进度良好
📈 治疗效果呈上升趋势
📈 建议保持当前训练频率

💡 建议
• 基于趋势的训练调整
• 预期目标设定

⚠️ 注意：此功能正在开发中，将在第二阶段完善
"""
        self.analysis_detail_text.setPlainText(analysis_text)
        self.advanced_chart_label.setText("📈 趋势预测图表\n\n功能开发中...")

    def _perform_heatmap_analysis(self, patient_id: int):
        """执行热力图分析"""
        analysis_text = f"""🔥 热力图分析

🎯 分析维度
• 时间-效果热力图
• 通道-功率热力图
• 频率-时间热力图

📊 分析结果
🔥 最佳训练时段：下午2-4点
🔥 最活跃脑区：运动皮层
🔥 最佳频率范围：8-12Hz

💡 建议
• 优化训练时间安排
• 重点关注活跃脑区

⚠️ 注意：此功能正在开发中，将在第二阶段完善
"""
        self.analysis_detail_text.setPlainText(analysis_text)
        self.advanced_chart_label.setText("🔥 热力图显示\n\n功能开发中...")

    def _perform_radar_analysis(self, patient_id: int):
        """执行雷达图分析"""
        analysis_text = f"""🎪 雷达图分析

🎯 评估维度
• 运动想象能力
• 注意力集中度
• 学习适应性
• 疲劳抵抗力
• 配合度

📊 分析结果
🎪 运动想象能力：85%
🎪 注意力集中度：78%
🎪 学习适应性：82%
🎪 疲劳抵抗力：75%
🎪 配合度：90%

💡 建议
• 加强注意力训练
• 适当控制训练强度

⚠️ 注意：此功能正在开发中，将在第二阶段完善
"""
        self.analysis_detail_text.setPlainText(analysis_text)
        self.advanced_chart_label.setText("🎪 雷达图显示\n\n功能开发中...")

    def _generate_training_chart(self, patient_id: int):
        """生成训练分析图表"""
        try:
            # 获取最近10次治疗记录
            treatment_data = self.db_manager.execute_query("""
                SELECT * FROM zhiliao
                WHERE bianh = ?
                ORDER BY rq DESC, shijian DESC
                LIMIT 10
            """, (patient_id,))

            if treatment_data:
                # 生成趋势图
                chart = self.chart_generator.generate_treatment_trend_chart(treatment_data)
                if chart:
                    self._display_advanced_chart(chart)
                else:
                    self.advanced_chart_label.setText("📊 图表生成失败")
            else:
                self.advanced_chart_label.setText("📊 暂无足够数据生成图表")

        except Exception as e:
            self.logger.error(f"生成训练图表失败: {e}")
            self.advanced_chart_label.setText("📊 图表生成失败")

    def _display_advanced_chart(self, chart_base64: str):
        """显示高级分析图表"""
        try:
            if chart_base64 and chart_base64.startswith('data:image/png;base64,'):
                # 解码base64图片
                import base64
                image_data = base64.b64decode(chart_base64.split(',')[1])

                # 创建QPixmap
                pixmap = QPixmap()
                pixmap.loadFromData(image_data)

                # 缩放图片以适应标签
                scaled_pixmap = pixmap.scaled(
                    self.advanced_chart_label.size(),
                    Qt.KeepAspectRatio,
                    Qt.SmoothTransformation
                )

                self.advanced_chart_label.setPixmap(scaled_pixmap)
            else:
                self.advanced_chart_label.setText("📊 图表数据格式错误")

        except Exception as e:
            self.logger.error(f"显示高级图表失败: {e}")
            self.advanced_chart_label.setText("📊 图表显示失败")

    def cleanup(self):
        """清理资源"""
        try:
            self.logger.info("报告分析界面资源清理完成")
        except Exception as e:
            self.logger.error(f"报告分析界面资源清理失败: {e}")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EEGNet迁移学习模块
Transfer Learning Module for EEGNet

实现基于公开数据集的EEGNet预训练和迁移学习功能
支持从PhysioNet、BCI Competition等数据集进行预训练

作者: AI Assistant
版本: 1.0.0
"""

import logging
import numpy as np
import time
from typing import Optional, Callable, Tuple, Dict, Any
from dataclasses import dataclass

# 深度学习框架
try:
    import tensorflow as tf
    from tensorflow import keras
    TF_AVAILABLE = True
except ImportError:
    TF_AVAILABLE = False
    logging.error("TensorFlow未安装，无法使用迁移学习功能")

from core.eegnet_model import EEGNetModel, TrainingConfig, ModelInfo, create_eegnet_model
from core.dataset_manager import DatasetManager
from core.pretrained_model_manager import PretrainedModelManager


@dataclass
class TransferLearningConfig:
    """迁移学习配置"""
    # 预训练模型选择
    use_pretrained_model: bool = True  # 是否使用预训练模型
    pretrained_model_id: str = "eegnet_general"  # 预训练模型ID

    # 自定义预训练配置（当use_pretrained_model=False时使用）
    pretrain_epochs: int = 50
    pretrain_batch_size: int = 32
    pretrain_learning_rate: float = 0.001
    pretrain_dataset: str = "bci_competition_iv_2b"
    max_pretrain_samples: int = 500

    # 微调配置
    finetune_epochs: int = 20
    finetune_batch_size: int = 16
    finetune_learning_rate: float = 0.0001
    validation_split: float = 0.2

    # 冻结层配置
    freeze_layers: int = 2  # 冻结前N层


class TransferLearningManager:
    """迁移学习管理器"""

    def __init__(self):
        """初始化迁移学习管理器"""
        self.logger = logging.getLogger(__name__)

        if not TF_AVAILABLE:
            raise ImportError("TensorFlow未安装，无法使用迁移学习功能")

        # 组件
        self.dataset_manager = DatasetManager()
        self.pretrained_manager = PretrainedModelManager()

        # 模型
        self.pretrained_model: Optional[tf.keras.Model] = None
        self.target_model: Optional[EEGNetModel] = None

        # 状态
        self.is_pretrained = False
        self.pretrained_source = None  # 'downloaded' 或 'custom'

        self.logger.info("迁移学习管理器初始化完成")
    
    def pretrain_model(self, config: TransferLearningConfig = None,
                      progress_callback: Callable = None) -> bool:
        """预训练EEGNet模型或加载预训练模型"""
        try:
            if config is None:
                config = TransferLearningConfig()

            # 选择预训练策略
            if config.use_pretrained_model:
                return self._load_pretrained_model(config, progress_callback)
            else:
                return self._train_custom_model(config, progress_callback)

        except Exception as e:
            self.logger.error(f"预训练失败: {e}")
            return False

    def _load_pretrained_model(self, config: TransferLearningConfig,
                              progress_callback: Callable = None) -> bool:
        """加载预训练模型"""
        try:
            self.logger.info(f"加载预训练模型: {config.pretrained_model_id}")

            if progress_callback:
                progress_callback("检查预训练模型...", 10)

            # 检查模型是否已下载
            model_info = self.pretrained_manager.get_model_info(config.pretrained_model_id)
            if not model_info:
                self.logger.error(f"未知的预训练模型: {config.pretrained_model_id}")
                return False

            if not model_info.is_downloaded:
                if progress_callback:
                    progress_callback("下载预训练模型...", 20)

                # 下载模型
                if not self.pretrained_manager.download_model(config.pretrained_model_id, progress_callback):
                    self.logger.error("预训练模型下载失败")
                    return False

            if progress_callback:
                progress_callback("加载预训练模型...", 80)

            # 加载模型
            self.pretrained_model = self.pretrained_manager.load_model(config.pretrained_model_id)
            if self.pretrained_model is None:
                self.logger.error("预训练模型加载失败")
                return False

            self.is_pretrained = True
            self.pretrained_source = 'downloaded'

            if progress_callback:
                progress_callback("预训练模型加载完成", 100)

            self.logger.info(f"预训练模型加载成功: {config.pretrained_model_id}")
            self.logger.info(f"模型准确率: {model_info.accuracy:.3f} (在{model_info.dataset}上)")
            return True

        except Exception as e:
            self.logger.error(f"加载预训练模型失败: {e}")
            return False

    def _train_custom_model(self, config: TransferLearningConfig,
                           progress_callback: Callable = None) -> bool:
        """自定义预训练模型"""
        try:
            self.logger.info(f"开始自定义预训练，使用数据集: {config.pretrain_dataset}")

            if progress_callback:
                progress_callback("准备预训练数据...", 0)

            # 准备预训练数据
            X_pretrain, y_pretrain = self.dataset_manager.prepare_transfer_learning_data(
                config.pretrain_dataset,
                target_channels=8,
                target_samples=250,
                target_sr=125,
                max_samples_per_class=config.max_pretrain_samples // 2
            )

            if X_pretrain.size == 0:
                self.logger.error("无法获取预训练数据")
                return False

            self.logger.info(f"预训练数据: {X_pretrain.shape[0]} 个样本")

            if progress_callback:
                progress_callback("创建预训练模型...", 10)

            # 创建EEGNet模型
            self.pretrained_model = create_eegnet_model(
                n_channels=8,
                n_samples=250,
                n_classes=2,
                dropout_rate=0.25
            )

            # 更新学习率
            self.pretrained_model.compile(
                optimizer=tf.keras.optimizers.Adam(learning_rate=config.pretrain_learning_rate),
                loss='sparse_categorical_crossentropy',
                metrics=['accuracy']
            )

            if progress_callback:
                progress_callback("开始预训练...", 20)

            # 预训练模型
            history = self.pretrained_model.fit(
                X_pretrain, y_pretrain,
                epochs=config.pretrain_epochs,
                batch_size=config.pretrain_batch_size,
                validation_split=config.validation_split,
                verbose=1
            )

            self.is_pretrained = True
            self.pretrained_source = 'custom'

            if progress_callback:
                progress_callback("预训练完成", 100)

            # 获取最终性能
            final_acc = history.history['accuracy'][-1]
            final_val_acc = history.history['val_accuracy'][-1]

            self.logger.info(f"自定义预训练完成 - 训练准确率: {final_acc:.3f}, 验证准确率: {final_val_acc:.3f}")
            return True

        except Exception as e:
            self.logger.error(f"自定义预训练失败: {e}")
            return False
    
    def create_transfer_model(self, model_name: str, config: TransferLearningConfig = None) -> Optional[EEGNetModel]:
        """创建迁移学习模型"""
        try:
            if not self.is_pretrained or self.pretrained_model is None:
                self.logger.error("需要先进行预训练")
                return None
            
            if config is None:
                config = TransferLearningConfig()
            
            self.logger.info(f"创建迁移学习模型: {model_name}")
            
            # 创建目标模型
            self.target_model = EEGNetModel(model_name)
            
            # 复制预训练模型的权重
            self.target_model.model = tf.keras.models.clone_model(self.pretrained_model)
            self.target_model.model.set_weights(self.pretrained_model.get_weights())
            
            # 冻结指定层数
            if config.freeze_layers > 0:
                self._freeze_layers(self.target_model.model, config.freeze_layers)
            
            # 重新编译模型（使用较小的学习率）
            self.target_model.model.compile(
                optimizer=tf.keras.optimizers.Adam(learning_rate=config.finetune_learning_rate),
                loss='sparse_categorical_crossentropy',
                metrics=['accuracy']
            )
            
            # 更新模型信息
            self.target_model.model_info.name = model_name
            self.target_model.model_info.version = 2  # 迁移学习版本
            self.target_model.model_info.created_time = time.time()
            
            self.logger.info(f"迁移学习模型创建完成，冻结了前 {config.freeze_layers} 层")
            return self.target_model
            
        except Exception as e:
            self.logger.error(f"创建迁移学习模型失败: {e}")
            return None
    
    def finetune_model(self, target_model: EEGNetModel, 
                      training_data: np.ndarray, training_labels: np.ndarray,
                      config: TransferLearningConfig = None,
                      progress_callback: Callable = None) -> bool:
        """微调模型"""
        try:
            if config is None:
                config = TransferLearningConfig()
            
            if target_model.model is None:
                self.logger.error("目标模型未初始化")
                return False
            
            self.logger.info(f"开始微调模型，样本数: {training_data.shape[0]}")
            
            if progress_callback:
                progress_callback("准备微调数据...", 0)
            
            # 准备数据格式
            X = training_data.reshape(training_data.shape[0], 8, 250, 1)
            y = training_labels
            
            if progress_callback:
                progress_callback("开始微调...", 20)
            
            # 微调模型
            history = target_model.model.fit(
                X, y,
                epochs=config.finetune_epochs,
                batch_size=config.finetune_batch_size,
                validation_split=config.validation_split,
                verbose=1
            )
            
            # 更新模型状态
            target_model.is_trained = True
            target_model.model_info.training_rounds += 1
            target_model.model_info.last_updated = time.time()
            target_model.model_info.total_samples = len(training_data)
            
            if progress_callback:
                progress_callback("微调完成", 100)
            
            # 获取最终性能
            final_acc = history.history['accuracy'][-1]
            final_val_acc = history.history['val_accuracy'][-1]
            
            self.logger.info(f"微调完成 - 训练准确率: {final_acc:.3f}, 验证准确率: {final_val_acc:.3f}")
            return True
            
        except Exception as e:
            self.logger.error(f"微调失败: {e}")
            return False
    
    def _freeze_layers(self, model: tf.keras.Model, num_layers: int):
        """冻结模型的前N层"""
        try:
            layers_frozen = 0
            for layer in model.layers:
                if layers_frozen < num_layers:
                    layer.trainable = False
                    layers_frozen += 1
                    self.logger.debug(f"冻结层: {layer.name}")
                else:
                    layer.trainable = True
            
            self.logger.info(f"已冻结前 {layers_frozen} 层")
            
        except Exception as e:
            self.logger.error(f"冻结层失败: {e}")
    
    def get_pretrain_info(self) -> Dict[str, Any]:
        """获取预训练信息"""
        return {
            'is_pretrained': self.is_pretrained,
            'model_available': self.pretrained_model is not None,
            'model_layers': len(self.pretrained_model.layers) if self.pretrained_model else 0
        }
    
    def save_pretrained_model(self, filepath: str) -> bool:
        """保存预训练模型"""
        try:
            if not self.is_pretrained or self.pretrained_model is None:
                self.logger.error("没有可保存的预训练模型")
                return False
            
            self.pretrained_model.save(filepath)
            self.logger.info(f"预训练模型已保存: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存预训练模型失败: {e}")
            return False
    
    def load_pretrained_model(self, filepath: str) -> bool:
        """加载预训练模型"""
        try:
            self.pretrained_model = tf.keras.models.load_model(filepath)
            self.is_pretrained = True
            self.logger.info(f"预训练模型已加载: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"加载预训练模型失败: {e}")
            return False


def create_transfer_learning_pipeline(pretrain_dataset: str = "bci_competition_iv_2b",
                                    model_name: str = "Transfer_EEGNet",
                                    config: TransferLearningConfig = None) -> Tuple[TransferLearningManager, Optional[EEGNetModel]]:
    """创建完整的迁移学习流水线"""
    try:
        if config is None:
            config = TransferLearningConfig()
            config.pretrain_dataset = pretrain_dataset
        
        # 创建迁移学习管理器
        manager = TransferLearningManager()
        
        # 预训练
        if not manager.pretrain_model(config):
            return manager, None
        
        # 创建迁移学习模型
        transfer_model = manager.create_transfer_model(model_name, config)
        
        return manager, transfer_model
        
    except Exception as e:
        logging.error(f"创建迁移学习流水线失败: {e}")
        return None, None

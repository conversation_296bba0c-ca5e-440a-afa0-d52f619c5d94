# 脑电原始数据存储使用指南

## 📋 概述

本指南介绍如何在您的脑机接口系统中使用新增的带标签脑电原始数据存储功能。该功能可以完整保存治疗过程中的脑电原始数据，为后续的数据分析、模型训练和研究提供支持。

## 🎯 功能特点

### 核心功能
- ✅ **完整数据保存**：保存8通道125Hz原始脑电数据
- ✅ **标签化存储**：每个数据段都有明确的标签（休息/运动想象）
- ✅ **高效压缩**：使用HDF5格式，压缩比约3:1
- ✅ **质量评估**：自动评估数据质量并过滤低质量数据
- ✅ **完整性验证**：确保数据完整性和一致性
- ✅ **便捷查询**：支持按患者、时间、质量等条件查询数据

### 技术规格
- **存储格式**：HDF5 (.h5)
- **压缩算法**：gzip，压缩级别6
- **数据精度**：32位浮点数
- **元数据**：包含患者信息、会话信息、试验参数等
- **索引支持**：数据库索引，快速查询

## 🚀 快速开始

### 1. 启用功能

在 `data/user_config.json` 中确认原始数据存储已启用：

```json
{
  "raw_data": {
    "enabled": true,
    "storage_format": "hdf5",
    "compression": true,
    "quality_threshold": 0.7
  }
}
```

### 2. 基本使用

#### 在治疗工作流程中自动记录

系统已经集成到现有的治疗工作流程中，无需额外操作：

```python
# 治疗开始时自动开始数据记录
# 每个运动想象和休息状态的数据都会自动保存
# 治疗结束时自动结束数据记录
```

#### 手动使用数据管理器

```python
from core.eeg_raw_data_manager import EEGRawDataManager
from core.database_manager import DatabaseManager

# 初始化
db_manager = DatabaseManager()
db_manager.initialize()
raw_data_manager = EEGRawDataManager(db_manager)

# 开始会话
patient_id = 123
session_id = raw_data_manager.start_session(patient_id)

# 保存试验数据
eeg_data = np.random.randn(8, 500)  # 8通道，500个时间点
label = 1  # 1=运动想象，0=休息
raw_data_manager.save_trial_data(eeg_data, label)

# 结束会话
raw_data_manager.end_session()
```

### 3. 数据查询和加载

```python
from core.eeg_data_loader import EEGDataLoader

# 初始化数据加载器
data_loader = EEGDataLoader(db_manager)

# 加载患者数据
patient_data = data_loader.load_patient_data(patient_id=123)

# 获取训练数据集
dataset = data_loader.get_training_dataset(
    patient_ids=[123, 124, 125],
    balance_classes=True,
    min_quality=0.7
)

# 使用数据
train_data = dataset.train_data
train_labels = dataset.train_labels
```

## 📁 数据存储结构

### 目录组织
```
data/
├── raw_eeg_data/                    # 原始数据根目录
│   ├── patients/                    # 按患者组织
│   │   ├── patient_001/             # 患者001
│   │   │   ├── sessions/            # 会话数据
│   │   │   │   ├── 2024-12-19/      # 按日期组织
│   │   │   │   │   ├── P001_20241219_143022_treatment.h5
│   │   │   │   │   └── P001_20241219_150030_training.h5
│   │   │   │   └── 2024-12-20/
│   │   │   └── models/              # 患者专用模型
│   │   └── patient_002/
│   ├── exports/                     # 导出数据
│   └── backup/                      # 备份数据
└── nk_system.db                     # 数据库文件
```

### HDF5文件结构
```
patient_123_session_001.h5
├── /session_metadata               # 会话元数据
│   ├── session_id                 # 会话ID
│   ├── patient_id                 # 患者ID
│   ├── start_time                 # 开始时间
│   └── channel_names              # 通道名称
├── /trials                        # 试验数据组
│   ├── /trial_000                 # 试验0
│   │   ├── eeg_data              # 脑电数据 (8, N)
│   │   ├── label                 # 标签
│   │   ├── start_time            # 开始时间
│   │   └── data_quality          # 数据质量
│   ├── /trial_001                 # 试验1
│   └── ...
```

### 数据库表结构

#### eeg_sessions 表
| 字段 | 类型 | 说明 |
|------|------|------|
| session_id | INTEGER | 会话ID（主键）|
| patient_id | INTEGER | 患者ID |
| treatment_id | INTEGER | 治疗记录ID |
| session_type | TEXT | 会话类型 |
| start_time | TIMESTAMP | 开始时间 |
| end_time | TIMESTAMP | 结束时间 |
| total_trials | INTEGER | 总试验数 |
| successful_trials | INTEGER | 成功试验数 |

#### eeg_raw_data 表
| 字段 | 类型 | 说明 |
|------|------|------|
| id | INTEGER | 记录ID（主键）|
| patient_id | INTEGER | 患者ID |
| session_id | INTEGER | 会话ID |
| trial_id | INTEGER | 试验ID |
| file_path | TEXT | 文件路径 |
| label | INTEGER | 标签（0=休息，1=运动想象）|
| data_quality | REAL | 数据质量评分 |
| file_size_bytes | INTEGER | 文件大小 |
| checksum | TEXT | 文件校验和 |

## 🔧 配置选项

### 完整配置参数

```python
RAW_DATA_CONFIG = {
    'enabled': True,                          # 启用原始数据存储
    'storage_format': 'hdf5',                 # 存储格式
    'compression': True,                      # 启用压缩
    'compression_level': 6,                   # 压缩级别 (1-9)
    'max_file_size_mb': 100,                  # 单文件最大大小
    'auto_backup': True,                      # 自动备份
    'backup_interval_hours': 24,              # 备份间隔
    'data_retention_days': 365,               # 数据保留天数
    'quality_threshold': 0.7,                 # 数据质量阈值
    'cache_size_limit': 100,                  # 数据缓存限制
    'enable_data_validation': True,           # 启用数据验证
    'auto_cleanup': True,                     # 自动清理过期数据
}
```

### 配置说明

- **enabled**: 是否启用原始数据存储功能
- **compression_level**: 压缩级别，1最快，9最小
- **quality_threshold**: 数据质量阈值，低于此值的数据不会保存
- **data_retention_days**: 数据保留天数，超过此时间的数据会被自动清理

## 📊 数据质量评估

### 质量评分标准

数据质量评分范围为0.0-1.0，评估标准包括：

1. **信号幅度检查**：检测异常大的信号幅度
2. **信号方差检查**：检测信号方差是否正常
3. **饱和度检查**：检测信号是否达到ADC极限

### 质量等级

- **优秀 (0.9-1.0)**：信号质量很好，适合所有分析
- **良好 (0.7-0.9)**：信号质量较好，适合大部分分析
- **一般 (0.5-0.7)**：信号质量一般，可用于基础分析
- **较差 (0.0-0.5)**：信号质量差，不建议使用

## 🔍 数据查询示例

### 按条件搜索数据

```python
from core.eeg_data_loader import EEGDataLoader
from datetime import datetime, timedelta

data_loader = EEGDataLoader(db_manager)

# 搜索条件
criteria = {
    'patient_ids': [123, 124],              # 患者ID列表
    'labels': [1],                          # 只要运动想象数据
    'min_quality': 0.8,                     # 最小质量要求
    'date_range': (                         # 时间范围
        datetime.now() - timedelta(days=30),
        datetime.now()
    ),
    'session_types': ['treatment'],         # 会话类型
    'limit': 100                           # 限制结果数量
}

trials = data_loader.search_trials(criteria)
print(f"找到 {len(trials)} 个符合条件的试验")
```

### 获取统计信息

```python
# 获取整体统计
stats = data_loader.get_data_statistics()
print(f"总试验数: {stats['total_trials']}")
print(f"总患者数: {stats['total_patients']}")
print(f"平均质量: {stats['avg_quality']:.3f}")

# 获取特定患者统计
patient_stats = data_loader.get_data_statistics([123])
print(f"患者123的数据: {patient_stats}")
```

## 🛠️ 维护和管理

### 数据备份

```python
from core.eeg_raw_data_manager import EEGRawDataManager

# 手动备份
raw_data_manager = EEGRawDataManager(db_manager)

# 导出患者数据
success = raw_data_manager.export_patient_data(
    patient_id=123,
    output_dir="./exports/patient_123",
    format_type='hdf5'
)
```

### 数据清理

```python
# 清理过期数据（保留365天）
cleaned_count = raw_data_manager.cleanup_old_data(retention_days=365)
print(f"清理了 {cleaned_count} 个过期会话")
```

### 数据完整性验证

```python
from core.treatment_data_integration import TreatmentDataIntegration

integration = TreatmentDataIntegration(db_manager)

# 验证特定会话的数据完整性
validation_result = integration.validate_data_integrity(session_id=123)

if validation_result['valid']:
    print("数据完整性验证通过")
else:
    print(f"数据完整性问题: {validation_result['error']}")
    print(f"详细信息: {validation_result['details']}")
```

## 📈 存储空间管理

### 空间需求估算

- **单次试验**：约16KB（原始）→ 5KB（压缩后）
- **单次治疗**（60试验）：约300KB
- **每患者每月**：约9MB（30次治疗）
- **100患者一年**：约11GB

### 空间优化建议

1. **启用压缩**：可节省约70%存储空间
2. **设置质量阈值**：过滤低质量数据
3. **定期清理**：删除过期数据
4. **分级存储**：重要数据保留更长时间

## ⚠️ 注意事项

### 使用限制

1. **依赖HDF5**：需要安装h5py库
2. **磁盘空间**：确保有足够的存储空间
3. **性能影响**：大量数据保存可能影响实时性能
4. **数据隐私**：注意患者数据的隐私保护

### 故障排除

#### 常见问题

1. **数据保存失败**
   - 检查磁盘空间是否充足
   - 确认HDF5库是否正确安装
   - 检查文件权限

2. **数据加载失败**
   - 验证文件是否存在
   - 检查文件是否损坏
   - 确认数据库连接正常

3. **性能问题**
   - 调整压缩级别
   - 增加缓存大小
   - 优化查询条件

## 🎉 总结

脑电原始数据存储功能为您的系统提供了完整的数据管理能力，支持：

- ✅ 完整的原始数据保存
- ✅ 智能的数据质量管理
- ✅ 便捷的数据查询和分析
- ✅ 可靠的数据完整性保证

通过合理配置和使用，这个功能将大大提升您系统的数据管理能力和研究价值。

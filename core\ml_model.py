# EEGNet深度学习模型接口
from core.eegnet_model import (
    EEGNetModel as BaseEEGNetModel,
    ModelManager as BaseModelManager,
    ModelPerformance,
    ModelInfo,
    TrainingConfig,
    create_eegnet_model
)

import logging
import numpy as np
from typing import List, Tuple, Optional, Callable


class MotorImageryModel:
    """EEGNet深度学习运动想象模型"""

    def __init__(self, model_name: str = "EEGNet_Model"):
        """初始化EEGNet运动想象模型"""
        self.logger = logging.getLogger(__name__)
        self.model_name = model_name

        # 模型状态
        self.is_trained = False
        self.training_data = []
        self.training_labels = []

        # 初始化EEGNet模型
        try:
            self.eegnet_model = BaseEEGNetModel(model_name)
            self.logger.info(f"EEGNet深度学习模型初始化完成: {model_name}")
        except ImportError as e:
            self.logger.error(f"EEGNet初始化失败: {e}")
            self.logger.error("请确保TensorFlow已正确安装")
            raise ImportError("EEGNet深度学习模型需要TensorFlow支持")



    def add_training_data(self, data: np.ndarray, label: int) -> bool:
        """添加训练数据到EEGNet模型"""
        try:
            # 添加到本地缓存
            self.training_data.append(data)
            self.training_labels.append(label)

            # 添加到EEGNet模型
            success = self.eegnet_model.add_training_data(data, label)

            if success:
                self.logger.debug(f"添加训练数据成功，总计: {len(self.training_data)}")

            return success

        except Exception as e:
            self.logger.error(f"添加训练数据失败: {e}")
            return False

    def train_model(self, neural_network: str = "eegnet", config: TrainingConfig = None,
                   progress_callback: Callable = None) -> bool:
        """训练EEGNet深度学习模型"""
        try:
            # 确保使用EEGNet
            if neural_network != "eegnet":
                self.logger.info(f"强制使用EEGNet深度学习架构，忽略参数 '{neural_network}'")

            # 训练EEGNet模型
            success = self.eegnet_model.train_model(config, progress_callback)

            if success:
                self.is_trained = True
                self.logger.info("EEGNet深度学习模型训练完成")

            return success

        except Exception as e:
            self.logger.error(f"EEGNet模型训练失败: {e}")
            return False

    def predict(self, data: np.ndarray) -> Tuple[int, float]:
        """EEGNet深度学习预测"""
        try:
            if not self.is_trained:
                raise ValueError("EEGNet模型未训练")

            # 调用EEGNet模型的预测方法
            prediction, confidence = self.eegnet_model.predict(data)
            return int(prediction), float(confidence)

        except Exception as e:
            self.logger.error(f"EEGNet预测失败: {e}")
            return 0, 0.0

    def predict_with_adjustment(self, data: np.ndarray) -> Tuple[int, float, str]:
        """带神经网络调整机制的预测"""
        try:
            if not self.is_trained:
                raise ValueError("EEGNet模型未训练")

            # 调用EEGNet模型的带调整预测方法
            return self.eegnet_model.predict_with_adjustment(data)

        except Exception as e:
            self.logger.error(f"EEGNet带调整预测失败: {e}")
            return 0, 0.0, "error"

    def get_model_info(self) -> ModelInfo:
        """获取EEGNet模型信息"""
        return self.eegnet_model.model_info

    def save_model(self, filepath: str = None) -> bool:
        """保存EEGNet模型"""
        return self.eegnet_model.save_model(filepath)

    def load_model(self, filepath: str) -> bool:
        """加载EEGNet模型"""
        success = self.eegnet_model.load_model(filepath)
        if success:
            self.is_trained = True
            # 同步训练数据
            self.training_data = self.eegnet_model.training_data.copy()
            self.training_labels = self.eegnet_model.training_labels.copy()
        return success

    def clear_training_data(self):
        """清空训练数据"""
        self.eegnet_model.clear_training_data()
        self.training_data.clear()
        self.training_labels.clear()

    def calibrate_model(self, recent_predictions: List[Tuple[int, float]],
                       expected_balance: float = 0.3) -> bool:
        """神经网络模型校准"""
        return self.eegnet_model.calibrate_model(recent_predictions, expected_balance)

    def adjust_difficulty(self, level: int):
        """调整神经网络敏感度等级"""
        self.eegnet_model.adjust_difficulty(level)


class ModelManager:
    """EEGNet深度学习模型管理器"""

    def __init__(self):
        """初始化EEGNet模型管理器"""
        self.logger = logging.getLogger(__name__)

        # 当前模型
        self.current_model: Optional[MotorImageryModel] = None

        # 初始化EEGNet模型管理器
        try:
            self.base_manager = BaseModelManager()
            self.logger.info("EEGNet深度学习模型管理器初始化完成")
        except ImportError as e:
            self.logger.error(f"EEGNet模型管理器初始化失败: {e}")
            self.logger.error("请确保TensorFlow已正确安装")
            raise ImportError("EEGNet深度学习模型管理器需要TensorFlow支持")

    def create_model(self, model_name: str) -> MotorImageryModel:
        """创建新的EEGNet模型"""
        model = MotorImageryModel(model_name)
        return model

    def save_model(self, model: MotorImageryModel, model_name: str = None) -> bool:
        """保存EEGNet模型"""
        try:
            # 使用底层EEGNet模型进行保存
            return self.base_manager.save_model(model.eegnet_model, model_name)

        except Exception as e:
            self.logger.error(f"保存EEGNet模型失败: {e}")
            return False

    def load_model(self, model_name: str) -> Optional[MotorImageryModel]:
        """加载EEGNet模型"""
        try:
            # 使用底层管理器加载EEGNet模型
            base_model = self.base_manager.load_model(model_name)

            if base_model:
                # 创建适配器包装 - 避免重新初始化EEGNet
                wrapper_model = MotorImageryModel.__new__(MotorImageryModel)
                wrapper_model.logger = logging.getLogger(__name__)
                wrapper_model.model_name = model_name
                wrapper_model.is_trained = base_model.is_trained
                wrapper_model.training_data = base_model.training_data.copy()
                wrapper_model.training_labels = base_model.training_labels.copy()

                # 直接使用加载的EEGNet模型
                wrapper_model.eegnet_model = base_model

                return wrapper_model

            return None

        except Exception as e:
            self.logger.error(f"加载EEGNet模型失败: {e}")
            return None

    def list_models(self) -> List[dict]:
        """列出所有可用的EEGNet模型"""
        return self.base_manager.list_models()

    def delete_model(self, model_name: str) -> bool:
        """删除EEGNet模型"""
        return self.base_manager.delete_model(model_name)

    def set_current_model(self, model: MotorImageryModel):
        """设置当前EEGNet模型"""
        self.current_model = model

    def get_current_model(self) -> Optional[MotorImageryModel]:
        """获取当前EEGNet模型"""
        return self.current_model


# 导出EEGNet深度学习相关类和函数
__all__ = [
    'MotorImageryModel',
    'ModelManager',
    'ModelPerformance',
    'ModelInfo',
    'TrainingConfig',
    'create_eegnet_model'
]


def create_motor_imagery_model(model_name: str = "EEGNet_Model") -> MotorImageryModel:
    """创建EEGNet运动想象模型的便捷函数"""
    return MotorImageryModel(model_name)


def create_model_manager() -> ModelManager:
    """创建EEGNet模型管理器的便捷函数"""
    return ModelManager()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
样式模块
Styles Module

作者: AI Assistant
版本: 2.0.0
"""

# 样式文件路径常量
import os
from pathlib import Path

STYLES_DIR = Path(__file__).parent

MEDICAL_THEME_QSS = STYLES_DIR / "medical_theme.qss"
TECH_THEME_QSS = STYLES_DIR / "tech_theme.qss"
BASE_STYLES_QSS = STYLES_DIR / "base_styles.qss"

def load_stylesheet(theme_name: str) -> str:
    """加载指定主题的样式表"""
    if theme_name == "medical":
        qss_file = MEDICAL_THEME_QSS
    elif theme_name == "tech":
        qss_file = TECH_THEME_QSS
    else:
        qss_file = BASE_STYLES_QSS
    
    try:
        with open(qss_file, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        return ""

__all__ = [
    'MEDICAL_THEME_QSS',
    'TECH_THEME_QSS', 
    'BASE_STYLES_QSS',
    'load_stylesheet',
]

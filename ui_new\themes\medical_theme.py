#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医疗主题配置
Medical Theme Configuration

作者: AI Assistant
版本: 2.0.0
"""

from typing import Dict, Any


class MedicalTheme:
    """医疗主题配置类"""
    
    # 颜色系统
    COLORS = {
        'primary': '#2563eb',           # 主要蓝色
        'secondary': '#1e40af',         # 深蓝色
        'accent': '#0ea5e9',            # 强调蓝色
        'success': '#10b981',           # 成功绿色
        'warning': '#f59e0b',           # 警告橙色
        'danger': '#ef4444',            # 危险红色
        'bg_primary': '#f8fafc',        # 主背景（浅灰）
        'bg_secondary': '#ffffff',      # 次背景（白色）
        'bg_tertiary': '#f1f5f9',       # 三级背景
        'text_primary': '#1e293b',      # 主文字色
        'text_secondary': '#64748b',    # 次文字色
        'border': '#e2e8f0',            # 边框色
        'shadow': 'rgba(0, 0, 0, 0.1)', # 阴影色
        'shadow_lg': 'rgba(0, 0, 0, 0.1)', # 大阴影色
    }
    
    # 字体系统
    FONTS = {
        'primary': 'Segoe UI, -apple-system, BlinkMacSystemFont, sans-serif',
        'mono': 'Consolas, Monaco, monospace',
        'sizes': {
            'xs': 12,
            'sm': 14,
            'base': 16,
            'lg': 18,
            'xl': 20,
            'xxl': 24,
            'xxxl': 32,
        }
    }
    
    # 间距系统
    SPACING = {
        'xs': 4,
        'sm': 8,
        'base': 16,
        'lg': 24,
        'xl': 32,
        'xxl': 48,
    }
    
    # 圆角系统
    BORDER_RADIUS = {
        'small': 6,
        'base': 8,
        'large': 12,
        'xl': 16,
        'full': 9999,  # 完全圆角
    }
    
    # 阴影系统
    SHADOWS = {
        'sm': '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
        'base': '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        'lg': '0 10px 25px -3px rgba(0, 0, 0, 0.1)',
        'xl': '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
    }
    
    @classmethod
    def get_theme_config(cls) -> Dict[str, Any]:
        """获取完整主题配置"""
        return {
            'name': 'medical',
            'display_name': '医疗主题',
            'colors': cls.COLORS,
            'fonts': cls.FONTS,
            'spacing': cls.SPACING,
            'border_radius': cls.BORDER_RADIUS,
            'shadows': cls.SHADOWS,
        }
    
    @classmethod
    def get_color(cls, color_name: str) -> str:
        """获取指定颜色值"""
        return cls.COLORS.get(color_name, '#000000')
    
    @classmethod
    def get_font_size(cls, size_name: str) -> int:
        """获取指定字体大小"""
        return cls.FONTS['sizes'].get(size_name, 16)
    
    @classmethod
    def get_spacing(cls, spacing_name: str) -> int:
        """获取指定间距值"""
        return cls.SPACING.get(spacing_name, 16)
    
    @classmethod
    def get_border_radius(cls, radius_name: str) -> int:
        """获取指定圆角值"""
        return cls.BORDER_RADIUS.get(radius_name, 8)

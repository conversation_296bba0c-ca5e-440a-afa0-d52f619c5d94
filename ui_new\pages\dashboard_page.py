#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时监测仪表板页面
Real-time Monitoring Dashboard Page

作者: AI Assistant
版本: 2.0.0
"""

import logging
from typing import Dict, Any, Optional
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, QPushButton,
    QFrame, QGroupBox, QProgressBar, QSizePolicy, QMessageBox
)
from PySide6.QtCore import Qt, QTimer, Signal
from PySide6.QtGui import QFont, QPalette

from .base_page import BasePage
from core.auth_manager import Permission


class MetricCard(QWidget):
    """指标卡片组件"""
    
    def __init__(self, title: str, value: str = "0", unit: str = "", parent=None):
        super().__init__(parent)
        self.title = title
        self.value = value
        self.unit = unit
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        self.setProperty("class", "card")
        self.setFixedHeight(120)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 16, 20, 16)
        layout.setSpacing(8)
        
        # 标题
        title_label = QLabel(self.title)
        title_label.setProperty("class", "text-sm")
        title_label.setAlignment(Qt.AlignLeft)
        layout.addWidget(title_label)
        
        # 数值
        value_layout = QHBoxLayout()
        value_layout.setContentsMargins(0, 0, 0, 0)
        
        self.value_label = QLabel(self.value)
        self.value_label.setProperty("class", "text-xxxl font-bold")
        value_layout.addWidget(self.value_label)
        
        if self.unit:
            unit_label = QLabel(self.unit)
            unit_label.setProperty("class", "text-base")
            unit_label.setAlignment(Qt.AlignBottom)
            value_layout.addWidget(unit_label)
        
        value_layout.addStretch()
        layout.addLayout(value_layout)
        
        layout.addStretch()
    
    def update_value(self, value: str):
        """更新数值"""
        self.value = value
        self.value_label.setText(value)


class BrainVisualization(QWidget):
    """脑电可视化组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        self.setProperty("class", "card-large")
        self.setMinimumHeight(400)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(24, 24, 24, 24)
        layout.setSpacing(16)
        
        # 标题
        title_layout = QHBoxLayout()
        title_label = QLabel("脑电信号实时监测")
        title_label.setProperty("class", "card-title")
        title_layout.addWidget(title_label)
        
        title_layout.addStretch()
        
        # 控制按钮
        self.start_button = QPushButton("开始记录")
        self.start_button.setProperty("class", "btn-primary")
        title_layout.addWidget(self.start_button)
        
        self.pause_button = QPushButton("暂停")
        self.pause_button.setProperty("class", "btn-secondary")
        title_layout.addWidget(self.pause_button)
        
        layout.addLayout(title_layout)
        
        # 脑电可视化区域
        self.visualization_area = QWidget()
        self.visualization_area.setMinimumHeight(300)
        self.visualization_area.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                            stop:0 rgba(37, 99, 235, 0.1),
                                            stop:1 rgba(14, 165, 233, 0.1));
                border-radius: 12px;
                border: 2px dashed rgba(37, 99, 235, 0.3);
            }
        """)
        
        # 占位内容
        viz_layout = QVBoxLayout(self.visualization_area)
        viz_layout.setAlignment(Qt.AlignCenter)
        
        placeholder_label = QLabel("🧠")
        placeholder_label.setProperty("class", "text-xxxl")
        placeholder_label.setAlignment(Qt.AlignCenter)
        viz_layout.addWidget(placeholder_label)
        
        placeholder_text = QLabel("脑电信号可视化区域\n(将集成PyQtGraph实时波形显示)")
        placeholder_text.setProperty("class", "text-base text-center")
        placeholder_text.setAlignment(Qt.AlignCenter)
        viz_layout.addWidget(placeholder_text)
        
        layout.addWidget(self.visualization_area)


class ControlPanel(QWidget):
    """控制面板组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        self.setProperty("class", "card")
        self.setFixedWidth(300)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(16)
        
        # 标题
        title_label = QLabel("训练参数控制")
        title_label.setProperty("class", "card-title")
        layout.addWidget(title_label)
        
        # 刺激强度
        intensity_label = QLabel("刺激强度")
        intensity_label.setProperty("class", "text-sm font-medium")
        layout.addWidget(intensity_label)
        
        self.intensity_bar = QProgressBar()
        self.intensity_bar.setRange(0, 100)
        self.intensity_bar.setValue(65)
        self.intensity_bar.setTextVisible(True)
        self.intensity_bar.setFormat("%p%")
        layout.addWidget(self.intensity_bar)
        
        # 训练状态
        status_label = QLabel("训练状态")
        status_label.setProperty("class", "text-sm font-medium")
        layout.addWidget(status_label)
        
        self.status_display = QLabel("待机中")
        self.status_display.setProperty("class", "text-base")
        self.status_display.setAlignment(Qt.AlignCenter)
        self.status_display.setStyleSheet("""
            QLabel {
                background-color: rgba(245, 158, 11, 0.1);
                color: #f59e0b;
                border-radius: 6px;
                padding: 8px;
            }
        """)
        layout.addWidget(self.status_display)
        
        # 开始训练按钮
        self.start_training_button = QPushButton("开始训练")
        self.start_training_button.setProperty("class", "btn-primary")
        self.start_training_button.setFixedHeight(50)
        layout.addWidget(self.start_training_button)
        
        layout.addStretch()


class PatientInfo(QWidget):
    """患者信息组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_patient = None
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        self.setProperty("class", "card")
        self.setFixedWidth(300)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(16)
        
        # 标题
        title_label = QLabel("当前患者")
        title_label.setProperty("class", "card-title")
        layout.addWidget(title_label)
        
        # 患者头像
        self.avatar_label = QLabel("?")
        self.avatar_label.setAlignment(Qt.AlignCenter)
        self.avatar_label.setFixedSize(80, 80)
        self.avatar_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                            stop:0 #2563eb,
                                            stop:1 #0ea5e9);
                border-radius: 40px;
                color: white;
                font-size: 32px;
                font-weight: 600;
            }
        """)
        
        avatar_layout = QHBoxLayout()
        avatar_layout.addStretch()
        avatar_layout.addWidget(self.avatar_label)
        avatar_layout.addStretch()
        layout.addLayout(avatar_layout)
        
        # 患者姓名
        self.name_label = QLabel("未选择患者")
        self.name_label.setProperty("class", "text-lg font-semibold text-center")
        self.name_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.name_label)
        
        # 患者ID
        self.id_label = QLabel("请先选择患者")
        self.id_label.setProperty("class", "text-sm text-center")
        self.id_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.id_label)
        
        # 选择患者按钮
        self.select_patient_button = QPushButton("选择患者")
        self.select_patient_button.setProperty("class", "btn-secondary")
        layout.addWidget(self.select_patient_button)
        
        layout.addStretch()
    
    def update_patient_info(self, patient_data: Dict[str, Any]):
        """更新患者信息"""
        self.current_patient = patient_data
        
        name = patient_data.get('name', '未知')
        patient_id = patient_data.get('bianhao', '未知')
        
        # 更新头像
        if name and len(name) > 0:
            self.avatar_label.setText(name[0])
        else:
            self.avatar_label.setText("?")
        
        # 更新信息
        self.name_label.setText(name)
        self.id_label.setText(f"患者ID: {patient_id}")


class DashboardPage(BasePage):
    """实时监测仪表板页面"""
    
    # 信号定义
    patient_selection_requested = Signal()  # 请求选择患者
    training_started = Signal()             # 训练开始
    recording_started = Signal()            # 记录开始
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 页面组件
        self.metrics_grid = None
        self.brain_visualization = None
        self.control_panel = None
        self.patient_info = None
        
        # 指标卡片
        self.metric_cards = {}
        
        # 模拟数据定时器
        self.data_simulation_timer = QTimer()
        self.data_simulation_timer.timeout.connect(self.update_simulated_data)
    
    def setup_ui(self):
        """设置UI"""
        try:
            # 创建内容组件
            self.content_widget = QWidget()
            self.main_layout.addWidget(self.content_widget)
            
            # 主网格布局
            main_grid = QGridLayout(self.content_widget)
            main_grid.setContentsMargins(0, 0, 0, 0)
            main_grid.setSpacing(24)
            
            # 左侧：脑电可视化 + 指标卡片
            left_widget = QWidget()
            left_layout = QVBoxLayout(left_widget)
            left_layout.setContentsMargins(0, 0, 0, 0)
            left_layout.setSpacing(24)
            
            # 脑电可视化
            self.brain_visualization = BrainVisualization()
            left_layout.addWidget(self.brain_visualization)
            
            # 指标卡片网格
            self.create_metrics_grid()
            left_layout.addWidget(self.metrics_grid)
            
            main_grid.addWidget(left_widget, 0, 0)
            
            # 右侧：控制面板 + 患者信息
            right_widget = QWidget()
            right_layout = QVBoxLayout(right_widget)
            right_layout.setContentsMargins(0, 0, 0, 0)
            right_layout.setSpacing(24)
            
            # 控制面板
            self.control_panel = ControlPanel()
            right_layout.addWidget(self.control_panel)
            
            # 患者信息
            self.patient_info = PatientInfo()
            right_layout.addWidget(self.patient_info)
            
            right_layout.addStretch()
            
            main_grid.addWidget(right_widget, 0, 1)
            
            # 设置列比例
            main_grid.setColumnStretch(0, 2)  # 左侧占2/3
            main_grid.setColumnStretch(1, 1)  # 右侧占1/3
            
        except Exception as e:
            self.logger.error(f"设置仪表板UI失败: {e}")
            raise
    
    def create_metrics_grid(self):
        """创建指标卡片网格"""
        self.metrics_grid = QWidget()
        grid_layout = QGridLayout(self.metrics_grid)
        grid_layout.setContentsMargins(0, 0, 0, 0)
        grid_layout.setSpacing(16)
        
        # 创建指标卡片
        metrics_config = [
            ("alpha", "Alpha频率", "8.3", "Hz"),
            ("beta", "Beta频率", "15.2", "Hz"),
            ("recognition", "意图识别率", "87", "%"),
            ("snr", "信噪比", "23.5", "dB"),
        ]
        
        for i, (key, title, value, unit) in enumerate(metrics_config):
            card = MetricCard(title, value, unit)
            self.metric_cards[key] = card
            
            row = i // 2
            col = i % 2
            grid_layout.addWidget(card, row, col)
    
    def setup_connections(self):
        """设置信号连接"""
        try:
            # 脑电可视化控制
            self.brain_visualization.start_button.clicked.connect(self.start_recording)
            self.brain_visualization.pause_button.clicked.connect(self.pause_recording)
            
            # 控制面板
            self.control_panel.start_training_button.clicked.connect(self.start_training)
            
            # 患者信息
            self.patient_info.select_patient_button.clicked.connect(self.select_patient)
            
        except Exception as e:
            self.logger.error(f"设置仪表板信号连接失败: {e}")
    
    def load_data(self):
        """加载数据"""
        try:
            # 检查权限
            if not self.has_permission(Permission.PATIENT_VIEW):
                self.show_error("权限不足，无法查看患者数据")
                return
            
            # 模拟数据加载
            self.simulate_data_loading()
            
            # 启动数据模拟
            self.data_simulation_timer.start(2000)  # 每2秒更新一次
            
            # 发送加载完成信号
            self.page_loaded.emit()
            
        except Exception as e:
            self.logger.error(f"加载仪表板数据失败: {e}")
            self.show_error(f"加载数据失败: {e}")
    
    def simulate_data_loading(self):
        """模拟数据加载"""
        import random
        
        # 更新指标卡片
        self.metric_cards["alpha"].update_value(f"{random.uniform(7.5, 9.0):.1f}")
        self.metric_cards["beta"].update_value(f"{random.uniform(14.0, 16.5):.1f}")
        self.metric_cards["recognition"].update_value(f"{random.randint(80, 95)}")
        self.metric_cards["snr"].update_value(f"{random.uniform(20.0, 28.0):.1f}")
    
    def update_simulated_data(self):
        """更新模拟数据"""
        if self.is_visible:
            self.simulate_data_loading()
    
    def start_recording(self):
        """开始记录"""
        try:
            self.log_operation("开始脑电记录")
            self.recording_started.emit()
            
            # 更新按钮状态
            self.brain_visualization.start_button.setText("记录中...")
            self.brain_visualization.start_button.setEnabled(False)
            self.brain_visualization.pause_button.setEnabled(True)
            
        except Exception as e:
            self.logger.error(f"开始记录失败: {e}")
    
    def pause_recording(self):
        """暂停记录"""
        try:
            self.log_operation("暂停脑电记录")
            
            # 更新按钮状态
            self.brain_visualization.start_button.setText("开始记录")
            self.brain_visualization.start_button.setEnabled(True)
            self.brain_visualization.pause_button.setEnabled(False)
            
        except Exception as e:
            self.logger.error(f"暂停记录失败: {e}")
    
    def start_training(self):
        """开始训练"""
        try:
            if not self.patient_info.current_patient:
                QMessageBox.warning(self, "提示", "请先选择患者")
                return
            
            self.log_operation("开始康复训练")
            self.training_started.emit()
            
            # 更新状态显示
            self.control_panel.status_display.setText("训练中")
            self.control_panel.status_display.setStyleSheet("""
                QLabel {
                    background-color: rgba(16, 185, 129, 0.1);
                    color: #10b981;
                    border-radius: 6px;
                    padding: 8px;
                }
            """)
            
        except Exception as e:
            self.logger.error(f"开始训练失败: {e}")
    
    def select_patient(self):
        """选择患者"""
        try:
            self.patient_selection_requested.emit()
        except Exception as e:
            self.logger.error(f"选择患者失败: {e}")
    
    def set_patient_info(self, patient_data: Dict[str, Any]):
        """设置患者信息"""
        try:
            self.patient_info.update_patient_info(patient_data)
            self.log_operation("切换患者", f"患者: {patient_data.get('name', '未知')}")
        except Exception as e:
            self.logger.error(f"设置患者信息失败: {e}")
    
    def check_permissions(self) -> bool:
        """检查页面权限"""
        return self.has_permission(Permission.PATIENT_VIEW)
    
    def hide_page(self):
        """页面隐藏时停止数据模拟"""
        super().hide_page()
        self.data_simulation_timer.stop()

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>脑机接口康复训练系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        /* 主题变量 */
        .theme-medical {
            --primary-color: #2563eb;
            --secondary-color: #1e40af;
            --accent-color: #0ea5e9;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --bg-primary: #f8fafc;
            --bg-secondary: #ffffff;
            --bg-tertiary: #f1f5f9;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --border-color: #e2e8f0;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
        }
        
        .theme-tech {
            --primary-color: #06b6d4;
            --secondary-color: #0891b2;
            --accent-color: #00d4ff;
            --success-color: #00ff88;
            --warning-color: #ffaa00;
            --danger-color: #ff3366;
            --bg-primary: #0f172a;
            --bg-secondary: #1e293b;
            --bg-tertiary: #334155;
            --text-primary: #f1f5f9;
            --text-secondary: #94a3b8;
            --border-color: #475569;
            --shadow: 0 4px 6px -1px rgba(0, 255, 255, 0.1);
            --shadow-lg: 0 10px 25px -3px rgba(0, 255, 255, 0.2);
        }
        
        .app-container {
            background: var(--bg-primary);
            color: var(--text-primary);
            min-height: 100vh;
            display: flex;
            transition: all 0.3s ease;
        }
        
        /* 侧边栏 */
        .sidebar {
            width: 280px;
            background: var(--bg-secondary);
            border-right: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            transition: all 0.3s ease;
            box-shadow: var(--shadow);
            position: relative;
            z-index: 100;
        }
        
        .sidebar.collapsed {
            width: 80px;
        }
        
        .sidebar-header {
            padding: 24px 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .logo {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            font-size: 20px;
            flex-shrink: 0;
        }
        
        .logo-text {
            font-size: 18px;
            font-weight: 600;
            opacity: 1;
            transition: all 0.3s ease;
        }
        
        .sidebar.collapsed .logo-text {
            opacity: 0;
            width: 0;
            overflow: hidden;
        }
        
        .nav-menu {
            flex: 1;
            padding: 20px 0;
            overflow-y: auto;
        }
        
        .nav-section {
            margin-bottom: 32px;
        }
        
        .nav-title {
            padding: 0 20px 12px;
            font-size: 12px;
            font-weight: 600;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            opacity: 1;
            transition: all 0.3s ease;
        }
        
        .sidebar.collapsed .nav-title {
            opacity: 0;
            height: 0;
            margin: 0;
            padding: 0;
        }
        
        .nav-item {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            margin: 0 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            gap: 12px;
        }
        
        .nav-item:hover {
            background: var(--bg-tertiary);
        }
        
        .nav-item.active {
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            color: white;
        }
        
        .nav-item.active::before {
            content: '';
            position: absolute;
            left: -12px;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 24px;
            background: var(--accent-color);
            border-radius: 2px;
        }
        
        .nav-icon {
            width: 20px;
            height: 20px;
            flex-shrink: 0;
        }
        
        .nav-text {
            opacity: 1;
            transition: all 0.3s ease;
        }
        
        .sidebar.collapsed .nav-text {
            opacity: 0;
            width: 0;
            overflow: hidden;
        }
        
        .sidebar-footer {
            padding: 20px;
            border-top: 1px solid var(--border-color);
        }
        
        .user-profile {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            border-radius: 8px;
            background: var(--bg-tertiary);
        }
        
        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            flex-shrink: 0;
        }
        
        .user-info {
            opacity: 1;
            transition: all 0.3s ease;
        }
        
        .sidebar.collapsed .user-info {
            opacity: 0;
            width: 0;
            overflow: hidden;
        }
        
        .user-name {
            font-weight: 600;
            font-size: 14px;
        }
        
        .user-role {
            font-size: 12px;
            color: var(--text-secondary);
        }
        
        /* 主内容区 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .top-bar {
            height: 72px;
            background: var(--bg-secondary);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
            box-shadow: var(--shadow);
        }
        
        .top-bar-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }
        
        .menu-toggle {
            width: 40px;
            height: 40px;
            border: none;
            background: var(--bg-tertiary);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .menu-toggle:hover {
            background: var(--border-color);
        }
        
        .page-title {
            font-size: 24px;
            font-weight: 600;
            margin-left: 8px;
        }
        
        .top-bar-right {
            display: flex;
            align-items: center;
            gap: 16px;
        }
        
        .theme-switch {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: var(--bg-tertiary);
            border-radius: 8px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .switch {
            position: relative;
            width: 48px;
            height: 24px;
            background: var(--border-color);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .switch.active {
            background: var(--primary-color);
        }
        
        .switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
        }
        
        .switch.active::after {
            transform: translateX(24px);
        }
        
        .status-indicators {
            display: flex;
            gap: 12px;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-normal {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }
        
        .status-warning {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: currentColor;
        }
        
        /* 内容区域 */
        .content-area {
            flex: 1;
            overflow: hidden;
            position: relative;
        }
        
        .page-content {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            padding: 24px;
            overflow: auto;
            opacity: 0;
            transform: translateX(20px);
            transition: all 0.3s ease;
            pointer-events: none;
        }
        
        .page-content.active {
            opacity: 1;
            transform: translateX(0);
            pointer-events: all;
        }
        
        /* 实时监测页面样式 */
        .dashboard-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            grid-template-rows: auto 1fr;
            gap: 24px;
            height: calc(100vh - 120px);
        }
        
        .main-monitor {
            grid-column: 1;
            grid-row: 1 / 3;
            background: var(--bg-secondary);
            border-radius: 16px;
            padding: 24px;
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--border-color);
        }
        
        .control-panel {
            grid-column: 2;
            grid-row: 1;
            background: var(--bg-secondary);
            border-radius: 16px;
            padding: 24px;
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--border-color);
        }
        
        .patient-info {
            grid-column: 2;
            grid-row: 2;
            background: var(--bg-secondary);
            border-radius: 16px;
            padding: 24px;
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--border-color);
        }
        
        /* 患者管理页面样式 */
        .patient-layout {
            display: flex;
            gap: 24px;
            height: calc(100vh - 120px);
        }
        
        .patient-list-section {
            width: 60%;
            background: var(--bg-secondary);
            border-radius: 16px;
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow-lg);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .patient-detail-section {
            width: 40%;
            background: var(--bg-secondary);
            border-radius: 16px;
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow-lg);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        /* 数据报告页面样式 */
        .report-layout {
            display: flex;
            gap: 24px;
            height: calc(100vh - 120px);
        }
        
        .report-control {
            width: 320px;
            background: var(--bg-secondary);
            border-radius: 16px;
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow-lg);
        }
        
        .report-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 24px;
        }
        
        /* 通用组件样式 */
        .card {
            background: var(--bg-secondary);
            border-radius: 12px;
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow);
            padding: 20px;
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .card-title {
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn {
            padding: 10px 16px;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 14px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-lg);
        }
        
        /* 科技主题特效 */
        .theme-tech .content-area::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                linear-gradient(rgba(6, 182, 212, 0.05) 1px, transparent 1px),
                linear-gradient(90deg, rgba(6, 182, 212, 0.05) 1px, transparent 1px);
            background-size: 20px 20px;
            pointer-events: none;
            opacity: 0.3;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: -280px;
                top: 0;
                height: 100vh;
                z-index: 1000;
            }
            
            .sidebar.mobile-open {
                left: 0;
            }
            
            .dashboard-grid,
            .patient-layout,
            .report-layout {
                grid-template-columns: 1fr;
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="app-container theme-tech" id="app">
        <!-- 侧边栏 -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo">NK</div>
                <div class="logo-text">神经康复系统</div>
            </div>
            
            <div class="nav-menu">
                <div class="nav-section">
                    <div class="nav-title">监测训练</div>
                    <div class="nav-item active" data-page="dashboard" data-title="实时脑电监测">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                        </svg>
                        <span class="nav-text">实时监测</span>
                    </div>
                    <div class="nav-item" data-page="training" data-title="康复训练">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        <span class="nav-text">康复训练</span>
                    </div>
                    <div class="nav-item" data-page="evaluation" data-title="疗效评估">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z M4 5a2 2 0 012-2v1a1 1 0 001 1h6a1 1 0 001-1V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5z"/>
                        </svg>
                        <span class="nav-text">疗效评估</span>
                    </div>
                </div>
                
                <div class="nav-section">
                    <div class="nav-title">患者管理</div>
                    <div class="nav-item" data-page="patients" data-title="患者档案">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                        </svg>
                        <span class="nav-text">患者档案</span>
                    </div>
                    <div class="nav-item" data-page="sessions" data-title="治疗记录">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1z"/>
                        </svg>
                        <span class="nav-text">治疗记录</span>
                    </div>
                </div>
                
                <div class="nav-section">
                    <div class="nav-title">数据分析</div>
                    <div class="nav-item" data-page="reports" data-title="数据报告中心">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"/>
                        </svg>
                        <span class="nav-text">数据报告</span>
                    </div>
                    <div class="nav-item" data-page="analytics" data-title="趋势分析">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3z"/>
                        </svg>
                        <span class="nav-text">趋势分析</span>
                    </div>
                </div>
                
                <div class="nav-section">
                    <div class="nav-title">系统管理</div>
                    <div class="nav-item" data-page="users" data-title="用户管理">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"/>
                        </svg>
                        <span class="nav-text">用户管理</span>
                    </div>
                    <div class="nav-item" data-page="settings" data-title="系统设置">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                        </svg>
                        <span class="nav-text">系统设置</span>
                    </div>
                </div>
            </div>
            
            <div class="sidebar-footer">
                <div class="user-profile">
                    <div class="user-avatar">A</div>
                    <div class="user-info">
                        <div class="user-name">管理员</div>
                        <div class="user-role">系统管理员</div>
                    </div>
                </div>
            </div>
        </nav>
        
        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部栏 -->
            <header class="top-bar">
                <div class="top-bar-left">
                    <button class="menu-toggle" id="menuToggle">
                        <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"/>
                        </svg>
                    </button>
                    <h1 class="page-title" id="pageTitle">实时脑电监测</h1>
                </div>
                
                <div class="top-bar-right">
                    <div class="theme-switch">
                        <span>医疗</span>
                        <div class="switch active" id="themeSwitch"></div>
                        <span>科技</span>
                    </div>
                    
                    <div class="status-indicators">
                        <div class="status-indicator status-normal">
                            <div class="status-dot"></div>
                            <span>设备在线</span>
                        </div>
                        <div class="status-indicator status-normal">
                            <div class="status-dot"></div>
                            <span>信号良好</span>
                        </div>
                        <div class="status-indicator status-warning">
                            <div class="status-dot"></div>
                            <span>校准中</span>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- 内容区域 -->
            <div class="content-area">
                <!-- 实时监测页面 -->
                <div class="page-content active" id="dashboard-page">
                    <div class="dashboard-grid">
                        <div class="main-monitor">
                            <div class="card-header">
                                <div class="card-title">
                                    <svg width="24" height="24" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"/>
                                    </svg>
                                    脑电信号实时监测
                                </div>
                                <div style="display: flex; gap: 8px;">
                                    <button class="btn btn-primary">开始记录</button>
                                    <button class="btn" style="background: var(--bg-tertiary);">暂停</button>
                                </div>
                            </div>
                            
                            <div style="height: 300px; background: linear-gradient(135deg, var(--bg-tertiary), var(--bg-primary)); border-radius: 12px; position: relative; margin-bottom: 24px; overflow: hidden;">
                                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 200px; height: 200px; border: 2px solid var(--accent-color); border-radius: 50%; opacity: 0.7; animation: pulse 2s ease-in-out infinite;"></div>
                                <div style="position: absolute; top: 45%; left: 30%; width: 12px; height: 12px; background: var(--primary-color); border-radius: 50%; border: 2px solid var(--accent-color);"></div>
                                <div style="position: absolute; top: 45%; right: 30%; width: 12px; height: 12px; background: var(--primary-color); border-radius: 50%; border: 2px solid var(--accent-color);"></div>
                            </div>
                            
                            <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 16px;">
                                <div class="card">
                                    <div style="font-size: 24px; font-weight: 700; color: var(--primary-color); margin-bottom: 4px;">8.3</div>
                                    <div style="font-size: 12px; color: var(--text-secondary);">Alpha频率 (Hz)</div>
                                </div>
                                <div class="card">
                                    <div style="font-size: 24px; font-weight: 700; color: var(--primary-color); margin-bottom: 4px;">15.2</div>
                                    <div style="font-size: 12px; color: var(--text-secondary);">Beta频率 (Hz)</div>
                                </div>
                                <div class="card">
                                    <div style="font-size: 24px; font-weight: 700; color: var(--primary-color); margin-bottom: 4px;">87%</div>
                                    <div style="font-size: 12px; color: var(--text-secondary);">意图识别率</div>
                                </div>
                                <div class="card">
                                    <div style="font-size: 24px; font-weight: 700; color: var(--primary-color); margin-bottom: 4px;">23.5</div>
                                    <div style="font-size: 12px; color: var(--text-secondary);">信噪比 (dB)</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="control-panel">
                            <div class="card-title">训练参数控制</div>
                            <div style="margin-top: 20px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: 500;">刺激强度</label>
                                <div style="background: var(--bg-tertiary); height: 6px; border-radius: 3px; position: relative;">
                                    <div style="height: 100%; width: 65%; background: linear-gradient(90deg, var(--primary-color), var(--accent-color)); border-radius: 3px;"></div>
                                </div>
                                <button class="btn btn-primary" style="width: 100%; margin-top: 20px;">开始训练</button>
                            </div>
                        </div>
                        
                        <div class="patient-info">
                            <div class="card-title">当前患者</div>
                            <div style="text-align: center; margin-top: 20px;">
                                <div style="width: 60px; height: 60px; border-radius: 50%; background: linear-gradient(135deg, var(--primary-color), var(--accent-color)); display: flex; align-items: center; justify-content: center; color: white; font-size: 24px; font-weight: 600; margin: 0 auto 16px;">张</div>
                                <div style="font-size: 18px; font-weight: 600; margin-bottom: 8px;">张三</div>
                                <div style="font-size: 14px; color: var(--text-secondary);">患者ID: **********</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 患者管理页面 -->
                <div class="page-content" id="patients-page">
                    <div class="patient-layout">
                        <div class="patient-list-section">
                            <div class="card-header">
                                <div class="card-title">患者列表 (共24名)</div>
                                <button class="btn btn-primary">新增患者</button>
                            </div>
                            <div style="padding: 20px;">
                                <div style="background: var(--bg-tertiary); padding: 16px; border-radius: 8px; margin-bottom: 16px;">
                                    <div style="display: flex; align-items: center; gap: 12px;">
                                        <div style="width: 32px; height: 32px; border-radius: 50%; background: linear-gradient(135deg, var(--primary-color), var(--accent-color)); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 12px;">张</div>
                                        <div>
                                            <div style="font-weight: 600;">张三</div>
                                            <div style="font-size: 12px; color: var(--text-secondary);">ID: **********</div>
                                        </div>
                                    </div>
                                </div>
                                <div style="background: var(--bg-tertiary); padding: 16px; border-radius: 8px; margin-bottom: 16px;">
                                    <div style="display: flex; align-items: center; gap: 12px;">
                                        <div style="width: 32px; height: 32px; border-radius: 50%; background: linear-gradient(135deg, var(--primary-color), var(--accent-color)); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 12px;">李</div>
                                        <div>
                                            <div style="font-weight: 600;">李四</div>
                                            <div style="font-size: 12px; color: var(--text-secondary);">ID: 6886622</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="patient-detail-section">
                            <div class="card-header">
                                <div class="card-title">患者详情</div>
                            </div>
                            <div style="padding: 20px; text-align: center;">
                                <div style="width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, var(--primary-color), var(--accent-color)); display: flex; align-items: center; justify-content: center; color: white; font-size: 32px; font-weight: 600; margin: 0 auto 16px;">张</div>
                                <div style="font-size: 24px; font-weight: 600; margin-bottom: 8px;">张三</div>
                                <div style="color: var(--text-secondary); font-size: 14px;">患者ID: **********</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 数据报告页面 -->
                <div class="page-content" id="reports-page">
                    <div class="report-layout">
                        <div class="report-control">
                            <div class="card-header">
                                <div class="card-title">报告生成器</div>
                            </div>
                            <div style="padding: 20px;">
                                <div style="margin-bottom: 20px;">
                                    <label style="display: block; margin-bottom: 8px; font-weight: 500;">报告类型</label>
                                    <select style="width: 100%; padding: 12px; border: 1px solid var(--border-color); border-radius: 8px; background: var(--bg-tertiary); color: var(--text-primary);">
                                        <option>个人报告</option>
                                        <option>汇总报告</option>
                                    </select>
                                </div>
                                <button class="btn btn-primary" style="width: 100%;">生成报告</button>
                            </div>
                        </div>
                        
                        <div class="report-content">
                            <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px;">
                                <div class="card">
                                    <div style="font-size: 28px; font-weight: 700; margin-bottom: 4px;">24</div>
                                    <div style="font-size: 14px; color: var(--text-secondary);">总患者数</div>
                                </div>
                                <div class="card">
                                    <div style="font-size: 28px; font-weight: 700; margin-bottom: 4px;">156</div>
                                    <div style="font-size: 14px; color: var(--text-secondary);">完成训练</div>
                                </div>
                                <div class="card">
                                    <div style="font-size: 28px; font-weight: 700; margin-bottom: 4px;">87.3%</div>
                                    <div style="font-size: 14px; color: var(--text-secondary);">平均识别率</div>
                                </div>
                                <div class="card">
                                    <div style="font-size: 28px; font-weight: 700; margin-bottom: 4px;">2</div>
                                    <div style="font-size: 14px; color: var(--text-secondary);">设备故障</div>
                                </div>
                            </div>
                            
                            <div class="card" style="flex: 1; margin-top: 20px;">
                                <div class="card-header">
                                    <div class="card-title">康复进度趋势分析</div>
                                </div>
                                <div style="height: 300px; background: linear-gradient(135deg, var(--bg-tertiary), var(--bg-primary)); border-radius: 8px; position: relative;">
                                    <div style="position: absolute; bottom: 20px; left: 20px; right: 20px; height: 2px; background: linear-gradient(90deg, var(--primary-color), var(--accent-color)); clip-path: polygon(0% 100%, 10% 80%, 20% 60%, 30% 70%, 40% 40%, 50% 30%, 60% 50%, 70% 20%, 80% 35%, 90% 15%, 100% 25%, 100% 100%);"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 其他页面占位 -->
                <div class="page-content" id="training-page">
                    <div class="card">
                        <div class="card-title">康复训练</div>
                        <p style="margin-top: 20px; color: var(--text-secondary);">康复训练页面内容待开发...</p>
                    </div>
                </div>
                
                <div class="page-content" id="evaluation-page">
                    <div class="card">
                        <div class="card-title">疗效评估</div>
                        <p style="margin-top: 20px; color: var(--text-secondary);">疗效评估页面内容待开发...</p>
                    </div>
                </div>
                
                <div class="page-content" id="sessions-page">
                    <div class="card">
                        <div class="card-title">治疗记录</div>
                        <p style="margin-top: 20px; color: var(--text-secondary);">治疗记录页面内容待开发...</p>
                    </div>
                </div>
                
                <div class="page-content" id="analytics-page">
                    <div class="card">
                        <div class="card-title">趋势分析</div>
                        <p style="margin-top: 20px; color: var(--text-secondary);">趋势分析页面内容待开发...</p>
                    </div>
                </div>
                
                <div class="page-content" id="users-page">
                    <div class="card">
                        <div class="card-title">用户管理</div>
                        <p style="margin-top: 20px; color: var(--text-secondary);">用户管理页面内容待开发...</p>
                    </div>
                </div>
                
                <div class="page-content" id="settings-page">
                    <div class="card">
                        <div class="card-title">系统设置</div>
                        <p style="margin-top: 20px; color: var(--text-secondary);">系统设置页面内容待开发...</p>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <script>
        // 主题切换功能
        const themeSwitch = document.getElementById('themeSwitch');
        const app = document.getElementById('app');
        
        themeSwitch.addEventListener('click', () => {
            if (app.classList.contains('theme-medical')) {
                app.classList.remove('theme-medical');
                app.classList.add('theme-tech');
                themeSwitch.classList.add('active');
            } else {
                app.classList.remove('theme-tech');
                app.classList.add('theme-medical');
                themeSwitch.classList.remove('active');
            }
        });
        
        // 侧边栏折叠功能
        const menuToggle = document.getElementById('menuToggle');
        const sidebar = document.getElementById('sidebar');
        
        menuToggle.addEventListener('click', () => {
            sidebar.classList.toggle('collapsed');
        });
        
        // 页面切换功能
        const navItems = document.querySelectorAll('.nav-item');
        const pageContents = document.querySelectorAll('.page-content');
        const pageTitle = document.getElementById('pageTitle');
        
        navItems.forEach(item => {
            item.addEventListener('click', () => {
                // 移除所有活跃状态
                navItems.forEach(nav => nav.classList.remove('active'));
                pageContents.forEach(page => page.classList.remove('active'));
                
                // 设置当前项为活跃状态
                item.classList.add('active');
                
                // 显示对应页面
                const targetPage = item.getAttribute('data-page');
                const targetPageElement = document.getElementById(targetPage + '-page');
                if (targetPageElement) {
                    targetPageElement.classList.add('active');
                }
                
                // 更新页面标题
                const title = item.getAttribute('data-title');
                if (title) {
                    pageTitle.textContent = title;
                }
            });
        });
        
        // 响应式处理
        function handleResize() {
            if (window.innerWidth <= 768) {
                sidebar.classList.add('collapsed');
            }
        }
        
        window.addEventListener('resize', handleResize);
        handleResize();
        
        // 添加pulse动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes pulse {
                0%, 100% { 
                    transform: translate(-50%, -50%) scale(1); 
                    opacity: 0.7; 
                }
                50% { 
                    transform: translate(-50%, -50%) scale(1.05); 
                    opacity: 1; 
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
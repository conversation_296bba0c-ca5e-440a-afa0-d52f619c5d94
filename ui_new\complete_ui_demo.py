#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整UI演示 - 展示整个系统界面效果
Complete UI Demo - Showcase Full System Interface

作者: AI Assistant
版本: 2.0.0
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel
from PySide6.QtCore import Qt, QTimer

# 导入新UI组件
from ui_new.main_window_new import ModernMainWindow
from ui_new.themes.theme_manager import ThemeManager


class CompleteUIDemo(ModernMainWindow):
    """完整UI演示 - 继承现代化主窗口"""
    
    def __init__(self):
        super().__init__()
        
        # 设置演示模式
        self.setup_demo_mode()
        
        # 模拟用户登录
        self.simulate_user_login()
        
        # 启动演示
        self.start_demo()
    
    def setup_demo_mode(self):
        """设置演示模式"""
        # 设置窗口标题
        self.setWindowTitle("NK脑机接口康复系统 - 完整UI演示")
        
        # 设置窗口大小
        self.resize(1600, 1000)
        
        # 居中显示
        self.center_window()
    
    def center_window(self):
        """窗口居中显示"""
        screen = QApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)
    
    def simulate_user_login(self):
        """模拟用户登录"""
        # 模拟用户信息
        user_info = {
            'name': '演示用户',
            'role': '医生'
        }
        
        # 设置用户登录状态
        self.current_user = user_info['name']
        self.current_user_role = user_info['role']
        self.is_user_logged_in = True
        
        # 更新侧边栏用户信息
        self.sidebar.update_user_info(user_info['name'], user_info['role'])
        
        # 显示敏感内容
        self.show_sensitive_content()
    
    def start_demo(self):
        """启动演示"""
        # 设置默认页面为仪表板
        self.sidebar.set_active_page("dashboard")
        self.on_page_changed("dashboard")
        
        # 启动状态更新
        self.demo_timer = QTimer()
        self.demo_timer.timeout.connect(self.update_demo_status)
        self.demo_timer.start(5000)  # 每5秒更新状态
        
        # 显示演示信息
        self.show_demo_info()
    
    def update_demo_status(self):
        """更新演示状态"""
        import random
        
        # 随机更新状态指示器
        statuses = ["normal", "warning", "error"]
        
        # 更新设备状态
        device_status = random.choice(statuses)
        device_texts = {
            "normal": "设备在线",
            "warning": "设备连接中",
            "error": "设备离线"
        }
        self.top_bar.set_device_status(device_status, device_texts[device_status])
        
        # 更新信号状态
        signal_status = random.choice(statuses)
        signal_texts = {
            "normal": "信号良好",
            "warning": "信号微弱", 
            "error": "信号中断"
        }
        self.top_bar.set_signal_status(signal_status, signal_texts[signal_status])
    
    def show_demo_info(self):
        """显示演示信息"""
        print("=" * 80)
        print("🎉 NK脑机接口康复系统 - 完整UI演示")
        print("=" * 80)
        print()
        print("✨ 界面特性展示：")
        print("  🎨 现代化侧边栏 - 可折叠导航，分组菜单")
        print("  📊 智能顶部栏 - 主题切换，实时状态监控")
        print("  🧠 精美仪表板 - 脑电可视化，动态指标卡片")
        print("  🎯 双主题系统 - 医疗专业主题 + 科技荧光主题")
        print("  ⚡ 动画效果 - 脉冲动画，发光效果，平滑切换")
        print()
        print("🎮 交互演示：")
        print("  • 点击侧边栏菜单项 - 页面切换")
        print("  • 点击顶部栏菜单按钮 - 侧边栏折叠/展开")
        print("  • 点击主题切换开关 - 医疗/科技主题切换")
        print("  • 点击'开始记录'按钮 - 脑电可视化动画")
        print("  • 观察状态指示器 - 实时状态变化")
        print()
        print("⌨️  快捷键：")
        print("  • T键 - 切换主题")
        print("  • M键 - 切换侧边栏")
        print("  • ESC键 - 退出演示")
        print()
        print("🔍 重点观察：")
        print("  1. 整体布局 - 现代化单页应用架构")
        print("  2. 视觉设计 - 渐变色，阴影，圆角等现代元素")
        print("  3. 交互反馈 - 悬停效果，点击反馈，状态变化")
        print("  4. 专业性 - 医疗设备级别的界面标准")
        print("=" * 80)
    
    def keyPressEvent(self, event):
        """键盘事件处理"""
        if event.key() == Qt.Key_T:
            # T键切换主题
            current_theme = self.theme_manager.get_current_theme()
            new_theme = "tech" if current_theme == "medical" else "medical"
            self.theme_manager.switch_theme(new_theme)
            print(f"🎨 主题已切换到: {'科技主题' if new_theme == 'tech' else '医疗主题'}")
            
        elif event.key() == Qt.Key_M:
            # M键切换侧边栏
            self.toggle_sidebar()
            print("📱 侧边栏已切换")
            
        elif event.key() == Qt.Key_Escape:
            # ESC键退出
            print("👋 感谢使用NK脑机接口康复系统UI演示！")
            self.close()
        
        super().keyPressEvent(event)
    
    def on_page_changed(self, page_id: str):
        """重写页面切换处理"""
        try:
            if page_id == "logout":
                print("👋 演示结束")
                self.close()
                return
            
            # 调用父类方法
            super().on_page_changed(page_id)
            
            # 演示信息
            page_names = {
                'dashboard': '实时监测仪表板',
                'patients': '患者管理',
                'treatment': '治疗系统',
                'data_management': '数据管理',
                'reports': '报告分析',
                'users': '用户管理',
                'settings': '系统设置',
            }
            
            page_name = page_names.get(page_id, '未知页面')
            print(f"📄 已切换到: {page_name}")
            
        except Exception as e:
            print(f"页面切换错误: {e}")
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        print("\n🎊 演示结束，感谢体验NK脑机接口康复系统的现代化UI！")
        super().closeEvent(event)


def main():
    """主函数"""
    try:
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("NK脑机接口康复系统 - 完整UI演示")
        
        # 创建完整UI演示
        demo = CompleteUIDemo()
        demo.show()
        
        # 运行应用程序
        return app.exec()
        
    except Exception as e:
        print(f"演示启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())

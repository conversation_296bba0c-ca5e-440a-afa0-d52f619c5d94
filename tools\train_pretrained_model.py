#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预训练模型训练工具
Pretrained Model Training Tool

使用下载的BCI数据集训练基础EEGNet模型，包含详细的训练进度和结果输出

作者: AI Assistant
版本: 1.0.0
"""

import sys
import os
from pathlib import Path
import numpy as np
import pickle
import time
import json

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

try:
    import tensorflow as tf
    from tensorflow import keras
    from sklearn.model_selection import train_test_split
    from sklearn.preprocessing import StandardScaler
    TF_AVAILABLE = True
except ImportError as e:
    print(f"❌ 依赖库缺失: {e}")
    print("请安装: pip install tensorflow scikit-learn")
    sys.exit(1)

from core.eegnet_model import create_eegnet_model


class PretrainedModelTrainer:
    """预训练模型训练器"""

    def __init__(self, dataset_path="data/bci_dataset/physionet_your_system_dataset.pkl"):
        self.dataset_path = Path(dataset_path)
        self.model = None
        self.history = None
        self.scaler = None
        
        # 训练配置
        self.config = {
            'epochs': 100,
            'batch_size': 32,
            'learning_rate': 0.001,
            'validation_split': 0.2,
            'test_split': 0.1,
            'early_stopping_patience': 15,
            'reduce_lr_patience': 8,
            'min_lr': 1e-6
        }
        
        print("🧠 预训练模型训练器初始化完成")
        print(f"数据集路径: {self.dataset_path}")
    
    def load_dataset(self):
        """加载数据集"""
        print("\n📂 加载BCI数据集...")
        
        if not self.dataset_path.exists():
            raise FileNotFoundError(f"数据集文件不存在: {self.dataset_path}")
        
        with open(self.dataset_path, 'rb') as f:
            dataset = pickle.load(f)
        
        X = dataset['data']
        y = dataset['labels']
        subjects = dataset['subjects']

        # 处理不同数据集格式的info字段
        if 'info' in dataset:
            info = dataset['info']
        elif 'system_config' in dataset:
            # PhysioNet数据集格式
            info = {
                'name': 'PhysioNet EEG Motor Movement/Imagery Dataset',
                'description': '运动想象vs平静状态脑电数据集',
                'subjects': len(np.unique(subjects)),
                'channels': dataset['system_config']['n_channels'],
                'sampling_rate': dataset['system_config']['sampling_rate'],
                'task': dataset['system_config']['task']
            }
        else:
            # 默认信息
            info = {
                'name': 'Unknown Dataset',
                'description': '脑电数据集',
                'subjects': len(np.unique(subjects)),
                'channels': X.shape[1],
                'sampling_rate': 125,
                'task': 'motor_imagery_vs_rest'
            }

        print(f"✅ 数据集加载成功:")
        print(f"  - 数据形状: {X.shape}")
        print(f"  - 标签分布: {dict(zip(*np.unique(y, return_counts=True)))}")
        print(f"  - 受试者数: {len(np.unique(subjects))}")
        print(f"  - 采样率: {info['sampling_rate']} Hz")
        print(f"  - 通道数: {info['channels']}")
        print(f"  - 任务类型: {info['task']}")

        return X, y, subjects, info
    
    def preprocess_data(self, X, y, subjects):
        """数据预处理"""
        print("\n🔧 数据预处理...")
        
        # 数据形状转换为EEGNet格式: (samples, channels, time_points, 1)
        X_processed = X.reshape(X.shape[0], X.shape[1], X.shape[2], 1)
        
        print(f"  - 原始形状: {X.shape}")
        print(f"  - 处理后形状: {X_processed.shape}")
        
        # 按受试者划分数据集（确保同一受试者的数据不会同时出现在训练和测试集中）
        unique_subjects = np.unique(subjects)
        n_subjects = len(unique_subjects)
        
        # 选择测试受试者（最后1个受试者用于测试）
        test_subjects = unique_subjects[-1:]
        train_val_subjects = unique_subjects[:-1]
        
        print(f"  - 训练/验证受试者: {train_val_subjects}")
        print(f"  - 测试受试者: {test_subjects}")
        
        # 划分数据
        train_val_mask = np.isin(subjects, train_val_subjects)
        test_mask = np.isin(subjects, test_subjects)
        
        X_train_val = X_processed[train_val_mask]
        y_train_val = y[train_val_mask]
        X_test = X_processed[test_mask]
        y_test = y[test_mask]
        
        # 进一步划分训练和验证集
        X_train, X_val, y_train, y_val = train_test_split(
            X_train_val, y_train_val, 
            test_size=self.config['validation_split'],
            random_state=42,
            stratify=y_train_val
        )
        
        print(f"  - 训练集: {X_train.shape[0]} 样本")
        print(f"  - 验证集: {X_val.shape[0]} 样本")
        print(f"  - 测试集: {X_test.shape[0]} 样本")
        
        # 数据标准化（基于训练集计算参数）
        print("  - 应用数据标准化...")
        original_shape = X_train.shape
        X_train_flat = X_train.reshape(X_train.shape[0], -1)
        X_val_flat = X_val.reshape(X_val.shape[0], -1)
        X_test_flat = X_test.reshape(X_test.shape[0], -1)
        
        self.scaler = StandardScaler()
        X_train_scaled = self.scaler.fit_transform(X_train_flat)
        X_val_scaled = self.scaler.transform(X_val_flat)
        X_test_scaled = self.scaler.transform(X_test_flat)
        
        # 恢复原始形状
        X_train = X_train_scaled.reshape(original_shape)
        X_val = X_val_scaled.reshape(X_val.shape)
        X_test = X_test_scaled.reshape(X_test.shape)
        
        print(f"  - 标准化后数据范围: [{X_train.min():.3f}, {X_train.max():.3f}]")
        print(f"  - 标准化后均值: {X_train.mean():.3f}")
        print(f"  - 标准化后标准差: {X_train.std():.3f}")
        
        return X_train, X_val, X_test, y_train, y_val, y_test
    
    def create_model(self, input_shape):
        """创建EEGNet模型"""
        print(f"\n🏗️ 创建EEGNet模型...")
        print(f"  - 输入形状: {input_shape}")

        # 输入形状应该是 (channels, time_points, 1)
        # 但create_eegnet_model期望的是 (channels, time_points)
        self.model = create_eegnet_model(
            n_channels=input_shape[0],  # channels是第一维
            n_samples=input_shape[1],   # time_points是第二维
            n_classes=2,
            dropout_rate=0.25
        )
        
        # 编译模型
        self.model.compile(
            optimizer=keras.optimizers.Adam(learning_rate=self.config['learning_rate']),
            loss='sparse_categorical_crossentropy',
            metrics=['accuracy']
        )
        
        print("✅ 模型创建完成")
        print(f"  - 总参数数量: {self.model.count_params():,}")
        
        # 显示模型结构
        print("\n📋 模型结构:")
        self.model.summary()
        
        return self.model
    
    def setup_callbacks(self):
        """设置训练回调函数"""
        print("\n⚙️ 设置训练回调...")
        
        callbacks = [
            # 早停
            keras.callbacks.EarlyStopping(
                monitor='val_accuracy',
                patience=self.config['early_stopping_patience'],
                restore_best_weights=True,
                verbose=1
            ),
            
            # 学习率衰减
            keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=self.config['reduce_lr_patience'],
                min_lr=self.config['min_lr'],
                verbose=1
            ),
            
            # 模型检查点
            keras.callbacks.ModelCheckpoint(
                'temp_best_model.keras',
                monitor='val_accuracy',
                save_best_only=True,
                verbose=1
            ),
            
            # 自定义进度回调
            TrainingProgressCallback()
        ]
        
        print("✅ 回调函数设置完成")
        return callbacks
    
    def train_model(self, X_train, X_val, y_train, y_val):
        """训练模型"""
        print(f"\n🚀 开始训练预训练模型...")
        print(f"训练配置:")
        for key, value in self.config.items():
            print(f"  - {key}: {value}")
        print()
        
        # 设置回调
        callbacks = self.setup_callbacks()
        
        # 记录训练开始时间
        start_time = time.time()
        
        # 开始训练
        print("🔄 训练进行中...")
        print("=" * 80)
        
        self.history = self.model.fit(
            X_train, y_train,
            validation_data=(X_val, y_val),
            epochs=self.config['epochs'],
            batch_size=self.config['batch_size'],
            callbacks=callbacks,
            verbose=1
        )
        
        # 计算训练时间
        training_time = time.time() - start_time
        
        print("=" * 80)
        print(f"✅ 训练完成! 总用时: {training_time:.1f} 秒")
        
        # 加载最佳模型
        if Path('temp_best_model.keras').exists():
            self.model = keras.models.load_model('temp_best_model.keras')
            print("✅ 已加载最佳模型权重")
        
        return self.history
    
    def evaluate_model(self, X_test, y_test):
        """评估模型性能"""
        print(f"\n📊 评估模型性能...")
        
        # 测试集评估
        test_loss, test_accuracy = self.model.evaluate(X_test, y_test, verbose=0)
        
        # 获取训练历史中的最佳性能
        best_train_acc = max(self.history.history['accuracy'])
        best_val_acc = max(self.history.history['val_accuracy'])
        best_train_loss = min(self.history.history['loss'])
        best_val_loss = min(self.history.history['val_loss'])
        
        # 计算训练轮次
        epochs_trained = len(self.history.history['accuracy'])
        
        results = {
            'best_train_accuracy': float(best_train_acc),
            'best_val_accuracy': float(best_val_acc),
            'test_accuracy': float(test_accuracy),
            'best_train_loss': float(best_train_loss),
            'best_val_loss': float(best_val_loss),
            'test_loss': float(test_loss),
            'epochs_trained': epochs_trained,
            'total_epochs': self.config['epochs']
        }
        
        print("📈 训练结果:")
        print(f"  - 最佳训练准确率: {best_train_acc:.4f}")
        print(f"  - 最佳验证准确率: {best_val_acc:.4f}")
        print(f"  - 测试准确率: {test_accuracy:.4f}")
        print(f"  - 最佳训练损失: {best_train_loss:.4f}")
        print(f"  - 最佳验证损失: {best_val_loss:.4f}")
        print(f"  - 测试损失: {test_loss:.4f}")
        print(f"  - 实际训练轮次: {epochs_trained}/{self.config['epochs']}")
        
        # 性能分析
        print(f"\n🔍 性能分析:")
        overfitting = best_train_acc - best_val_acc
        generalization = best_val_acc - test_accuracy
        
        print(f"  - 过拟合程度: {overfitting:.4f} ({'高' if overfitting > 0.1 else '中' if overfitting > 0.05 else '低'})")
        print(f"  - 泛化能力: {generalization:.4f} ({'好' if abs(generalization) < 0.05 else '一般'})")
        
        if test_accuracy > 0.7:
            print("  - 模型质量: 优秀 ✅")
        elif test_accuracy > 0.6:
            print("  - 模型质量: 良好 ✅")
        else:
            print("  - 模型质量: 需要改进 ⚠️")
        
        return results
    
    def save_pretrained_model(self, results, output_dir="pretrained_models"):
        """保存预训练模型"""
        print(f"\n💾 保存预训练模型...")
        
        output_dir = Path(output_dir)
        output_dir.mkdir(exist_ok=True)
        
        # 生成模型文件名
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        model_name = f"eegnet_bci_pretrained_{timestamp}"
        model_path = output_dir / f"{model_name}.keras"
        
        # 保存模型
        self.model.save(str(model_path))
        
        # 保存标准化器
        scaler_path = output_dir / f"{model_name}_scaler.pkl"
        with open(scaler_path, 'wb') as f:
            pickle.dump(self.scaler, f)
        
        # 保存模型信息
        model_info = {
            'model_name': model_name,
            'model_type': 'EEGNet_BCI_Pretrained',
            'dataset': 'BCI Competition IV 2b (Simulated)',
            'input_shape': [3, 1000, 1],  # channels, time_points, 1
            'n_classes': 2,
            'sampling_rate': 250,
            'channels': ['C3', 'Cz', 'C4'],
            'training_config': self.config,
            'performance': results,
            'created_time': time.time(),
            'created_date': time.strftime("%Y-%m-%d %H:%M:%S"),
            'version': '1.0.0'
        }
        
        info_path = output_dir / f"{model_name}_info.json"
        with open(info_path, 'w', encoding='utf-8') as f:
            json.dump(model_info, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 预训练模型已保存:")
        print(f"  - 模型文件: {model_path}")
        print(f"  - 标准化器: {scaler_path}")
        print(f"  - 模型信息: {info_path}")
        
        # 清理临时文件
        temp_file = Path('temp_best_model.keras')
        if temp_file.exists():
            temp_file.unlink()
        
        return model_path, model_info


class TrainingProgressCallback(keras.callbacks.Callback):
    """自定义训练进度回调"""
    
    def on_epoch_begin(self, epoch, logs=None):
        self.epoch_start_time = time.time()
        print(f"\n📍 Epoch {epoch + 1}/{self.params['epochs']}")
    
    def on_epoch_end(self, epoch, logs=None):
        epoch_time = time.time() - self.epoch_start_time
        
        train_acc = logs.get('accuracy', 0)
        val_acc = logs.get('val_accuracy', 0)
        train_loss = logs.get('loss', 0)
        val_loss = logs.get('val_loss', 0)
        lr = float(self.model.optimizer.learning_rate)
        
        print(f"⏱️  用时: {epoch_time:.1f}s | 学习率: {lr:.6f}")
        print(f"📊 训练 - 准确率: {train_acc:.4f}, 损失: {train_loss:.4f}")
        print(f"📊 验证 - 准确率: {val_acc:.4f}, 损失: {val_loss:.4f}")
        
        # 性能趋势分析
        if epoch > 0:
            if val_acc > train_acc:
                print("💡 验证性能优于训练性能，模型泛化良好")
            elif train_acc - val_acc > 0.1:
                print("⚠️  可能存在过拟合，注意监控")


def main():
    """主函数"""
    print("🧠 预训练模型训练工具")
    print("=" * 60)
    
    try:
        # 创建训练器
        trainer = PretrainedModelTrainer()
        
        # 加载数据集
        X, y, subjects, info = trainer.load_dataset()
        
        # 数据预处理
        X_train, X_val, X_test, y_train, y_val, y_test = trainer.preprocess_data(X, y, subjects)
        
        # 创建模型
        model = trainer.create_model(X_train.shape[1:])
        
        # 训练模型
        history = trainer.train_model(X_train, X_val, y_train, y_val)
        
        # 评估模型
        results = trainer.evaluate_model(X_test, y_test)
        
        # 保存模型
        model_path, model_info = trainer.save_pretrained_model(results)
        
        print(f"\n🎉 预训练模型训练完成!")
        print(f"模型已保存到: {model_path}")
        print(f"现在可以在您的系统中使用此预训练模型进行迁移学习")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
